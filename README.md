



# 服务端运行说明

## 环境安装

1. 下载安装`Navicat`，安装`mysql 5.7`

2. 新建数据库（字符集：`utf8`；排序规则：utf8_general_ci`；），导入项目数据库

3. 下载安装`IDEA`，导入服务端项目，IDEA自动下载相关项目依赖

4. 在IDEA中下载`JDK 1.8`

5. 下载 `Redis-x64-3.2.100` 并解压，点击`redis-server.exe`文件，将弹出的窗口一直挂在后台

6. 修改项目数据库配置`byun-module-system——main——resources——application-dev.yml（140-142）`

7. 运行：`byun-module-system——main——java——com.byun——MclSystemApplication`，注意后台挂着redis
## 问题

### IDEA下载依赖失败问题

1. **报错提示**：com.fasterxml.jackson:jackson-bom:pom:2.8.8 failed to transfer from http://0.0.0.0/ during a previous attempt. This failure was cached in the local repository and resolution is not reattempted until the update interval of maven-default-http-blocker has elapsed or updates are forced. Original error: Could not transfer artifact com.fasterxml.jackson:jackson-bom:pom:2.8.8 from/to maven-default-http-blocker (http://0.0.0.0/): Blocked mirror for repositories: [central (http://maven.aliyun.com/nexus/content/groups/public/, default, releases+snapshots)]

   *Since Maven 3.8.1 http repositories are blocked.*

   Possible solutions:

    - Check that Maven pom files do not contain http repository http://maven.aliyun.com/nexus/content/groups/public/
    - Add a mirror(s) for http://maven.aliyun.com/nexus/content/groups/public/ that allows http url in the Maven settings.xml
    - Downgrade Maven to version 3.8.1 or earlier in settings

   **报错原因**：Maven配置中使用了一个被阻止的HTTP镜像源，导致无法从该源下载依赖项。从错误信息来看，你使用了阿里云的一个HTTP镜像源，而Maven 3.8.1及更高版本默认禁止使用非安全的HTTP协议下载依赖。

   **解决方法**：

   ​	升级到使用HTTPS的镜像源：推荐的做法是将Maven的镜像源配置改为使用HTTPS协议。

   ​	在你的Maven项目或全局设置中，找到``settings.xml`文件（通常位于`~/.m2/settings.xml`），确保使用的镜像源是HTTPS的。(settings.xml的地址也可以在IDEA里面的setting——maven——user settings file中查看)

   做如下修改：（注意，这里的`<url>`标签中使用了`https://`而不是`http://`。）

   ```xml
   <mirrors>
     <mirror>
       <id>aliyun-maven</id>
       <mirrorOf>central</mirrorOf>
       <name>Aliyun Maven Mirror</name>
       <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
       <blocked>false</blocked>
     </mirror>
   </mirrors>
   
   ```

   **如果`.m2`目录下没有`settings.xml`文件**：

   ​	在`~/.m2`目录下新建一个`settings.xml`文件，并添加以下内容：

   ```xml
   <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                                 http://maven.apache.org/xsd/settings-1.0.0.xsd">
   
     <mirrors>
       <mirror>
         <id>aliyun-maven</id>
         <mirrorOf>central</mirrorOf>
         <name>Aliyun Maven Mirror</name>
         <url>https://maven.aliyun.com/repository/public</url>
       </mirror>
     </mirrors>
   
   </settings>
   
   ```

   