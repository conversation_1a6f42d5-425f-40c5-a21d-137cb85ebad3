package com.byun.common.api.dto;

import lombok.Data;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * @description: 文件下载 cloud api 用到的接口传输对象
 * <AUTHOR>
 * @date 2021/11/3 13:50
 * @version 1.0
 */
@Data
public class FileDownDTO implements Serializable {

    private static final long serialVersionUID = 6749126258686446019L;

    private String filePath;
    private String uploadpath;
    private String uploadType;
    private HttpServletResponse response;

    public FileDownDTO(){}

    public FileDownDTO(String filePath, String uploadpath, String uploadType,HttpServletResponse response){
        this.filePath = filePath;
        this.uploadpath = uploadpath;
        this.uploadType = uploadType;
        this.response = response;
    }
}
