package com.byun.common.api.dto;
import lombok.Data;
import com.byun.common.system.vo.LoginUser;
import java.io.Serializable;
import java.util.Date;

/**
 * @description: 日志对象 cloud api 用到的接口传输对象
 * <AUTHOR>
 * @date 2021/11/3 13:50
 * @version 1.0
 */
@Data
public class LogDTO implements Serializable {

    private static final long serialVersionUID = 8482720462943906924L;

    /**内容*/
    private String logContent;

    /**日志类型(-1:定时任务;0:登录日志;1:操作日志;)  */
    private Integer logType;

    /**操作类型(1查询，2添加，3修改，4删除,5导入，6导出) */
    private Integer operateType;

    /**登录用户 */
    private LoginUser loginUser;

    private String id;
    private String createBy;
    private Date createTime;
    private Long costTime;
    private String ip;

    /**请求参数 */
    private String requestParam;

    /**请求类型*/
    private String requestType;

    /**请求路径*/
    private String requestUrl;

    /**请求方法 */
    private String method;

    /**操作人用户名称*/
    private String username;

    /**操作人用户账户*/
    private String userid;

    public LogDTO(){

    }

    public LogDTO(String logContent, Integer logType, Integer operatetype){
        this.logContent = logContent;
        this.logType = logType;
        this.operateType = operatetype;
    }

    public LogDTO(String logContent, Integer logType, Integer operatetype, LoginUser loginUser){
        this.logContent = logContent;
        this.logType = logType;
        this.operateType = operatetype;
        this.loginUser = loginUser;
    }
}
