package com.byun.common.aspect;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.byun.common.constant.enums.OperateTypeEnum;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import com.byun.common.api.dto.LogDTO;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.enums.ModuleType;
import com.byun.modules.base.service.BaseCommonService;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.IPUtils;
import com.byun.common.util.SpringContextUtils;
import com.byun.common.util.WxlConvertUtils;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;


/**
 * @description: 系统日志AOP类 规则:AutoLog注解
 * <AUTHOR>
 * @date 2021/11/3 14:13
 * @version 1.0
 */
@Aspect
@Component
public class AutoLogAspect {
    @Resource
    private BaseCommonService baseCommonService;
    /** 
     * @description:  切点Pointcut
     * @param: []
     * @return: void
     * <AUTHOR>
     * @date: 2021/11/3 14:16
     */ 
    @Pointcut("@annotation(com.byun.common.aspect.annotation.AutoLog)")
    public void logPointCut() {

    }
    /**
     * @description: Around 属于环绕增强，能控制切点执行前，执行后
     * @param point 获取当前切入的方法的参数、代理类等信息，因此可以记录一些信息，验证一些信息等；
     * @return: java.lang.Object
     * <AUTHOR>
     * @date: 2021/11/4 17:10
     */
    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        //保存日志
        saveSysLog(point, time, result);
        return result;
    }

   /**
    * @description:
    * @param joinPoint 切入方法参数、代理类等信息
    * @param time
    * @param obj 方法执行的返回值
    * @return: void
    * <AUTHOR>
    * @date: 2021/11/4 17:11
    */
    private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object obj) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        LogDTO dto = new LogDTO();
        //获取方法上面的AutoLog注解
        AutoLog syslog = method.getAnnotation(AutoLog.class);
        if(syslog != null){
            //日志内容
            String content = syslog.value();
            //模块类型：线上
            if(syslog.module()== ModuleType.ONLINE){
                content = getOnlineLogContent(obj, content);
            }
            //注解上的描述,操作日志内容
            dto.setLogType(syslog.logType());
            dto.setLogContent(content);
        }

        //请求的方法名
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = signature.getName();
        dto.setMethod(className + "." + methodName + "()");


        //操作类型 OP:操作
        if (CommonConstant.LOG_TYPE_2 == dto.getLogType()) {
            dto.setOperateType(getOperateType(methodName, syslog.operateType()));
        }

        //获取request
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        //请求的参数
        dto.setRequestParam(getReqestParams(request,joinPoint));
        //设置IP地址
        dto.setIp(IPUtils.getIpAddr(request));
        //获取登录用户信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if(sysUser!=null){
            dto.setUserid(sysUser.getUsername());
            dto.setUsername(WxlConvertUtils.isNotEmpty(sysUser.getRealNameName()) ? sysUser.getRealNameName() : sysUser.getRealname());
        }
        //耗时
        dto.setCostTime(time);
        dto.setCreateTime(new Date());
        //保存系统日志
        baseCommonService.addLog(dto);
    }


    /** 
     * @description: 获取操作类型 
     * @param: [methodName, operateType]
     * @return: int
     * <AUTHOR>
     * @date: 2021/11/3 14:16
     */ 
    private int getOperateType(String methodName,int operateType) {
        if (operateType > 0) {
            return operateType;
        }
        //阿里云代码扫描规范(不允许任何魔法值出现在代码中)------------
        return OperateTypeEnum.getTypeByMethodName(methodName);
        //阿里云代码扫描规范(不允许任何魔法值出现在代码中)------------
    }

    /** 
     * @description: 获取请求参数 
     * @param: [request, joinPoint]
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2021/11/3 14:15
     */ 
    private String getReqestParams(HttpServletRequest request, JoinPoint joinPoint) {
        String httpMethod = request.getMethod();
        String params = "";
        if ("POST".equals(httpMethod) || "PUT".equals(httpMethod) || "PATCH".equals(httpMethod)) {
            Object[] paramsArray = joinPoint.getArgs();
            // java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
            //  https://my.oschina.net/mengzhang6/blog/2395893
            Object[] arguments  = new Object[paramsArray.length];
            for (int i = 0; i < paramsArray.length; i++) {
                if (paramsArray[i] instanceof BindingResult || paramsArray[i] instanceof ServletRequest || paramsArray[i] instanceof ServletResponse || paramsArray[i] instanceof MultipartFile) {
                    //ServletRequest不能序列化，从入参里排除，否则报异常：java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
                    //ServletResponse不能序列化 从入参里排除，否则报异常：java.lang.IllegalStateException: getOutputStream() has already been called for this response
                    continue;
                }
                arguments[i] = paramsArray[i];
            }
            //update-begin 日志数据太长的直接过滤掉
            PropertyFilter profilter = new PropertyFilter() {
                @Override
                public boolean apply(Object o, String name, Object value) {
                    if(value!=null && value.toString().length()>500){
                        return false;
                    }
                    return true;
                }
            };
            params = JSONObject.toJSONString(arguments, profilter);
            //update-end 日志数据太长的直接过滤掉
        } else {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            // 请求的方法参数值
            Object[] args = joinPoint.getArgs();
            // 请求的方法参数名称
            LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
            String[] paramNames = u.getParameterNames(method);
            if (args != null && paramNames != null) {
                for (int i = 0; i < args.length; i++) {
                    params += "  " + paramNames[i] + ": " + args[i];
                }
            }
        }
        return params;
    }
    /**
     * online日志内容拼接
     * @param obj
     * @param content
     * @return
     */
    private String getOnlineLogContent(Object obj, String content){
        if (Result.class.isInstance(obj)){
            Result res = (Result)obj;
            String msg = res.getMessage();
            String tableName = res.getOnlTable();
            if(WxlConvertUtils.isNotEmpty(tableName)){
                content+=",表名:"+tableName;
            }
            if(res.isSuccess()){
                content+= ","+(WxlConvertUtils.isEmpty(msg)?"操作成功":msg);
            }else{
                content+= ","+(WxlConvertUtils.isEmpty(msg)?"操作失败":msg);
            }
        }
        return content;
    }
}
