package com.byun.common.aspect.annotation;

import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.enums.ModuleType;

import java.lang.annotation.*;

/**
 * @description: 系统日志注解
 * <AUTHOR>
 * @date 2021/11/3 14:01
 * @version 1.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AutoLog {

	/**
	 * 日志内容
	 * 
	 * @return
	 */
	String value() default "";

	/**
	 * 日志类型
	 * 
	 * @return 1:登录日志;2:操作日志;3:定时任务;
	 */
	int logType() default CommonConstant.LOG_TYPE_2;
	
	/**
	 * 操作日志类型
	 * 
	 * @return （1查询，2添加，3修改，4删除,5导入，6导出）
	 */
	int operateType() default 0;

	/**
	 * 模块类型 默认为common
	 * @return
	 */
	ModuleType module() default ModuleType.COMMON;
}
