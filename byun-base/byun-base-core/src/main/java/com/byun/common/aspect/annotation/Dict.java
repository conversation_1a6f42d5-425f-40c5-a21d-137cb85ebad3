package com.byun.common.aspect.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description: 字典注解
 * <AUTHOR>
 * @date 2021/11/3 14:08
 * @version 1.0
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Dict {
    /**
     * @description: 数据code
     * @param: []
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2021/11/3 14:08
     */
    String dicCode();

    /**
     * @description: 数据Text
     * @param: []
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2021/11/3 14:08
     */
    String dicText() default "";

   /**
    * @description:
    * @param: []
    * @return: java.lang.String
    * <AUTHOR>
    * @date: 2021/11/3 14:09
    */
    String dictTable() default "";
}
