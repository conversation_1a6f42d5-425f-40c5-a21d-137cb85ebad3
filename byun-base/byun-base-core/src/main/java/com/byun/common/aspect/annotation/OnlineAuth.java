package com.byun.common.aspect.annotation;

import java.lang.annotation.*;

/**
 * @description: TODO  online请求拦截专用注解
 * <AUTHOR>
 * @date 2021/11/3 14:10
 * @version 1.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE,ElementType.METHOD})
@Documented
public @interface OnlineAuth {

    /** 
     * @description: 请求关键字，在xxx/code之前的字符串 
     * @param: []
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2021/11/3 15:13
     */ 
    String value();
}
