package com.byun.common.aspect.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description: 数据权限注解 
 * <AUTHOR>
 * @date 2021/11/3 14:11
 * @version 1.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE,ElementType.METHOD})
@Documented
public @interface PermissionData {
	/** 
	 * @description: 暂时没用 
	 * @param: []
	 * @return: java.lang.String
	 * <AUTHOR>
	 * @date: 2021/11/3 14:12
	 */ 
	String value() default "";
	
	
	/** 
	 * @description: 配置菜单的组件路径,用于数据权限 
	 * @param: []
	 * @return: java.lang.String
	 * <AUTHOR>
	 * @date: 2021/11/3 15:13
	 */ 
	String pageComponent() default "";
}