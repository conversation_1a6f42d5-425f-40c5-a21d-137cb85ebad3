package com.byun.common.constant;

import io.swagger.models.auth.In;

/**
 * @description: 公共常量
 * <AUTHOR>
 * @date 2021/11/3 16:26
 * @version 1.0
 */
public interface  CommonConstant {

    /**
     * 正常状态
     */
    Integer STATUS_NORMAL = 0;

    /**
     * 禁用状态
     */
    Integer STATUS_DISABLE = -1;
    /**
     * 邮件发送
     */
    Integer SEND_EMAIL_OK = 1;
    /**
     * 未发送
     */
    Integer SEND_EMAIL_NO = 0;
    /**
     * 删除标志
     */
    Integer DEL_FLAG_1 = 1;

    /**
     * 未删除
     */
    Integer DEL_FLAG_0 = 0;
    /**
     * 表示审核失败
     */
    Integer DEL_FLAG_2 = 2;
    /**
     * 系统日志类型： 登录
     */
    int LOG_TYPE_1 = 1;

    /**
     * 系统日志类型： 操作
     */
    int LOG_TYPE_2 = 2;

    /**
     * 操作日志类型： 查询
     */
    int OPERATE_TYPE_1 = 1;

    /**
     * 操作日志类型： 添加
     */
    int OPERATE_TYPE_2 = 2;

    /**
     * 操作日志类型： 更新
     */
    int OPERATE_TYPE_3 = 3;

    /**
     * 操作日志类型： 删除
     */
    int OPERATE_TYPE_4 = 4;

    /**
     * 操作日志类型： 倒入
     */
    int OPERATE_TYPE_5 = 5;

    /**
     * 操作日志类型： 导出
     */
    int OPERATE_TYPE_6 = 6;
	
	
	/** {@code 500 Server Error} (HTTP/1.0 - RFC 1945) */
    public static final Integer SC_INTERNAL_SERVER_ERROR_500 = 500;
    /** {@code 200 OK} (HTTP/1.0 - RFC 1945) */
    public static final Integer SC_OK_200 = 200;
    
    /**访问权限认证未通过 510*/
    public static final Integer SC_BYUN_NO_AUTHZ=510;

    /** 登录用户Shiro权限缓存KEY前缀 */
    public static String PREFIX_USER_SHIRO_CACHE  = "shiro:cache:com.byun.config.shiro.ShiroRealm.authorizationCache:";
    /** 登录用户Token令牌缓存KEY前缀 */
    public static final String PREFIX_USER_TOKEN  = "prefix_user_token_";
    /** Token缓存时间：3600秒即一小时 */
    public static final int  TOKEN_EXPIRE_TIME  = 3600;
    

    /**
     *  0：一级菜单
     */
    public static final Integer MENU_TYPE_0  = 0;
   /**
    *  1：子菜单 
    */
    public static final Integer MENU_TYPE_1  = 1;
    /**
     *  2：按钮权限
     */
    public static final Integer MENU_TYPE_2  = 2;
    
    /**通告对象类型（USER:指定用户，ALL:全体用户）*/
    public static final String MSG_TYPE_UESR  = "USER";
    public static final String MSG_TYPE_ALL  = "ALL";
    
    /**发布（0未发布，1已发布，2已撤销）*/
    public static final String NO_SEND  = "0";
    public static final String HAS_SEND  = "1";
    public static final String HAS_CANCLE  = "2";
    
    /**阅读状态（0未读，1已读）*/
    public static final String HAS_READ_FLAG  = "1";
    public static final String NO_READ_FLAG  = "0";
    
    /**优先级（L低，M中，H高，T成功，F失败）*/
    public static final String PRIORITY_L  = "L";
    public static final String PRIORITY_M  = "M";
    public static final String PRIORITY_H  = "H";
    public static final String PRIORITY_T  = "T";
    public static final String PRIORITY_F  = "F";
    /**
     * 短信模板方式  0 .登录模板、1.注册模板、2.忘记密码模板、3、添加其他人报名信息时，手机验证码
     */
    public static final String SMS_TPL_TYPE_0  = "0";
    public static final String SMS_TPL_TYPE_1  = "1";
    public static final String SMS_TPL_TYPE_2  = "2";
    public static final String SMS_TPL_TYPE_3  = "3";

    /**
     * 状态(0无效1有效)
     */
    public static final String STATUS_0 = "0";
    public static final String STATUS_1 = "1";
    
    /**
     * 同步任务流引擎1同步0不同步
     */
    public static final Integer ACT_SYNC_1 = 1;
    public static final Integer ACT_SYNC_0 = 0;

    /**
     * 消息类型1:通知公告2:系统消息
     */
    public static final String MSG_CATEGORY_1 = "1";
    public static final String MSG_CATEGORY_2 = "2";
    
    /**
     * 是否配置菜单的数据权限 1是0否
     */
    public static final Integer RULE_FLAG_0 = 0;
    public static final Integer RULE_FLAG_1 = 1;

    /**
     * 是否用户已被冻结 1正常(解冻) 2冻结
     */
    public static final Integer USER_UNFREEZE = 1;
    public static final Integer USER_FREEZE = 2;
    
    /**字典翻译文本后缀*/
    public static final String DICT_TEXT_SUFFIX = "_dictText";

    /**
     * 表单设计器主表类型
     */
    public static final Integer DESIGN_FORM_TYPE_MAIN = 1;

    /**
     * 表单设计器子表表类型
     */
    public static final Integer DESIGN_FORM_TYPE_SUB = 2;

    /**
     * 表单设计器URL授权通过
     */
    public static final Integer DESIGN_FORM_URL_STATUS_PASSED = 1;

    /**
     * 表单设计器URL授权未通过
     */
    public static final Integer DESIGN_FORM_URL_STATUS_NOT_PASSED = 2;

    /**
     * 表单设计器新增 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_ADD = "add";
    /**
     * 表单设计器修改 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_EDIT = "edit";
    /**
     * 表单设计器详情 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_DETAIL = "detail";
    /**
     * 表单设计器复用数据 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_REUSE = "reuse";
    /**
     * 表单设计器编辑 Flag （已弃用）
     */
    public static final String DESIGN_FORM_URL_TYPE_VIEW = "view";

    /**
     * online参数值设置（是：Y, 否：N）
     */
    public static final String ONLINE_PARAM_VAL_IS_TURE = "Y";
    public static final String ONLINE_PARAM_VAL_IS_FALSE = "N";

    /**
     * 文件上传类型（本地：local，Minio：minio，阿里云：alioss）
     */
    public static final String UPLOAD_TYPE_LOCAL = "local";
    public static final String UPLOAD_TYPE_MINIO = "minio";
    public static final String UPLOAD_TYPE_OSS = "alioss";

    /**
     * 文档上传自定义桶名称
     */
    public static final String UPLOAD_CUSTOM_BUCKET = "eoafile";
    /**
     * 文档上传自定义路径
     */
    public static final String UPLOAD_CUSTOM_PATH = "eoafile";
    /**
     * 文件外链接有效天数
     */
    public static final Integer UPLOAD_EFFECTIVE_DAYS = 1;

    /**
     * 员工身份 （1:普通员工  2:上级 3用户 4、招聘官）
     */
    public static final Integer USER_IDENTITY_1 = 1;
    public static final Integer USER_IDENTITY_2 = 2;
    public static final Integer USER_IDENTITY_3 = 3;
    public static final Integer USER_IDENTITY_4 = 4;

    /** sys_user 表 username 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_USERNAME = "uniq_sys_user_username";
    /** sys_user 表 work_no 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_WORK_NO = "uniq_sys_user_work_no";
    /** sys_user 表 phone 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_PHONE = "uniq_sys_user_phone";
    /** sys_user 表 email 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_EMAIL = "uniq_sys_user_email";
    /** sys_quartz_job 表 job_class_name 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_JOB_CLASS_NAME = "uniq_job_class_name";
    /** sys_position 表 code 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_CODE = "uniq_code";
    /** sys_role 表 code 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_ROLE_CODE = "uniq_sys_role_role_code";
    /** sys_depart 表 code 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_DEPART_ORG_CODE = "uniq_depart_org_code";
    /**
     * 在线聊天 是否为默认分组
     */
    public static final String IM_DEFAULT_GROUP = "1";
    /**
     * 在线聊天 图片文件保存路径
     */
    public static final String IM_UPLOAD_CUSTOM_PATH = "imfile";
    /**
     * 在线聊天 用户状态
     */
    public static final String IM_STATUS_ONLINE = "online";

    /**
     * 在线聊天 SOCKET消息类型
     */
    public static final String IM_SOCKET_TYPE = "chatMessage";

    /**
     * 在线聊天 是否开启默认添加好友 1是 0否
     */
    public static final String IM_DEFAULT_ADD_FRIEND = "1";

    /**
     * 在线聊天 用户好友缓存前缀
     */
    public static final String IM_PREFIX_USER_FRIEND_CACHE = "sys:cache:im:im_prefix_user_friend_";

    /**
     * 考勤补卡业务状态 （1：同意  2：不同意）
     */
    public static final String SIGN_PATCH_BIZ_STATUS_1 = "1";
    public static final String SIGN_PATCH_BIZ_STATUS_2 = "2";

    /**
     * 公文文档上传自定义路径
     */
    public static final String UPLOAD_CUSTOM_PATH_OFFICIAL = "officialdoc";
     /**
     * 公文文档下载自定义路径
     */
    public static final String DOWNLOAD_CUSTOM_PATH_OFFICIAL = "officaldown";

    /**
     * WPS存储值类别(1 code文号 2 text（WPS模板还是公文发文模板）)
     */
    public static final String WPS_TYPE_1="1";
    public static final String WPS_TYPE_2="2";


    public final static String X_ACCESS_TOKEN = "X-Access-Token";
    public final static String X_SIGN = "X-Sign";
    public final static String X_TIMESTAMP = "X-TIMESTAMP";
    public final static String TOKEN_IS_INVALID_MSG = "Token失效，请重新登录!";

    /**
     * 多租户 请求头
     */
    public final static String TENANT_ID = "tenant-id";

    /**
     * 微服务读取配置文件属性 服务地址
     */
    public final static String CLOUD_SERVER_KEY = "spring.cloud.nacos.discovery.server-addr";

    /**
     * 第三方登录 验证密码/创建用户 都需要设置一个操作码 防止被恶意调用
     */
    public final static String THIRD_LOGIN_CODE = "third_login_code";

    /**
     * 第三方APP同步方向：本地 --> 第三方APP
     */
    String THIRD_SYNC_TO_APP = "SYNC_TO_APP";
    /**
     * 第三方APP同步方向：第三方APP --> 本地
     */
    String THIRD_SYNC_TO_LOCAL = "SYNC_TO_LOCAL";

    /** 系统通告消息状态：0=未发布 */
    String ANNOUNCEMENT_SEND_STATUS_0 = "0";
    /** 系统通告消息状态：1=已发布 */
    String ANNOUNCEMENT_SEND_STATUS_1 = "1";
    /** 系统通告消息状态：2=已撤销 */
    String ANNOUNCEMENT_SEND_STATUS_2 = "2";

    /** redis 小程序缓存*/
    public static final String SESSION_KEY_CACHE = "session_key_cache:";//小程序登录sessionKey缓存

    /** 用户类型 1、普通管理员 2、上级管理员 3、灵活用工普通用户 4、招聘官 */
    public static final Integer USER_IDENTITY_ADMIN = 1;
    public static final Integer USER_IDENTITY_HIGHER_ADMIN = 2;
    public static final Integer USER_IDENTITY_STAFFING_USER = 3;
    public static final Integer USER_IDENTITY_STAFFING_EXTENSION = 4;

    /** 状态类型状态0未发布 1完成 2发布中 3服务中 4结算中 5 已下架*/
    public static final Integer WORK_STATUS_0 = 0;
    public static final Integer WORK_STATUS_1 = 1;
    public static final Integer WORK_STATUS_2 = 2;
    public static final Integer WORK_STATUS_3 = 3;
    public static final Integer WORK_STATUS_4 = 4;
    public static final Integer WORK_STATUS_5 = 5;
    /*0公开  1 不公开*/
//    public static final Integer WORK_IS_PUBLIC_0 = 0;
//    public static final Integer WORK_IS_PUBLIC_1 = 1;

    /** 是否在首页展示0不展示1展示*/
    public static final Integer LIST_SHOW_STATUS_0 = 0;
    public static final Integer LIST_SHOW_STATUS_1 = 1;
    /** 状态0 撤销 1完成 2申请中 3工作中 4待结算 5待就职 6未通过审核 7通过审核后被剔除
     * 8任务完成 9 申请已过期 10任务完成申请中 11 结算中
     * */
    public static final Integer ORDER_STATUS_0 = 0;
    public static final Integer ORDER_STATUS_1 = 1;

    public static final Integer ORDER_STATUS_2 = 2;
    public static final Integer ORDER_STATUS_3 = 3;
    public static final Integer ORDER_STATUS_4 = 4;
    public static final Integer ORDER_STATUS_5 = 5;

    public static final Integer ORDER_STATUS_6 = 6;
    public static final Integer ORDER_STATUS_7 = 7;
    public static final Integer ORDER_STATUS_8 = 8;
    public static final Integer ORDER_STATUS_9 = 9;
    public static final Integer ORDER_STATUS_10 = 10;
    public static final Integer ORDER_STATUS_11 = 11;
    public static final Integer ORDER_STATUS_13 = 13;
    public static final Integer ORDER_STATUS_14 = 14;
    /*任务凭证 */
    public static final Integer MISSION_STATUS_0 = 0 ; //未上传
    public static final Integer MISSION_STATUS_1 = 1 ; //已上传
    public static final Integer MISSION_STATUS_2 = 2 ; //审核通过
    public static final Integer MISSION_STATUS_3 = 3 ; //审核未通过
    public static final Integer MISSION_STATUS_4 = 4 ; //确定评分
    /*是否已评分*/
    public static final Integer SCORE_STATUS_0 = 0 ; //未评分
    public static final Integer SCORE_STATUS_1 = 1;//已评分

    //报名状态 0拒绝 1通过 2 待审核 3待面试
    public static final Integer APPLY_STATUS_0 = 0;
    public static final Integer APPLY_STATUS_1 = 1;
    public static final Integer APPLY_STATUS_2 = 2;
    public static final Integer APPLY_STATUS_3 = 3;

 //  用户关系0自己1家人2朋友
    public static final Integer USER_REL_0 = 0;
    public static final Integer USER_REL_1 = 1;
    public static final Integer USER_REL_2 = 2;
    //  用户系统关系1、用工第一推广人2、用户绑定的招聘官3、招聘官下的代理（下级招聘官）
    public static final Integer SYS_USER_REL_1 = 1;
    public static final Integer SYS_USER_REL_2 = 2;
    public static final Integer SYS_USER_REL_3 = 3;

    //  任务单类型，1、自己报名 2、他人帮忙报名 3、补录
    public static final Integer ENROLL_TYPE_1 = 1;
    public static final Integer ENROLL_TYPE_2 = 2;
    public static final Integer ENROLL_TYPE_3 = 3;
    /** redis 灵工任务报名通过数*/
    public static final String WORK_ADOPT_NUM_CACHE = "work_adopt_num_cache:";//任务通过报名人数
    /** 部门类别 1公司，2组织机构，3岗位*/
    public static final String DEPART_ORG_CATEGORY_1 = "1";//顶级公司
    public static final String DEPART_ORG_CATEGORY_2 = "2";//组织机构
    public static final String DEPART_ORG_CATEGORY_3 = "3";//3岗位( 权限层级)
    /**部门类型  1一级部门 2子部门 大于1的都是子部门*/
//    public static final String DEPART_ORG_TYPE_1 = "1";
//    public static final String DEPART_ORG_TYPE_2 = "2";
    /**记录类型 1、浏览记录2、任职记录3、收藏记录4、申请记录5、撤销记录6、开始任务记录7、任务完成记录 */
    public  static final Integer  RECORD_TYPE_1 = 1;
    public  static final Integer  RECORD_TYPE_2 = 2;
    public  static final Integer  RECORD_TYPE_3 = 3;
    public  static final Integer  RECORD_TYPE_4 = 4;
    public  static final Integer  RECORD_TYPE_5 = 5;
    public  static final Integer  RECORD_TYPE_6 = 6;
    public  static final Integer  RECORD_TYPE_7 = 7;
    /**公司角色编码*/
    public static final String DEPART_ROLE_CODE_SUPER_ADMIN = "_a01";
    public static final String DEPART_ROLE_CODE_WORK = "_b01";
    public static final String DEPART_ROLE_CODE_RECRUITER = "_c01";
//    public static final String DEPART_ROLE_CODE_AGENT = "d01";

    public  static final Integer VERIFICATION_1 = 1;//已验证

    public  static final Integer VERIFICATION_0 = 0;//未验证

    /**POST请求*/
    String HTTP_POST = "POST";

    /**PUT请求*/
    String HTTP_PUT = "PUT";

    /**PATCH请求*/
    String HTTP_PATCH = "PATCH";

    /**未知的*/
    String UNKNOWN = "unknown";

    /**字符串http*/
    String STR_HTTP = "http";

    /**String 类型的空值*/
    String STRING_NULL = "null";

    /**前端vue3版本Header参数名*/
    String VERSION="X-Version";

    /**存储在线程变量里的动态表名*/
    String DYNAMIC_TABLE_NAME="DYNAMIC_TABLE_NAME";
    /**
     * http:// http协议
     */
    String HTTP_PROTOCOL = "http://";

    /**
     * https:// https协议
     */
    String HTTPS_PROTOCOL = "https://";

    /** 部门表唯一key，id */
    String DEPART_KEY_ID = "id";
    /** 部门表唯一key，orgCode */
    String DEPART_KEY_ORG_CODE = "orgCode";

    /**
     * 发消息 会传递一些信息到map
     */
    String NOTICE_MSG_SUMMARY = "NOTICE_MSG_SUMMARY";

    /**
     * 发消息 会传递一个业务ID到map
     */
    String NOTICE_MSG_BUS_ID = "NOTICE_MSG_BUS_ID";

    /**
     * 邮箱消息中地址登录时地址后携带的token,需要替换成真实的token值
     */
    String LOGIN_TOKEN = "{LOGIN_TOKEN}";

    /**
     * 模板消息中 跳转地址的对应的key
     */
    String MSG_HREF_URL = "url";

    //签到
    Integer CLOCK_TYPE_0 = 0;//其他
    Integer CLOCK_TYPE_1 = 1;//签到
    Integer CLOCK_TYPE_2 = 2;//签退
    //签到状态
    Integer CLOCK_STATE_FLAG_0 = 0;//未签到
    Integer CLOCK_STATE_FLAG_1 = 1;//正常签到
    Integer CLOCK_STATE_FLAG_2 = 2;//迟到
    Integer CLOCK_STATE_FLAG_3 = 3;//早退
    Integer CLOCK_STATE_FLAG_4 = 4;//超出签到范围,签到
    Integer CLOCK_STATE_FLAG_5 = 5;//超出签到范围，迟到
    Integer CLOCK_STATE_FLAG_6 = 6;//超出签到范围，早退
    Integer CLOCK_STATE_FLAG_7 = 7;//补卡
    Integer CLOCK_STATE_FLAG_8 = 8;//无排版

    //银行卡
    String CARD_STATUS0 = "0" ;//未绑定
    String CARD_STATUS1 = "1" ;//已绑定
    //用户黑名单
    String BLACKLIST0 = "0"; //正常用户
    String BLACKLIST1 = "1"; //黑名单用户
    //黑名单类型 开除、劝退、内盗、劳动争议 其他
    String BLACLLISTTYPE0 = "0"; // 开除
    String BLACLLISTTYPE1 = "1"; // 劝退
    String BLACLLISTTYPE2 = "2"; //内盗
    String BLACLLISTTYPE3 = "3"; //劳动争议
    String BLACLLISTTYPE4 = "4"; //其他

    //任务凭证类型
    String  TASK_VOUCHER_TYPE0 = "0"; //图片
    String  TASK_VOUCHER_TYPE1 = "1"; //视频
    //是否签约
    Integer AGENCY_STATUS0 = 0;
    Integer AGENCY_STATUS1 = 1; //已签
    //实名认证
    Integer AUTHENTICATION_STATUS0 = 0;
    Integer AUTHENTICATION_STATUS1 = 1; //已认证
    //客户端类型
    String   CLIENT_TYPE_0 = "0"; //网页
    String   CLIENT_TYPE_1 = "1"; //小程序
    //健康证审核 (0未审核 1审核通过 2审核驳回 3已过期)
    Integer HEALTHY_ID_STATUS_0 = 0;
    Integer HEALTHY_ID_STATUS_1 = 1;
    Integer HEALTHY_ID_STATUS_2 = 2;
    Integer HEALTHY_ID_STATUS_3 = 3;
    /**通用状态 Y-N-NN  0-1-2  **/
    Integer GENNERASTATUS0 = 0;
    Integer GENNERASTATUS1 = 1;
    /**
     * 结算类型 1银行卡 2支付宝
     */
    Integer ACCOUNTTYPE1 = 1;
    Integer ACCOUNTTYPE2 = 2;
    /**
     * 签约编号 1银行卡 2支付宝
     */
    String TASKNO1 = "*******************";
    String TASKNO2 = "*******************";
    String SETTLETANACCOUNSTATUS0 = "0";//待提现
    String SETTLETANACCOUNSTATUS1 = "1";//提现成功
    String SETTLETANACCOUNSTATUS2 = "2";//提现中
    String SETTLETANACCOUNSTATUS99 = "99";//提现失败
    Integer payStatusFlag0 = 0;
    Integer payStatusFlag1 = 1;
    /**
     * 用户月工时0正常1超标
     */
    String  MONTH_WORK_TIME_CATCH = "USER_MONTH_WORK_TIME_STATUS_";
    Integer USER_MONTH_WORK_TIME_STATUS0 = 0;
    Integer USER_MONTH_WORK_TIME_STATUS1 = 1;
    /**
     * 问卷填写状态
     * 0未填  1已填
     */
    Integer QUESTIONNAIRESTATUS0 = 0;
    Integer QUESTIONNAIRESTATUS1 = 1;
}
