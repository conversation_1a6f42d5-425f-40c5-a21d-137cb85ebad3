package com.byun.common.system.base.controller;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:  Controller基类
 * <AUTHOR>
 * @date 2021/11/4 16:40
 * @version 1.0
 */
public class ByunController<T, S extends IService<T>> {
    //byunController注入service时改用protected修饰，能避免重复引用service
    @Autowired
    protected S baseService;
}
