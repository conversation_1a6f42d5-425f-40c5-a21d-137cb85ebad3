package com.byun.common.system.base.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:  租户模式Entity基类
 * @date 2021/11/4 22:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ByunTenantEntity extends ByunEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 租户编码 **/
    private java.lang.Integer tenantId;
}
