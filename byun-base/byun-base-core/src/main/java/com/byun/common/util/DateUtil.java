package com.byun.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class DateUtil {
    /**
     * 获取两个日期之间相隔的日期包含开始日期和结束日期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return list<String>  yyyy-MM-dd
     */
    public static List<String> getDateRange(Date startDate, Date endDate) {
        List<String> dateList = new ArrayList<>();
        Calendar calStart = Calendar.getInstance();
        Calendar calEnd = Calendar.getInstance();
        calStart.setTime(startDate);
        calEnd.setTime(endDate);
        // 添加开始日期
        dateList.add(formatDate(calStart.getTime()));
        // 计算日期差并遍历每个日期
        while (calStart.before(calEnd)) {
            calStart.add(Calendar.DAY_OF_MONTH, 1); // 增加一天
            if (!calStart.after(calEnd)) { // 确保不超过结束日期
                dateList.add(formatDate(calStart.getTime()));
            }
        }
        return dateList;
    }
    private static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }
    /** 
     * 计算出两个日期之间相差的天数
     * 建议date1 大于 date2 这样计算的值为正数
     * @param date1 日期1
     * @param date2 日期2
     * @return date1 - date2
     */  
    public static int dateInterval(long date1, long date2) {  
        if(date2 > date1){  
            date2 = date2 + date1;  
            date1 = date2 - date1;  
            date2 = date2 - date1;  
        }  
        /* 
         * Canlendar 该类是一个抽象类  
         * 提供了丰富的日历字段 
         *  
         * 本程序中使用到了 
         * Calendar.YEAR    日期中的年份 
         * Calendar.DAY_OF_YEAR     当前年中的天数 
         * getActualMaximum(Calendar.DAY_OF_YEAR) 返回今年是 365 天还是366天 
         */  
        Calendar calendar1 = Calendar.getInstance(); // 获得一个日历  
        calendar1.setTimeInMillis(date1); // 用给定的 long 值设置此 Calendar 的当前时间值。  
          
        Calendar calendar2 = Calendar.getInstance();  
        calendar2.setTimeInMillis(date2);  
        // 先判断是否同年  
        int y1 = calendar1.get(Calendar.YEAR);  
        int y2 = calendar2.get(Calendar.YEAR);  
          
        int d1 = calendar1.get(Calendar.DAY_OF_YEAR);  
        int d2 = calendar2.get(Calendar.DAY_OF_YEAR);  
        int maxDays = 0;  
        int day = 0;  
        if(y1 - y2 > 0){  
            day = numerical(maxDays, d1, d2, y1, y2, calendar2);  
        }else{  
            day = d1 - d2;  
        }  
        return day;  
    }  
      
    /** 
     * 日期间隔计算 
     * 计算公式(示例): 
     *      20121230 - 20071001 
     *      取出20121230这一年过了多少天 d1 = 365     取出20071001这一年过了多少天 d2 = 274 
     *      如果2007年这一年有366天就要让间隔的天数+1，因为2月份有29日。 
     * @param maxDays   用于记录一年中有365天还是366天 
     * @param d1    表示在这年中过了多少天 
     * @param d2    表示在这年中过了多少天 
     * @param y1    当前为2010年 
     * @param y2    当前为2012年 
     * @param calendar  根据日历对象来获取一年中有多少天 
     * @return  计算后日期间隔的天数 
     */  
    public static int numerical(int maxDays, int d1, int d2, int y1, int y2, Calendar calendar){  
        int day = d1 - d2;  
        int betweenYears = y1 - y2;  
        List<Integer> d366 = new ArrayList<Integer>();  
          
        if(calendar.getActualMaximum(Calendar.DAY_OF_YEAR) == 366){  
            System.out.println(calendar.getActualMaximum(Calendar.DAY_OF_YEAR));  
            day += 1;  
        }  
          
        for (int i = 0; i < betweenYears; i++) {  
            // 当年 + 1 设置下一年中有多少天  
            calendar.set(Calendar.YEAR, (calendar.get(Calendar.YEAR)) + 1);  
            maxDays = calendar.getActualMaximum(Calendar.DAY_OF_YEAR);  
            // 第一个 366 天不用 + 1 将所有366记录，先不进行加入然后再少加一个  
            if(maxDays != 366){  
                day += maxDays;  
            }else{  
                d366.add(maxDays);  
            }  
            // 如果最后一个 maxDays 等于366 day - 1  
            if(i == betweenYears-1 && betweenYears > 1 && maxDays == 366){  
                day -= 1;  
            }  
        }  
          
        for(int i = 0; i < d366.size(); i++){  
            // 一个或一个以上的366天  
            if(d366.size() >= 1){  
                day += d366.get(i);  
            }  
//          else{  
//              day -= 1;  
//          }  
        }  
        return day;  
    }  
      
    /** 
     * 将日期字符串装换成日期 
     * @param strDate   日期支持年月日 示例:yyyyMMdd 
     * @return  1970年1月1日器日期的毫秒数 
     */  
    public static long getDateTime(String strDate) {  
        return getDateByFormat(strDate, "yyyyMMdd").getTime();  
    }  
      
    /** 
     * @param strDate   日期字符串 
     * @param format    日期格式 
     * @return      Date 
     */  
    public static Date getDateByFormat(String strDate, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return (sdf.parse(strDate));
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * Mon Mar 18 2024 15:24:42 GMT+0800格式转换为yyyy——MM-dd 字符串格式
     * @param strDate 日期字符串
     * @param format SimpleDateFormat 格式 EEE MMM dd HH:mm:ss zzz yyyy ,Locale.ENGLISH
     * @return
     */
    public static String getSimpleDateFormat(String strDate, SimpleDateFormat format){
        try {
            Date date = format.parse(strDate);
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = outputFormat.format(date);
            return formattedDate;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
    /** 
     * 获取当前系统时间  
     *  @return 返回短时间字符串格式yyyy-MM-dd  
     * */
    public static String getNowStringDate() {  
    	Date currentTime = new Date(); 
    	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
    	String dateString = formatter.format(currentTime);
    	return dateString; 
    } 
    public static String getFormatStringDate(Date date) {  
    	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd"); 
    	String dateString = formatter.format(date);
    	return dateString; 
    }

    public static String getFormatStringTime(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        String dateString = formatter.format(date);
        return dateString;
    }
    /** 
     * 获取当前系统时间  
     *  @return 返回短时间字符串格式yyyy-MM-dd  
     * */
    public static Date getNowDate() {  
    	Date currentTime = new Date(); 
    	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");  
    	String dateString = formatter.format(currentTime);  
    	ParsePosition pos = new ParsePosition(8);  
    	Date currentTime_2 = formatter.parse(dateString, pos);  
    	return currentTime_2; }
	/**
	 * @param timeBucket 时间格式yyyy-MM-dd hh:mm:ss时间段商品时间比较
	 * 判断给的时间和 当前时间的大小，如果大于当前时间，返回true,否则返回false
	 * @return
	 */
	public static  boolean compareTimeBucketDate(Date timeBucket)  {
    	try {
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
			Date d2 = df.parse(DateUtil.getNowStringDate());//系统时间
			long value = timeBucket.getTime() - d2.getTime();
			if(value>0){
				return true;
			}else{
				return false;
			}	
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return false;	
		}
	public static  boolean compareDate(Date startDate,Date middleDate,Date endDate)  {
    	
			try {
				long value1 = startDate.getTime() - middleDate.getTime();
				long value2 = endDate.getTime() - middleDate.getTime();
				if(value2>=0&&value1<=0){
					return true;
				}else{
					return false;
				}
			} catch (Exception e) {
				return false;
			}
		}
	
    /** 
     * 计算出两个日期之间相差的小时数
     * 建议date1 大于 date2 这样计算的值为正数 
     * @param date1 日期1 
     * @param date2 日期2 
     * @return date1 - date2 
     */  
    public static int hourInterval(long date1, long date2) {  
        if(date2 > date1){  
            date2 = date2 + date1;  
            date1 = date2 - date1;  
            date2 = date2 - date1;  
        }  
        long value = date1 - date2;//计算出相差的毫秒数
        long hour = value/3600000;//除以1000是秒，再除以3600是小时
		return (int) hour;
   
    }
    public static String getDateFormatter(Date date){
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return df.format(date);
	}
    
    /**
     * 计算两个日期(date1>date2)相差的分钟数
     * <AUTHOR>
     * @date 2019-4-13 上午9:29:49
     * @param date1
     * @param date2
     * @return 
     * @return int
     */
    public static int minuteInterval(long date1, long date2) {  
        if(date2 > date1){  
            date2 = date2 + date1;  
            date1 = date2 - date1;  
            date2 = date2 - date1;  
        }  
        long value = date1 - date2;//计算出相差的毫秒数
        long minute = value/60000;//除以1000是秒，再除以60是分钟
		return (int) minute;
    }
    
    /**
     * 计算两个时间相差天数
     * <AUTHOR>
     * @date 2019-6-5 下午4:35:33
     * @param date1
     * @param date2
     * @return int
     */
    public static int dayInterval(long date1, long date2) {  
        if(date2 > date1){  
            date2 = date2 + date1;  
            date1 = date2 - date1;  
            date2 = date2 - date1;  
        }  
        long value = date1 - date2;//计算出相差的毫秒数
        long minute = value/1000/60/60/24;//除以1000是秒，再除以60是分钟，再除以60是小时数，再除以24是天数
		return (int) minute;
    }
    
    /**
     * 根据时间类型比较时间大小 
     * @param source
     * @param traget
     * @param type "YYYY-MM-DD" "yyyyMMdd HH:mm:ss"  类型可自定义
     * @param 传递时间的对比格式
     * @return 
     *  0 ：source和traget时间相同    
     *  1 ：source比traget时间大  
     *  -1：source比traget时间小
     * @throws Exception
     */
    public static int DateCompare(String source, String traget, String type) throws Exception{
        int ret = 2;
        SimpleDateFormat format = new SimpleDateFormat(type);
        Date sourcedate = format.parse(source);
        Date tragetdate = format.parse(traget);
        ret = sourcedate.compareTo(tragetdate);
        return ret;
    }
}
