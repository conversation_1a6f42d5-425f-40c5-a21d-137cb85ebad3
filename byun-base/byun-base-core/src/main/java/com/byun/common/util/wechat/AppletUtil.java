package com.byun.common.util.wechat;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信小程序工具类
 * @date 2021/11/16 0:15
 */
@Slf4j
public class AppletUtil {
    private static String staSecret;
    private static String staAppid;
    private static String qrcodeVersion;

    public static void setStaSecret(String staSecret) {
        AppletUtil.staSecret = staSecret;
    }

    public static void setStaAppid(String staAppid) {
        AppletUtil.staAppid = staAppid;
    }

    public static void setQrcodeVersion(String qrcodeVersion) {
        AppletUtil.qrcodeVersion = qrcodeVersion;
    }

    public static String getStaSecret() {
        return staSecret;
    }

    public static String getStaAppid() {
        return staAppid;
    }

    public static String getQrcodeVersion() {
        return qrcodeVersion;
    }

    private static String code2Session_https = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";
    /**
     * @description: 获取openid、unionid
     * <AUTHOR>
     * @date 2021/11/16 0:28
     * @version 1.0
     */
    public static String getCode2SessionUrl(String code){
        return String.format(code2Session_https,
                staAppid,
                staSecret,
                code);
    }
    public static String AccessToken_https = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    /**
     * @description: 微信服务器 获取 AccessToken的请求地址
     * <AUTHOR>
     * @date 2021/11/16 0:33
     * @version 1.0
     */
    public static String getAccessTokenUrl(){
        return String.format(AccessToken_https,
                staAppid,
                staSecret);
    }

    public static String Send_https = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/send?access_token=%s";
    /**
     * @description: 发送模版消息
     * <AUTHOR>
     * @date 2021/11/16 0:34
     * @version 1.0
     */
    public static String getSendUrl(String accessToken){
        return String.format(Send_https,accessToken);
    }

    public static String getwxacode_https = "https://api.weixin.qq.com/wxa/getwxacode?access_token=%s";
    /**
     * @description: (未使用)
     *      * 获取小程序码，适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制
     *      * 与 createwxaqrcode_https 总共生成的码数量限制为 100,000，请谨慎调用。
     * <AUTHOR>
     * @date 2021/11/16 0:43
     * @version 1.0
     */
    public static String getwxacode(String accessToken){
        return String.format(getwxacode_https,accessToken);
    }

    public static String getwxacodeunlimit_https = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s";
    /**
     * @description: (当前业务使用的是该接口)
     *      * 获取小程序码，适用于需要的码数量极多的业务场景。通过该接口生成的小程序码，永久有效，数量暂无限制。
     * <AUTHOR>
     * @date 2021/11/16 0:42
     * @version 1.0
     */
    public static String getwxacodeunlimit(String accessToken){
        return String.format(getwxacodeunlimit_https,accessToken);
    }

    public static String createwxaqrcode_https = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=%s";
    /**
     * @description: (未使用)
     *      * 获取小程序二维码，适用于需要的码数量较少的业务场景。通过该接口生成的小程序码，永久有效，有数量限制
     *      * 与 getwxacode 总共生成的码数量限制为 100,000，请谨慎调用。
     * <AUTHOR>
     * @date 2021/11/16 0:42
     * @version 1.0
     */
    public static String createwxaqrcode_https(String accessToken){
        return String.format(createwxaqrcode_https,accessToken);
    }

    public static String orderquery_https = "https://api.mch.weixin.qq.com/pay/orderquery";
    /**
     * @description: 主动查询订单
     * <AUTHOR>
     * @date 2021/11/16 0:42
     * @version 1.0
     */
    public static String orderquery_https(){
        return orderquery_https;
    }
}
