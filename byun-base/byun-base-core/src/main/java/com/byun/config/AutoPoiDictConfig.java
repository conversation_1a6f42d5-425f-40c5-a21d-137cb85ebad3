package com.byun.config;

import lombok.extern.slf4j.Slf4j;
import com.byun.common.api.CommonAPI;
import com.byun.common.system.vo.DictModel;
import com.byun.common.util.WxlConvertUtils;
import org.jeecgframework.dict.service.AutoPoiDictServiceI;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: TODO	需替换为自己的AUTOPOI
 * 描述：AutoPoi Excel注解支持字典参数设置
 *  举例： @Excel(name = "性别", width = 15, dicCode = "sex")
 * 1、导出的时候会根据字典配置，把值1,2翻译成：男、女;
 * 2、导入的时候，会把男、女翻译成1,2存进数据库;
 * <AUTHOR>
 * @date 2021/11/4 14:42
 * @version 1.0
 */
@Slf4j
@Service
public class AutoPoiDictConfig implements AutoPoiDictServiceI {
	@Lazy
	@Resource
	private CommonAPI commonAPI;

	/**
	 * 通过字典查询easypoi，所需字典文本
	 * 
	 * @Author:scott 
	 * @since：2019-04-09
	 * @return
	 */
	@Override
	public String[] queryDict(String dicTable, String dicCode, String dicText) {
		List<String> dictReplaces = new ArrayList<String>();
		List<DictModel> dictList = null;
		// step.1 如果没有字典表则使用系统字典表
		if (WxlConvertUtils.isEmpty(dicTable)) {
			dictList = commonAPI.queryDictItemsByCode(dicCode);
		} else {
			try {
				dicText = WxlConvertUtils.getString(dicText, dicCode);
				dictList = commonAPI.queryTableDictItemsByCode(dicTable, dicText, dicCode);
			} catch (Exception e) {
				log.error(e.getMessage(),e);
			}
		}
		for (DictModel t : dictList) {
			if(t!=null){
				dictReplaces.add(t.getText() + "_" + t.getValue());
			}
		}
		if (dictReplaces != null && dictReplaces.size() != 0) {
			log.info("---AutoPoi--Get_DB_Dict------"+ dictReplaces.toString());
			return dictReplaces.toArray(new String[dictReplaces.size()]);
		}
		return null;
	}
}
