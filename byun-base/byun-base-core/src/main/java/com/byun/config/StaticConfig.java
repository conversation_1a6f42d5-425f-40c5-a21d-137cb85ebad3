package com.byun.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 设置静态参数初始化
 * <AUTHOR>
 * @date 2021/11/4 14:51
 * @version 1.0
 */
@Component
@Data
public class StaticConfig {

    @Value("${byun.oss.accessKey}")
    private String accessKeyId;

    @Value("${byun.oss.secretKey}")
    private String accessKeySecret;

    @Value(value = "${spring.mail.username}")
    private String emailFrom;

    /**
     * 签名密钥串
     */
    @Value(value = "${byun.signatureSecret}")
    private String signatureSecret;


    /*@Bean
    public void initStatic() {
       DySmsHelper.setAccessKeyId(accessKeyId);
       DySmsHelper.setAccessKeySecret(accessKeySecret);
       EmailSendMsgHandle.setEmailFrom(emailFrom);
    }*/

}
