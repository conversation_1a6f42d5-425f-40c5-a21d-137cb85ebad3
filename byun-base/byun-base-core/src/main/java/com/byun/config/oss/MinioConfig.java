package com.byun.config.oss;

import com.byun.common.util.MinioUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

/**
 * @description: Minio文件上传配置文件
 * <AUTHOR>
 * @date 2021/11/4 11:01
 * @version 1.0
 */
//@Slf4j
//@Configuration
public class MinioConfig {
    @Value(value = "${byun.minio.minio_url}")
    private String minioUrl;
    @Value(value = "${byun.minio.minio_name}")
    private String minioName;
    @Value(value = "${byun.minio.minio_pass}")
    private String minioPass;
    @Value(value = "${byun.minio.bucketName}")
    private String bucketName;

    @Bean
    public void initMinio(){
        if(!minioUrl.startsWith("http")){
            minioUrl = "http://" + minioUrl;
        }
        if(!minioUrl.endsWith("/")){
            minioUrl = minioUrl.concat("/");
        }
        MinioUtil.setMinioUrl(minioUrl);
        MinioUtil.setMinioName(minioName);
        MinioUtil.setMinioPass(minioPass);
        MinioUtil.setBucketName(bucketName);
    }

}
