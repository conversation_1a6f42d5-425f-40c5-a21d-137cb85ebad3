package com.byun.config.oss;

import com.byun.common.util.oss.OssBootUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description: OSS云存储 配置
 * <AUTHOR>
 * @date 2021/11/4 11:01
 * @version 1.0
 */
@Configuration
public class OssConfiguration {

    @Value("${byun.oss.endpoint}")
    private String endpoint;
    @Value("${byun.oss.accessKey}")
    private String accessKeyId;
    @Value("${byun.oss.secretKey}")
    private String accessKeySecret;
    @Value("${byun.oss.bucketName}")
    private String bucketName;
    @Value("${byun.oss.staticDomain:}")
    private String staticDomain;


    @Bean
    public void initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);

    }

}