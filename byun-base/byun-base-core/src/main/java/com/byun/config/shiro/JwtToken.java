package com.byun.config.shiro;
 
import org.apache.shiro.authc.AuthenticationToken;

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/4 11:06
 * @version 1.0
 */
public class JwtToken implements AuthenticationToken {
	
	private static final long serialVersionUID = 1L;
	private String token;
 
    public JwtToken(String token) {
        this.token = token;
    }
 
    @Override
    public Object getPrincipal() {
        return token;
    }
 
    @Override
    public Object getCredentials() {
        return token;
    }
}
