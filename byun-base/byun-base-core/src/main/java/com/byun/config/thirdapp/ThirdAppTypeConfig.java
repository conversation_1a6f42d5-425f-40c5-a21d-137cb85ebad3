package com.byun.config.thirdapp;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @description: 第三方APP配置
 * <AUTHOR>
 * @date 2021/11/4 11:58
 * @version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "third-app.type")
public class ThirdAppTypeConfig {

    /**
     * 对应企业微信配置
     */
    private ThirdAppTypeItemVo WECHAT_ENTERPRISE;
    /**
     * 对应钉钉配置
     */
    private ThirdAppTypeItemVo DINGTALK;

}
