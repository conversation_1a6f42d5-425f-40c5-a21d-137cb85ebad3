package com.byun.config.wechat;

import com.byun.common.util.wechat.AppletUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信小程序配置
 * @date 2021/11/16 0:04
 */
@Configuration
public class AppletConfig {
    @Value("${byun.wechat.applet.staSecret}")
    private String staSecret;
    @Value("${byun.wechat.applet.staAppid}")
    private String staAppid;
    @Value("${byun.wechat.version.qrcode}")
    private String qrcodeVersion;

    @Bean
    public void initAppletConfig() {
        AppletUtil.setStaAppid(staAppid);
        AppletUtil.setStaSecret(staSecret);
        AppletUtil.setQrcodeVersion(qrcodeVersion);
    }

}
