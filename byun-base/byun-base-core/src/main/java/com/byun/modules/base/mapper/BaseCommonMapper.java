package com.byun.modules.base.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;
import com.byun.common.api.dto.LogDTO;

/**
 * @description: 日志保存
 * <AUTHOR>
 * @date 2021/11/4 15:15
 * @version 1.0
 */
public interface BaseCommonMapper {

    /**
     * 保存日志
     * @param dto
     */
    //@SqlParser(filter=true)
    @InterceptorIgnore(illegalSql = "true", tenantLine = "true")
    void saveLog(@Param("dto")LogDTO dto);

}
