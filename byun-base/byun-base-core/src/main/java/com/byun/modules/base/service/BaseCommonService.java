package com.byun.modules.base.service;

import com.byun.common.api.dto.LogDTO;
import com.byun.common.system.vo.LoginUser;

/**
 * @description: common服务 日志操作
 * <AUTHOR>
 * @date 2021/11/3 15:15
 * @version 1.0
 */
public interface BaseCommonService {

    /**
     * 保存日志
     * @param logDTO
     */
    void addLog(LogDTO logDTO);

    /**
     * 保存日志
     * @param LogContent
     * @param logType
     * @param operateType
     * @param user
     */
    void addLog(String LogContent, Integer logType, Integer operateType, LoginUser user);

    /**
     * 保存日志
     * @param LogContent
     * @param logType
     * @param operateType
     */
    void addLog(String LogContent, Integer logType, Integer operateType);

}
