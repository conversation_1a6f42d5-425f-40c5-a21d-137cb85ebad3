package com.byun.common.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype .Component;

import java.lang.annotation.*;

/**
 * @description: 消息队列初始化注解
 * <AUTHOR>
 * @date 2021/11/4 15:12
 * @version 1.0
 */
@Documented
@Inherited
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface RabbitComponent {
    @AliasFor(
            annotation = Component.class
    )
    String value();
}
