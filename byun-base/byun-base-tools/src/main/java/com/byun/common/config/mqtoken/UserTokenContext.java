package com.byun.common.config.mqtoken;


/**
 * @description: 用户token上下文
 * <AUTHOR>
 * @date 2021/11/4 15:13
 * @version 1.0
 */
public class UserTokenContext {

    private static ThreadLocal<String> userToken = new ThreadLocal<String>();

    public UserTokenContext() {
    }

    public static String getToken(){
        return userToken.get();
    }

    public static void setToken(String token){
        userToken.set(token);
    }
}
