package com.byun.common.modules.redis.receiver;


import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import com.byun.common.base.BaseMap;
import com.byun.common.constant.GlobalConstants;
import com.byun.common.util.SpringContextHolder;
import org.springframework.stereotype.Component;

/**
 * @description: Rides接受消息并调用业务逻辑处理器
 * <AUTHOR>
 * @date 2021/11/4 15:29
 * @version 1.0
 */
@Component
@Data
public class RedisReceiver {


    /**
     *
     *
     * @param params
     */
    public void onMessage(BaseMap params) {
        Object handlerName = params.get(GlobalConstants.HANDLER_NAME);
        com.byun.common.modules.redis.listener.ByunRedisListerer messageListener = SpringContextHolder.getHandler(handlerName.toString(), com.byun.common.modules.redis.listener.ByunRedisListerer.class);
        if (ObjectUtil.isNotEmpty(messageListener)) {
            messageListener.onMessage(params);
        }
    }

}
