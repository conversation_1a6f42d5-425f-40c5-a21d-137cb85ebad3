<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.byun</groupId>
        <artifactId>byun-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>byun-module-system</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.byun</groupId>
            <artifactId>byun-base-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--微服务模式下修改为true,跳过此打包插件，否则微服务模块无法引用-->
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
