package com.byun.modules.bosong.controller;
import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.RestUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.wechat.AppletUtil;
import com.byun.modules.bosong.entity.BoSong;
import com.byun.modules.bosong.entity.BoSongCoupon;
import com.byun.modules.bosong.entity.BosongUserCoupons;
import com.byun.modules.bosong.service.IBoSongCouponService;
import com.byun.modules.bosong.service.IBosongUserCouponsService;
import com.byun.modules.bosong.utils.QrUtil;
import com.byun.modules.bosong.utils.WechatQrcodeUtil;
import com.byun.modules.bosong.vo.BoSongCouponDTO;
import com.byun.modules.bosong.vo.BoSongUserAndCouponDTO;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysUserService;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bosong/coupon")
public class BoSongCouponController {
    @Autowired
    private IBoSongCouponService boSongCouponService;
    @Autowired
    private IBosongUserCouponsService bosongUserCouponsService;
    @Autowired
    private ISysUserService sysUserService;
    @GetMapping("list")
    public Result listPage(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            Result.error("登录失效");
        }
        Page<BoSongCoupon> page = boSongCouponService.page(new Page<>(pageNo, pageSize));
        System.out.println(page.getRecords());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<BoSongCouponDTO> dtoList = page.getRecords().stream().map(entity -> {
            BoSongCouponDTO dto = new BoSongCouponDTO();
            dto.setId(entity.getId());
            dto.setIssuedQuantity(entity.getIssuedQuantity());
            dto.setCouponName(entity.getCouponName());
            dto.setDiscountAmount(entity.getDiscountAmount());
            dto.setMinAmount(entity.getMinAmount());
            dto.setTotalQuantity(entity.getTotalQuantity());
            dto.setStatus(entity.getStatus());
            if (entity.getValidEndTime() != null) {
                dto.setValidEndTime(entity.getValidEndTime().format(formatter));
            }
            return dto;
        }).collect(Collectors.toList());
        Page<BoSongCouponDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(page.getCurrent());
        dtoPage.setSize(page.getSize());
        dtoPage.setTotal(page.getTotal());
        dtoPage.setRecords(dtoList);
        return Result.OK(dtoPage);
    }
    @PostMapping("create")
    public Result create(@RequestBody BoSongCoupon boSongCoupon) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            Result.error("登录失效");
        }
        String random32 = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        boSongCoupon.setCode(random32);
        boSongCoupon.setCreateBy(user.getUsername());
        boSongCoupon.setValidStartTime(LocalDateTime.now());
        boSongCoupon.setCreatedAt(LocalDateTime.now());
        boSongCoupon.setUpdatedAt(LocalDateTime.now());
        boSongCoupon.setStatus(1);
        boSongCoupon.setIssuedQuantity(0);//默认0
        boSongCoupon.setDelFlag(CommonConstant.DEL_FLAG_0);
        boSongCouponService.save(boSongCoupon);
        return Result.OK();
    }
    /**
     * 小程序生成班级二维码
     *
     * @param json
     * @return
     * @throws Exception
     */
    @PostMapping("/qrcode")
    public Result<?> qrcode(@RequestBody JSONObject json) throws Exception {
        JSONObject params = new JSONObject();
        String url = "pages/coupon/list/list";
        String scene = json.getString("couponId");
        params.put("scene", scene);
        //小程序页面路径
        if (WxlConvertUtils.isNotEmpty(url)) {
            params.put("page", url);
        }
        System.out.println("url---------------"+url);
        System.out.println("scene---------------"+scene);
        String accessToken = QrUtil.postToken();
        System.out.println("accessToken--------------"+accessToken);
        params.put("env_version", AppletUtil.getQrcodeVersion());//要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
        String wxacodeUrl = AppletUtil.getwxacodeunlimit(accessToken);
        System.out.println("wxacodeUrl--------------"+wxacodeUrl);
        ResponseEntity<byte[]> response = RestUtil.request(wxacodeUrl, HttpMethod.POST, null, null, params, byte[].class);

        // 使用工具类处理微信API响应
        return WechatQrcodeUtil.processWechatQrcodeResponse(response);
    }
    @RequestMapping("/exportXls")
    public ModelAndView exportXls(HttpServletRequest request){
        List<BosongUserCoupons> queryList = bosongUserCouponsService.list(null);
        // 构建返回列表
        List<BoSongUserAndCouponDTO> boSongUserAndCouponDTOS = new ArrayList<>();
        if (!queryList.isEmpty()) {
            // 获取用户ID列表
            List<String> sysUserIds = queryList.stream()
                    .map(BosongUserCoupons::getUserId)
                    .collect(Collectors.toList());
            // 查询用户信息列表
            List<SysUser> sysUserList = sysUserService.list(
                    new LambdaQueryWrapper<SysUser>().in(SysUser::getId, sysUserIds)
            );
            // 把用户信息放入 Map（userId -> username）
            Map<String, String> userMap = sysUserList.stream()
                    .collect(Collectors.toMap(SysUser::getId, SysUser::getUsername));
            // 遍历 queryList 构建 DTO
            for (BosongUserCoupons coupon : queryList) {
                BoSongUserAndCouponDTO dto = new BoSongUserAndCouponDTO();
                dto.setPhone(userMap.getOrDefault(coupon.getUserId(), "未知用户"));
                boSongUserAndCouponDTOS.add(dto);
            }
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "列表");
        mv.addObject(NormalExcelConstants.CLASS, BoSongUserAndCouponDTO.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("列表", "报名"));
        mv.addObject(NormalExcelConstants.DATA_LIST, boSongUserAndCouponDTOS);
        return mv;
    }
}
