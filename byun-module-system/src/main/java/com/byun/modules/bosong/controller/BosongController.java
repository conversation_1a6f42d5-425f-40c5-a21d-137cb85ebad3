package com.byun.modules.bosong.controller;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.CommonUtils;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.bosong.entity.BoSong;
import com.byun.modules.bosong.service.IBosongService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 博颂农名工技能培训
 * @date : 2023-3-6 11:31
 */
@Api(tags="博颂农名工技能培训")
@RestController
@RequestMapping("/bosong")
@Slf4j
public class BosongController {
    @Autowired
    private IBosongService bosongService;
    @Value(value = "${byun.path.upload}")
    private String uploadpath;
    /**
     * 本地：local minio：minio 阿里：alioss
     */
    @Value(value="${byun.uploadType}")
    private String uploadType;
    /**
     * 农名工技能培训登记信息保存
     * @param json
     * @return result
     */
    @ApiOperation("博颂-报名信息保存")
    @PostMapping("/save")
    @Transactional
    public Result saveApplicationUser(@RequestBody JSONObject json){
        Result result =new Result();
        BoSong boSong = new BoSong();
        String userName = json.getString("userName");//姓名
        String sex = json.getString("sex");//性别
        String idCard = json.getString("idCard");//身份证号码
        String address = json.getString("address");//身份证地址
        String photos = json.getString("photos");//身份证正面
        String photos2 = json.getString("photos2");//身份证反面
        String phone = json.getString("phone");//联系方式
        String accountType = json.getString("accountType");//户口类型
        String trainingProfessional = json.getString("trainingProfessional");//专业
        String category = json.getString("category");//人员类别
        String degree = json.getString("degree");//学历
        boSong.setUserName(userName);
        //男 0 女 1
        //boSong.setSex()
        if (sex.equals("0")){
            boSong.setSex("男");
        }else if (sex.equals("1")){
            boSong.setId("女");
        }
        boSong.setAddress(address);
        boSong.setIdCard(idCard);
        boSong.setPhotos(photos.substring(1,photos.length()-1));
        boSong.setPhotos2(photos2.substring(1,photos2.length() - 1));
        boSong.setPhone(phone);
        boSong.setAccountType(accountType.substring(7,accountType.length() -1 ));
        boSong.setTrainingProfessional(trainingProfessional);
        boSong.setCreateTime(new Date());
        switch (category){
            case "0" :
                boSong.setCategory("企业职工");
                break;
            case "1" :
                boSong.setCategory("转岗职工");
                break;
            case "2" :
                boSong.setCategory("离校未就业高校毕业生");
                break;
            case "3" :
                boSong.setCategory("贫困家庭子女");
                break;
            case "4" :
                boSong.setCategory("贫困劳动力");
                break;
            case "5" :
                boSong.setCategory("平台经济从业人员");
                break;
            case "6" :
                boSong.setCategory("城乡未继续升学初高中毕业生");
                break;
            case "7" :
                boSong.setCategory("无业人员");
                break;
            case "8" :
                boSong.setCategory("农村转移就业劳动者");
                break;
            case "9" :
                boSong.setCategory("下岗失业人员");
                break;
            case "10" :
                boSong.setCategory("退役军人");
                break;
            case "11" :
                boSong.setCategory("残疾人");
                break;
            case "12" :
                boSong.setCategory("毕业学年高校毕业生");
                break;
            case "13" :
                boSong.setCategory("社区矫正人员");
                break;
        }
        switch (degree){
            case "0" :
                boSong.setDegree("无");
                break;
            case "1" :
                boSong.setDegree("小学");
                break;
            case "2" :
                boSong.setDegree("初中");
                break;
            case "3" :
                boSong.setDegree("职高");
                break;
            case "4" :
                boSong.setDegree("高中");
                break;
            case "5" :
                boSong.setDegree("技师学院");
                break;
            case "6" :
                boSong.setDegree("高级技校");
                break;
            case "7" :
                boSong.setDegree("技校");
                break;
            case "8" :
                boSong.setDegree("中等专业学校");
                break;
            case "9" :
                boSong.setDegree("大学专科和专科学校");
                break;
            case "10" :
                boSong.setDegree("大学本科");
                break;
            case "11" :
                boSong.setDegree("研究生");
                break;
            case "12" :
                boSong.setDegree("博士");
                break;
        }
        boSong.setIsDelete(CommonConstant.DEL_FLAG_0);
        boolean save = bosongService.save(boSong);
        if (save){
            result.setSuccess(true);
            result.setMessage("提交成功");
            result.setCode(200);
            result.setResult(new Date());
        }else {
            result.setSuccess(false);
            result.setMessage("系统异常");
            result.setCode(500);
        }
        return  result;
//        String userName = json.getString("userName");
//        String idcard = json.getString("idcard");
//        String phone = json.getString("phone");
//        String address = json.getString("address");
//        String category = json.getString("category");
//        String training = json.getString("training");
//        String specialized = json.getString("specialized");
//        String intention = json.getString("intention");
//        boSong.setUserName(userName);
//        boSong.setIdcard(idcard);
//        boSong.setPhone(phone);
//        boSong.setAddress(address);
//        //单选 7 {value=   length -1   }  //多选 8 {value=[   length -2   ]}
//        boSong.setCategory(category.substring(7,category.length() - 1));//单选
//        switch (training.length()){
//            case 9 :
//                boSong.setTraining(training.substring(7, training.length() - 1));
//                break;
//            default:
//                boSong.setTraining(training.substring(8,training.length() - 2));
//                break;
//        }
//        switch (specialized.length()){
//            case 9 :
//                boSong.setSpecialized(specialized.substring(7,specialized.length() - 1));
//                break;
//            default:
//                boSong.setSpecialized(specialized.substring(8,specialized.length() - 2));
//                break;
//        }
//        boSong.setIntention(intention.substring(7,intention.length() - 1));//单选
//        boSong.setCreateTime(new Date());
//        boSong.setIsDelete(CommonConstant.DEL_FLAG_0);
//        boolean save = bosongService.save(boSong);
//        Result result = new Result();
//        if (save){
//            result.setSuccess(true);
//            result.setMessage("提交成功");
//            result.setCode(200);
//            result.setResult(new Date());
//        }else {
//            result.setSuccess(false);
//            result.setMessage("系统异常");
//            result.setCode(500);
//        }
//        return  result;
    }

    @ApiOperation("博颂-获取报名用户信息")
    @GetMapping("/bsList")
    public Result bsList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                          HttpServletRequest req) {
        Result result = new Result();
        Page<BoSong> page = new Page<BoSong>(pageNo, pageSize);
        QueryWrapper<BoSong> wrapper = new QueryWrapper();
        wrapper.eq("is_delete", CommonConstant.DEL_FLAG_0);
        Page<BoSong> boSongPage = bosongService.page(page, wrapper);
        List<BoSong> records = boSongPage.getRecords();
        result.setResult(boSongPage);
        result.setSuccess(true);
        result.setCode(200);
        return result;
        }
//        try {
////            records.forEach(record -> {
////                //人员类别
////                if (record.getCategory().equals("0")){
////                    record.setCategory("农村户籍人员");
////                }else if (record.getCategory().equals("1")){
////                    record.setCategory("城镇失业人员");
////                }else if (record.getCategory().equals("2")){
////                    record.setCategory("城镇无业人员");
////                }
////                if (record.getIntention().equals("0")){
////                    record.setIntention("是");
////                }else if (record.getIntention().equals("1")){
////                    record.setIntention("否");
////                }
////                //培训类型
////                if (record.getTraining().length() > 1){
////                    String training = "";
////                    String tr = record.getTraining().replaceAll(" ", "");//排除空格
////                    String[] split = tr.split(",");//“,”分割
////                    Arrays.sort(split);//排序ASC
////                    for (String s : split) {
////                        switch (s){
////                            case "0":
////                                training = training + "40课时," ;
////                                break;
////                            case "1":
////                                training = training + "72课时," ;
////                                break;
////                            case "2":
////                                training = training + "85课时," ;
////                                break;
////                        }
////                    }
////                    training = training.substring(0,training.length() - 1);//割舍最后一个“,”号
////                    record.setTraining(training);
////                }
////                //培训专业
////                if (record.getSpecialized().length() > 1){
////                    String specialized = "";
////                    String tr = record.getSpecialized().replaceAll(" ", "");//排除空格
////                    String[] split2 = tr.split(","); //“,”分割
////                    Arrays.sort(split2);//ASC排序
////                    for (String s : split2) {
////                        switch (s) {
////                            case "0" :
////                                specialized = specialized + "中式烹调师,";
////                                break;
////                            case "1" :
////                                specialized = specialized + "中式面点师,";
////                                break;
////                            case "2" :
////                                specialized = specialized + "西式面点师,";
////                                break;
////                            case "3" :
////                                specialized = specialized + "保育员,";
////                                break;
////                            case "4" :
////                                specialized = specialized + "电工,";
////                                break;
////                            case "5" :
////                                specialized = specialized + "美容师,";
////                                break;
////                            case "6" :
////                                specialized = specialized + "家政服务员,";
////                                break;
////                            case "7" :
////                                specialized = specialized + "茶艺师,";
////                                break;
////                            case "8" :
////                                specialized = specialized + "电子商务师,";
////                                break;
////                            case "9" :
////                                specialized = specialized + "保安员,";
////                                break;
////                            case "10" :
////                                specialized = specialized + "营销员,";
////                                break;
////                            case "11" :
////                                specialized = specialized + "养老护理员,";
////                                break;
////                            case "12" :
////                                specialized = specialized + "插花花艺师,";
////                                break;
////                            case "13" :
////                                specialized = specialized + "物流师,";
////                                break;
////                            case "14" :
////                                specialized = specialized + "公共营养师,";
////                                break;
////                            case "15" :
////                                specialized = specialized + "网络与信息安全管理员,";
////                                break;
////                            case "16" :
////                                specialized = specialized + "企业人力资源管理师,";
////                                break;
////                        }
////                        record.setSpecialized(specialized);
////                    }
////                    specialized = specialized.substring(0,specialized.length() - 1);//割舍最后一个“,”号
////                    record.setSpecialized(specialized);
////                }
//            //});
//            result.setResult(boSongPage);
//            result.setSuccess(true);
//            result.setCode(200);
//            return result;
//        }catch (Exception e){
//            result.setSuccess(false);
//            result.setCode(500);
//            result.setMessage("系统异常");
//            return result;
//        }
  //  }
    @ApiOperation("博颂-报名信息execl导出")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BoSong boSong){
        QueryWrapper<BoSong> queryWrapper = new QueryWrapper<BoSong>();
        List<BoSong> queryList = bosongService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<BoSong> boSongList = new ArrayList<BoSong>();
        if(WxlConvertUtils.isEmpty(selections)) {
            boSongList = queryList;
        }else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            boSongList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "报名列表");
        mv.addObject(NormalExcelConstants.CLASS, BoSong.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("报名列表", "报名"));
        mv.addObject(NormalExcelConstants.DATA_LIST, boSongList);
        return mv;
    }

    /**
     * 身份证上传
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/upload")
    public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
        Result<?> result = new Result<>();
        String savePath = "";
        String bizPath = request.getParameter("biz");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        if(WxlConvertUtils.isEmpty(bizPath)){
            if(CommonConstant.UPLOAD_TYPE_OSS.equals(uploadType)){
                //未指定目录，则用阿里云默认目录 upload
//                bizPath = "upload";
                bizPath = uploadpath;
                //result.setMessage("使用阿里云文件上传时，必须添加目录！");
                //result.setSuccess(false);
                //return result;
            }else{
                bizPath = "";
            }
        }
        if(CommonConstant.UPLOAD_TYPE_LOCAL.equals(uploadType)){
            //update-begin:修改JEditor编辑器本地上传
            savePath = this.uploadLocal(file,bizPath);
            //update-begin:修改JEditor编辑器本地上传
            /**  富文本编辑器及markdown本地上传时，采用返回链接方式
             //针对jeditor编辑器如何使 lcaol模式，采用 base64格式存储
             String jeditor = request.getParameter("jeditor");
             if(ByunConvertUtils.isNotEmpty(jeditor)){
             result.setMessage(CommonConstant.UPLOAD_TYPE_LOCAL);
             result.setSuccess(true);
             return result;
             }else{
             savePath = this.uploadLocal(file,bizPath);
             }
             */
        }else{
            //update-begin:文件上传改造
            savePath = CommonUtils.upload(file, bizPath, uploadType);
            //update-end:文件上传改造
        }
        if(WxlConvertUtils.isNotEmpty(savePath)){
            result.setMessage(savePath);
            result.setSuccess(true);
        }else {
            result.setMessage("上传失败！");
            result.setSuccess(false);
        }
        return result;
    }
    private String uploadLocal(MultipartFile mf,String bizPath){
        try {
            String ctxPath = uploadpath;
            String fileName = null;
            File file = new File(ctxPath + File.separator + bizPath + File.separator );
            if (!file.exists()) {
                file.mkdirs();// 创建文件根目录
            }
            String orgName = mf.getOriginalFilename();// 获取文件名
            orgName = CommonUtils.getFileName(orgName);
            if(orgName.indexOf(".")!=-1){
                fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
            }else{
                fileName = orgName+ "_" + System.currentTimeMillis();
            }
            String savePath = file.getPath() + File.separator + fileName;
            File savefile = new File(savePath);
            FileCopyUtils.copy(mf.getBytes(), savefile);
            String dbpath = null;
            if(WxlConvertUtils.isNotEmpty(bizPath)){
                dbpath = bizPath + File.separator + fileName;
            }else{
                dbpath = fileName;
            }
            if (dbpath.contains("\\")) {
                dbpath = dbpath.replace("\\", "/");
            }
            return dbpath;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

//    public static void main(String[] args) {
//        String a = "[https://wxlmcl-test.oss-cn-beijing.aliyuncs.com/opt/upFiles/777kCztyZhIia6c416838d4e32fa1df7aa26e3e74cb5_1678357963462.png]";
//        System.out.println(a.substring(1,a.length()-1));
//    }

}
