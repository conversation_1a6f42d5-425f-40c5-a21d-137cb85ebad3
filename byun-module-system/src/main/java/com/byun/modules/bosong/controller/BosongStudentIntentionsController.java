package com.byun.modules.bosong.controller;
import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.util.RestUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.wechat.AppletUtil;
import com.byun.modules.bosong.entity.BosongStudentIntentions;
import com.byun.modules.bosong.service.IBosongStudentIntentionsService;
import com.byun.modules.bosong.utils.QrUtil;
import com.byun.modules.bosong.utils.WechatQrcodeUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/bosong/intentions")
public class BosongStudentIntentionsController {
    @Autowired
    private IBosongStudentIntentionsService bosongStudentIntentionsService;
    @PostMapping("/save")
    public Result<?> save(@RequestBody BosongStudentIntentions bosongStudentIntentions) {
        //校验
        if (WxlConvertUtils.isEmpty(bosongStudentIntentions.getName())) {
            return Result.error("姓名为空!");
        }
        if (WxlConvertUtils.isEmpty(bosongStudentIntentions.getPhone())) {
            return Result.error("电话为空!");
        }
        if (WxlConvertUtils.isEmpty(bosongStudentIntentions.getCurrentEducation())) {
            return Result.error("目前最高学历为空!");
        }
        bosongStudentIntentions.setCreatedAt(LocalDateTime.now());
        bosongStudentIntentionsService.save(bosongStudentIntentions);
        return Result.OK("操作成功","");
    }
    @GetMapping("/list")
    public Result<?> list(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<BosongStudentIntentions> page = new Page<>(pageNo, pageSize);
        IPage<BosongStudentIntentions> bosongStudentIntentionsIPage = bosongStudentIntentionsService.page(page, null);
        return Result.OK(bosongStudentIntentionsIPage);
    }
    /**
     * 导出Excel
     */
    @RequestMapping("/exportXls")
    public ModelAndView exportXls() {
        List<BosongStudentIntentions> bosongStudentIntentions = bosongStudentIntentionsService.list(null);
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "学历提升意向表");
        mv.addObject(NormalExcelConstants.CLASS, BosongStudentIntentions.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("列表", "意向表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, bosongStudentIntentions);
        return mv;
    }
    //nohup java -jar mcl-staff.jar >catalina20250719.out 2>&1 &
    /**
     * 小程序生成班级二维码
     *
     * @param json
     * @return
     * @throws Exception
     */
    @PostMapping("/qrcode")
    public Result<?> qrcode(@RequestBody JSONObject json) throws Exception {
        JSONObject params = new JSONObject();
        String url = "pages/education/application/application";//json.getString("pages/education/application/application");
        String scene = "qrcode1qwerf";
        params.put("scene", scene);
        //小程序页面路径
        if (WxlConvertUtils.isNotEmpty(url)) {
            params.put("page", url);
        }
        String accessToken = QrUtil.postToken();
        params.put("env_version", AppletUtil.getQrcodeVersion());//要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
        String wxacodeUrl = AppletUtil.getwxacodeunlimit(accessToken);
        ResponseEntity<byte[]> response = RestUtil.request(wxacodeUrl, HttpMethod.POST, null, null, params, byte[].class);

        // 使用工具类处理微信API响应
        return WechatQrcodeUtil.processWechatQrcodeResponse(response);
    }
}
