package com.byun.modules.bosong.controller;
import cn.hutool.core.codec.Base64;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.bosong.entity.BoSongCoupon;
import com.byun.modules.bosong.entity.BosongUserCoupons;
import com.byun.modules.bosong.service.IBoSongCouponService;
import com.byun.modules.bosong.service.IBosongUserCouponsService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@RestController
@RequestMapping("/bosong/userCoupons")
public class BosongUserCouponsController {
    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CODE_LENGTH = 10;
    private static final Random RANDOM = new Random();
    @Autowired
    private IBosongUserCouponsService boSongUserCouponsService;
    @Autowired
    private IBoSongCouponService boSongCouponService;
    /**
     * 获取本人优惠卷
     */
    @GetMapping("/getCouponsList")
    public Result<?> list(@RequestParam(name = "userId", required = false) String userId,
                          @RequestParam(name = "type",required = false) int type
    ) {
        if (WxlConvertUtils.isEmpty(userId)) {
            return Result.error("用户丢失");
        }
        LambdaQueryWrapper<BosongUserCoupons> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BosongUserCoupons::getUserId, userId);
        queryWrapper.eq(BosongUserCoupons::getIsUsed,type);
        return Result.OK(boSongUserCouponsService.list(queryWrapper));
    }
    /**
     * 领取优惠券
     * @return 领取结果
     */
    @ApiOperation("领取优惠券")
    @Transactional
    @PostMapping("/addUserCoupons")
    public Result<?> addUserCoupons(@RequestBody JSONObject jsonObject) {
        String couponCode = jsonObject.getString("couponCode");
        String userId = jsonObject.getString("userId");
        if (WxlConvertUtils.isEmpty(userId) || WxlConvertUtils.isEmpty(couponCode)) {
            return Result.error("参数丢失");
        }

        BoSongCoupon boSongCoupon = boSongCouponService.getOne(new LambdaQueryWrapper<BoSongCoupon>().eq(BoSongCoupon::getCode, couponCode));
        if (boSongCoupon.getTotalQuantity() <= 0) {
            return Result.OK("来晚了，优惠卷已领取完");
        }
        LambdaQueryWrapper<BosongUserCoupons> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BosongUserCoupons::getUserId, userId);
        queryWrapper.eq(BosongUserCoupons::getCouponId, couponCode);
        if (boSongUserCouponsService.count(queryWrapper) > 0) {
            return Result.OK();
        }
        //正常领取优惠卷
        BosongUserCoupons bosongUserCoupons = new BosongUserCoupons();
        bosongUserCoupons.setUserId(userId);
        bosongUserCoupons.setCouponId(couponCode);
        bosongUserCoupons.setIsUsed(0);//未使用
        bosongUserCoupons.setCode(generateCardCode());//8位数字卡密
        boSongUserCouponsService.save(bosongUserCoupons);

        boSongCoupon.setIssuedQuantity(boSongCoupon.getIssuedQuantity() + 1);//领取数量+1
        boSongCoupon.setTotalQuantity(boSongCoupon.getTotalQuantity() - 1);
        boSongCouponService.updateById(boSongCoupon);
        return Result.OK("领取成功", "");
    }

    /**
     * 生成优惠券使用二维码并返回Base64字符串
     *
     * @param userCouponId 用户优惠券ID
     * @return Base64编码的二维码图片
     */
    @GetMapping("/generateQRCodeBase64/{code}")
    public Result<?> generateQRCodeBase64(@PathVariable("code") String userCouponId) {
        try {
            // 查询用户优惠券信息
            BosongUserCoupons userCoupon = boSongUserCouponsService.getById(userCouponId);
            if (userCoupon == null) {
                return Result.error("用户优惠券不存在");
            }
            // 要编码的二维码内容
            //String qrContent = "couponId=" + userCouponId + "&code=" + userCoupon.getCode();
            String qrContent = userCoupon.getCode();
            // 使用 Hutool 生成二维码 BufferedImage
            BufferedImage image = QrCodeUtil.generate(qrContent, 300, 300);
            // 转为 Base64 字符串
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            String base64 = Base64.encode(outputStream.toByteArray());
            String base64Image = "data:image/png;base64," + base64;
            // 封装返回值到 Map
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("base64Image", base64Image);
            resultMap.put("couponCode", userCoupon.getCode());
            resultMap.put("userCouponId", userCouponId);

            return Result.OK("生成成功", resultMap);
        } catch (Exception e) {
            return Result.error("生成二维码失败: " + e.getMessage());
        }
    }

    /**
     * 验证优惠卷
     * @param body
     * @return
     */
    @PostMapping("/validate")
    public Result<?> validateCoupon(@RequestBody Map<String, Object> body) {
        String code = (String) body.get("code");
        if (WxlConvertUtils.isEmpty( code)) {
            return Result.error("参数错误");
        }
        BosongUserCoupons bosongUserCoupons = boSongUserCouponsService.getOne(new LambdaQueryWrapper<BosongUserCoupons>()
                .eq(BosongUserCoupons::getCode, code));
        if (WxlConvertUtils.isEmpty(bosongUserCoupons)) {
            return Result.error("优惠码无效");
        }
        switch (bosongUserCoupons.getIsUsed()) {
            case 0:
                bosongUserCoupons.setIsUsed(1);
                bosongUserCoupons.setUsedAt(LocalDateTime.now());//使用时间
                boSongUserCouponsService.updateById(bosongUserCoupons);
                return Result.OK("未使用");
            case 1:
                return Result.error("优惠卷已使用");
            case 2:
                return Result.error("已过期");
        }
        return null;
    }


    /**
     * 卡密
     *
     * @return
     */
    public static String generateCardCode() {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(CHAR_POOL.length());
            code.append(CHAR_POOL.charAt(index));
        }
        return code.toString();
    }
}
