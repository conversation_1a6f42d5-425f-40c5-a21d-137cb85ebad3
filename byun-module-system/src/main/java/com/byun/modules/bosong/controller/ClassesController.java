package com.byun.modules.bosong.controller;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.ImportExcelUtil;
import com.byun.common.util.RedisUtil;
import com.byun.common.util.RestUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.wechat.AppletUtil;
import com.byun.modules.bosong.entity.Classes;
import com.byun.modules.bosong.entity.QuestionnaireStatus;
import com.byun.modules.bosong.entity.Student;
import com.byun.modules.bosong.service.IClassesService;
import com.byun.modules.bosong.service.IQuestionnaireStatusService;
import com.byun.modules.bosong.service.IStudentService;
import com.byun.modules.bosong.utils.QrUtil;
import com.byun.modules.bosong.utils.WechatQrcodeUtil;
import com.byun.modules.bosong.vo.ClassesQuestionnaireVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.shiro.SecurityUtils;
import org.checkerframework.checker.units.qual.C;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 班级控制器
 */
@Api(tags = "班级")
@Slf4j
@RestController
@RequestMapping("/bs/classes")
public class ClassesController {
    @Autowired
    private IClassesService classesService;
    @Autowired
    private IStudentService studentService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IQuestionnaireStatusService questionnaireStatusService;

    /**
     * 班级列表
     *
     * @param classes
     * @param pageNo
     * @param pageSize
     * @param request
     * @return
     */
    @GetMapping("/list")
    public Result<?> list(Classes classes,
                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                          HttpServletRequest request) {

        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        System.out.println(user);
        if (!WxlConvertUtils.isNotEmpty(user)) {
            return Result.error("登录失效");
        }
        Page<Classes> page = new Page<>(pageNo, pageSize);
        IPage<Classes> pageList = classesService.getClassPageWithQuestionnaireCount(page, classes.getClassName(), classes.getLecturer1());
//        LambdaQueryWrapper<Classes> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(Classes::getDelFlag, CommonConstant.DEL_FLAG_0);
//        queryWrapper.like(WxlConvertUtils.isNotEmpty(classes.getClassName()), Classes::getClassName, classes.getClassName());
//        queryWrapper.like(WxlConvertUtils.isNotEmpty(classes.getLecturer1()), Classes::getLecturer1, classes.getLecturer1())
//                .or()
//                .like(WxlConvertUtils.isNotEmpty(classes.getLecturer1()), Classes::getLecturer2, classes.getLecturer1());
//        queryWrapper.last("ORDER BY create_time DESC, CAST(serial_number AS UNSIGNED) ASC");
//        Page<Classes> page = new Page<Classes>(pageNo, pageSize);
//        Page<Classes> pageList = classesService.page(page, queryWrapper);
//        List<String> collect = pageList.getRecords().stream().map(Classes::getId).collect(Collectors.toList());
//        questionnaireStatusService.count(new LambdaQueryWrapper<QuestionnaireStatus>().in(QuestionnaireStatus::getClassesId,collect));
        //获取调查问卷个数
        if (!pageList.getRecords().isEmpty()) {
            SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd");
            for (Classes record : pageList.getRecords()) {
                if (record.getStartDate() != null && record.getEndDate() != null) {
                    String startDate = sb.format(record.getStartDate());
                    String endDate = sb.format(record.getEndDate());
                    record.setStartDateAndEndDate(startDate + " - " + endDate);
                }
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 获取单个班级
     *
     * @param id
     * @return
     */
    @GetMapping("/getClassesById/{id}")
    public Result<?> getById(@PathVariable("id") String id) {
        if (WxlConvertUtils.isEmpty(id)) {
            return Result.error("参数缺失");
        }
        Classes classes = classesService.getById(id);
        return Result.OK("操作成功", classes);
    }

    /**
     * 获取班级学生问卷调查填写状态
     *
     * @param id 班级id
     * @return
     */
    @GetMapping("/getClassesStudentListQuestionnaireStatus/{id}")
    public Result<ClassesQuestionnaireVo> getClassesStudentListQuestionnaireStatus(@PathVariable("id") String id) {
        Classes classes = classesService.getById(id);
        List<QuestionnaireStatus> questionnaireStatusList = questionnaireStatusService.list(new LambdaQueryWrapper<QuestionnaireStatus>()
                .eq(QuestionnaireStatus::getClassesId, classes.getId()));
        // List<ClassesQuestionnaireVo> classesQuestionnaireVos = new ArrayList<>();
        System.out.println(questionnaireStatusList);
        //long totalStudents = questionnaireStatusList.size();
        long completedStudents = questionnaireStatusList.stream()
                .filter(qs -> qs.getQuestionnaireStatus() == 1)
                .count();
//        for (QuestionnaireStatus questionnaireStatus : questionnaireStatusList) {
//            ClassesQuestionnaireVo classesQuestionnaireVo = new ClassesQuestionnaireVo();
//            BeanUtils.copyProperties(questionnaireStatus,classesQuestionnaireVo);
//        }
//        JSONObject result = new JSONObject();
//        result.put("students",questionnaireStatusList);
//        result.put("totalStudents",totalStudents);
//        result.put("completedStudents",completedStudents);
//        result.put("classesName",classes.getClassName());
        ClassesQuestionnaireVo classesQuestionnaireVo = new ClassesQuestionnaireVo();
        classesQuestionnaireVo.setQuestionnaireStatusList(questionnaireStatusList);
        classesQuestionnaireVo.setCompletedStudents((int) completedStudents);
        classesQuestionnaireVo.setTotalStudents(questionnaireStatusList.size());
        classesQuestionnaireVo.setClassesName(classes.getClassName());
        return Result.OK(classesQuestionnaireVo);
    }

    /**
     * 添加班级
     *
     * @param classes
     * @return
     * @throws ParseException
     */
    @Transactional
    @PostMapping("/save")
    public Result save(@RequestBody Classes classes) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (!WxlConvertUtils.isNotEmpty(classes)) {
            return Result.error("参数丢失");
        }
        if (WxlConvertUtils.isNotEmpty(classes.getStartDateAndEndDate())) {
            String[] startDateAndEndDateArray = classes.getStartDateAndEndDate().split(",");
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH);
            Date startDate = inputFormat.parse(startDateAndEndDateArray[0]);
            Date endDate = inputFormat.parse(startDateAndEndDateArray[1]);
            classes.setStartDate(startDate);
            classes.setEndDate(endDate);
        }
        classes.setCreateTime(new Date());
        classes.setCreateBy(user.getUsername());
        classes.setCreateById(user.getId());
        classes.setDelFlag(CommonConstant.DEL_FLAG_0);
        classesService.save(classes);
        return Result.OK("添加成功", "");
    }

    /**
     * 班级修改
     *
     * @param classes
     * @return
     * @throws ParseException
     */
    @PutMapping("/edit")
    public Result<?> edit(@RequestBody Classes classes) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (!WxlConvertUtils.isNotEmpty(classes)) {
            return Result.error("参数丢失");
        }
        if (WxlConvertUtils.isNotEmpty(classes.getStartDateAndEndDate())) {
            String[] startDateAndEndDateArray = classes.getStartDateAndEndDate().split(",");
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH);
            Date startDate = inputFormat.parse(startDateAndEndDateArray[0]);
            Date endDate = inputFormat.parse(startDateAndEndDateArray[1]);
            classes.setStartDate(startDate);
            classes.setEndDate(endDate);
        }
        classesService.updateById(classes);
        return Result.OK("修改成功", "");
    }

    /**
     * 小程序生成班级二维码
     *
     * @param json
     * @return
     * @throws Exception
     */
    @PostMapping("/qrcode")
    public Result<?> qrcode(@RequestBody JSONObject json) throws Exception {
        JSONObject params = new JSONObject();
        String url = json.getString("url");
        String scene = json.getString("scene");
        //参数班级ID
        params.put("scene", scene);
        //小程序页面路径
        if (WxlConvertUtils.isNotEmpty(url)) {
            params.put("page", url);
        }
        String accessToken = QrUtil.postToken();
        params.put("env_version", AppletUtil.getQrcodeVersion());//要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
        String wxacodeUrl = AppletUtil.getwxacodeunlimit(accessToken);
        ResponseEntity<byte[]> response = RestUtil.request(wxacodeUrl, HttpMethod.POST, null, null, params, byte[].class);

        // 使用工具类处理微信API响应
        return WechatQrcodeUtil.processWechatQrcodeResponse(response);
    }

    /**
     * 异步导出学员证件照、学生证、身份证、社保卡图片
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/exportLearnerCredentials")
    public Result<?> downloadImages(@RequestBody JSONObject jsonObject) throws IOException {
        String chassesId = jsonObject.getString("id");
        String classesIds = jsonObject.getString("classIds");
        String timeStamp = jsonObject.getString("timeStamp");
        String className = jsonObject.getString("className");

        if (WxlConvertUtils.isEmpty(timeStamp)) {
            return Result.error("参数丢失");
        }
        // 判断是单个导出还是批量导出
        if (WxlConvertUtils.isNotEmpty(chassesId)) {
            // 单个导出
            List<Student> studentsList = studentService.list(new LambdaQueryWrapper<Student>()
                    .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
                    .eq(Student::getChassesId, chassesId)
                    .apply("CAST(serial_number AS UNSIGNED)") //serial_number转换为无符号整数
            );
            if (studentsList.isEmpty()) {
                return Result.error("班级不存在");
            }
            //检查数据
            boolean hasPhoto = studentsList.stream().anyMatch(student ->
                    WxlConvertUtils.isNotEmpty(student.getIdPhoto()) ||
                    WxlConvertUtils.isNotEmpty(student.getStudentIdPhoto()) ||
                    WxlConvertUtils.isNotEmpty(student.getIdCard()) ||
                    WxlConvertUtils.isNotEmpty(student.getSocialSecurityCard())
            );
            if (hasPhoto) {
                String catchKey = className + "_" + timeStamp;
                //异步导出（单个）
                classesService.asyncExport(studentsList, catchKey, className);
                return Result.OK("", catchKey);
            }
            return Result.error("班级数据为空！");

        } else if (WxlConvertUtils.isNotEmpty(classesIds)) {
            // 批量导出
            // 处理可能包含方括号的字符串，如 "[id1,id2,id3]" 或 "id1,id2,id3"
            String cleanClassesIds = classesIds.trim();
            if (cleanClassesIds.startsWith("[") && cleanClassesIds.endsWith("]")) {
                cleanClassesIds = cleanClassesIds.substring(1, cleanClassesIds.length() - 1);
            }
            String[] classIdArray = cleanClassesIds.split(",");
            List<String> classIdList = new ArrayList<>();
            for (String classId : classIdArray) {
                String trimmedId = classId.trim();
                if (WxlConvertUtils.isNotEmpty(trimmedId)) {
                    classIdList.add(trimmedId);
                }
            }

            List<Student> studentsList = studentService.list(new LambdaQueryWrapper<Student>()
                    .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
                    .in(Student::getChassesId, classIdList)
                    .apply("CAST(serial_number AS UNSIGNED)") //serial_number转换为无符号整数
            );
            if (studentsList.isEmpty()) {
                return Result.error("班级不存在");
            }

            //班级分组拆分 key班级Id value学员信息
            Map<String, List<Student>> studentsMap = studentsList.stream()
                    .collect(Collectors.groupingBy(Student::getChassesId));

            //获取班级信息
            List<Classes> classesList = classesService.list(new LambdaQueryWrapper<Classes>()
                    .in(Classes::getId, classIdList)
                    .eq(Classes::getDelFlag, CommonConstant.DEL_FLAG_0));

            String catchKey = "批量导出_" + timeStamp;
            String fileName = "批量导出证件";
            if (WxlConvertUtils.isNotEmpty(className)) {
                fileName = className;
            }

            //异步批量导出
            classesService.asyncBatchExport(studentsMap, classesList, catchKey, fileName);
            return Result.OK("", catchKey);
        } else {
            return Result.error("参数丢失");
        }
    }

    /**
     * 返回导出信息
     *
     * @param key
     * @return
     */
    @GetMapping("/getExportProgress")
    public Result<Map<String, Object>> getExportProgress(@RequestParam("key") String key) {
        Object progressObj = redisUtil.get(key);
        if (progressObj == null) {
            return Result.error("参数丢失", null);
        }
        Map<String, Object> result = new HashMap<>();
        int progress;
        try {
            progress = Integer.parseInt(progressObj.toString());
        } catch (NumberFormatException e) {
            return Result.error("数据异常", null);
        }
        result.put("progress", progress);
        //如果进度为 100，返回文件信息
        if (progress == 100) {
            Map<Object, Object> fileData = redisUtil.hmget(key + "_file_info");
            result.put("fileData", fileData != null ? fileData : new HashMap<>());
        }
        return Result.OK(result);
    }

    /**
     * 下载导出文件.zip,异步删除临时文件
     *
     * @param fileName
     * @return
     */
    @PostMapping("/download/zip")
    public void downloadZipFile(
            @RequestParam("fileName") String fileName,
            @RequestParam("chunkSize") Integer chunkSize,
            @RequestParam("chunkTotal") Integer chunkTotal,
            @RequestParam("index") Integer index,
            @RequestParam("md5") String md5,
            HttpServletResponse response
    ) {
        String os = System.getProperty("os.name").toLowerCase();
        String baseDir;
        // 根据操作系统设置不同的文件路径
        if (os.contains("win")) {
            baseDir = "C:\\opt";  // Windows 系统路径
        } else {
            baseDir = "/home/<USER>";  // Linux MacOs 系统路径
        }
        String[] splits = fileName.split("\\.");
        String type = splits[splits.length - 1];
        // 使用系统分隔符来拼接文件路径
        String filePath = Paths.get(baseDir, "upFiles", "bs", md5 + "." + type).toString();
        File file = new File(filePath); //获取文件
        long offset = (long) chunkSize * (index - 1);
        if (Objects.equals(index, chunkTotal)) {
            offset = file.length() - chunkSize;
        }
        byte[] chunk = classesService.getChunk(index, chunkSize, filePath, offset);
        //System.out.println("下载文件分片" +filePath+ index + "," + chunkSize + "," + chunk.length+","+offset);
        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.addHeader("Content-Length", "" + (chunk.length));
        response.setHeader("filename", fileName);
        response.setContentType("application/octet-stream");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            out.write(chunk);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 班级Excel导入模板
     *
     * @return
     */
    @RequestMapping(value = "/exportTemplate", method = RequestMethod.GET)
    public void exportClassesTemplate(HttpServletResponse response) throws IOException {
        // 创建一个空的班级列表
        List<Classes> classes = new ArrayList<>(); // 模板不需要数据
        ExportParams exportParams = new ExportParams("班级导入模板", "班级", ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, Classes.class, classes);
        // 获取工作表
        Sheet sheet = workbook.getSheetAt(0);
        // 设置红色字体样式
        CellStyle redHeaderStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redHeaderStyle.setFont(redFont);
        redHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        redHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 获取表头行
        Row headerRow = sheet.getRow(1); // 获取表头行（一般是第1行，根据需要调整）
        //List<String> fieldsToChange = Arrays.asList("序号", "班次", "开班日期", "人员类型", "讲师1", "培训地点");
        List<String> fieldsToChange = Arrays.asList("序号", "班次", "开班日期", "人员类型", "讲师1");
        // 设置指定表头单元格的样式
        for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
            Cell headerCell = headerRow.getCell(i);
            if (fieldsToChange.contains(headerCell.getStringCellValue())) {
                headerCell.setCellStyle(redHeaderStyle);
            }
        }
        // 设置响应头并输出文件
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=classTemplate.xlsx");
        try (OutputStream out = response.getOutputStream()) {
            workbook.write(out);
        } finally {
            workbook.close();
        }
    }

    /**
     * 班级批量导入
     *
     * @param request
     * @return
     */
    @PostMapping("/importExcel")
    public Result<?> importExcel(HttpServletRequest request) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0; //数据第3行开始
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd");
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(2);
            params.setNeedSave(true);
            int row = 2;//读取的数据行
            try {
                List<Classes> classesList = ExcelImportUtil.importExcel(file.getInputStream(), Classes.class, params);
                for (Classes classes : classesList) {
                    row++;
                    if (WxlConvertUtils.isEmpty(classes.getSerialNumber())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行序号为空");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    if (WxlConvertUtils.isEmpty(classes.getClassName())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行班别为空");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    if (WxlConvertUtils.isEmpty(classes.getStartDateAndEndDate())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行开班日期为空");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    if (WxlConvertUtils.isEmpty(classes.getPersonnelType())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行人员类型为空");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    if (WxlConvertUtils.isEmpty(classes.getLecturer1())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行讲师1为空");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    //日期拆分（开班日期）
                    if (!isValidDateRange(classes.getStartDateAndEndDate())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行开班日期不合法");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    String[] startDateAndEndDate = classes.getStartDateAndEndDate().split("-");
                    // 拼接 startDate 和 endDate 字符串
                    String startDateStr = startDateAndEndDate[0];
                    String endDateStr = startDateAndEndDate[1];
                    // 定义两种日期格式：一种带前导零，一种不带前导零
                    SimpleDateFormat sdfWithLeadingZero = new SimpleDateFormat("yyyy.MM.dd");
                    SimpleDateFormat sdfWithoutLeadingZero = new SimpleDateFormat("yyyy.M.d");
                    try {
                        // 解析 startDate
                        Date startDate = tryParseDate(startDateStr, sdfWithLeadingZero, sdfWithoutLeadingZero);
                        // 解析 endDate
                        Date endDate = tryParseDate(endDateStr, sdfWithLeadingZero, sdfWithoutLeadingZero);
                        // 设置到 classes 对象中
                        classes.setStartDate(startDate);
                        classes.setEndDate(endDate);
                    } catch (ParseException e) {
                        // 如果解析失败，输出错误信息
                        System.err.println("日期格式无效: " + e.getMessage());
                    }
                }
                classesService.saveBatch(classesList);
                successLines = classesList.size();
                return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
            } catch (Exception e) {
                return Result.error("格式错误");
            }
        }
        return Result.error("其他错误");
    }

    /**
     * 判断日期是否合法
     *
     * @param input
     * @return
     */
    public static boolean isValidDateRange(String input) {
        // 正则表达式：检查日期格式是否为 yyyy.MM.dd-yyyy.MM.dd 或 yyyy.M.d-yyyy.M.d
        String regex = "^\\d{4}\\.\\d{1,2}\\.\\d{1,2}-\\d{4}\\.\\d{1,2}\\.\\d{1,2}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.matches()) {
            // 日期格式符合要求，继续检查日期是否合法
            String[] parts = input.split("-");
            String startDateStr = parts[0];
            String endDateStr = parts[1];
            // 使用两种日期格式进行解析
            DateTimeFormatter formatterWithLeadingZero = DateTimeFormatter.ofPattern("yyyy.MM.dd");
            DateTimeFormatter formatterWithoutLeadingZero = DateTimeFormatter.ofPattern("yyyy.M.d");
            try {
                // 尝试使用两种格式解析日期
                LocalDate startDate = tryParseDate(startDateStr, formatterWithLeadingZero, formatterWithoutLeadingZero);
                LocalDate endDate = tryParseDate(endDateStr, formatterWithLeadingZero, formatterWithoutLeadingZero);
                return true;  // 日期合法
            } catch (DateTimeParseException e) {
                return false;  // 日期无效
            }
        }
        return false;  // 不符合日期格式
    }

    //日期格式解析 yyyy-MM-dd yyyy-M-d
    private static Date tryParseDate(String dateStr, SimpleDateFormat sdf1, SimpleDateFormat sdf2) throws ParseException {
        try {
            return sdf1.parse(dateStr);  // 尝试使用带前导零的格式
        } catch (ParseException e) {
            // 如果失败，尝试使用不带前导零的格式
            return sdf2.parse(dateStr);
        }
    }

    private static LocalDate tryParseDate(String dateStr, DateTimeFormatter formatter1, DateTimeFormatter formatter2) {
        try {
            return LocalDate.parse(dateStr, formatter1);  // 尝试使用带前导零的格式
        } catch (DateTimeParseException e) {
            // 如果失败，尝试使用不带前导零的格式
            return LocalDate.parse(dateStr, formatter2);
        }
    }

    // 生成文件并写入一些内容
    public static void createFile(String filePath) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write("This is a test file for MD5 hash calculation.");
            System.out.println("File created: " + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 获取文件的 MD5 值
    public static String getFileMD5(File file) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            FileInputStream fis = new FileInputStream(file);
            byte[] byteArray = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(byteArray)) != -1) {
                digest.update(byteArray, 0, bytesRead);
            }
            fis.close();
            byte[] md5Bytes = digest.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : md5Bytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
