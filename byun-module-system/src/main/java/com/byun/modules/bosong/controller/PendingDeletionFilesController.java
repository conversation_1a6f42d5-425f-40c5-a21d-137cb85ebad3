package com.byun.modules.bosong.controller;

import com.byun.common.api.vo.Result;
import com.byun.modules.bosong.entity.PendingDeletionFiles;
import com.byun.modules.bosong.service.IPendingDeletionFilesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/bs/pendingDeletionFiles")
public class PendingDeletionFilesController {
    @Autowired
    private IPendingDeletionFilesService pendingDeletionFilesService;

    /**
     * 保存需要删除的Oss资源
     * @param pendingDeletionFiles
     * @return
     */
    @PostMapping("/save")
    public Result<?> add(@RequestBody PendingDeletionFiles pendingDeletionFiles) {
        pendingDeletionFiles.setCreatedAt(LocalDateTime.now().withNano(0));
        pendingDeletionFiles.setDeletedAt(LocalDateTime.now().withNano(0));
        pendingDeletionFiles.setStatus(0);
        pendingDeletionFilesService.save(pendingDeletionFiles);
        return Result.OK("","");
    }
}
