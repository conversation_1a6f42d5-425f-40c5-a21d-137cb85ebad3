package com.byun.modules.bosong.controller;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.bosong.entity.Classes;
import com.byun.modules.bosong.entity.QuestionnaireContextAnswer;
import com.byun.modules.bosong.entity.QuestionnaireStatus;
import com.byun.modules.bosong.entity.Student;
import com.byun.modules.bosong.excel.QuestionnaireExcel;
import com.byun.modules.bosong.service.IClassesService;
import com.byun.modules.bosong.service.IQuestionnaireContextAnswerService;
import com.byun.modules.bosong.service.IQuestionnaireStatusService;
import com.byun.modules.bosong.service.IStudentService;
import com.byun.modules.bosong.vo.StudentQuestionnaireVo;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/bs/questionnaire")
public class QuestionnaireContextAnswerController {
    @Autowired
    private IQuestionnaireContextAnswerService questionnaireContextAnswerService;
    @Autowired
    private IQuestionnaireStatusService questionnaireStatusService;
    @Autowired
    private IStudentService studentService;
    @Autowired
    private IClassesService classesService;
    @GetMapping("/list")
    public Result<?> list(
                          @RequestParam(value = "classesId") String chassesId,
                          @RequestParam(value = "username",required = false) String username,
                          @RequestParam(value = "phone",required = false) String phone,
                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10" ) Integer pageSize,
                          HttpServletRequest request

    ) {
        //TODO Shiro 已放行当前所有接口，无法获取到登录用户信息
//        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//        if (!WxlConvertUtils.isNotEmpty(user)) {
//            return Result.error("登录失效");
//        }
        if (WxlConvertUtils.isEmpty(chassesId)) {
            return Result.error("参数丢失");
        }
        //TODO 添加搜索条件
        Classes classes = classesService.getById(chassesId);
        IPage<Student> page = new Page<>(pageNo, pageSize);
        IPage<Student> studentIPage = studentService.page(
                page, new LambdaQueryWrapper<Student>()
                        .eq(Student::getChassesId, chassesId)
                        .eq(WxlConvertUtils.isNotEmpty(phone),Student::getPhone,phone)
                        .likeRight(WxlConvertUtils.isNotEmpty(username),Student::getUserName,username)
                        .last("ORDER BY CAST(serial_number AS UNSIGNED) ASC")
        );
        List<String> studentIds = studentIPage.getRecords().stream()
                .map(Student::getId)
                .collect(Collectors.toList());
        List<QuestionnaireContextAnswer> questionnaireContextAnswers = WxlConvertUtils.isEmpty(studentIds)
                ? Collections.emptyList()
                : questionnaireContextAnswerService.list(
                new LambdaQueryWrapper<QuestionnaireContextAnswer>()
                        .in(QuestionnaireContextAnswer::getStudentId, studentIds));
        Map<String, QuestionnaireContextAnswer> answerMap = questionnaireContextAnswers.stream()
                .collect(Collectors.toMap(
                        QuestionnaireContextAnswer::getStudentId,
                        Function.identity()));
        List<StudentQuestionnaireVo> voList = studentIPage.getRecords().stream()
                .map(record -> {
                    StudentQuestionnaireVo vo = new StudentQuestionnaireVo();
                    vo.setQuestionnaireId(record.getId());
                    vo.setChassesName(classes.getClassName());
                    vo.setSerialNumber(record.getSerialNumber());
                    vo.setUsername(record.getUserName());
                    vo.setPhone(record.getPhone());
                    QuestionnaireContextAnswer answer = answerMap.get(record.getId());
                    if (answer != null) {
                        vo.setQuestionnaireStatus(CommonConstant.QUESTIONNAIRESTATUS1);
                        vo.setQuestionnaireContentAnswer(answer.getQuestionnaireContentAnswer());
                    } else {
                        vo.setQuestionnaireStatus(CommonConstant.QUESTIONNAIRESTATUS0);
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        return Result.OK(new Page<StudentQuestionnaireVo>(pageNo, pageSize)
                .setRecords(voList)
                .setTotal(studentIPage.getTotal()));
        /**
        Page<QuestionnaireContextAnswer> page = new Page<>();
        LambdaQueryWrapper<QuestionnaireContextAnswer> questionnaireContextAnswerLambdaQueryWrapper
                = new LambdaQueryWrapper<>();
        questionnaireContextAnswerLambdaQueryWrapper.eq(QuestionnaireContextAnswer::getClassesId,chassesId);
        Page<QuestionnaireContextAnswer> questionnaireContextAnswerPage = questionnaireContextAnswerService.page(page, questionnaireContextAnswerLambdaQueryWrapper);
        //System.out.println(questionnaireContextAnswerPage);
        return Result.OK(questionnaireContextAnswerPage);
         *
         */
    }
    @PutMapping("/edit")
    public Result<?> edit(@RequestBody JSONObject jsonObject) {
        String  studentId = jsonObject.getString("studentId");
        if (WxlConvertUtils.isEmpty(studentId)) {
            return new Result<>().error500("id丢失");
        }
        QuestionnaireContextAnswer questionnaireContextAnswer = questionnaireContextAnswerService
                .getOne(new LambdaQueryWrapper<QuestionnaireContextAnswer>()
                        .eq(QuestionnaireContextAnswer::getStudentId,studentId ));
        if (WxlConvertUtils.isEmpty(questionnaireContextAnswer)) {
            return new Result<>().error500("学员问卷不存在");
        }
        String questionnaireContentAnswer = questionnaireContextAnswer.getQuestionnaireContentAnswer();
        //问卷实体
        JSONObject questionnaireValue = JSONObject.parseObject(questionnaireContentAnswer);
        //1: 基本信息
        //姓名
        String username = jsonObject.getString("username");
        if ( WxlConvertUtils.isNotEmpty(username) ) {
            questionnaireContextAnswer.setStudentUsername(username) ;
            questionnaireValue.getJSONObject("userInfo").put("username", username);
        }
        //电话
        String phone = jsonObject.getString("phone");
        if ( WxlConvertUtils.isNotEmpty(phone) ) {
            questionnaireValue.put("phone", phone);
        }
        //创业培训类型
        JSONObject trainingPrograms = jsonObject.getJSONObject("trainingPrograms");
        if (!trainingPrograms.isEmpty()) {
            questionnaireValue.put("trainingPrograms", trainingPrograms);
        }
        //培训评价
        JSONObject trainingRatings = jsonObject.getJSONObject("trainingRatings");
        if (!trainingRatings.isEmpty()) {
            questionnaireValue.put("trainingRatings", trainingRatings);
        }
        JSONObject businessChallenges = jsonObject.getJSONObject("businessChallenges");
        if (!businessChallenges.isEmpty()) {
            questionnaireValue.put("businessChallenges", businessChallenges);
        }
        JSONObject followUpService = jsonObject.getJSONObject("followUpService");
        if (!followUpService.isEmpty()) {
            questionnaireValue.put("followUpService", followUpService);
        }
        questionnaireValue.put("businessStarted", jsonObject.getBoolean("businessStarted"));
        questionnaireValue.put("entrepreneurshipPlan", jsonObject.getBoolean("entrepreneurshipPlan"));
        questionnaireValue.put("otherNeeds", jsonObject.getString("otherNeeds"));
        questionnaireValue.put("otherSuggestions",jsonObject.getString("otherSuggestions"));
        //添加问卷内容
        questionnaireContextAnswer.setQuestionnaireContentAnswer(questionnaireValue.toJSONString());
        questionnaireContextAnswerService.updateById(questionnaireContextAnswer);
        return Result.OK();
    }
    @GetMapping("/getQuestionnaireInfo/{studentId}")
    public Result<QuestionnaireContextAnswer> getQuestionnaire(@PathVariable("studentId") String studentId) {
        QuestionnaireContextAnswer questionnaireContextAnswer =
                questionnaireContextAnswerService.getOne(new LambdaQueryWrapper<QuestionnaireContextAnswer>()
                .eq(QuestionnaireContextAnswer::getStudentId, studentId)
        );
        return Result.OK(questionnaireContextAnswer);
    }

    /**
     * 保存问卷
     * @param jsonObject
     * @return
     */
    @Transactional
    @PostMapping("/fillQuestionnaire")
    public Result<?> fillContextAnswer(@RequestBody JSONObject jsonObject) {
        String classesName = jsonObject.getString("classesName");
        JSONObject userInfo = jsonObject.getJSONObject("userInfo");
        String studentQuestionnaireStatusId = userInfo.getString("id");//问卷状态id
        String classesId = userInfo.getString("classesId");
        String username = userInfo.getString("username");
        String studentId = userInfo.getString("studentId");
        String studentSerialNumber = userInfo.getString("studentSerialNumber");
        QuestionnaireContextAnswer questionnaireContextAnswer = new QuestionnaireContextAnswer();
        questionnaireContextAnswer.setStudentQuestionnaireStatusId(studentQuestionnaireStatusId);
        questionnaireContextAnswer.setClassesName(classesName);
        questionnaireContextAnswer.setClassesId(classesId);
        questionnaireContextAnswer.setStudentUsername(username);
        questionnaireContextAnswer.setStudentId(studentId);
        questionnaireContextAnswer.setStudentSerialNumber(studentSerialNumber);
        questionnaireContextAnswer.setQuestionnaireContentAnswer(jsonObject.toJSONString());
        QuestionnaireContextAnswer questionnaireContextAnswerServiceOne =
                questionnaireContextAnswerService.getOne(new LambdaQueryWrapper<QuestionnaireContextAnswer>()
                        .eq(QuestionnaireContextAnswer::getStudentId, studentId)
                );
        if (WxlConvertUtils.isNotEmpty(questionnaireContextAnswerServiceOne)) {
            questionnaireContextAnswer.setId(questionnaireContextAnswerServiceOne.getId());
            questionnaireContextAnswerService.updateById(questionnaireContextAnswer);
            return Result.OK();
        }
        questionnaireContextAnswerService.save(questionnaireContextAnswer);
        QuestionnaireStatus questionnaireStatus = questionnaireStatusService.getById(studentQuestionnaireStatusId);
        questionnaireStatus.setQuestionnaireStatus(CommonConstant.QUESTIONNAIRESTATUS1);
        questionnaireStatusService.updateById(questionnaireStatus);
        return Result.OK();
    }
    /**
     * 导出调查问卷PdfAndWord
     * @param jsonObject
     * @return
     * @throws IOException
     */
    @PostMapping("/exportQuestionnairePdfAndWord")
    public Result<?> exportQuestionnairePdf(@RequestBody JSONObject jsonObject)throws IOException {
        String chassesId =jsonObject.getString("id");
        String timeStamp = jsonObject.getString("timeStamp");
        String className =jsonObject.getString("className");
        String exportType = jsonObject.getString("exportType"); //导出类型 1 PDF 2 Word
        if (WxlConvertUtils.isEmpty(chassesId) || WxlConvertUtils.isEmpty(timeStamp)) {
            return Result.error("参数丢失");
        }
        List<QuestionnaireContextAnswer> questionnaireContextAnswers = questionnaireContextAnswerService
                .list(new LambdaQueryWrapper<QuestionnaireContextAnswer>()
                .eq(QuestionnaireContextAnswer::getClassesId, chassesId)
        );
        if (questionnaireContextAnswers.isEmpty()) {
            return Result.error("无调查问卷");
        }
        String catchKey = className + "_"+timeStamp+"questionnaire" ;
        //填充编号
//        List<Student> students = studentService.list(new LambdaQueryWrapper<Student>()
//                .eq(Student::getChassesId, chassesId));
//        Map<String, String> studentIdToSerialNumber   = students.stream()
//                .collect(Collectors.toMap(Student::getId, Student::getSerialNumber));
//        for (QuestionnaireContextAnswer questionnaireContextAnswer : questionnaireContextAnswers) {
//            if (studentIdToSerialNumber.containsKey(questionnaireContextAnswer.getStudentId())) {
//                questionnaireContextAnswer.setStudentSerialNumber(studentIdToSerialNumber
//                        .get(questionnaireContextAnswer.getStudentId()));
//            }
//        }
        questionnaireContextAnswerService.asyncExport(questionnaireContextAnswers,catchKey,className);
        return Result.OK("",catchKey);
    }
    @GetMapping("/exportQuestionnaireExcel")
    public ModelAndView exportQuestionnaireExcel(@RequestParam("chassesId") String chassesId,
            HttpServletRequest request) {
//        Map<String, String[]> parameterMap = request.getParameterMap();
//        String[] chassesId = parameterMap.get("classesId");
        if (WxlConvertUtils.isEmpty(chassesId)) {
            return new ModelAndView();
        }
        List<QuestionnaireContextAnswer> questionnaireContextAnswers =
                questionnaireContextAnswerService.list(new LambdaQueryWrapper<QuestionnaireContextAnswer>()
                .eq(QuestionnaireContextAnswer::getClassesId, chassesId)
                //.orderByAsc(QuestionnaireContextAnswer::getStudentSerialNumber)
        );
        List<QuestionnaireExcel> resultList = new ArrayList<>();
        if (!questionnaireContextAnswers.isEmpty()) {
            for (QuestionnaireContextAnswer questionnaireContextAnswer : questionnaireContextAnswers) {
                QuestionnaireExcel questionnaireExcel = new QuestionnaireExcel();
                questionnaireExcel.setStudentSerialNumber(questionnaireContextAnswer.getStudentSerialNumber());//编号
                questionnaireExcel.setClassesName(questionnaireContextAnswer.getClassesName()); //班次
                questionnaireExcel.setUsername(questionnaireContextAnswer.getStudentUsername());//姓名
                questionnaireExcel.setQuestionnaireStatus(//状态
                        WxlConvertUtils.isEmpty(questionnaireContextAnswer.getQuestionnaireContentAnswer())
                                ? "未填" : "已填" );
                if (WxlConvertUtils.isNotEmpty(questionnaireContextAnswer.getQuestionnaireContentAnswer())) {
                    JSONObject questionnaireObject = JSONObject.parseObject(questionnaireContextAnswer.getQuestionnaireContentAnswer());
                    questionnaireExcel.setPhone(questionnaireObject.getString("phone")); //电话
                    //创业培训类型
                    JSONObject trainingPrograms = questionnaireObject.getJSONObject("trainingPrograms");
                    Set<String> trainingProgramsKeys = trainingPrograms.keySet();
                    StringBuilder trainingProgramsResult = new StringBuilder();
                    for (String key : trainingProgramsKeys) {
                        if (trainingPrograms.getBoolean(key)) {
                            if (trainingProgramsResult.length() > 0) {
                                trainingProgramsResult.append("-");
                            }
                            trainingProgramsResult.append(key);
                        }
                    }
                    questionnaireExcel.setTrainingPrograms(trainingProgramsResult.toString());
                    //课程培训评价
                    JSONObject trainingRatings = questionnaireObject.getJSONObject("trainingRatings");
//                    Boolean GYB = trainingRatings.getBoolean("GYB");
//                    Boolean SYB = trainingRatings.getBoolean("SYB");
//                    Boolean WC = trainingRatings.getBoolean("WC");
//                    questionnaireExcel.setTrainingRatingsGYB(trainingRatings.getBoolean("GYB") == null ? ""
//                            : trainingRatings.getBoolean("GYB") ? "满意" : "不满意");
                    questionnaireExcel.setTrainingRatingsGYB(getTrainingRating(trainingRatings.getBoolean("GYB")));
                    questionnaireExcel.setTrainingRatingsSYB(getTrainingRating(trainingRatings.getBoolean("SYB")));
                    questionnaireExcel.setTrainingRatingsWC(getTrainingRating(trainingRatings.getBoolean("WC")));
                    //是否有创业打算
                    questionnaireExcel.setEntrepreneurshipPlan(questionnaireObject.getBoolean("entrepreneurshipPlan") ? "是" : "否");
                    //是否已开业
                    questionnaireExcel.setBusinessStarted(questionnaireObject.getBoolean("businessStarted") ? "是" : "否");
                    //创业面临困境
                    JSONObject businessChallenges = questionnaireObject.getJSONObject("businessChallenges");
                    if (businessChallenges != null) {
                        List<String> challengeList = new ArrayList<>();
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("fundingShortage"))) {
                            challengeList.add("资金短缺");
                        }
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("marketCompetition"))) {
                            challengeList.add("市场开拓困难");
                        }
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("talentShortage"))) {
                            challengeList.add("缺乏专业人才");
                        }
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("policyEnvironment"))) {
                            challengeList.add("技术研发瓶颈");
                        }
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("resourceAccess"))) {
                            challengeList.add("政策法规不熟悉");
                        }
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("marketingPromotion"))) {
                            challengeList.add("同行竞争压力大");
                        }
                        if (Boolean.TRUE.equals(businessChallenges.getBoolean("other"))) {
                            String otherText = businessChallenges.getString("otherText");
                            challengeList.add("其他:"+ otherText.trim());
                        }
                        // '-' 拼接起来
                        String businessChallengesStr = String.join("-", challengeList);
                        questionnaireExcel.setBusinessChallenges(businessChallengesStr);
                        //是否需要创业后续服务
                        JSONObject followUpService = questionnaireObject.getJSONObject("followUpService");
                        //TODO 为空处理
                        questionnaireExcel.setFollowUpServiceNeeded(followUpService.getBoolean("needed") ? "是" : "否");
                        List<String> followUpServiceList = new ArrayList<>();
                        // 根据布尔值添加相应描述
                        if (Boolean.TRUE.equals(followUpService.getBoolean("policyConsultation"))) {
                            followUpServiceList.add("政策咨询");
                        }
                        if (Boolean.TRUE.equals(followUpService.getBoolean("projectGuidance"))) {
                            followUpServiceList.add("项目指导");
                        }
                        if (Boolean.TRUE.equals(followUpService.getBoolean("resourceMatching"))) {
                            followUpServiceList.add("资源对接");
                        }
                        if (Boolean.TRUE.equals(followUpService.getBoolean("financingService"))) {
                            followUpServiceList.add("融资服务");
                        }
                        if (Boolean.TRUE.equals(followUpService.getBoolean("other"))) {
                            String otherText = followUpService.getString("otherText");
                            followUpServiceList.add("其他:" + otherText.trim());
                        }
                        String followUpServiceStr = String.join("-", followUpServiceList);
                        questionnaireExcel.setFollowUpService(followUpServiceStr);
                        // 其他需求
                        questionnaireExcel.setOtherNeeds(questionnaireObject.getString("otherNeeds"));
                        //其他建议
                        questionnaireExcel.setOtherSuggestions(questionnaireObject.getString("otherSuggestions"));
                    }
//                    Set<String> trainingRatingKeys = trainingRatings.keySet();
//                    for (String key : trainingRatingKeys) {
//
//                    }
                }
                resultList.add(questionnaireExcel);
            }
        }
        //序号排序
        resultList = resultList.stream().sorted(Comparator.comparing(Q -> Integer.parseInt(Q.getStudentSerialNumber()))).collect(Collectors.toList());
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "创业调查列表");
        mv.addObject(NormalExcelConstants.CLASS, QuestionnaireExcel.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("创业调查问卷", "导出人:博颂", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, resultList);
        return mv;
    }
    private String getTrainingRating(Boolean rating) {
        if (rating == null) {
            return "";
        }
        return rating ? "满意" : "不满意";
    }

    /**
     * 导出调查问卷Word
     * @param jsonObject
     * @return
     * @throws IOException
     */
//    @PostMapping("/exportQuestionnaireWord")
//    public Result<?> exportQuestionnaireWord(@RequestBody JSONObject jsonObject)throws IOException {
//
//        return null;
//    }
}
