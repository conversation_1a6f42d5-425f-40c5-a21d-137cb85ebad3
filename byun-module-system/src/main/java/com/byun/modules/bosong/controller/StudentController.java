package com.byun.modules.bosong.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.ImportExcelUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.oss.OssBootUtil;
import com.byun.modules.bosong.entity.Classes;
import com.byun.modules.bosong.entity.PendingDeletionFiles;
import com.byun.modules.bosong.entity.QuestionnaireStatus;
import com.byun.modules.bosong.entity.Student;
import com.byun.modules.bosong.service.IClassesService;
import com.byun.modules.bosong.service.IPendingDeletionFilesService;
import com.byun.modules.bosong.service.IQuestionnaireStatusService;
import com.byun.modules.bosong.service.IStudentService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;


@Api(tags = "学生")
@Slf4j
@RestController
@RequestMapping("/bs/student")
public class StudentController {
    @Autowired
    private IStudentService studentService;
    @Autowired
    private IClassesService classesService;
    @Autowired
    private IQuestionnaireStatusService questionnaireStatusService;
    @Autowired
    private IPendingDeletionFilesService pendingDeletionFilesService;
    /**
     * 学员列表
     * @param student
     * @param pageNo
     * @param pageSize
     * @param request
     * @return
     */
    @GetMapping("/list")
    public Result<?> adminList(Student student,
                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                               HttpServletRequest request   ) {

        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (!WxlConvertUtils.isNotEmpty(user)) {
            return Result.error("登录失效");
        }
        Page<Student> page = new Page<Student>(pageNo, pageSize);
        LambdaQueryWrapper<Student> lambdaQueryWrapper = new LambdaQueryWrapper<Student>();
        lambdaQueryWrapper.eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.eq(Student::getChassesId, student.getChassesId());
        lambdaQueryWrapper.eq(WxlConvertUtils.isNotEmpty(student.getSerialNumber()), Student::getSerialNumber, student.getSerialNumber());
        lambdaQueryWrapper.eq(WxlConvertUtils.isNotEmpty(student.getPhone()), Student::getPhone, student.getPhone());
        lambdaQueryWrapper.eq(WxlConvertUtils.isNotEmpty(student.getIdCardNumber()), Student::getIdCardNumber, student.getIdCardNumber());
        lambdaQueryWrapper.eq(WxlConvertUtils.isNotEmpty(student.getCertificateNumber()), Student::getCertificateNumber, student.getCertificateNumber());
        lambdaQueryWrapper.like(WxlConvertUtils.isNotEmpty(student.getUserName()), Student::getUserName, student.getUserName());
        //lambdaQueryWrapper.orderByAsc(Student::getSerialNumber); //序号升序
        // 使用last方法对serial_number进行CAST转换后排序
        lambdaQueryWrapper.last("ORDER BY CAST(serial_number AS UNSIGNED) ASC");
        Page<Student> pageList = studentService.page(page, lambdaQueryWrapper);
        if (!pageList.getRecords().isEmpty()) {
            Classes classes = classesService.getById(student.getChassesId());
            for (Student record : pageList.getRecords()) {
                record.setChassesName(classes.getClassName());
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 下载学员导入模板
     *
     * @return
     */
    @RequestMapping("/downloadStudentTemplate")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 创建一个空的学生列表（数据不需要，只用于生成表头）
        List<Student> students = new ArrayList<>();
        // 创建导出参数，禁用表头行生成
        ExportParams exportParams = new ExportParams("学员导入", "学员", ExcelType.XSSF);
        // 手动创建工作簿
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, Student.class, students);
        // 获取工作表
        Sheet sheet = workbook.getSheetAt(0);
        // 设置红色字体样式
        CellStyle redHeaderStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());  // 设置字体颜色为红色
        redHeaderStyle.setFont(redFont);
        // 设置水平居中和垂直居中
        redHeaderStyle.setAlignment(HorizontalAlignment.CENTER);  // 水平居中
        redHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
        // 获取表头行
        Row headerRow = sheet.getRow(1);  // 获取第一行（表头）
        List<String> fieldsToChange = new ArrayList<>();
        fieldsToChange.add("序号");
        fieldsToChange.add("班次");
        fieldsToChange.add("姓名");
        //fieldsToChange.add("性别");
        fieldsToChange.add("手机号");
        fieldsToChange.add("身份证号");
        fieldsToChange.add("人员类别");
        //修改字体颜色
        for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
            Cell headerCell = headerRow.getCell(i);
            if (fieldsToChange.contains(headerCell.getStringCellValue())) {
                headerCell.setCellStyle(redHeaderStyle);  // 设置表头"姓名"字段的样式
            }
        }
        // 设置响应头并输出文件
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=personInfo.xlsx");
        // 使用 OutputStream 将文件写入响应
        try (OutputStream out = response.getOutputStream()) {
            workbook.write(out);  // 将内容写入 OutputStream
        } finally {
            workbook.close(); // 关闭工作簿，释放资源
        }
    }
    /**
     * Excel导入学员
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/importExcel")
    @Transactional
    public Result<?> importExcel(HttpServletRequest request) throws Exception {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, String[]> parameterMap = request.getParameterMap();
        String[] ids = parameterMap.get("recordId");
        String classesId = ids[0]; //班级ID
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0;
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(2);
            params.setNeedSave(true);
            int row = 2;//读取的数据行
            try {
                List<Student> staUserSettlements = ExcelImportUtil.importExcel(file.getInputStream(), Student.class, params);
                //过滤出姓名为空的数据
                List<Student> collect = staUserSettlements.stream().filter(student -> WxlConvertUtils.isNotEmpty(student.getUserName())).collect(Collectors.toList());
                List<Student> studentList = studentService.list(new LambdaQueryWrapper<Student>()
                        .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .eq(Student::getChassesId, classesId)
                );
                List<String> studentSerialNumberStream = studentList.stream().map(Student::getSerialNumber).collect(Collectors.toList());
                for (Student student : collect) {
                    row++;
                    //TODO 非空验证序号，班次，姓名，性别，手机号，身份证号，人员类别
                    //TODO 手机号格式
                    //TODO 性别(男或女)
                    if (WxlConvertUtils.isEmpty(student.getSerialNumber())) {
                        errorLines++;
                        errorMessage.add("第" + row + "序号为空");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }

                    if (!IdCardUtil.isIdCardNumberValid(student.getIdCardNumber())) {
                        errorLines++;
                        errorMessage.add("第" + row + "行身份证格式错误-" + "姓名：" + student.getUserName() + ",身份证-" + student.getIdCardNumber());
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    if (studentSerialNumberStream.stream().anyMatch(serialNumber -> serialNumber.equals(student.getSerialNumber()))) {
                        errorLines++;
                        errorMessage.add("第" + row + "行序号已存在-" + "序号：" + student.getSerialNumber() + ",姓名：" + student.getUserName());
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    String personnelType = determinePersonnelType(student.getEducationLevel(), student.getCertificateNumber());
                    if (personnelType.equals("农民工") && !(student.getEducationLevel().equals("在校大学生")
                            || student.getEducationLevel().equals("毕业学年"))) {
                        student.setEducationLevel("农民工");
                    }else {
                        student.setPersonnelType(personnelType);
                    }
                    //必填项验证
                    student.setGender(IdCardUtil.judgeGender(student.getIdCardNumber()));
                    student.setChassesId(classesId); //班级Id
                    student.setCreateTime(new Date());
                    student.setCreateBy(user.getUsername());
                    student.setCreateById(user.getId());
                    student.setDelFlag(CommonConstant.DEL_FLAG_0);
                }
                //插入学生信息
                studentService.saveBatch(collect);
                //更新班级人数
                successLines = collect.size();
                int studentCount = studentService.count(new LambdaQueryWrapper<Student>()
                        .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .eq(Student::getChassesId, classesId));
                Classes classes = classesService.getById(classesId);
                classes.setNumberOfStudents(String.valueOf(studentCount));
                classesService.updateById(classes);
                List<QuestionnaireStatus> questionnaireStatusList = new ArrayList<>();
                for (Student student : collect) {
                    QuestionnaireStatus questionnaireStatus  = new QuestionnaireStatus();
                    questionnaireStatus.setStudentId(student.getId());
                    questionnaireStatus.setClassesId(student.getChassesId());
                    questionnaireStatus.setQuestionnaireStatus(CommonConstant.QUESTIONNAIRESTATUS0);
                    questionnaireStatus.setUsername(student.getUserName());
                    questionnaireStatus.setStudentSerialNumber(student.getSerialNumber());
                    questionnaireStatusList.add(questionnaireStatus);
                }
                //更新问卷状态
                questionnaireStatusService.saveBatch(questionnaireStatusList);
                return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
            } catch (Exception e) {
                return Result.error("格式错误");
            }
        }
        return Result.error("其他错误");
    }
    /**
     * 单个学员添加
     * @param student
     * @return
     */
    @PostMapping("/save")
    public Result<?> save(@RequestBody Student student) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(student.getChassesId())) {
            return Result.error("参数丢失");
        }
        String chassesId = student.getChassesId();
        if (
                studentService.count(new LambdaQueryWrapper<Student>()
                        .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .eq(Student::getSerialNumber, student.getSerialNumber())
                        .eq(Student::getChassesId, chassesId)) > 0
        ) {
            return Result.error("序号：" + student.getSerialNumber() + "已存在");
        }
        //学生类别
        String personnelType = determinePersonnelType(student.getEducationLevel(), student.getCertificateNumber());
        if (personnelType.equals("农民工") && !(student.getEducationLevel().equals("在校大学生") || student.getEducationLevel().equals("毕业学年"))) {
            student.setEducationLevel("农民工");
        }else {
            student.setPersonnelType(personnelType);
        }
        student.setGender(IdCardUtil.judgeGender(student.getIdCardNumber()));
        studentService.save(student);
        QuestionnaireStatus questionnaireStatus = new QuestionnaireStatus();
        questionnaireStatus.setStudentId(student.getId());
        questionnaireStatus.setClassesId(student.getChassesId());
        questionnaireStatus.setUsername(student.getUserName());
        questionnaireStatus.setStudentSerialNumber(student.getSerialNumber());
        questionnaireStatus.setQuestionnaireStatus(CommonConstant.QUESTIONNAIRESTATUS0);
        questionnaireStatusService.save(questionnaireStatus);
        return Result.OK("添加成功", "");
    }
    public  String determinePersonnelType(String educationLevel, String certificateNumber) {
        if (WxlConvertUtils.isEmpty(educationLevel) || WxlConvertUtils.isEmpty(certificateNumber)) {
            return "农民工";
        }
        try {
            String admissionYear = certificateNumber.substring(0, 4);
            int admission = Integer.parseInt(admissionYear);
            int currentYear = java.time.Year.now().getValue();
            int duration = getEducationDuration(educationLevel);
            if (duration == 0) {
                return "农民工";
            }
            int expectedGraduationYear = admission + duration - 1;
            return currentYear == expectedGraduationYear ? "毕业学年" : "在校大学生";
        } catch (Exception e) {
            return "农民工"; // 异常情况默认返回
        }
    }

    private  int getEducationDuration(String educationLevel) {
        switch (educationLevel) {
            case "大学专科":
                return 3;
            case "大学本科":
                return 4;
            default:
                return 0;
        }
    }
    /**
     * 学员编辑(个人信息) AND 问卷状态（姓名，序号）
     * @param student
     * @return
     */
    @PutMapping("/edit")
    @Transactional
    public Result<?> edit(@RequestBody Student student) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(student.getChassesId())) {
            return Result.error("参数丢失");
        }
        String chassesId = student.getChassesId();
        if (
                studentService.count(new LambdaQueryWrapper<Student>()
                        .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .ne(Student::getId, student.getId())
                        .eq(Student::getSerialNumber, student.getSerialNumber())
                        .eq(Student::getChassesId, chassesId)) > 0
        ) {
            return Result.error("序号：" + student.getSerialNumber() + "已存在");
        }
        String personnelType = determinePersonnelType(student.getEducationLevel(), student.getCertificateNumber());
        student.setPersonnelType(personnelType);

        studentService.updateById(student);
        QuestionnaireStatus questionnaireStatus = questionnaireStatusService.getOne(new LambdaQueryWrapper<QuestionnaireStatus>()
                .eq(QuestionnaireStatus::getStudentId, student.getId())
        );
        if (WxlConvertUtils.isNotEmpty(questionnaireStatus)) {
            questionnaireStatus.setUsername(student.getUserName());
            questionnaireStatus.setStudentSerialNumber(student.getSerialNumber());
            questionnaireStatusService.updateById(questionnaireStatus);
        }
        return Result.OK("修改成功", "");
    }
    /**
     * 删除单个学员，同步更新班级人数
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    @Transactional
    public Result<?> deleteOne(@RequestParam("id") String id) {
        Student student = studentService.getById(id);
        String chassesId = student.getChassesId();
        Classes classes = classesService.getById(chassesId);
        classes.setNumberOfStudents(String.valueOf(Integer.parseInt(classes.getNumberOfStudents()) - 1));
        studentService.removeById(id);
        classesService.updateById(classes);
        return Result.OK("删除成功", "");
    }

    /**
     * 批量删除学员
     * @param idsMap
     * @return
     */
    @DeleteMapping("/batchDelete")
    @Transactional
    public Result batchDelete(@RequestBody Map<String,String[]> idsMap) {
        String[] ids = idsMap.get("ids");
        if (WxlConvertUtils.isEmpty(ids)) {
            return Result.error("参数为空");
        }
        List<QuestionnaireStatus> questionnaireStatusList = questionnaireStatusService.list(new LambdaQueryWrapper<QuestionnaireStatus>()
                .in(QuestionnaireStatus::getStudentId, ids));
        studentService.removeByIds(Arrays.asList(ids));
        if (!questionnaireStatusList.isEmpty()) {
            questionnaireStatusService.removeByIds(questionnaireStatusList.stream().map(QuestionnaireStatus::getId).collect(Collectors.toList()));
        }
        return Result.OK("删除成功","");
    }
    /**
     * 获取单个学员(使用场景：上传证件照，学生证，身份证，社保卡场景使用)
     * @param classesId
     * @param serialNumber
     * @param userName
     * @return
     */
    @GetMapping("/getStudentOne")
    public Result<?> getStudentOne(@RequestParam(value = "classesId", required = true) String classesId,
                                   @RequestParam(value = "serialNumber", required = false) String serialNumber,
                                   @RequestParam(value = "userName", required = false) String userName) {
        if (WxlConvertUtils.isEmpty(classesId)) {
            return Result.error("参数丢失");
        }
        if (WxlConvertUtils.isNotEmpty(serialNumber)) {
            Student student = studentService.getOne(new LambdaQueryWrapper<Student>()
                    .eq(Student::getChassesId, classesId)
                    .eq(Student::getSerialNumber, serialNumber)
                    .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
            );
            return Result.OK("操作成功", student);
        }
        if (WxlConvertUtils.isNotEmpty(userName)) {
            List<Student> student = studentService.list(new LambdaQueryWrapper<Student>()
                    .eq(Student::getChassesId, classesId)
                    .like(Student::getUserName, userName)
                    .eq(Student::getDelFlag, CommonConstant.DEL_FLAG_0)
            );
            if (student.isEmpty()) {
                return null;
            }
            return Result.OK("操作成功", student.get(0));
        }
        return null;
    }
    /**
     * 学员添加或更新证件照等信息
     * @param jsonObject
     * @return
     */
    @PostMapping("/updateInfo")
    public Result<?> updateInfo(@RequestBody JSONObject jsonObject) {
        String studentId = jsonObject.getString("id");
        String idPhoto = jsonObject.getString("idPhoto");
        String studentIdPhoto = jsonObject.getString("studentIdPhoto");
        String socialSecurityCard = jsonObject.getString("socialSecurityCard");
        String idCard = jsonObject.getString("idCard");
        String personType = jsonObject.getString("personnelType");
        // 参数基础校验
        if (WxlConvertUtils.isEmpty(studentId)) {
            return Result.error("缺少学员ID");
        }
        if (WxlConvertUtils.isEmpty(personType)) {
            return Result.error("缺少人员类型");
        }
        if(WxlConvertUtils.isEmpty(idPhoto)) {
            return Result.error("缺少证件照");
        }
        Student student = studentService.getById(studentId);
        if (student == null) {
            return Result.error("学员信息不存在");
        }
        // 校验人员类型
        Result<?> validationResult = validatePersonType(personType, idCard, studentIdPhoto, socialSecurityCard);
        if (!validationResult.isSuccess()) {
            return validationResult; // 返回校验错误
        }
        // 更新字段
        if (WxlConvertUtils.isNotEmpty(idPhoto)) student.setIdPhoto(idPhoto);
        if (WxlConvertUtils.isNotEmpty(studentIdPhoto)) student.setStudentIdPhoto(studentIdPhoto);
        if (WxlConvertUtils.isNotEmpty(socialSecurityCard)) student.setSocialSecurityCard(socialSecurityCard);
        if (WxlConvertUtils.isNotEmpty(idCard)) student.setIdCard(idCard);
        studentService.updateById(student);
        List<PendingDeletionFiles> pendingDeletionFilesList = pendingDeletionFilesService
                .list(new LambdaQueryWrapper<PendingDeletionFiles>()
                .eq(PendingDeletionFiles::getStudentId, studentId));
        //待删除oss文件url
        List<String> pendingDeletionFilesUrlList = pendingDeletionFilesList.stream()
                .map(PendingDeletionFiles::getFileUrl).collect(Collectors.toList());
        //删除
        OssBootUtil.deleteBatch(pendingDeletionFilesUrlList);
        pendingDeletionFilesService.removeByIds(pendingDeletionFilesList.stream()
                .map(PendingDeletionFiles::getId).collect(Collectors.toList()));
        return Result.OK("操作成功");
    }
    /**
     * 学员信息字段校验
     * @param personType
     * @param idCard
     * @param studentIdPhoto
     * @param socialSecurityCard
     * @return
     */
    private Result<?> validatePersonType(String personType, String idCard, String studentIdPhoto, String socialSecurityCard) {
        switch (personType) {
            case "农民工":
                if (WxlConvertUtils.isEmpty(idCard)) {
                    return Result.error("缺少身份证");
                }
                break;
            case "在校大学生":
                if (WxlConvertUtils.isEmpty(studentIdPhoto)) {
                    return Result.error("缺少学生证");
                }
                break;
            case "毕业学年":
                if (WxlConvertUtils.isEmpty(studentIdPhoto)) {
                    return Result.error("缺少学生证");
                }
                if (WxlConvertUtils.isEmpty(socialSecurityCard)) {
                    return Result.error("缺少社保卡");
                }
                break;
            default:
                return Result.error("人员类型错误");
        }
        return Result.OK(); // 校验通过
    }
}