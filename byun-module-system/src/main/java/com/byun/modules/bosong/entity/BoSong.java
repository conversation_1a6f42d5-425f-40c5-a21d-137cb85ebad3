package com.byun.modules.bosong.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2023-3-6 11:22
 */
@Data
@TableName("bosong")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="BoSong", description="博颂报名")
public class BoSong implements Serializable {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @Excel(name = "姓名", width = 15)
    private String userName;
    @Excel(name = "性别", width = 15)
    private String sex;
    @Excel(name = "身份证号码", width = 30)
    private String idCard;
    @Excel(name = "身份证地址", width = 50)
    private String address;
    @Excel(name = "报名时间", width = 30,exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    @ApiModelProperty(value = "人员类型")
    private String category;
    @Excel(name = "联系方式", width = 15)
    private String phone;
    @Excel(name = "学历", width = 30)
    private String degree;
    @Excel(name = "户口类型", width = 15)
    private String accountType;
    @Excel(name = "专业", width = 40)
    private String trainingProfessional;
    @Excel(name = "身份证正面", width = 150)
    private String photos;
    @Excel(name = "身份证反面", width = 150)
    private String photos2;
//    @Excel(name = "姓名", width = 15)
//    private String userName;
//    @Excel(name = "身份证", width = 15)
//    private String idcard;
//    @Excel(name = "地址",width = 30)
//    private String address;
//    @Excel(name = "培训类型", width = 30)
//    private String training;
//    @Excel(name = "培训专业", width = 30)
//    private String specialized;
//    @Excel(name = "就业意向", width = 30)
//    private String intention;
    private int isDelete;
}
