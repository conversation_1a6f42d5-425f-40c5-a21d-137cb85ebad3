package com.byun.modules.bosong.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 博颂优惠券
 * @date : 2025-07-16
 */
@Data
@TableName("bosong_coupons")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="BoSongCoupon", description="博颂优惠券")
public class BoSongCoupon implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 优惠卷代码
     */
    private String code;
    /**优惠券名称*/
    @Excel(name = "优惠券名称", width = 20)
    @ApiModelProperty(value = "优惠券名称")
    @NotBlank(message = "优惠券名称不能为空")
    private String couponName;

    /**优惠券描述*/
    @Excel(name = "优惠券描述", width = 30)
    @ApiModelProperty(value = "优惠券描述")
    private String description;

    /**优惠金额*/
    @Excel(name = "优惠金额", width = 15)
    @ApiModelProperty(value = "优惠金额")
    @NotNull(message = "优惠金额不能为空")
    private BigDecimal discountAmount;

    /**最低消费金额*/
    @Excel(name = "最低消费金额", width = 15)
    @ApiModelProperty(value = "最低消费金额")
    private BigDecimal minAmount;

    /**发行总数量*/
    @Excel(name = "发行总数量", width = 15)
    @ApiModelProperty(value = "发行总数量")
    @NotNull(message = "发行总数量不能为空")
    private Integer totalQuantity;

    /**已发放数量*/
    @Excel(name = "已发放数量", width = 15)
    @ApiModelProperty(value = "已发放数量")
    private Integer issuedQuantity;

    /**已使用数量*/
    @Excel(name = "已使用数量", width = 15)
    @ApiModelProperty(value = "已使用数量")
    private Integer usedQuantity;

    /**有效期开始时间*/
    @Excel(name = "有效期开始时间", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "有效期开始时间")
    @NotNull(message = "有效期开始时间不能为空")
    private LocalDateTime validStartTime;

    /**有效期结束时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "有效期结束时间")
    @NotNull(message = "有效期结束时间不能为空")
    private LocalDateTime validEndTime;

    /**状态 0-禁用 1-启用*/
    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**创建时间*/
    @Excel(name = "创建时间", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    /**删除状态 0-正常 1-已删除*/
    @ApiModelProperty(value = "删除状态 0-正常 1-已删除")
    private Integer delFlag;
}
