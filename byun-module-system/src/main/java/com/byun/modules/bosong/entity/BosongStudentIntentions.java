package com.byun.modules.bosong.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.time.LocalDateTime;

/**
 * 学历提升意向表
 */
@Data
@TableName("bosong_student_intentions")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class BosongStudentIntentions {
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    //姓名
    @Excel(name = "姓名",width = 15)
    private String name;
    @Excel(name = "电话",width = 15)
    private String phone;
    @Excel(name = "目前最高学历",width = 20)
    private String currentEducation;
    @Excel(name = "报考层次",width = 20)
    private String applicationLevel;
    @Excel(name = "意向专业",width = 50)
    private String intendedMajors;
    @Excel(name = "其他",width = 20)
    private String otherMajor;
    //创建时间
    private LocalDateTime createdAt;
}
