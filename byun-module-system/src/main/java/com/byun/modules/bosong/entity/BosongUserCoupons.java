package com.byun.modules.bosong.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 用户优惠卷
 */
@Data
@TableName("bosong_user_coupons")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="bosongUserCoupons", description="")
public class BosongUserCoupons {
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    private String userId;//用户id
    private String couponId;//优惠卷ID
    private int isUsed;//是否已使用
    private String code;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "有效期开始时间")
    private LocalDateTime usedAt;//使用时间

}
