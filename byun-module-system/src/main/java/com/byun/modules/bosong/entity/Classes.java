package com.byun.modules.bosong.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
@TableName("bosong_classes")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="BoSong_classes", description="班级表")
public class Classes {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @Excel(name = "序号", width = 15)
    String serialNumber;
    @Excel(name = "班次", width = 15)
    private String className;//班次(班级名称)
    private Date startDate;//开班日期
    private Date endDate;//结束日期
    @Excel(name = "开班日期", width = 30)
    @TableField(exist = false)
    private String  startDateAndEndDate;
    @Excel(name = "人员类型", width = 15)
    private String personnelType;
    @Excel(name = "讲师1", width = 15)
    private String lecturer1;
    @Excel(name = "开班人数", width = 15)
    private String numberOfStudents;
    @Excel(name = "讲师2", width = 15)
    private String lecturer2;
    @Excel(name = "培训地点", width = 80)
    private String trainingLocation;
    /**
     * 问卷数量
     */
    @TableField(exist = false)
    private String questionnaireFilledRate;
//    @TableField(exist = false)
//    private int questionnaireCount;
//    @TableField(exist = false)
//    private int filledCount;

    private Date createTime;//创建时间
    private String createBy;//创建人(手机号)
    private String createById;//创建人ID
    private int delFlag;//删除状态
}
