package com.byun.modules.bosong.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 学员证件待删除表
 */
@TableName("bosong_pending_deletion_files")
@Data
public class PendingDeletionFiles {
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String studentId;
    private String studentName;
    private String fileUrl;
    private int fileType;
    private LocalDateTime createdAt;
    private LocalDateTime deletedAt;
    //0-待删除，1-已删除，2-删除失败
    private int status;

}
