package com.byun.modules.bosong.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 问卷调查答案表
 */
@TableName("bosong_questionnaire_context_answer")
@Data
public class QuestionnaireContextAnswer {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 班级ID
     */
    private String classesId;
    /**
     * 班级名称
     */
    private String classesName;
    /**
     * 学员id
     */
    private String studentId;
    /**
     * 学员编号
     */
    private String studentSerialNumber;
    /**
     * 学员调查问卷状态id
     */
    private String studentQuestionnaireStatusId;
    /**
     * 学员姓名
     */
    private String studentUsername;
    /**
     * 问卷填写内容
     */
    private String questionnaireContentAnswer;

}
