package com.byun.modules.bosong.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
@TableName("bosong_student_questionnaire_status")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class QuestionnaireStatus {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    private java.lang.String id;
    /**
     * 班级ID
     */
    private String classesId;
    /**
     * 学生ID
     */
    private String studentId;
    /**
     * 学员姓名
     */
    private String username;
    /**
     * 学员编号
     */
    private String studentSerialNumber;
    /**
     * 问卷填写状态
     */
    private int questionnaireStatus;

}
