package com.byun.modules.bosong.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

@TableName("bosong_student")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="BoSong_student", description="学生表")
public class Student {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @Excel(name = "序号", width = 10)
    private String serialNumber;
    /**
     * 班次（班级名称）
     */
    //@Excel(name = "班次", width = 15)
    @TableField(exist = false)
    private String chassesName;
    @Excel(name = "姓名", width = 15)
    private String userName;
    @Excel(name = "性别", width = 10)
    private String gender;
    @Excel(name = "民族", width = 10)
    private String ethnicity;
    @Excel(name = "文化程度", width = 10)
    private String educationLevel;
    @Excel(name = "专业", width = 20)
    private String major;
    @Excel(name = "手机号", width = 15)
    private String phone;
    @Excel(name = "身份证号", width = 30)
    private String idCardNumber;
    @Excel(name = "人员类别", width = 15)
    private String personnelType;
    @Excel(name = "证件号码（学号）", width = 30)
    private String certificateNumber;
    @Excel(name = "备注", width = 15)
    private String remarks;
    //@Excel(name = "证件照片", width = 15)
    private String idPhoto;
    //@Excel(name = "学生证", width = 15)
    private String studentIdPhoto;
    //社保卡
    private String socialSecurityCard;
    //身份证
    private String idCard;
    //班级id
    private String chassesId;
    //创建时间
    private Date createTime;
    //删除状态
    private int delFlag;
    //创建人(手机号)
    private String createBy;
    //创建人(id)
    private String createById;

}
