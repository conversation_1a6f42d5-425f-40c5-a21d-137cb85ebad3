package com.byun.modules.bosong.excel;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * 问卷导出Excel实体类
 */
@Data
public class QuestionnaireExcel {
    @Excel(name = "序号")
    private String studentSerialNumber;
    @Excel(name = "班次",width = 15)
    private String classesName;
    @Excel(name = "姓名")
    private String username;
    @Excel(name = "电话",width = 15)
    private String phone;
    @Excel(name = "状态")
    private String questionnaireStatus;
    @Excel(name = "创业培训类型",width = 20)
    private String trainingPrograms;
    @Excel(name = "GYB评价")
    private String trainingRatingsGYB;
    @Excel(name = "SYB评价")
    private String trainingRatingsSYB;
    @Excel(name = "WC评价")
    private String trainingRatingsWC;
    @Excel(name = "是否有创业打算",width = 20)
    private String entrepreneurshipPlan;
    @Excel(name = "是否已开业",width = 20)
    private String businessStarted;
    @Excel(name = "创业面临困境",width = 35)
    private String businessChallenges;
    @Excel(name = "是否需要创业后续服务",width = 20)
    private String followUpServiceNeeded;
    @Excel(name = "期望的后续服务形式",width = 35)
    private String followUpService;
    @Excel(name = "其他需求",width = 50)
    private String otherNeeds;
    @Excel(name = "其他建议",width = 50)
    private String otherSuggestions;
}
