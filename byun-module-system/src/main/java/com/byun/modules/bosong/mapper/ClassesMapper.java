package com.byun.modules.bosong.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.bosong.entity.Classes;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

/**
 * 班级
 */
@Mapper
public interface ClassesMapper extends BaseMapper<Classes> {


    IPage<Classes> getClassPageWithQuestionnaireCount(Page<Classes> page, @Param("className") String className, @Param("lecturer") String lecturer);

}
