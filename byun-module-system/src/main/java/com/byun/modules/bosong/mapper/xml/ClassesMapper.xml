<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.bosong.mapper.ClassesMapper">
    <select id="getClassPageWithQuestionnaireCount" resultType="com.byun.modules.bosong.entity.Classes">
        SELECT
        c.*,
        CONCAT(
        SUM(CASE WHEN q.questionnaire_status = 1 THEN 1 ELSE 0 END),
        '/',
        COUNT(q.id)
        ) AS questionnaireFilledRate
        FROM
        bosong_classes c
        LEFT JOIN
        bosong_student_questionnaire_status q ON c.id = q.classes_id
        WHERE
        c.del_flag = 0
        <if test="className != null and className != ''">
            AND c.class_name LIKE CONCAT('%', #{className}, '%')
        </if>
        <if test="lecturer != null and lecturer != ''">
            AND (
            CONVERT(c.lecturer1 USING utf8mb4) COLLATE utf8mb4_general_ci LIKE CONCAT('%', #{lecturer}, '%')
            OR
            CONVERT(c.lecturer2 USING utf8mb4) COLLATE utf8mb4_general_ci LIKE CONCAT('%', #{lecturer}, '%')
            )
        </if>
        GROUP BY
        c.id
        ORDER BY
        c.create_time DESC, CAST(c.serial_number AS UNSIGNED) ASC
    </select>


</mapper>