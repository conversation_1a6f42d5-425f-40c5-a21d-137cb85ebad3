package com.byun.modules.bosong.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.bosong.entity.Classes;
import com.byun.modules.bosong.entity.Student;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 班级
 */
public interface IClassesService extends IService<Classes> {
    void asyncExport(List<Student> studentsList, String catchKey,String className) throws IOException;

    void asyncBatchExport(Map<String, List<Student>> studentsMap, List<Classes> classesList, String catchKey, String fileName) throws IOException;

    byte[] getChunk(Integer index, Integer chunkSize, String filePath, long offset);

    IPage<Classes> getClassPageWithQuestionnaireCount(Page<Classes> page, String className, String lecturer);


    // void removeFile(String path);
}
