package com.byun.modules.bosong.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.bosong.entity.QuestionnaireContextAnswer;

import java.io.IOException;
import java.util.List;

public interface IQuestionnaireContextAnswerService extends IService<QuestionnaireContextAnswer>  {

    String asyncExport(List<QuestionnaireContextAnswer> questionnaireContextAnswers, String catchKey, String className) throws IOException;
}
