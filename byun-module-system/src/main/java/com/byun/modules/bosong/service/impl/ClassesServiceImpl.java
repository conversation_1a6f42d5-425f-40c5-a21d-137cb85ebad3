package com.byun.modules.bosong.service.impl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.util.RedisUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.bosong.entity.Classes;
import com.byun.modules.bosong.entity.Student;
import com.byun.modules.bosong.mapper.ClassesMapper;
import com.byun.modules.bosong.service.IClassesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
/**
 * 班级
 */
@Service
public class ClassesServiceImpl  extends ServiceImpl<ClassesMapper, Classes> implements IClassesService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ClassesMapper classesMapper;
    @Async("studentExportTaskExecutor")
    @Override
    public void asyncExport(List<Student> studentsList, String catchKey, String className) throws IOException {
        redisUtil.set(catchKey, 0, 60 * 10); // 设置初始进度
        String os = System.getProperty("os.name").toLowerCase();
        String projectDir = os.contains("win") ? "C:\\opt" : "/home/<USER>";
        Path targetDir = Paths.get(projectDir, "upFiles", "bs").normalize();
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }
        String tempZipFileName = Paths.get(targetDir.toString(), catchKey + "_temp.zip").toString(); // 临时文件名
        AtomicInteger processedFiles = new AtomicInteger(0);
        AtomicInteger totalFiles = new AtomicInteger(0);
        // 统计总文件数
        studentsList.forEach(student -> {
            Stream.of(student.getIdPhoto(), student.getStudentIdPhoto(), student.getIdCard(), student.getSocialSecurityCard())
                    .filter(WxlConvertUtils::isNotEmpty)
                    .forEach(file -> totalFiles.incrementAndGet());
        });
        // 创建4个线程
        ExecutorService executor = Executors.newFixedThreadPool(4);
        List<Future<?>> futures = new ArrayList<>();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(Files.newOutputStream(Paths.get(tempZipFileName)))) {
            Object lock = new Object(); // 压缩写入对象锁
            for (Student student : studentsList) {
                futures.add(executor.submit(() -> {
                    String fileName = student.getSerialNumber() + student.getUserName();
                    processStudentFiles(student, fileName, zipOutputStream, lock, processedFiles, totalFiles, catchKey);
                }));
            }
            // 等待所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (ExecutionException e) {
                    Throwable cause = e.getCause();
                    e.printStackTrace();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    e.printStackTrace();
                }
            }
            zipOutputStream.finish();
            // 计算压缩文件的 MD5 值
            Path tempZipFilePath = Paths.get(tempZipFileName);
            String fileMd5 = calculateMD5(tempZipFilePath.toFile());
            // 使用 MD5 作为最终文件名
            String finalZipFileName = Paths.get(targetDir.toString(), fileMd5 + ".zip").toString();
            Files.move(tempZipFilePath, Paths.get(finalZipFileName), StandardCopyOption.REPLACE_EXISTING);
            // 获取文件大小
            long fileSize = Files.size(Paths.get(finalZipFileName));
            // 保存到 Redis
            Map<String, Object> map = new HashMap<>();
            map.put("fileName", className + ".zip");
            map.put("fileSize", fileSize);
            map.put("fileMd5", fileMd5);
            redisUtil.hmset(catchKey + "_file_info", map, 600);
            redisUtil.set(catchKey, 100, 600);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executor.shutdown(); // 关闭线程池
        }
    }

    @Async("studentExportTaskExecutor")
    @Override
    public void asyncBatchExport(Map<String, List<Student>> studentsMap, List<Classes> classesList, String catchKey, String fileName) throws IOException {
        redisUtil.set(catchKey, 0, 60 * 10); // 设置初始进度
        String os = System.getProperty("os.name").toLowerCase();
        String projectDir = os.contains("win") ? "C:\\opt" : "/home/<USER>";
        Path targetDir = Paths.get(projectDir, "upFiles", "bs").normalize();
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }
        String tempZipFileName = Paths.get(targetDir.toString(), catchKey + "_temp.zip").toString(); // 临时文件名
        AtomicInteger processedFiles = new AtomicInteger(0);
        AtomicInteger totalFiles = new AtomicInteger(0);

        // 创建班级ID到班级名称的映射
        Map<String, String> classIdToNameMap = new HashMap<>();
        for (Classes classes : classesList) {
            classIdToNameMap.put(classes.getId(), classes.getClassName());
        }

        // 统计总文件数
        studentsMap.values().forEach(studentsList -> {
            studentsList.forEach(student -> {
                Stream.of(student.getIdPhoto(), student.getStudentIdPhoto(), student.getIdCard(), student.getSocialSecurityCard())
                        .filter(WxlConvertUtils::isNotEmpty)
                        .forEach(file -> totalFiles.incrementAndGet());
            });
        });

        // 创建4个线程
        ExecutorService executor = Executors.newFixedThreadPool(12);
        List<Future<?>> futures = new ArrayList<>();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(Files.newOutputStream(Paths.get(tempZipFileName)))) {
            Object lock = new Object(); // 压缩写入对象锁

            // 遍历每个班级的学员
            for (Map.Entry<String, List<Student>> entry : studentsMap.entrySet()) {
                String classId = entry.getKey();
                List<Student> studentsList = entry.getValue();
                String className = classIdToNameMap.get(classId);

                for (Student student : studentsList) {
                    futures.add(executor.submit(() -> {
                        String studentFileName = student.getSerialNumber() + student.getUserName();
                        processBatchStudentFiles(student, studentFileName, className, zipOutputStream, lock, processedFiles, totalFiles, catchKey);
                    }));
                }
            }

            // 等待所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (ExecutionException e) {
                    Throwable cause = e.getCause();
                    e.printStackTrace();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    e.printStackTrace();
                }
            }
            zipOutputStream.finish();
            // 计算压缩文件的 MD5 值
            Path tempZipFilePath = Paths.get(tempZipFileName);
            String fileMd5 = calculateMD5(tempZipFilePath.toFile());
            // 使用 MD5 作为最终文件名
            String finalZipFileName = Paths.get(targetDir.toString(), fileMd5 + ".zip").toString();
            Files.move(tempZipFilePath, Paths.get(finalZipFileName), StandardCopyOption.REPLACE_EXISTING);
            // 获取文件大小
            long fileSize = Files.size(Paths.get(finalZipFileName));
            // 保存到 Redis
            Map<String, Object> map = new HashMap<>();
            map.put("fileName", fileName + ".zip");
            map.put("fileSize", fileSize);
            map.put("fileMd5", fileMd5);
            redisUtil.hmset(catchKey + "_file_info", map, 600);
            redisUtil.set(catchKey, 100, 600);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executor.shutdown(); // 关闭线程池
        }
    }

    /**
     * 计算文件MD5
     * @param file
     * @return
     * @throws IOException
     */
    private String calculateMD5(File file) throws IOException {
        try (InputStream fis = new FileInputStream(file)) {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] byteArray = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(byteArray)) != -1) {
                digest.update(byteArray, 0, bytesRead);
            }
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest.digest()) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new IOException("Failed to calculate MD5", e);
        }
    }

    /**
     * 获取分片
     * @param index
     * @param chunkSize
     * @param filePath
     * @param offset
     * @return
     */
    @Override
    public byte[] getChunk(Integer index, Integer chunkSize, String filePath, long offset) {
        // resultFile = new File(filePath);
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(filePath, "r")) {
            // 定位到该分片的偏移量
            randomAccessFile.seek(offset);
            //读取
            byte[] buffer = new byte[chunkSize];
            randomAccessFile.read(buffer);
            return buffer;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 问卷
     * @param page
     * @param className
     * @param lecturer
     * @return
     */
    @Override
    public IPage<Classes> getClassPageWithQuestionnaireCount(Page<Classes> page, String className, String lecturer) {
        return classesMapper.getClassPageWithQuestionnaireCount(page,className,lecturer);
    }


    /**
     * 执行文件压缩
     * @param student
     * @param fileName
     * @param zipOutputStream
     * @param lock
     * @param processedFiles
     * @param totalFiles
     * @param catchKey
     */
    private void processStudentFiles(
            Student student, String fileName, ZipOutputStream zipOutputStream, Object lock,
            AtomicInteger processedFiles, AtomicInteger totalFiles, String catchKey) {
        try {
            // 证件照文件夹
            if (WxlConvertUtils.isNotEmpty(student.getIdPhoto())) {
                String newFileName = "证件照/" + fileName + "证件照" + student.getIdPhoto().substring(student.getIdPhoto().length() - 4);
                compressAndZipImageSync(student.getIdPhoto(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
            // 学生证文件夹
            if (WxlConvertUtils.isNotEmpty(student.getStudentIdPhoto())) {
                String newFileName = "学生证/" + fileName + "学生证" + student.getStudentIdPhoto().substring(student.getStudentIdPhoto().length() - 4);
                compressAndZipImageSync(student.getStudentIdPhoto(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
            // 身份证文件夹
            if (WxlConvertUtils.isNotEmpty(student.getIdCard())) {
                String newFileName = "身份证/" + fileName + "身份证" + student.getIdCard().substring(student.getIdCard().length() - 4);
                compressAndZipImageSync(student.getIdCard(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
            // 社保卡文件夹
            if (WxlConvertUtils.isNotEmpty(student.getSocialSecurityCard())) {
                String newFileName = "社保卡/" + fileName + "社保卡" + student.getSocialSecurityCard().substring(student.getSocialSecurityCard().length() - 4);
                compressAndZipImageSync(student.getSocialSecurityCard(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量导出时执行文件压缩（带班级文件夹）
     * @param student
     * @param fileName
     * @param className
     * @param zipOutputStream
     * @param lock
     * @param processedFiles
     * @param totalFiles
     * @param catchKey
     */
    private void processBatchStudentFiles(
            Student student, String fileName, String className, ZipOutputStream zipOutputStream, Object lock,
            AtomicInteger processedFiles, AtomicInteger totalFiles, String catchKey) {
        try {
            // 证件照文件夹
            if (WxlConvertUtils.isNotEmpty(student.getIdPhoto())) {
                String newFileName = className + "/证件照/" + fileName + "证件照" + student.getIdPhoto().substring(student.getIdPhoto().length() - 4);
                compressAndZipImageSync(student.getIdPhoto(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
            // 学生证文件夹
            if (WxlConvertUtils.isNotEmpty(student.getStudentIdPhoto())) {
                String newFileName = className + "/学生证/" + fileName + "学生证" + student.getStudentIdPhoto().substring(student.getStudentIdPhoto().length() - 4);
                compressAndZipImageSync(student.getStudentIdPhoto(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
            // 身份证文件夹
            if (WxlConvertUtils.isNotEmpty(student.getIdCard())) {
                String newFileName = className + "/身份证/" + fileName + "身份证" + student.getIdCard().substring(student.getIdCard().length() - 4);
                compressAndZipImageSync(student.getIdCard(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
            // 社保卡文件夹
            if (WxlConvertUtils.isNotEmpty(student.getSocialSecurityCard())) {
                String newFileName = className + "/社保卡/" + fileName + "社保卡" + student.getSocialSecurityCard().substring(student.getSocialSecurityCard().length() - 4);
                compressAndZipImageSync(student.getSocialSecurityCard(), zipOutputStream, newFileName, lock);
                updateProgress(processedFiles, totalFiles, catchKey);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 压缩文件
     * @param filePath
     * @param zipOutputStream
     * @param entryName
     * @param lock
     * @throws IOException
     */
    private void compressAndZipImageSync(String filePath, ZipOutputStream zipOutputStream, String entryName, Object lock) throws IOException {
        synchronized (lock) {
            compressAndZipImage(filePath, zipOutputStream, entryName); // 调用原始压缩方法
        }
    }

    /**
     * 更新进度
     * @param processedFiles
     * @param totalFiles
     * @param catchKey
     */
    private void updateProgress(AtomicInteger processedFiles, AtomicInteger totalFiles,String catchKey) {
        int progress = (int) (((double) processedFiles.incrementAndGet() / totalFiles.get()) * 100);
        //进度条 key班级名称_请求时间戳 value 当前导出进度 过期时间10分钟
        //TODO redis string类型修改为hash类型 缓存异步线程异常，轮询接口获取
        if (progress < 100) {
            //redisUtil.set(catchKey, progress, 60 << 3 + 60 << 1);
            redisUtil.set(catchKey,progress,600);
        }
    }
    /**
     * 创建压缩文件 .zip
     * @param imageUrl
     * @param zipOutputStream
     * @param fileName
     * @throws IOException
     */
    private void compressAndZipImage(String imageUrl, ZipOutputStream zipOutputStream, String fileName) {
        // 检查并补全 URL 协议
        if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
            //资源无效
            return;
        }
        //zipOutputStream.setLevel(9);
        HttpURLConnection connection = null;
        try {
            connection = (HttpURLConnection) new URL(imageUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                zipOutputStream.putNextEntry(new ZipEntry(fileName));
                try (InputStream inputStream = connection.getInputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zipOutputStream.write(buffer, 0, bytesRead);
                    }
                }
                zipOutputStream.closeEntry();
            } else { //资源无法访问
                //System.err.println("资源无法访问: " + imageUrl + " (HTTP " + responseCode + ")");
            }
        } catch (IOException e) {
            //IO异常
        } finally {
            //关闭连接
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
