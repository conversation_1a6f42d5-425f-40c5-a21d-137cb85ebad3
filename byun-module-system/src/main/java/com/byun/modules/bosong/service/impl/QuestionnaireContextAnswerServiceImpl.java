package com.byun.modules.bosong.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.util.RedisUtil;
import com.byun.modules.bosong.entity.QuestionnaireContextAnswer;
import com.byun.modules.bosong.mapper.QuestionnaireContextAnswerMapper;
import com.byun.modules.bosong.service.IQuestionnaireContextAnswerService;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class QuestionnaireContextAnswerServiceImpl extends ServiceImpl<QuestionnaireContextAnswerMapper,
        QuestionnaireContextAnswer> implements IQuestionnaireContextAnswerService {
    @Autowired
    private RedisUtil redisUtil;

    @Async
    @Override
    public String asyncExport(List<QuestionnaireContextAnswer> questionnaireContextAnswers, String catchKey, String className) throws IOException {
        redisUtil.set(catchKey, 0, 60 * 10); // 设置初始进度
        String os = System.getProperty("os.name").toLowerCase();
        String projectDir = os.contains("win") ? "C:\\opt" : "/home/<USER>";
        Path targetDir = Paths.get(projectDir, "upFiles", "bs").normalize();
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }
        String tempZipFileName = Paths.get(targetDir.toString(), catchKey + "_temp.zip").toString(); // 临时文件名
        AtomicInteger processedFiles = new AtomicInteger(0);//已处理文件数量
        AtomicInteger totalFiles = new AtomicInteger(questionnaireContextAnswers.size()); //文件总数
        //创建8个线程
        ExecutorService executor = Executors.newFixedThreadPool(8);
        List<Future<?>> futures = new ArrayList<>();
        //创建压缩文件
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(Files.newOutputStream(Paths.get(tempZipFileName)))) {
            Object lock = new Object(); // 压缩写入对象锁
            for (QuestionnaireContextAnswer questionnaireContextAnswer : questionnaireContextAnswers) {
                futures.add(executor.submit(() -> {
                    //TODO Word格式
                    //PDF格式
                    //pdf文件名
                    String filePdfName = questionnaireContextAnswer.getClassesName() + "_" + questionnaireContextAnswer.getStudentSerialNumber() + "_" +
                            questionnaireContextAnswer.getStudentUsername() + "_调查问卷"+".pdf";
                    processtQuestionnaireFiles(questionnaireContextAnswer, filePdfName, zipOutputStream, lock, processedFiles, totalFiles, catchKey);
                }));
            }
            // 等待所有线程任务完成
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (ExecutionException e) {
                    Throwable cause = e.getCause();
                    e.printStackTrace();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    e.printStackTrace();
                }
            }
            zipOutputStream.finish();
            // 计算压缩文件的 MD5 值
            Path tempZipFilePath = Paths.get(tempZipFileName);
            String fileMd5 = calculateMD5(tempZipFilePath.toFile());
            // 使用 MD5 作为最终文件名
            String finalZipFileName = Paths.get(targetDir.toString(), fileMd5 + ".zip").toString();
            Files.move(tempZipFilePath, Paths.get(finalZipFileName), StandardCopyOption.REPLACE_EXISTING);
            // 获取文件大小
            long fileSize = Files.size(Paths.get(finalZipFileName));
            // 保存到 Redis
            Map<String, Object> map = new HashMap<>();
            map.put("fileName", className +"调查问卷" + ".zip");
            map.put("fileSize", fileSize);
            map.put("fileMd5", fileMd5);
            redisUtil.hmset(catchKey + "_file_info", map, 600);
            redisUtil.set(catchKey, 100, 600);
        }
        return null;
    }

    /**
     * 计算文件MD5值
     * @param file
     * @return
     * @throws IOException
     */
    private String calculateMD5(File file) throws IOException {
        try (InputStream fis = new FileInputStream(file)) {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] byteArray = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(byteArray)) != -1) {
                digest.update(byteArray, 0, bytesRead);
            }
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest.digest()) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new IOException("Failed to calculate MD5", e);
        }
    }
    /***
     * pdf格式处理
     * @param questionnaireContextAnswer
     * @param filePdfName
     * @param zipOutputStream
     * @param lock
     * @param processedFiles
     * @param totalFiles
     * @param catchKey
     */
    private void processtQuestionnaireFiles(QuestionnaireContextAnswer questionnaireContextAnswer, String filePdfName, ZipOutputStream zipOutputStream, Object lock, AtomicInteger processedFiles, AtomicInteger totalFiles, String catchKey) {
        Map<String, String> pdfFormDataMaps = questionnairePDFData(questionnaireContextAnswer);
        PdfReader reader = null;
        ByteArrayOutputStream bos = null;
        PdfStamper pdfStamper = null;
        FileOutputStream fos = null;
        try {
            //读取PDF模板
            InputStream in = this.getClass().getClassLoader().getResourceAsStream(pdfFormDataMaps.get("tempPath"));
            assert in != null;
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            byte[] pdfBytes = baos.toByteArray();
            //System.out.println("PDF 文件字节大小: " + pdfBytes.length);
            reader = new PdfReader(pdfBytes);
            // 输出流
            bos = new ByteArrayOutputStream();
            // 构建PDF对象
            pdfStamper = new PdfStamper(reader, bos);
            // 获取表单数据
            AcroFields form = pdfStamper.getAcroFields();
            // 使用中文字体 使用 AcroFields填充值的不需要在程序中设置字体，在模板文件中设置字体为中文字体 Adobe 宋体 std L
            BaseFont bfChinese = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            form.addSubstitutionFont(bfChinese);
            for (String key : pdfFormDataMaps.keySet()) {
                String value = pdfFormDataMaps.get(key);
                String[] states = form.getAppearanceStates(key);
                if (states != null && states.length > 1) {
                    // 是复选框：选中 = 第二个状态值，未选中 = 第一个（Off）
                    if ("Yes".equalsIgnoreCase(value) || "true".equalsIgnoreCase(value)) {
                        form.setField(key, states[1]);
                    } else {
                        form.setField(key, states[0]);
                    }
                } else {
                    // 普通文本字段
                    form.setField(key, value, true);
                    form.setFieldProperty(key, "textfont", bfChinese, null);
                }
            }
            pdfStamper.setFormFlattening(true);
            pdfStamper.close();
            compressAndZipPdfSync(bos.toByteArray(),zipOutputStream,filePdfName,lock);//执行压缩
            updateProgress(processedFiles,totalFiles,catchKey); //更新进度
//            String u = AliOSSUtil.uploadBytesFile("contract/" + "6666666" + ".pdf", bos.toByteArray());
//            System.out.println(u);
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 整合PDF需要插入的数据
     * @param questionnaireContextAnswer
     * @return
     */
    private Map<String,String> questionnairePDFData(QuestionnaireContextAnswer questionnaireContextAnswer) {
        HashMap<String, String> pdfFormDataMaps = new HashMap<>();
        //调查问卷类型
        JSONObject questionnaireContentAnswer = JSON.parseObject(questionnaireContextAnswer.getQuestionnaireContentAnswer());
        JSONObject trainingPrograms = questionnaireContentAnswer.getJSONObject("trainingPrograms");
        Boolean gyb = trainingPrograms.getBoolean("GYB");
        Boolean syb = trainingPrograms.getBoolean("SYB");
        Boolean wc = trainingPrograms.getBoolean("WC");
        //String tempPath = "";
        // 一 个人基本信息
        //1.1 姓名
        pdfFormDataMaps.put("userName", questionnaireContextAnswer.getStudentUsername());
        //1.2 手机
        String phone = questionnaireContentAnswer.getString("phone");
        pdfFormDataMaps.put("phone", phone);
        //1.3 参加过的创业培训类型（可多选）
        if (Boolean.TRUE.equals(gyb) && !Boolean.TRUE.equals(syb) && !Boolean.TRUE.equals(wc)) {
            // 只选了 GYB
            //tempPath = "contract/GYB.pdf";
            pdfFormDataMaps.put("tempPath","contract/GYB.pdf");
            pdfFormDataMaps.put("GYB", "Yes");
        } else if (!Boolean.TRUE.equals(gyb) && Boolean.TRUE.equals(syb) && !Boolean.TRUE.equals(wc)) {
            // 只选了 SYB
            //tempPath = "contract/SYB.pdf";
            pdfFormDataMaps.put("tempPath","contract/SYB.pdf");
            pdfFormDataMaps.put("SYB", "Yes");
        } else if (!Boolean.TRUE.equals(gyb) && !Boolean.TRUE.equals(syb) && Boolean.TRUE.equals(wc)) {
            // 只选了 WC
            //tempPath = "contract/WC.pdf";
            pdfFormDataMaps.put("tempPath","contract/WC.pdf");
            pdfFormDataMaps.put("WC", "Yes");
        } else if (Boolean.TRUE.equals(gyb) && Boolean.TRUE.equals(syb) && !Boolean.TRUE.equals(wc)) {
            // 选了 GYB + SYB
            //tempPath = "contract/GYB_SYB.pdf";
            pdfFormDataMaps.put("tempPath","contract/GYB_SYB.pdf");
            pdfFormDataMaps.put("GYB", "Yes");
            pdfFormDataMaps.put("SYB", "Yes");
        } else if (Boolean.TRUE.equals(gyb) && !Boolean.TRUE.equals(syb) && Boolean.TRUE.equals(wc)) {
            // 选了 GYB + WC
            //tempPath = "contract/GYB_WC.pdf";
            pdfFormDataMaps.put("tempPath","contract/GYB_WC.pdf");
            pdfFormDataMaps.put("GYB", "Yes");
            pdfFormDataMaps.put("WC", "Yes");
        } else if (!Boolean.TRUE.equals(gyb) && Boolean.TRUE.equals(syb) && Boolean.TRUE.equals(wc)) {
            // 选了 SYB + WC
            //tempPath = "contract/SYB_WC.pdf";
            pdfFormDataMaps.put("tempPath","contract/SYB_WC.pdf");
            pdfFormDataMaps.put("SYB", "Yes");
            pdfFormDataMaps.put("WC", "Yes");
        } else if (Boolean.TRUE.equals(gyb) && Boolean.TRUE.equals(syb) && Boolean.TRUE.equals(wc)) {
            // 全选 GYB + SYB + WC
            //tempPath = "contract/GYB_SYB_WC.pdf";
            pdfFormDataMaps.put("tempPath","contract/GYB_SYB_WC.pdf");
            pdfFormDataMaps.put("GYB", "Yes");
            pdfFormDataMaps.put("SYB", "Yes");
            pdfFormDataMaps.put("WC", "Yes");
        }
        //1.4 满意度(GYB SYB WC)
        JSONObject trainingRatings = questionnaireContentAnswer.getJSONObject("trainingRatings");
        // 处理 GYB满意度
        if (trainingRatings.containsKey("GYB")) {
            Boolean trainingRatingsGYB = trainingRatings.getBoolean("GYB");
            if (Boolean.TRUE.equals(trainingRatingsGYB)) {
                pdfFormDataMaps.put("trainingRatings_GYB_0", "Yes");
            } else {
                pdfFormDataMaps.put("trainingRatings_GYB_1", "Yes");
            }
        }
        // 处理 SYB满意度
        if (trainingRatings.containsKey("SYB")) {
            Boolean trainingRatingsSYB = trainingRatings.getBoolean("SYB");
            if (Boolean.TRUE.equals(trainingRatingsSYB)) {
                pdfFormDataMaps.put("trainingRatings_SYB_0", "Yes");
            } else {
                pdfFormDataMaps.put("trainingRatings_SYB_1", "Yes");
            }
        }
        // 处理 WC满意度
        if (trainingRatings.containsKey("WC")) {
            Boolean trainingRatingsWC = trainingRatings.getBoolean("WC");
            if (Boolean.TRUE.equals(trainingRatingsWC)) {
                pdfFormDataMaps.put("trainingRatings_WC_0", "Yes");
            } else {
                pdfFormDataMaps.put("trainingRatings_WC_1", "Yes");
            }
        }
        //二 创业形状
        //2.1 是否有创业打算
        Boolean entrepreneurshipPlan = questionnaireContentAnswer.getBoolean("entrepreneurshipPlan");
        if (Boolean.TRUE.equals(entrepreneurshipPlan)) {
            pdfFormDataMaps.put("entrepreneurshipPlan_0", "Yes");
        } else {
            pdfFormDataMaps.put("entrepreneurshipPlan_1", "Yes");
        }
        //2.2 是否已开业
        Boolean businessStarted = questionnaireContentAnswer.getBoolean("businessStarted");
        if (Boolean.TRUE.equals(businessStarted)) {
            pdfFormDataMaps.put("businessStarted_0", "Yes");
        } else {
            pdfFormDataMaps.put("businessStarted_1", "Yes");
        }
        //三 创业面临困境
        //3.1 目前，您在创业过程中面临的主要困境有哪些（可多选）
        JSONObject businessChallenges = questionnaireContentAnswer.getJSONObject("businessChallenges");
        // 处理 fundingShortage (资金短缺)
        if (businessChallenges.containsKey("fundingShortage")) {
            Boolean fundingShortage = businessChallenges.getBoolean("fundingShortage");
            if (Boolean.TRUE.equals(fundingShortage)) {
                pdfFormDataMaps.put("fundingShortage", "Yes");
            }
        }
        // 处理 marketCompetition (市场开拓困难)
        if (businessChallenges.containsKey("marketCompetition")) {
            Boolean marketCompetition = businessChallenges.getBoolean("marketCompetition");
            if (Boolean.TRUE.equals(marketCompetition)) {
                pdfFormDataMaps.put("marketCompetition", "Yes");
            }
        }
        // 处理 talentShortage (缺乏专业人才)
        if (businessChallenges.containsKey("talentShortage")) {
            Boolean talentShortage = businessChallenges.getBoolean("talentShortage");
            if (Boolean.TRUE.equals(talentShortage)) {
                pdfFormDataMaps.put("talentShortage", "Yes");
            }
        }
        // 处理 policyEnvironment (技术研发瓶颈)
        if (businessChallenges.containsKey("policyEnvironment")) {
            Boolean policyEnvironment = businessChallenges.getBoolean("policyEnvironment");
            if (Boolean.TRUE.equals(policyEnvironment)) {
                pdfFormDataMaps.put("policyEnvironment", "Yes");
            }
        }
        // 处理 resourceAccess (政策法规不熟悉)
        if (businessChallenges.containsKey("resourceAccess")) {
            Boolean resourceAccess = businessChallenges.getBoolean("resourceAccess");
            if (Boolean.TRUE.equals(resourceAccess)) {
                pdfFormDataMaps.put("resourceAccess", "Yes");
            }
        }
        // 处理 marketingPromotion (同行竞争压力大)
        if (businessChallenges.containsKey("marketingPromotion")) {
            Boolean marketingPromotion = businessChallenges.getBoolean("marketingPromotion");
            if (Boolean.TRUE.equals(marketingPromotion)) {
                pdfFormDataMaps.put("marketingPromotion", "Yes");
            }
        }
        if (businessChallenges.containsKey("other")) {
            Boolean other = businessChallenges.getBoolean("other");
            String otherText = businessChallenges.getString("otherText");
            if (Boolean.TRUE.equals(other)) {
                pdfFormDataMaps.put("businessChallenges_other", "Yes");
                pdfFormDataMaps.put("businessChallenges_otherText", otherText);
            }
        }
        //四 创业后续服务需求
        //4.1 是否需要创业后续服务
        JSONObject followUpService = questionnaireContentAnswer.getJSONObject("followUpService");
//        if (followUpService.containsKey("needed")) {
//            followUpService.get("needed")
//        }
        Boolean needed = followUpService.getBoolean("needed");
        if (Boolean.TRUE.equals(needed)) {
            pdfFormDataMaps.put("needed_0", "Yes");
        } else {
            pdfFormDataMaps.put("needed_1", "Yes");
        }
        //4.2 若需要，您期望的后续服务形式（可多选）
        // 处理 policyConsultation（政策咨询）
        if (followUpService.containsKey("policyConsultation")) {
            boolean policyConsultation = followUpService.getBooleanValue("policyConsultation");
            if (Boolean.TRUE.equals(policyConsultation)) {
                pdfFormDataMaps.put("policyConsultation", "Yes");
            }
        }
        // 处理 projectGuidance (项目指导)
        if (followUpService.containsKey("projectGuidance")) {
            boolean projectGuidance = followUpService.getBooleanValue("projectGuidance");
            if (Boolean.TRUE.equals(projectGuidance)) {
                pdfFormDataMaps.put("projectGuidance", "Yes");
            }
        }
        // 处理 resourceMatching (资源对接)
        if (followUpService.containsKey("resourceMatching")) {
            boolean resourceMatching = followUpService.getBooleanValue("resourceMatching");
            if (Boolean.TRUE.equals(resourceMatching)) {
                pdfFormDataMaps.put("resourceMatching", "Yes");
            }
        }
        // 处理 financingService (融资服务)
        if (followUpService.containsKey("financingService")) {
            boolean financingService = followUpService.getBooleanValue("financingService");
            if (Boolean.TRUE.equals(financingService)) {
                pdfFormDataMaps.put("financingService", "Yes");
            }
        }
        if (followUpService.containsKey("other")) {
            Boolean other = followUpService.getBoolean("other");
            String otherText = followUpService.getString("otherText");
            if (Boolean.TRUE.equals(other)) {
                pdfFormDataMaps.put("followUpService_other", "Yes");
                pdfFormDataMaps.put("followUpService_otherText", otherText);
            }
        }
        //五 其他需求和建议
        //5.1 您还有哪些与创业相关的其他需求，希望我们协助解决？
        String otherNeeds = questionnaireContentAnswer.getString("otherNeeds");
        pdfFormDataMaps.put("otherNeeds", otherNeeds);
        //5.2 对于创业服务，您有什么宝贵的建议？
        String otherSuggestions = questionnaireContentAnswer.getString("otherSuggestions");
        pdfFormDataMaps.put("otherSuggestions", otherSuggestions);
        return pdfFormDataMaps;
    }
    /**
     *
     * 压缩文件
     * @param filePath
     * @param zipOutputStream
     * @param entryName
     * @param lock
     * @throws IOException
     */
    private void compressAndZipPdfSync(byte[] filePath, ZipOutputStream zipOutputStream, String entryName, Object lock) throws IOException {
        synchronized (lock) {
            //转换为输入流
            InputStream inputStream = new ByteArrayInputStream(filePath);
            //压缩文件
            zipOutputStream.putNextEntry(new ZipEntry(entryName));
            try {
                byte[] buffer2 = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer2)) != -1) {
                    zipOutputStream.write(buffer2, 0, bytesRead);
                }
                zipOutputStream.closeEntry();
            } catch (IOException io) {
                //IO异常
            }
        }
    }
    /**
     * 更新进度PDF导出进度
     *
     * @param processedFiles
     * @param totalFiles
     * @param catchKey
     */
    private void updateProgress(AtomicInteger processedFiles, AtomicInteger totalFiles, String catchKey) {
        int progress = (int) (((double) processedFiles.incrementAndGet() / totalFiles.get()) * 100);
        //进度条 key班级名称_请求时间戳 value 当前导出进度 过期时间10分钟
        //TODO redis string类型修改为hash类型 缓存异步线程异常，轮询接口获取
        if (progress < 100) {
            //redisUtil.set(catchKey, progress, 60 << 3 + 60 << 1);
            redisUtil.set(catchKey, progress, 600);
        }
    }

}
