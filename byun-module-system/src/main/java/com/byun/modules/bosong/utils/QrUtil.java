package com.byun.modules.bosong.utils;

import com.alibaba.fastjson.JSON;
import org.simpleframework.xml.core.Complete;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
/**
 *
 * 微信工资平台Token获取
 */
@Complete
public class QrUtil {
    private static String API_KEY  = "wx010a461e54c12866";
    //private static String SECRET = "dfd93d55a08e88bd8b15e43d2987dda4"; 已失效
    private static String SECRET = "b80919b74b70eef8fc0a42273b03a5c1";
    public static String getApiKey() {
        return API_KEY;
    }

    public  void setApiKey(String apiKey) {
        API_KEY = apiKey;
    }

    public static String getSECRET() {
        return SECRET;
    }

    public void setSECRET(String SECRET) {
        QrUtil.SECRET = SECRET;
    }
    public static String postToken() throws Exception {
        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+ API_KEY +"&secret="+SECRET;
        URL url = new URL(requestUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes("");
        out.flush();
        out.close();
        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        if (requestUrl.contains("nlp"))
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "GBK"));
        else
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        String result = "";
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result += getLine;
        }
        in.close();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(result);
        return  jsonObject.getString("access_token");
    }
}
