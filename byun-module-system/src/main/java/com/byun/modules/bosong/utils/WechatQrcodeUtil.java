package com.byun.modules.bosong.utils;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.byun.common.api.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

/**
 * 微信小程序二维码工具类
 * 用于处理微信API返回的响应，区分图片数据和错误信息
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class WechatQrcodeUtil {

    /**
     * 处理微信小程序二维码API的响应
     * 
     * @param response 微信API响应
     * @return 处理后的结果
     */
    public static Result<String> processWechatQrcodeResponse(ResponseEntity<byte[]> response) {
        Result<String> result = new Result<>();
        int statusCode = response.getStatusCodeValue();
        result.setCode(statusCode);
        result.setSuccess(statusCode == 200);
        
        if (statusCode == 200 && response.getBody() != null) {
            // 检查返回的内容类型
            String contentType = response.getHeaders().getFirst("Content-Type");
            log.info("微信API返回Content-Type: {}, 数据大小: {} bytes", contentType, response.getBody().length);
            
            if (contentType != null && contentType.startsWith("image/")) {
                // 成功返回图片，进行base64编码
                String encode = Base64.encode(response.getBody());
                result.setResult(encode);
                log.info("成功生成二维码，base64长度: {}", encode.length());
            } else {
                // 返回的可能是错误信息JSON
                try {
                    String responseStr = new String(response.getBody(), "UTF-8");
                    log.warn("微信API返回非图片数据: {}", responseStr);
                    
                    JSONObject errorJson = JSONObject.parseObject(responseStr);
                    if (errorJson.containsKey("errcode")) {
                        int errcode = errorJson.getIntValue("errcode");
                        String errmsg = errorJson.getString("errmsg");
                        
                        result.setSuccess(false);
                        result.setMessage("微信API错误: " + errmsg + " (错误码: " + errcode + ")");
                        result.setResult(null);
                        
                        log.error("微信API返回错误 - errcode: {}, errmsg: {}", errcode, errmsg);
                        
                        // 记录常见错误的解决建议
                        logErrorSuggestion(errcode, errmsg);
                    } else {
                        // 如果不是标准错误格式，仍然尝试base64编码
                        String encode = Base64.encode(response.getBody());
                        result.setResult(encode);
                        log.warn("返回数据不是标准错误格式，仍进行base64编码");
                    }
                } catch (Exception e) {
                    // 解析失败，仍然尝试base64编码
                    String encode = Base64.encode(response.getBody());
                    result.setResult(encode);
                    log.error("解析微信API响应失败，进行base64编码: {}", e.getMessage());
                }
            }
        } else {
            result.setMessage("请求微信API失败，状态码: " + statusCode);
            result.setResult(null);
            log.error("请求微信API失败，状态码: {}", statusCode);
        }
        
        return result;
    }
    
    /**
     * 记录常见错误的解决建议
     */
    private static void logErrorSuggestion(int errcode, String errmsg) {
        switch (errcode) {
            case 40001:
                log.error("错误建议: access_token无效或已过期，请检查token获取逻辑");
                break;
            case 40013:
                log.error("错误建议: appid无效，请检查小程序配置");
                break;
            case 45009:
                log.error("错误建议: 接口调用超过限制，请控制调用频率");
                break;
            case 47001:
                log.error("错误建议: 数据格式错误，请检查POST参数格式是否为JSON");
                break;
            case 41030:
                log.error("错误建议: page页面路径无效，请检查小程序页面路径是否正确");
                break;
            case 45029:
                log.error("错误建议: 生成码个数总和到达最大个数限制");
                break;
            default:
                log.error("错误建议: 请参考微信官方文档查看错误码 {} 的具体含义", errcode);
                break;
        }
    }
    
    /**
     * 验证base64字符串是否为有效图片
     */
    public static boolean isValidImageBase64(String base64Str) {
        if (base64Str == null || base64Str.isEmpty()) {
            return false;
        }
        
        try {
            byte[] bytes = Base64.decode(base64Str);
            // 检查是否为常见图片格式的文件头
            if (bytes.length < 4) {
                return false;
            }
            
            // PNG文件头: 89 50 4E 47
            if (bytes[0] == (byte) 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
                return true;
            }
            
            // JPEG文件头: FF D8 FF
            if (bytes[0] == (byte) 0xFF && bytes[1] == (byte) 0xD8 && bytes[2] == (byte) 0xFF) {
                return true;
            }
            
            // GIF文件头: 47 49 46 38
            if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x38) {
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("验证base64图片格式失败: {}", e.getMessage());
            return false;
        }
    }
}
