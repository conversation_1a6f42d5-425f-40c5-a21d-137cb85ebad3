package com.byun.modules.chatMessage.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.chatMessage.entity.StaUserFriends;
import com.byun.modules.chatMessage.service.IStaUserFriendsService;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2022-12-1 13:48
 */
@RestController
@RequestMapping("/chat")
@Api(value = "小程序聊天")
public class StaMessageController {
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IStaUserFriendsService userFriendsService;
    /**
     * 获取联系人列表
     * @param userId
     * @return
     */
    @ApiOperation(value = "获取联系人列表", notes = "获取联系人列表")
    @GetMapping("/getFriends")
    public JSONObject getFriends(@RequestParam(name = "userId", required = true) String userId) {
        JSONObject result = new JSONObject();
        //排序
        ArrayList<String> orderByDesc = new ArrayList<>();
        orderByDesc.add("top_flag_update");
        orderByDesc.add("create_time");
        //联系人 单项联系人也返回 前端过滤
        List<StaUserFriends> userFriends = userFriendsService.list(new QueryWrapper<StaUserFriends>()
                .in("user_id", userId)
                .orderByDesc(orderByDesc));
        List<String> friendsIds = new ArrayList<>();
        userFriends.forEach(u -> {
            friendsIds.add(u.getFriendsId());
        });
        //联系人详细信息
        List<SysUser> sysUsersMsg = sysUserService.list(new QueryWrapper<SysUser>().in("id", friendsIds));
        //补充(用户名  头像 公司)
        sysUsersMsg.forEach(s -> {
            userFriends.forEach(u -> {
                if (s.getId().equals(u.getFriendsId())) {
                    u.setPhone(s.getUsername()); //手机号
                    u.setAvatar(s.getAvatar());//头像
                    u.setUserName(s.getRealname());//用户名
                    u.setDeptName(s.getOrgCodeTxt());//公司名称
                }
            });
        });
        result.put("info", userFriends);
        return result;
    }

    /**
     * 该方法用于第一次发起沟通时，获取联系人信息
     *
     * @param receiveId
     * @return
     */
    @ApiOperation(value = "获取联系人信息", notes = "获取联系人信息")
    @GetMapping("/getUserInfoById")
    public Result<SysUser> getUserInfoById(@RequestParam(name = "receiveId", required = true) String receiveId) {
        Result<SysUser> result = new Result<>();
        SysUser sysUser = sysUserService.getOne(new QueryWrapper<SysUser>().eq("username", receiveId));
        if (WxlConvertUtils.isNotEmpty(sysUser)) {
            //补充公司信息(顶级公司)
            SysDepart depart = sysDepartService.getOne(new QueryWrapper<SysDepart>().eq("org_code", sysUser.getOrgCode()));
            if (WxlConvertUtils.isNotEmpty(depart.getParentId())) {
                depart = sysDepartService.getById(depart.getId());
            }
            if  (depart != null){
                sysUser.setOrgCodeTxt(depart.getDepartName());
            }else {
                sysUser.setOrgCodeTxt("");
            }
            result.setSuccess(true);
            result.setResult(sysUser);
        } else {
            result.setSuccess(false);
            result.setMessage("当前联系人不存在!");
        }
        return result;
    }

    /**
     * 该方法用于用户发起共同 获取发送人和接收人的个人信息
     *
     * @param sendId    发送人ID 或 手机号
     * @param receiveId 接收人ID 或 手机号
     * @return
     */
    @GetMapping("/toUserInfoByIds/{sendId}/{receiveId}")
    public Result<List<SysUser>> getUserToInfoByIds(@PathVariable("sendId") String sendId, @PathVariable("receiveId") String receiveId) {
        Result<List<SysUser>> result = new Result();
        List<String> username = new ArrayList<>();
        String condition = null;
        if (sendId.length() == 11 && receiveId.length() == 11) {
            condition = "username";
        } else {
            condition = "id";
        }
        username.add(sendId);
        username.add(receiveId);
        List<SysUser> users = sysUserService.list(new QueryWrapper<SysUser>().in(condition, username));
        SysDepart depart = sysDepartService.getOne(new QueryWrapper<SysDepart>().eq("org_code", users.get(0).getOrgCode()));//发送人
        SysDepart depart1 = sysDepartService.getOne(new QueryWrapper<SysDepart>().eq("org_code", users.get(1).getOrgCode()));//接收人
        if (WxlConvertUtils.isNotEmpty(depart) && WxlConvertUtils.isNotEmpty(depart.getParentId())) {
            depart = sysDepartService.getById(depart.getId());
        }
        if (WxlConvertUtils.isNotEmpty(depart1) && WxlConvertUtils.isNotEmpty(depart1.getParentId())) {
            depart1 = sysDepartService.getById(depart1.getId());
        }
        if (depart != null) {
            users.get(0).setOrgCode(depart.getDepartName());
        }
        if (depart1 != null) {
            users.get(1).setOrgCode(depart1.getDepartName());
        }
        result.setSuccess(true);
        result.setResult(users);
        return result;
    }

    /**
     * 添加双向好友-实际插入两条记录(不允许重复)
     * 例:两条数据
     * 1：张三  李四
     * 2: 李四  张三
     *
     * @param userFriends 用户好友对象
     * @return Result
     */
    @PutMapping("/saveFriends")
    public Result saveFriends(@RequestBody StaUserFriends userFriends) {
        Result result = new Result();
        List<StaUserFriends> list = userFriendsService.list();
        Boolean flag = false;
        if (userFriends.getUserId().equals(userFriends.getFriendsId())) {
            result.setSuccess(false);
            return result;
        }
        if (list.size() > 0) {
            for (StaUserFriends userFriend : list) {
                if (userFriend.getUserId().equals(userFriends.getFriendsId()) && userFriend.getFriendsId().equals(userFriends.getUserId())) {
                    flag = false;
                    break;
                } else if (userFriend.getUserId().equals(userFriends.getUserId()) && userFriend.getFriendsId().equals(userFriends.getFriendsId())) {
                    flag = false;
                    break;
                } else {
                    flag = true;
                }
            }
        } else {
            flag = true;
        }
        if (flag) {
            StaUserFriends userFriends1 = new StaUserFriends();
            userFriends1.setUserId(userFriends.getUserId());
            userFriends1.setFriendsId(userFriends.getFriendsId());
            userFriends1.setCloudId(userFriends.getCloudId());
            userFriends1.setCreateTime(new Date());
            userFriends1.setStatus(CommonConstant.DEL_FLAG_0);
            userFriendsService.save(userFriends1);
            StaUserFriends userFriends2 = new StaUserFriends();
            userFriends2.setUserId(userFriends.getFriendsId());
            userFriends2.setFriendsId(userFriends.getUserId());
            userFriends2.setCloudId(userFriends.getCloudId());
            userFriends2.setCreateTime(new Date());
            userFriends2.setStatus(CommonConstant.DEL_FLAG_0);
            userFriendsService.save(userFriends2);
            result.setResult(true);
        }
        return result;
    }

    /**
     * 用户之间是否存在关联关系
     * @param userAid
     * @param userBId
     * @return
     */
    @GetMapping("getFriendsByIds/{userAid}/{userBId}")
    public Result getFriendsByIds(@PathVariable("userAid") String userAid, @PathVariable("userBId") String userBId) {
        Result result = new Result();
        QueryWrapper<StaUserFriends> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userAid);
        queryWrapper.eq("friends_id", userBId);
        List<StaUserFriends> list = userFriendsService.list(queryWrapper);
        if (list == null || list.size() <= 0) {
            queryWrapper = new QueryWrapper<StaUserFriends>().eq("friends_id", userAid)
                    .eq("user_id", userBId);
            list = userFriendsService.list(queryWrapper);
        }
        if (list == null || list.size() <= 0) {
            result.setSuccess(true);
            result.setResult(list);
        } else if (list.get(0).getStatus() == 1) {
            list.get(0).setStatus(0);
            userFriendsService.updateById(list.get(0));
            result.setSuccess(false);
            result.setResult(list);
        } else {
            result.setSuccess(false);
            result.setResult(list);
        }
        return result;
    }

    /***
     *  联系人置顶
     * @param uId 用户iD
     * @param pid 朋友ID
     * @return Result
     */
    @PutMapping(value = "/changeTopFlagUpdate/{uid}/{pid}")
    public Result changeTopFlagUpdate(@PathVariable("uid") String uId, @PathVariable("pid") String pid) {
        Result result = new Result();
        StaUserFriends userFriends = this.getUserFriends(pid);
        if (!WxlConvertUtils.isNotEmpty(userFriends.getTopFlagUpdate())) {
            userFriends.setTopFlagUpdate(new Date());
        } else {
            userFriends.setTopFlagUpdate(null);
        }
        boolean ok = userFriendsService.updateById(userFriends);
        if (ok) {
            if (WxlConvertUtils.isNotEmpty(userFriends.getTopFlagUpdate())) {
                result.setSuccess(true);
                result.setMessage("置顶成功");
            }else {
                result.setSuccess(true);
                result.setMessage("取消置顶成功！");
            }
        }else {
            result.setSuccess(false);
            result.setMessage("操作失败");
        }
        return result;
    }

    /**
     * @param pid 朋友id
     * @return Result
     */
    @DeleteMapping("/delFriends/{pid}")
    public Result delFriends(@PathVariable("pid") String pid) {
        Result result = new Result();
        StaUserFriends userFriends = this.getUserFriends(pid);
        userFriends.setStatus(CommonConstant.DEL_FLAG_1);
        userFriendsService.updateById(userFriends);
        result.setSuccess(true);
        result.setMessage("删除成功");
        result.setResult(userFriends);
        return result;
    }

    /**
     * 公共方法获取联系人信息  置顶 删除联系人 使用
     *
     * @param pid 联系人id
     * @return UserFriends
     */
    public StaUserFriends getUserFriends(String pid) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String id = user.getId();
        QueryWrapper<StaUserFriends> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", id);
        wrapper.eq("friends_id", pid);
        StaUserFriends userFriends = userFriendsService.getOne(wrapper);
        return userFriends;
    }

    @PutMapping("/editDelFriends/{pid}")
    public Result editDelFriends(@PathVariable String pid) {
        Result result = new Result();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String id = user.getId();
        QueryWrapper<StaUserFriends> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", pid);
        wrapper.eq("friends_id", id);
        wrapper.eq("status", CommonConstant.DEL_FLAG_1);
        try {
            StaUserFriends userFriends = userFriendsService.getOne(wrapper);
            userFriends.setStatus(CommonConstant.DEL_FLAG_0);
            userFriendsService.updateById(userFriends);
            return result.success("ok");
        } catch (NullPointerException e) {
            return result.success("无数据");
        }
    }
}
