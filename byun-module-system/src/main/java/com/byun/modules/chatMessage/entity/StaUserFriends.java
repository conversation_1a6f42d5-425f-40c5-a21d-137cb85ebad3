package com.byun.modules.chatMessage.entity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 好友关系表(双向)
 * @date : 2022-11-24 14:12
 */
@Data
@TableName("sta_user_friends")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_user_friends", description="用户好友表")
public class StaUserFriends {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /*朋友id*/
    private String friendsId;
    /*朋友状态*/
    private Integer status;
    /*id*/
    private String userId;
    /*云开发ID*/
    private String cloudId;
    /*创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /*置顶标记时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "置顶标记时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date topFlagUpdate;
    @TableField(exist = false)
    private String phone;
    @TableField(exist = false)
    private String avatar;
    @TableField(exist = false)
    private String userName;
    @TableField(exist = false)
    private String deptName;
}
