package com.byun.modules.chatMessage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.byun.modules.chatMessage.entity.StaUserFriends;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2022-12-2 10:53
 */
public interface StaUserFriendsMapper extends BaseMapper<StaUserFriends> {
    //获取10天之前的数据不包含今天
    @Select("SELECT id,cloud_id FROM user_friends WHERE TO_DAYS(NOW()) - TO_DAYS(create_time) >= 10")
    List<StaUserFriends> listByTime();
}
