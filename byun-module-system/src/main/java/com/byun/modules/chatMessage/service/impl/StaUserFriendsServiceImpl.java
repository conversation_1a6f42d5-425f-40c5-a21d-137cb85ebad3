package com.byun.modules.chatMessage.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.modules.chatMessage.entity.StaUserFriends;
import com.byun.modules.chatMessage.mapper.StaUserFriendsMapper;
import com.byun.modules.chatMessage.service.IStaUserFriendsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2022-12-2 10:53
 */
@Service
public class StaUserFriendsServiceImpl extends ServiceImpl<StaUserFriendsMapper, StaUserFriends> implements IStaUserFriendsService {
    @Autowired
    private StaUserFriendsMapper userFriendsMapper;
    @Override
    public List<StaUserFriends> listByTime() {

        return userFriendsMapper.listByTime();
    }
}
