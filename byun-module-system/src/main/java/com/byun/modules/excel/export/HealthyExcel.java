package com.byun.modules.excel.export;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.web.servlet.view.document.AbstractXlsxView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 健康证Execl导出
 * @date : 2024-3-11 14:16
 */
public class HealthyExcel extends AbstractXlsxView {
    /**
     * 构建Excel
     * @param model
     * @param workbook
     * @param httpServletRequest
     * @param httpServletResponse
     * @throws Exception
     */
    @Override
    protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        //响应格式
        httpServletResponse.setHeader("Content-Disposition", "attachment; filename=\"xxx.xls\"");
        httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 获取数据
        List<ExcelExportEntity> entityList = (List<ExcelExportEntity>) model.get("entityList");//表头
        List<Map<String, Object>> exportData = (List<Map<String, Object>>) model.get("exportData");//表数据
        Sheet sheet = workbook.createSheet("健康证");//sheet
        //初始行标题
        Row row1 = sheet.createRow(0);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue("健康证");
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, entityList.size() - 1);
        sheet.addMergedRegion(region);
        CellStyle fontCellStyle = workbook.createCellStyle();
        fontCellStyle.setAlignment(HorizontalAlignment.CENTER);
        fontCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cell1.setCellStyle(fontCellStyle);
        row1.setHeight((short) 600);
        //创建表头
        Row headerRow = sheet.createRow(1);
        for (int i = 0; i < entityList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(entityList.get(i).getName());
            cell.setCellStyle(fontCellStyle);//字体居中
            headerRow.setHeight((short) 600);//高度
            //表头宽度-MIN(256*1) MAX(265*256)
            sheet = this.sheetWidthStyle(i, sheet, entityList);
        }
        //字体居中和颜色样式
        CellStyle statusStyle = workbook.createCellStyle();
        statusStyle.setAlignment(HorizontalAlignment.CENTER);
        statusStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font statusFont = workbook.createFont();
        // 创建表数据
        for (int rowIndex = 0; rowIndex < exportData.size(); rowIndex++) {
            Row row = sheet.createRow(rowIndex + 2);
            row.setHeight((short) 1200);//宽度
            Map<String, Object> rowData = exportData.get(rowIndex);
            for (int cellIndex = 0; cellIndex < entityList.size(); cellIndex++) {
                Cell cell = row.createCell(cellIndex);
                cell.setCellStyle(fontCellStyle);//字体居中
                ExcelExportEntity entity = entityList.get(cellIndex);
                // 获取数据
                Object cellValue = rowData.get(entity.getKey());
                if (cellValue != null) {
                    //图片渲染（如果有的化）
                    if ("avatar".equals(entity.getKey())) {
                        this.insertImageToCell(workbook, sheet, (String) cellValue, rowIndex + 2, cellIndex);
                    }
                    if (cellValue instanceof String) {
                        cell.setCellValue((String) cellValue);
                    } else if (cellValue instanceof Integer) {
                        cell.setCellValue((Integer) cellValue);
                    } else if (cellValue instanceof Double) {
                        cell.setCellValue((Double) cellValue);
                    }
                    //设置单元格字体颜色
                    if ("status".equals(entity.getKey())) {
                        int status = (int) cellValue;
                        switch (status) {
                            case 0:
                                cell.setCellValue("待审核");
                                break;
                            case 1:
                                cell.setCellValue("正常");
                                break;
                            case 2://TODO 无效红色其他暂时默认
                                statusFont.setColor(IndexedColors.RED.getIndex());
                                statusStyle.setFont(statusFont);
                                cell.setCellStyle(statusStyle);
                                cell.setCellValue("无效");
                                break;
                            case 3:
                                cell.setCellValue("已过期");
                                break;
                            default:
                                cell.setCellValue("未知");
                                break;
                        }
                    }
                }
            }
        }
    }
    /**
     * 表头宽度
     * @param sheet
     * @return
     */
    private Sheet sheetWidthStyle(int index, Sheet sheet, List<ExcelExportEntity> entityList) {
        switch ((String) entityList.get(index).getKey()) {
            case "createTime":
            case "phone":
            case "endDate":
            case "avatar":
                sheet.setColumnWidth(index, 256 * 15);
                break;
            case "idCard":
                sheet.setColumnWidth(index, 256 * 25);
                break;
        }
        return sheet;
    }

    /**
     * 图片插入
     * @param workbook
     * @param sheet
     * @param imageUrl
     * @param rowIndex
     * @param colIndex
     * @throws IOException
     */
    private void insertImageToCell(Workbook workbook, Sheet sheet, String imageUrl, int rowIndex, int colIndex) throws IOException {
        Drawing drawing = sheet.createDrawingPatriarch();
        ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
        // 设置图片的起始和结束位置
        anchor.setCol1(colIndex);
        anchor.setRow1(rowIndex);
        anchor.setCol2(colIndex + 1);
        anchor.setRow2(rowIndex + 1);
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            byte[] bytes = IOUtils.toByteArray(inputStream);
            int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_JPEG);
            drawing.createPicture(anchor, pictureIdx);
        }
    }
}

