package com.byun.modules.excel.export.PayrollCalculation;

import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 薪资计算事业处导出
 * @date : 2024-6-13 9:55
 */
public class BusinessDepartmentExcel extends AbstractXlsxView {
    @Override
    protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        // 响应格式
        httpServletResponse.setHeader("Content-Disposition", "attachment; filename=\"xxx.xls\"");
        httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 获取数据
        List<ExcelExportEntity> entityList = (List<ExcelExportEntity>) model.get("entityList"); // 表头
        List<Map<String, Object>> exportData = (List<Map<String, Object>>) model.get("exportData"); // 表数据
        Sheet sheet = workbook.createSheet("事业处薪资计算汇总");
        // 初始行标题
        Row row1 = sheet.createRow(0);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue("事业处薪资计算汇总");
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, entityList.size() - 1);
        sheet.addMergedRegion(region);
        CellStyle fontCellStyle = workbook.createCellStyle();
        fontCellStyle.setAlignment(HorizontalAlignment.CENTER);
        fontCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cell1.setCellStyle(fontCellStyle);
        row1.setHeight((short) 600);
        // 创建表头
        Row headerRow = sheet.createRow(1);
        for (int i = 0; i < entityList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(entityList.get(i).getName());
            cell.setCellStyle(fontCellStyle); // 字体居中
            headerRow.setHeight((short) 400); // 高度
            // 表头宽度
            sheet = this.sheetWidthStyle(i, sheet, entityList);
        }
        // 字体居中和颜色样式
        CellStyle statusStyle = workbook.createCellStyle();
        statusStyle.setAlignment(HorizontalAlignment.CENTER);
        statusStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 创建表数据
        int dataSize = exportData.size();
        for (int rowIndex = 0; rowIndex < dataSize; rowIndex++) {
            Row row = sheet.createRow(rowIndex + 2);
            row.setHeight((short) 1200); // 宽度
            Map<String, Object> rowData = exportData.get(rowIndex);
            for (int cellIndex = 0; cellIndex < entityList.size(); cellIndex++) {
                Cell cell = row.createCell(cellIndex);
                cell.setCellStyle(fontCellStyle); // 字体居中
                ExcelExportEntity entity = entityList.get(cellIndex);
                //获取数据
                Object cellValue = rowData.get(entity.getKey());
                if (cellValue != null) {
                    if (cellValue instanceof String) {
                        cell.setCellValue((String) cellValue);
                    } else if (cellValue instanceof Integer) {
                        cell.setCellValue((Integer) cellValue);
                    } else if (cellValue instanceof Double) {
                        cell.setCellValue((Double) cellValue);
                    } else if (cellValue instanceof BigDecimal) {
                        cell.setCellValue(((BigDecimal) cellValue).doubleValue());
                    }
                }
            }
        }
        // 计算合计值
        Map<Object, Double> totals = new HashMap<>();
        for (ExcelExportEntity entity : entityList) {
            totals.put(entity.getKey(), 0.0);
        }
        for (Map<String, Object> rowData : exportData) {
            for (ExcelExportEntity entity : entityList) {
                Object value = rowData.get(entity.getKey());
                if (value instanceof Number) {
                    totals.put(entity.getKey(), totals.get(entity.getKey()) + ((Number) value).doubleValue());
                }
            }
        }
        Font statusFont = workbook.createFont();
        // 创建合计行
        Row totalRow = sheet.createRow(dataSize + 2);
        Cell cell = totalRow.createCell(0);
        cell.setCellValue("合计");
        statusFont.setColor(IndexedColors.RED.getIndex());
        statusStyle.setFont(statusFont);
        totalRow.setHeight((short) 400);
        cell.setCellStyle(statusStyle);
        // 合并第一列的单元格
        sheet.addMergedRegion(new CellRangeAddress(dataSize + 2, dataSize + 2, 0, 1));
        for (int cellIndex = 2; cellIndex < entityList.size(); cellIndex++) {
            Cell totalCell = totalRow.createCell(cellIndex);
            totalCell.setCellStyle(fontCellStyle); // 字体居中
            ExcelExportEntity entity = entityList.get(cellIndex);
            entity.setHeight(20);
            Double total = totals.get(entity.getKey());
            if (total != null) {
                totalCell.setCellValue(total);
            }
        }
        // 写入响应输出流
        workbook.write(httpServletResponse.getOutputStream());
    }

    /**
     * 设置宽度
     * @param index
     * @param sheet
     * @param entityList
     * @return
     */
    private Sheet sheetWidthStyle(int index, Sheet sheet, List<ExcelExportEntity> entityList) {
        switch ((String) entityList.get(index).getKey()) {
            case "businessDivisionName":
            case "storeRegionName":
            case "storeNo":
            case "companyName":
            case "workNameFull":
                sheet.setColumnWidth(index, 256 * 15);
                break;
        }
        return sheet;
    }
    /**
     * 图片插入
     * @param workbook
     * @param sheet
     * @param imageUrl
     * @param rowIndex
     * @param colIndex
     * @throws IOException
     */
    private void insertImageToCell(Workbook workbook, Sheet sheet, String imageUrl, int rowIndex, int colIndex) throws IOException {
        Drawing drawing = sheet.createDrawingPatriarch();
        ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
        // 设置图片的起始和结束位置
        anchor.setCol1(colIndex);
        anchor.setRow1(rowIndex);
        anchor.setCol2(colIndex + 1);
        anchor.setRow2(rowIndex + 1);
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            byte[] bytes = IOUtils.toByteArray(inputStream);
            int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_JPEG);
            drawing.createPicture(anchor, pictureIdx);
        }
    }
}
