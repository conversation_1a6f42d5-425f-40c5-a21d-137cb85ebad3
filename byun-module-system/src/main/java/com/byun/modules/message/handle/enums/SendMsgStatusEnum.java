package com.byun.modules.message.handle.enums;

/**
 * @description: 推送状态枚举
 * <AUTHOR>
 * @date 2021/11/4 16:20
 * @version 1.0
 */
public enum SendMsgStatusEnum {

//推送状态 0未推送 1推送成功 2推送失败
	WAIT("0"), SUCCESS("1"), FAIL("2");

	private String code;

	private SendMsgStatusEnum(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public void setStatusCode(String code) {
		this.code = code;
	}

}