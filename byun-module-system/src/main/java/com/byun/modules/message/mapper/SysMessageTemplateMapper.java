package com.byun.modules.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import com.byun.modules.message.entity.SysMessageTemplate;

import java.util.List;

/**
 * @description: 消息模板
 * <AUTHOR>
 * @date 2021/11/4 16:33
 * @version 1.0
 */
public interface SysMessageTemplateMapper extends BaseMapper<SysMessageTemplate> {
    @Select("SELECT * FROM SYS_SMS_TEMPLATE WHERE TEMPLATE_CODE = #{code}")
    List<SysMessageTemplate> selectByCode(String code);
}
