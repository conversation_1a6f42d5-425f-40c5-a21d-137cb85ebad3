package com.byun.modules.message.service.impl;

import com.byun.common.system.base.service.impl.ByunServiceImpl;
import com.byun.modules.message.entity.SysMessageTemplate;
import com.byun.modules.message.mapper.SysMessageTemplateMapper;
import com.byun.modules.message.service.ISysMessageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @description: 消息模板
 * <AUTHOR>
 * @date 2021/11/4 16:34
 * @version 1.0
 */
@Service
public class SysMessageTemplateServiceImpl extends ByunServiceImpl<SysMessageTemplateMapper, SysMessageTemplate> implements ISysMessageTemplateService {

    @Autowired
    private SysMessageTemplateMapper sysMessageTemplateMapper;


    @Override
    public List<SysMessageTemplate> selectByCode(String code) {
        return sysMessageTemplateMapper.selectByCode(code);
    }
}
