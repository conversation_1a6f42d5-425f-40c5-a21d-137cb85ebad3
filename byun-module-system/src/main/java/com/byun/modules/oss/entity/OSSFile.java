package com.byun.modules.oss.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.byun.common.system.base.entity.ByunTenantEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @description: OSS数据对象
 * <AUTHOR>
 * @date 2021/11/4 21:20
 * @version 1.0
 */
@Data
@TableName("oss_file")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OSSFile extends ByunTenantEntity {

	private static final long serialVersionUID = 1L;

	@Excel(name = "文件名称")
	private String fileName;

	@Excel(name = "文件地址")
	private String url;

}
