package com.byun.modules.oss.service;

import java.io.IOException;

import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.oss.entity.OSSFile;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description: OSS云存储示例 DEMO
 * <AUTHOR>
 * @date 2021/11/4 21:22
 * @version 1.0
 */
public interface IOSSFileService extends IService<OSSFile> {

	void upload(MultipartFile multipartFile) throws IOException;

	boolean delete(OSSFile ossFile);

}
