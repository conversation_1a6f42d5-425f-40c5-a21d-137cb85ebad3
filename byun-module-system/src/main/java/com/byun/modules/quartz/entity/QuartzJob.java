package com.byun.modules.quartz.entity;

import java.io.Serializable;

import com.byun.common.aspect.annotation.Dict;
import com.byun.common.system.base.entity.ByunTenantEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @description: 定时任务在线管理
 * <AUTHOR>
 * @date 2021/11/4 21:23
 * @version 1.0
 */
@Data
@TableName("sys_quartz_job")
public class QuartzJob extends ByunTenantEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**删除状态*/
	private java.lang.Integer delFlag;
	/**任务类名*/
	@Excel(name="任务类名",width=40)
	private java.lang.String jobClassName;
	/**cron表达式*/
	@Excel(name="cron表达式",width=30)
	private java.lang.String cronExpression;
	/**参数*/
	@Excel(name="参数",width=15)
	private java.lang.String parameter;
	/**描述*/
	@Excel(name="描述",width=40)
	private java.lang.String description;
	/**状态 0正常 -1停止*/
	@Excel(name="状态",width=15,dicCode="quartz_status")
	@Dict(dicCode = "quartz_status")
	private java.lang.Integer status;

}
