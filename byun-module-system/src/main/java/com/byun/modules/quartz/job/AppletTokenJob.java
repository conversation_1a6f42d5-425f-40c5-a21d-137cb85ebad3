package com.byun.modules.quartz.job;

import com.byun.common.util.DateUtils;
import com.byun.modules.system.service.ISysAppletService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 小程序定时获取access_token
 * @date 2021/11/28 21:59
 */
@Slf4j
@Component("appletTokenJob")
public class AppletTokenJob implements Job {
    @Resource
    private ISysAppletService sysAppletService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        log.info(String.format(" 普通定时任务 更新小程序Token 开始!  时间:" + DateUtils.getTimestamp()));
//        log.info("#----------------------------xmallAccessTokenTask is start---"+ DateUtil.getNowStringDate());
//        String localIp = CommonUtil.getLinuxLocalIp();
//        log.info("localIp:" + localIp);
//        if (StringUtils.isNotBlank(localIp) && localIp.trim().replace(" ", "").equals("***********")) {//判断定时任务 只在一个服务器上执行
            //重新获取下ACCESS_TOKEN，并存在服务器上
            String ret = sysAppletService.resetAccessToken();
            log.info("#普通定时任务 更新小程序Token 成功! ---"+ret);
//        }
//        log.info("#----------------------------xmallAccessTokenTask is end---"+DateUtil.getNowStringDate());
    }
}
