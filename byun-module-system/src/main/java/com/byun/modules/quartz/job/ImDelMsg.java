package com.byun.modules.quartz.job;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.byun.config.sign.util.HttpClientUtils;
import com.byun.modules.chatMessage.entity.StaUserFriends;
import com.byun.modules.chatMessage.service.IStaUserFriendsService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.*;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 每日凌晨3点执行访问微信云数据库清理 10天前 聊天记录 (聊天记录存储10天)
 * @date : 2022-12-22 16:43
 */
//@Slf4j
//@Component("ImDelMsg")
//public class ImDelMsg implements Job {
//    @Value("${byun.wechat.applet.staAppid}")
//    private String staAppid;
//    @Value("${byun.wechat.applet.staSecret}")
//    private String staSecret;
//    @Value("${byun.wechat.applet.env}")
//    private String env;
//    @Autowired
//    private IStaUserFriendsService userFriendsService;
//    @Override
//    public void execute(JobExecutionContext jobExecutionContext)  {
//        //获取10天之前的数据 计算周期包不含今天
//        List<StaUserFriends> userFriendsList = userFriendsService.listByTime();
//        if (userFriendsList.size() == 0 || userFriendsList == null){
//            return ;
//        }
//        HashSet<String>  cloudIds = new HashSet(); //微信云 mongodb-cloud_id
//        ArrayList<String> ids = new ArrayList<>(); //mysql-id
//        userFriendsList.forEach(u -> { cloudIds.add(u.getCloudId());ids.add(u.getId());});
//        userFriendsService.removeByIds(ids);
//        try {
//            /**
//             * GET
//             * 获取登录接口凭证
//             * url https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid= &secret =
//             * staAppid staSecret
//             */
//            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+staAppid+"&secret="+staSecret;
//            String res = HttpClientUtils.doGet(url);
//            JSONObject jsonObject =  JSON.parseObject(res);
//            String token = jsonObject.getString("access_token");//小程序登录凭证-token
//            for (String s : cloudIds) {
//                /**
//                 * POST
//                 * 根据cloud_id 对应微信云数据库_id对应删除记录
//                 * url https://api.weixin.qq.com/tcb/databasedelete?access_token
//                 * env  云环境ID
//                 * query  操作语句
//                 */
//                    String delURl = "https://api.weixin.qq.com/tcb/databasedelete?access_token="+token;
//                    JSONObject json = new JSONObject();
//                    json.put("env",env);
//                    String queryDel = "db.collection('chat_record').doc('"+s+"').remove()";
//                    json.put("query",queryDel);
//                    String conditions = json.toString();
//                    HttpClientUtils.doPostJson(delURl, conditions);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//}
