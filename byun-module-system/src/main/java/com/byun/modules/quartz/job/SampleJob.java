package com.byun.modules.quartz.job;

import com.byun.common.util.DateUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: TODO 示例不带参定时任务
 * <AUTHOR>
 * @date 2021/11/4 21:48
 * @version 1.0
 */
@Slf4j
public class SampleJob implements Job {

	@Override
	public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

		log.info(String.format(" Byun-Boot 普通定时任务 SampleJob !  时间:" + DateUtils.getTimestamp()));
	}
}
