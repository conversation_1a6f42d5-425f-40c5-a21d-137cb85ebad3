package com.byun.modules.quartz.job;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.DateUtils;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaSendEmail;
import com.byun.modules.staffing.service.IEmailService;
import com.byun.modules.staffing.service.IStaSendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 定时给符合条件的用户发送入职邮件
 * @date : 2023-1-5 15:13
 */
@Slf4j
@Component("SendEmail")
public class SendEmail implements Job {
    @Autowired
    private IStaSendEmailService staSendEmailService;
    @Autowired
    private IEmailService emailService;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        QueryWrapper<StaSendEmail> wrapper = new QueryWrapper<>();
        wrapper.eq("send_status", CommonConstant.SEND_EMAIL_NO);
        List<StaSendEmail> list = staSendEmailService.list(wrapper);
        if (WxlConvertUtils.isNotEmpty(list)){
            for (StaSendEmail staSendEmail : list) {
                    emailService.sendHtmlMail(staSendEmail.getEmail(),"入职通知",
                            "<div>\n" +
                                    "    <includetail>\n" +
                                    "        <div align=\"center\">\n" +
                                    "            <div class=\"open_email\" style=\"margin-left: 8px; margin-top: 8px; margin-bottom: 8px; margin-right: 8px;\">\n" +
                                    "                <div>\n" +
                                    "                    <br>\n" +
                                    "                    <span class=\"genEmailContent\">\n" +
                                    "                        <div id=\"cTMail-Wrap\"\n" +
                                    "                             style=\"word-break: break-all;box-sizing:border-box;text-align:center;min-width:320px; max-width:660px; border:1px solid #f6f6f6; background-color:#f7f8fa; margin:auto; padding:20px 0 30px; font-family:'helvetica neue',PingFangSC-Light,arial,'hiragino sans gb','microsoft yahei ui','microsoft yahei',simsun,sans-serif\">\n" +
                                    "                            <div class=\"main-content\" style=\"\">\n" +
                                    "                                <table style=\"width:100%;font-weight:300;margin-bottom:10px;border-collapse:collapse\">\n" +
                                    "                                    <tbody>\n" +
                                    "                                    <tr style=\"font-weight:300\">\n" +
                                    "                                        <td style=\"width:3%;max-width:30px;\"></td>\n" +
                                    "                                        <td style=\"max-width:600px;\">\n" +
                                    "                                            <p style=\"height:2px;background-color: #00a4ff;border: 0;font-size:0;padding:0;width:100%;margin-top:20px;\"></p>\n" +
                                    "                                            <div id=\"cTMail-inner\" style=\"background-color:#fff; padding:23px 0 20px;box-shadow: 0px 1px 1px 0px rgba(122, 55, 55, 0.2);text-align:left;\">\n" +
                                    "                                                <table style=\"width:100%;font-weight:300;margin-bottom:10px;border-collapse:collapse;text-align:left;\">\n" +
                                    "                                                    <tbody>\n" +
                                    "                                                    <tr style=\"font-weight:300\">\n" +
                                    "                                                        <td style=\"width:3.2%;max-width:30px;\"></td>\n" +
                                    "                                                        <td style=\"max-width:480px;text-align:left;\">\n" +
                                    "                                                            <h1 id=\"cTMail-title\" style=\"font-size: 20px; line-height: 36px; margin: 0px 0px 22px;\">\n" +
                                    "                                                                【鸣翠柳科技】欢迎使用鸣翠柳\n" +
                                    "                                                            </h1>\n" +
                                    "\n" +
                                    "                                                            <p id=\"cTMail-userName\" style=\"font-size:14px;color:#333; line-height:24px; margin:0;\">\n"
                                    +staSendEmail.getEnrollName()+" ，您好"+
                                    "                                                            </p>\n" +
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n" +
                                    "                                                            <p class=\"cTMail-content\" style=\"line-height: 24px; margin: 6px 0px 0px; overflow-wrap: break-word; word-break: break-all;\">\n" +
                                    "                                                                <span style=\"color: blue; font-size: 14px;\">\n"
                                    +" 你申请的任务：【"+staSendEmail.getWorkName()+"】通过审核"+
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</br>\n" +
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t报道地址：" +staSendEmail.getAddressName()+
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</br>\n" +
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t报道联系人：" +staSendEmail.getReportLiaison()+
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</br>\n" +
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t报道联系人电话：" +staSendEmail.getReportLiaisonTp()+
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</br>\n" +
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style=\"color: red;\">\n" +
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t备注：请您在【"+ DateUtils.formatTime(staSendEmail.getWorkStartDate())+"】前到任务地点报道"+
                                    "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\n" +
                                    "                                                                </span>\n" +
                                    "                                                            </p>\n" +
                                    "                                                        </td>\n" +
                                    "                                                        <td style=\"width:3.2%;max-width:30px;\"></td>\n" +
                                    "                                                    </tr>\n" +
                                    "                                                    </tbody>\n" +
                                    "                                                </table>\n" +
                                    "                                            </div>\n" +
                                    "\n" +
                                    "                                            <div id=\"cTMail-copy\" style=\"text-align:center; font-size:12px; line-height:18px; color:#999\">\n" +
                                    "                                                <table style=\"width:100%;font-weight:300;margin-bottom:10px;border-collapse:collapse\">\n" +
                                    "                                                    <tbody>\n" +
                                    "                                                    <tr style=\"font-weight:300\">\n" +
                                    "                                                        <td style=\"width:3.2%;max-width:30px;\"></td>\n" +
                                    "                                                        <td style=\"max-width:540px;\">\n" +
                                    "                                                            <p style=\"text-align:center; margin:20px auto 14px auto;font-size:12px;color:#999;\">\n" +
                                    "                                                                此为系统邮件,请勿回复。关注公众号了解更多任务信息                               \n" +
                                    "                                                            </p>\n" +
                                    "                                                            <p id=\"cTMail-rights\" style=\"max-width: 100%; margin:auto;font-size:12px;color:#999;text-align:center;line-height:22px;\">\n" +
                                    "                                                                <img border=\"0\" src=\"https://wxlmcl-test.oss-cn-beijing.aliyuncs.com/emailLog/qrcode_for_gh_2ecab86ec58f_258.jpg\"\n" +
                                    "                                                                     style=\"width:64px; height:64px; margin:0 auto;\">\n" +
                                    "             <!--                                                   <br>\n" +
                                    "                                                                <br> -->\n" +
                                    "                                                                <!-- <img src=\"https://imgcache.qq.com/open_proj/proj_qcloud_v2/gateway/mail/cr.svg\" style=\"margin-top: 10px;\"> -->\n" +
                                    "                                                            </p>\n" +
                                    "                                                        </td>\n" +
                                    "                                                        <td style=\"width:3.2%;max-width:30px;\"></td>\n" +
                                    "                                                    </tr>\n" +
                                    "                                                    </tbody>\n" +
                                    "                                                </table>\n" +
                                    "                                            </div>\n" +
                                    "                                        </td>\n" +
                                    "                                        <td style=\"width:3%;max-width:30px;\"></td>\n" +
                                    "                                    </tr>\n" +
                                    "                                    </tbody>\n" +
                                    "                                </table>\n" +
                                    "                            </div>\n" +
                                    "                        </div>\n" +
                                    "                    </span\n" +
                                    "                   <br>\n" +
                                    "                </div>\n" +
                                    "            </div>\n" +
                                    "        </div>\n" +
                                    "    </includetail>\n" +
                                    "</div>\n");
                    staSendEmail.setSendStatus(CommonConstant.SEND_EMAIL_OK);
            }
            staSendEmailService.updateBatchById(list);
        }
    }
}
