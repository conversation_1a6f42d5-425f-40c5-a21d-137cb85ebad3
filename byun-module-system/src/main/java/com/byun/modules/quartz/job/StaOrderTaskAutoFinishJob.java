package com.byun.modules.quartz.job;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.service.IStaOrderService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

/**
 * 此定时任务自动审批
 * 用户任务单提交任务(超过三天未被人工审批的任务单)
 * 每日凌晨03：25 自动审批通过 默认评分5分满分
 */
@Slf4j
@Component("StaOrderTaskAutoFinishJob")
public class StaOrderTaskAutoFinishJob  implements Job {
    private IStaOrderService staOrderService;
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        //staOrderService.list();
    }
}
