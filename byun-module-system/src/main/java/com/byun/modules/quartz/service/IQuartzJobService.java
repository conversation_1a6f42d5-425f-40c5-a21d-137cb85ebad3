package com.byun.modules.quartz.service;

import java.util.List;

import com.byun.common.system.base.service.IByunService;
import com.byun.modules.quartz.entity.QuartzJob;
import org.quartz.SchedulerException;

/**
 * @description: 定时任务在线管理
 * <AUTHOR>
 * @date 2021/11/4 23:25
 * @version 1.0
 */
public interface IQuartzJobService extends IByunService<QuartzJob> {

	List<QuartzJob> findByJobClassName(String jobClassName);

	boolean saveAndScheduleJob(QuartzJob quartzJob);

	boolean editAndScheduleJob(QuartzJob quartzJob) throws SchedulerException;

	boolean deleteAndStopJob(QuartzJob quartzJob);

	boolean resumeJob(QuartzJob quartzJob);

	/**
	 * 执行定时任务
	 * @param quartzJob
	 */
	void execute(QuartzJob quartzJob) throws Exception;

	/**
	 * 暂停任务
	 * @param quartzJob
	 * @throws SchedulerException
	 */
	void pause(QuartzJob quartzJob);
}
