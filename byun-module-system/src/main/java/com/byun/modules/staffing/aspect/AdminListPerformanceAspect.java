package com.byun.modules.staffing.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 管理员任务列表性能监控切面
 * 用于监控 adminList 接口的性能
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Aspect
@Component
public class AdminListPerformanceAspect {

    /**
     * 定义切点：监控 adminList 方法
     */
    @Pointcut("execution(* com.byun.modules.staffing.controller.StaWorkController.queryPageAdminList(..))")
    public void adminListPointcut() {}

    /**
     * 环绕通知：记录方法执行时间
     */
    @Around("adminListPointcut()")
    public Object monitorPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        try {
            // 记录请求参数
            log.info("开始执行 {} 方法，参数: {}", methodName, formatArgs(args));
            
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录执行时间
            log.info("方法 {} 执行完成，耗时: {} ms", methodName, executionTime);
            
            // 如果执行时间超过阈值，记录警告
            if (executionTime > 3000) { // 3秒阈值
                log.warn("方法 {} 执行时间过长: {} ms，建议进一步优化", methodName, executionTime);
            }
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            log.error("方法 {} 执行异常，耗时: {} ms，异常信息: {}", methodName, executionTime, e.getMessage());
            throw e;
        }
    }

    /**
     * 格式化参数信息
     */
    private String formatArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "无参数";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < args.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            
            Object arg = args[i];
            if (arg == null) {
                sb.append("null");
            } else if (arg instanceof String) {
                sb.append("\"").append(arg).append("\"");
            } else {
                sb.append(arg.toString());
            }
        }
        
        return sb.toString();
    }
}
