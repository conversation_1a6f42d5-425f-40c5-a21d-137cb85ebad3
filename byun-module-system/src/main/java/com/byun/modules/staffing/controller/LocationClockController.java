package com.byun.modules.staffing.controller;

import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.*;
import com.byun.modules.bosong.entity.BoSong;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.service.*;
import com.byun.modules.staffing.utils.oss.QRCodeUtils;
import com.byun.modules.staffing.vo.StaLocationClockPage;
import com.byun.modules.staffing.vo.importXls.entity.LocationClock;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.entity.SysUserDepart;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2022-10-28 10:57
 */
@Api(tags = "考勤单")
@RestController
@RequestMapping("/staffing/locationClock")
@Slf4j
public class LocationClockController {
    @Autowired
    private IStaLocationClockService staLocationClockService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private IStaScheduleService staScheduleService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IStaWorkAgentRelService staWorkAgentRelService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysUserService sysUserService;

    @AutoLog(value = "签到信息-平台分页列表查询")
    @ApiOperation(value = "签到信息-平台分页列表查询", notes = "签到信息-平台分页列表查询")
    @GetMapping(value = "/lclist")
    public Result<?> queryPageLocationClockList(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        String startTime = req.getParameter("startDate");
        String endTime = req.getParameter("entDate");
        StaLocationClock staLocationClock = new StaLocationClock();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return Result.error("未获取登录部门");
        }
        //获取当前部门权限
        String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
        if (WxlConvertUtils.isNotEmpty(departId)) {
            //用户权限
            Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
            boolean positionlist = userPermissionSet.contains("staffing:clock:list");//查询所有考勤列表权限
            QueryWrapper<StaLocationClock> queryWrapper = QueryGenerator.initQueryWrapper(staLocationClock, req.getParameterMap());
            if (positionlist) {
                //用户选择日期时间段
                if (!WxlConvertUtils.isEmpty(startTime) && !WxlConvertUtils.isEmpty(endTime)) {
                    queryWrapper.ge("create_time", startTime);
                    queryWrapper.le("create_time", endTime);
                }
                queryWrapper.orderByDesc(Arrays.asList("create_time", "create_by"));
                //queryWrapper.like("enroll_phone",enrollPhone);
                queryWrapper.eq("company_Id", departId);
                Page<StaLocationClock> page = new Page<StaLocationClock>(pageNo, pageSize);
                IPage<StaLocationClock> pageList = staLocationClockService.page(page, queryWrapper);
                List<StaLocationClock> records = pageList.getRecords();
                if (records.size() == 0) {
                    return Result.error("暂无数据");
                }
                List<String> oids = new ArrayList<>();
                records.forEach(rs -> {
                    oids.add(rs.getStaOrderId());
                });
                /**
                 * TODO 排班表 order_id -> 订单表 sys_user_id -> 签到表
                 * 获取所有排班 order_id 关联 订单表 关联 签到表
                 * 排班  签到
                 */
                List<StaOrder> order = staOrderService.list(new QueryWrapper<StaOrder>().in("id", oids));
                records.forEach(r -> {
                    for (StaOrder st : order) {
                        //符合条件数据组装(有签到)
                        if (r.getStaOrderId().equals(st.getId())) {
                            r.setStoreNo(st.getStoreNo()); //店编
                            r.setCompanyName(st.getCompanyName());//门店名称
                            r.setNameFull(st.getWorkNameFull()); //任务名称
                            r.setEnrollName(st.getEnrollName());//用户名
                            r.setEnrollPhone(st.getEnrollPhone()); //用户手机号
                        }
                    }
                });
                pageList.setRecords(records);
                return Result.OK(pageList);
            } else {
                return Result.error("暂无权限");
            }
        } else {
            return Result.error("未选择登录机构");
        }
    }

    /**
     * 考勤导出
     *
     * @param request
     * @param staLocationClock
     * @return
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaLocationClock staLocationClock) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<StaLocationClock> queryWrapper = QueryGenerator.initQueryWrapper(staLocationClock, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //搜索条件
        //查询所有负责部门
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(sysUser.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "未获取登录部门");
            return mv;
        }
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        //用户权限
        Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(sysUser.getUsername(), departId);
        boolean positionlist = userPermissionSet.contains("staffing:clock:list");//查询所有考勤列表权限
        if (positionlist) {
            QueryWrapper<StaLocationClock> wrapper = new QueryWrapper<>();
            //当前主部门数据
            wrapper.eq("company_Id", departId);
            //Step.2 获取导出数据
            List<StaLocationClock> queryList = staLocationClockService.list(wrapper);
            // 过滤选中数据
            String selections = request.getParameter("selections");
            List<StaLocationClock> staWorkList = new ArrayList<StaLocationClock>();
            if (WxlConvertUtils.isEmpty(selections)) {
                staWorkList = queryList;
            } else {
                List<String> selectionList = Arrays.asList(selections.split(","));
                staWorkList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
            }
            List<String> oids = new ArrayList<>();//订单Ids
            queryList.forEach(q -> {
                oids.add(q.getStaOrderId());
            });
            //身份证  电话 店编
            List<StaOrder> orderList = staOrderService.list(new QueryWrapper<StaOrder>().in("id", oids));
            // Step.3 组装pageList
            List<StaLocationClockPage> pageList = new ArrayList<StaLocationClockPage>();
            for (StaLocationClock main : staWorkList) {
                StaLocationClockPage sto = new StaLocationClockPage();
                for (StaOrder o : orderList) {
                    if (main.getStaOrderId().equals(o.getId())) {
                        sto.setTime(main.getTime());
                        sto.setStoreNo(o.getStoreNo());
                        sto.setIdCard(o.getEnrollIdCard());
                        sto.setPhone(o.getEnrollPhone());
                    } else {
                        sto.setTime(main.getTime());
                    }
                }
                pageList.add(sto);
            }
            // Step.4 AutoPoi 导出Excel
            pageList.sort((t1, t2) -> t2.getTime().compareTo(t1.getTime())); //排序 根据日期 降序
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "考勤列表列表");
            mv.addObject(NormalExcelConstants.CLASS, StaLocationClockPage.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("考勤列表数据", "导出人:" + sysUser.getRealname(), "考勤列表"));
            mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
            return mv;
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "暂无权限");
            return mv;
        }
    }
    /**
     * 导入考勤
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/importExcel")
    @Transactional
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            params.setNeedSave(true);
            int num = 2;
            try (InputStream inputStream = file.getInputStream()) {
                // 读取Excel
                List<LocationClock> locationClockList = ExcelImportUtil.importExcel(inputStream, LocationClock.class, params);
                List<StaLocationClock> staLocationClocks = new ArrayList<>();
                Set<String> shiftCodeSet = locationClockList.stream().map(LocationClock::getShiftCode).collect(Collectors.toSet());
                Set<String> storeNoSet = locationClockList.stream().map(LocationClock::getStoreNo).collect(Collectors.toSet());
                Set<String> idCardSet = locationClockList.stream().map(LocationClock::getIdCard).collect(Collectors.toSet());
                // 获取所有排班、门店、任务单和用户信息
                List<StaSchedule> staSchedules = staScheduleService.list(new QueryWrapper<StaSchedule>()
                        .lambda()
                        .in(StaSchedule::getCode, shiftCodeSet)
                        .in(StaSchedule::getUserIdCard, idCardSet)
                        .eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0));
                List<SysDepart> sysDeparts = sysDepartService.list(new LambdaQueryWrapper<SysDepart>()
                        .in(SysDepart::getStoreNo, storeNoSet)
                        .eq(SysDepart::getOrgType, 4)
                        .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));
                List<StaOrder> staOrders = staOrderService.list(new QueryWrapper<StaOrder>()
                        .lambda()
                        .in(StaOrder::getEnrollIdCard, idCardSet)
                        .notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_1, CommonConstant.ORDER_STATUS_2))
                        .eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0));
                List<SysUser> sysUsers = sysUserService.list(new QueryWrapper<SysUser>()
                        .lambda()
                        .in(SysUser::getIdCard, idCardSet));
                for (LocationClock locationClock : locationClockList) {
                    String businessDivisionName = locationClock.getBusinessDivisionName();
                    String regionName = locationClock.getRegionName();
                    String storeNo = locationClock.getStoreNo();
                    String companyName = locationClock.getCompanyName();
                    String shiftCode = locationClock.getShiftCode();
                    String idCard = locationClock.getIdCard();
                    String scheduleDay = locationClock.getScheduleDay();
                    String userName = locationClock.getUserName();
                    String[] clockInTimes = {locationClock.getClockInTime1(), locationClock.getClockInTime2(), locationClock.getClockInTime3(), locationClock.getClockInTime4()};
                    List<SysDepart> sysDepartList = sysDeparts.stream().filter(s -> s.getStoreNo().equals(storeNo)).collect(Collectors.toList());
                    if (sysDepartList.isEmpty()) {
                        errorLines++;
                        errorMessage.add("第"+num+"行出现错误"+"门店不存在：" + "店编" + storeNo + "店别" + companyName);
                        continue;
                    }
                    List<StaOrder> staOrderList = staOrders.stream()
                            .filter(s -> s.getEnrollIdCard().equals(idCard) && s.getCompanyId().equals(sysDepartList.get(0).getId()))
                            .collect(Collectors.toList());
                    if (staOrderList.isEmpty()) {
                        errorLines++;
                        errorMessage.add("第"+num+"行出现错误"+"任务单不存在：姓名-" + userName);
                        continue;
                    }
                    List<StaSchedule> staScheduleList = staSchedules.stream()
                            .filter(s -> {
                                try {
                                    return s.getCode().equals(shiftCode) && s.getUserIdCard().equals(idCard) && s.getScheduleDay().equals(dateFormat.parse(scheduleDay));
                                } catch (ParseException e) {
                                    throw new RuntimeException(e);
                                }
                            })
                            .collect(Collectors.toList());
                    if (staScheduleList.isEmpty()) {
                        errorLines++;
                        errorMessage.add("第"+num+"行出现错误"+"排班不存在：姓名-" + userName + "，班别代码-" + shiftCode + "，排班日期-" + scheduleDay);
                        continue;
                    }
                    List<SysUser> sysUserList = sysUsers.stream().filter(s -> s.getIdCard().equals(idCard)).collect(Collectors.toList());
                    if (sysUserList.isEmpty()) {
                        errorLines++;
                        errorMessage.add("第"+num+"行出现错误"+"用户不存在：姓名-" + userName);
                        continue;
                    }
                    // 处理每一轮打卡记录
                    for (int i = 0; i < clockInTimes.length; i++) {
                        if (WxlConvertUtils.isNotEmpty(clockInTimes[i])) {
                            StaLocationClock staLocationClock = createStaLocationClock(businessDivisionName, regionName, companyName, sysDepartList, staOrderList, sysUserList, staScheduleList, dateFormat, dateTimeFormat, timeFormat, scheduleDay, clockInTimes[i], i);
                            if (staLocationClock != null) {
                                staLocationClocks.add(staLocationClock);
                            }
                        }
                    }
                    successLines++;
                }
                //导入签到前删除重复签到数据
                Set<String> staOrderList = staLocationClocks.stream().map(StaLocationClock::getStaOrderId).collect(Collectors.toSet());
                Set<Date> scheduleDayList = staLocationClocks.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toSet());
                Set<String> formatScheduleDayList = new TreeSet<>();
                if (WxlConvertUtils.isNotEmpty(scheduleDayList) && WxlConvertUtils.isNotEmpty(scheduleDayList)) {
                    for (Date date : scheduleDayList) {
                        formatScheduleDayList.add(dateTimeFormat.format(date));
                    }
                    staLocationClockService.remove(new LambdaQueryWrapper<StaLocationClock>()
                            .in(StaLocationClock::getStaOrderId, staOrderList)
                            .in(StaLocationClock::getTimeExpect,formatScheduleDayList));
                }
                staLocationClockService.saveBatch(staLocationClocks);
            } catch (Exception e) {
                errorMessage.add("发生异常：" + e.getMessage());
                log.error(e.getMessage(), e);
            }
        }
        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
    }

    /**
     * 考勤导入模板
     *
     * @return
     */
    @RequestMapping("/exportXlsModel")
    public ModelAndView exportXlsModel() {
        List<LocationClock> locationClockList = new ArrayList<>();
        //locationClockList
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "考勤模板");
        mv.addObject(NormalExcelConstants.CLASS, LocationClock.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("考勤模板(统一格式文本日期格式yyyy-MM-dd时间格式:HH:mm)", "考勤"));
        mv.addObject(NormalExcelConstants.DATA_LIST, locationClockList);
        return mv;
    }

    private StaLocationClock createStaLocationClock(String businessDivisionName, String regionName, String companyName, List<SysDepart> sysDepartList, List<StaOrder> staOrderList, List<SysUser> sysUserList, List<StaSchedule> staScheduleList, SimpleDateFormat dateFormat, SimpleDateFormat dateTimeFormat, SimpleDateFormat timeFormat, String scheduleDay, String clockInTime, int round) {
        try {
            StaLocationClock staLocationClock = new StaLocationClock();
            staLocationClock.setBusinessDivisionName(businessDivisionName);
            staLocationClock.setRegionName(regionName);
            staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
            staLocationClock.setStaWorkId(staOrderList.get(0).getStaWorkId());
            staLocationClock.setStaOrderId(staOrderList.get(0).getId());
            staLocationClock.setSysUserId(sysUserList.get(0).getId());
            staLocationClock.setStaScheduleId(staScheduleList.get(round / 2).getId());
            staLocationClock.setScheduleDay(dateFormat.parse(scheduleDay));
            staLocationClock.setCompanyName(companyName);
            staLocationClock.setCompanyId(sysDepartList.get(0).getId());
            staLocationClock.setOrgCode(staOrderList.get(0).getSysOrgCode());
            // 获取排班日期和打卡时间
            Date scheduleDate = staScheduleList.get(round / 2).getScheduleDay();
            Date clockTime = timeFormat.parse(clockInTime);
            // 合并日期和时间
            LocalDate scheduleLocalDate = scheduleDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalTime clockLocalTime = clockTime.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            LocalDateTime combinedDateTime = LocalDateTime.of(scheduleLocalDate, clockLocalTime);
            Date clockDateTime = Date.from(combinedDateTime.atZone(ZoneId.systemDefault()).toInstant());
            staLocationClock.setTime(clockDateTime);
            if (round % 2 == 0) {
                // 上班卡
                staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_1);
                LocalTime expectedStartTime = staScheduleList.get(round / 2).getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
                LocalDateTime expectedStartDateTime = LocalDateTime.of(scheduleLocalDate, expectedStartTime);
                Date timeExpect = Date.from(expectedStartDateTime.atZone(ZoneId.systemDefault()).toInstant());
                staLocationClock.setTimeExpect(timeExpect);
                if (timeExpect.before(clockDateTime)) {
                    staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_2);
                    long diffMinutes = (clockDateTime.getTime() - timeExpect.getTime()) / (60 * 1000);
                    staLocationClock.setLengthOfTardiness((int) diffMinutes);//迟到
                } else {
                    staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
                }
            } else {
                // 下班卡
                staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_2);
                LocalTime expectedEndTime = staScheduleList.get(round / 2).getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
                LocalDateTime expectedEndDateTime = LocalDateTime.of(scheduleLocalDate, expectedEndTime);
                Date timeExpect = Date.from(expectedEndDateTime.atZone(ZoneId.systemDefault()).toInstant());
                staLocationClock.setTimeExpect(timeExpect);
                if (timeExpect.after(clockDateTime)) {
                    staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_3);
                    long diffMinutes = (timeExpect.getTime() - clockDateTime.getTime()) / (60 * 1000);
                    staLocationClock.setEarlyLeaveDuration((int) diffMinutes);//早退
                } else {
                    staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
                }
            }
            return staLocationClock;
        } catch (Exception e) {
            log.error("创建签到对象失败" + e.getMessage(), e);
            return null;
        }
    }

    @GetMapping("/getUserMonthlyWorkHours")
    public Result getUserMonthlyWorkHours() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isNotEmpty(sysUser)) {
            YearMonth currentYearMonth = YearMonth.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            String formattedYearMonth = currentYearMonth.format(formatter);
            LambdaQueryWrapper<StaLocationClock> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            List<StaOrder> staOrder = staOrderService.list(new LambdaQueryWrapper<StaOrder>().eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0)
                    .eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_3).eq(StaOrder::getSysUserId, sysUser.getId()));
            if (!staOrder.isEmpty()) {
                List<String> oids = new ArrayList<>();
                staOrder.forEach(s -> {
                    oids.add(s.getId());
                });
                lambdaQueryWrapper.in(StaLocationClock::getStaOrderId, oids);
            }
            lambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
            lambdaQueryWrapper.likeRight(StaLocationClock::getTime, formattedYearMonth);
            lambdaQueryWrapper.eq(StaLocationClock::getSysUserId, sysUser.getId());
            lambdaQueryWrapper.orderByAsc(StaLocationClock::getTime);
            List<StaLocationClock> staLocationClocks = staLocationClockService.list(lambdaQueryWrapper);
            if (!staLocationClocks.isEmpty()) {
                staLocationClocks = staLocationClocks.stream()
                        .sorted((c1, c2) -> c1.getTime().compareTo(c2.getTime()))
                        .collect(Collectors.toList());
                List<Date> punchInTimes = new ArrayList<>();
                staLocationClocks.forEach(cloks -> punchInTimes.add(cloks.getTime()));
                long totalHours = this.calculateWorkDuration(punchInTimes);
                System.out.println(totalHours);
                //100 6000
                //150 9000
                //200 12000
                //80个小时等于4800分钟。    96个小时等于5760分钟。
                if (totalHours >= 12000) {
                    return Result.error("当月时数已达到或大于200小时");
                } else if (totalHours >= 9000) {
                    return Result.error("当月时数已达到或大于150小时");
                } else if (totalHours >= 6000) {
                    return Result.error("当月时数已达到或大于100小时");
                }
            }
        } else {
            return Result.error("用户获取失败");
        }
        return Result.OK();
    }

    private long calculateWorkDuration(List<Date> punchInTimes) {
        if (punchInTimes.size() < 2) {
            return 0;
        }
        long totalWorkDuration = 0;
        Map<Date, Long> workDurationPerDay = new HashMap<>();
        for (int i = 0; i < punchInTimes.size() - 1; i += 2) {
            Date punchInTime = punchInTimes.get(i);
            Date punchOutTime = punchInTimes.get(i + 1);
            // TODO 判断签到时间是否在同一天 丹尼斯物流有跨日考勤需要在做处理
            if (isSameDay(punchInTime, punchOutTime)) {
                long workDuration = punchOutTime.getTime() - punchInTime.getTime();
                // 记录每天的工作时长
                Date workDate = getStartOfDay(punchInTime);
                workDurationPerDay.put(workDate, workDurationPerDay.getOrDefault(workDate, 0L) + workDuration);
            }
        }
        // 计算总的工作时长
        for (long workDuration : workDurationPerDay.values()) {
            totalWorkDuration += workDuration;
        }
        // 将工作时长从毫秒转换为分钟
        totalWorkDuration /= (1000 * 60);
        return totalWorkDuration;
    }

    // 判断两个日期是否在同一天
    private boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    // 获取一天的开始时间
    private Date getStartOfDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取用户当天签到次数
     *
     * @param today
     * @param oid
     * @return
     */
    @GetMapping("/getTodayCheckinCount")
    public Result getTodayCheckinCount(@RequestParam(value = "today", required = true) String today,
                                       @RequestParam(value = "oid", required = true) String oid) {
        if (WxlConvertUtils.isEmpty(today) || WxlConvertUtils.isEmpty(oid)) {
            return Result.error("系统异常");
        }
        LambdaQueryWrapper<StaLocationClock> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.likeRight(StaLocationClock::getTime, today);
        lambdaQueryWrapper.eq(StaLocationClock::getStaOrderId, oid);
        int count = staLocationClockService.count(lambdaQueryWrapper);
        return Result.OK(count);
    }
    public Result<?> getMonthWorkTime(@RequestParam(value = "oid",required = true) String uid) {
        SysUser sysUser = sysUserService.getById(uid);
        //获取本月工时

        return null;
    }

    /**
     * 生成考勤二维码
     *
     * @param wid 任务id
     * @return
     */
    @GetMapping("/QRCode")
    public Result generateAttendanceQRCode(@RequestParam("wid") String wid) {
        if (redisUtil.hasKey(CacheConstant.ATTENDANCEQRCODE + wid)) { //判断key是否存在
            return Result.OK(redisUtil.get(CacheConstant.ATTENDANCEQRCODE + wid));
        }
        //String logoUrl = "https://mcl666.oss-cn-zhangjiakou.aliyuncs.com/MCL.jpg";
        QrConfig qrConfig = new QrConfig(400, 400);
        qrConfig.setForeColor(Color.BLACK);
        qrConfig.setRatio(3);
        //qrConfig.setImg(QRCodeUtils.urlToBufferImage(logoUrl));
        String content = wid + "," + String.valueOf(System.currentTimeMillis());
        InputStream inputStream = QRCodeUtils.createQrCode(qrConfig, content);
        String base64Image = null;
        try {
            base64Image = QRCodeUtils.convertImageToBase64(inputStream);
            System.out.println(base64Image);
            //二维码过期时间2小时(redis中key过期并不会立即删除)
            redisUtil.set(CacheConstant.ATTENDANCEQRCODE + wid, base64Image, 7200);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return Result.OK(base64Image);
    }
}

