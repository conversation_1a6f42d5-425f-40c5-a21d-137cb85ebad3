package com.byun.modules.staffing.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaBlackList;
import com.byun.modules.staffing.service.IStaBlacklistService;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysUserService;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-5-24 10:20
 */
@RestController
@RequestMapping("/user/blacklist")
public class StaBlacklistController {
    @Autowired
    private IStaBlacklistService staBlacklistService;
    @Autowired
    private ISysUserService sysUserService;

    /**
     * 黑名单列表
     *
     * @param realNameName  真实姓名
     * @param username      手机号码
     * @param idCard        身份证
     * @param blackListType 黑名单类型
     * @param pageNo        页
     * @param pageSize      条
     * @return
     */
    @GetMapping("/list")
    public Result<?> list(@RequestParam(value = "realNameName", required = false) String realNameName,
                          @RequestParam(value = "username", required = false) String username,
                          @RequestParam(value = "idCard", required = false) String idCard,
                          @RequestParam(value = "blackListType", required = false) String blackListType,
                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(loginUser)) {
            return Result.error("登录失效");
        }
        LambdaQueryWrapper<StaBlackList> staBlackListLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staBlackListLambdaQueryWrapper.eq(StaBlackList::getDelFlag, CommonConstant.DEL_FLAG_0);
        staBlackListLambdaQueryWrapper.orderByDesc(StaBlackList::getCreateTime);
        IPage<StaBlackList> page = new Page<>(pageNo, pageSize);
        if (WxlConvertUtils.isNotEmpty(realNameName)) {
            staBlackListLambdaQueryWrapper.like(StaBlackList::getUserName, realNameName);
        }
        if (WxlConvertUtils.isNotEmpty(username)) {
            staBlackListLambdaQueryWrapper.eq(StaBlackList::getPhone, username);
        }
        if (WxlConvertUtils.isNotEmpty(idCard)) {
            staBlackListLambdaQueryWrapper.eq(StaBlackList::getIdCard, idCard);
        }
        if (WxlConvertUtils.isNotEmpty(blackListType)) {
            staBlackListLambdaQueryWrapper.eq(StaBlackList::getBlackListType, blackListType);
        }
        IPage<StaBlackList> staBlackListPage = staBlacklistService.page(page, staBlackListLambdaQueryWrapper);
        return Result.OK("操作成功", staBlackListPage);
    }

    /**
     * 添加黑名单
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/save")
    public Result save(@RequestBody JSONObject jsonObject) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(loginUser)) {
            return Result.error("登录失效");
        }
        String idCard = jsonObject.getString("idCard");
        String blackListType = jsonObject.getString("blackListType");
        if (WxlConvertUtils.isEmpty(idCard)) {
            return Result.error("身份证不能为空");
        }
        if (!IdCardUtil.isIdCardNumberValid(idCard)) {
            return Result.error("无效身份证");
        }
        if (WxlConvertUtils.isEmpty(blackListType)) {
            return Result.error("黑名单类型不能为空");
        }
        SysUser sysUser = sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getIdCard, idCard));
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("该用户不存在");
        }
        int count = staBlacklistService.count(new QueryWrapper<StaBlackList>().lambda()
                .eq(StaBlackList::getIdCard, idCard)
                .eq(StaBlackList::getDelFlag, CommonConstant.DEL_FLAG_0));
        if (count == 0) {
            staBlacklistService.saveUser(loginUser, sysUser, idCard, blackListType);
            return Result.OK("操作成功", "");
        } else {
            return Result.error("当前用户已在黑名单中");
        }

    }

    /**
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    public Result deleteById(@RequestParam(value = "id", required = true) String id) {
        StaBlackList staBlackList = staBlacklistService.getById(id);
        staBlackList.setDelFlag(CommonConstant.DEL_FLAG_1);
        staBlacklistService.updateById(staBlackList);
        return Result.OK("操作成功", "");
    }

    /**
     * 导出execl
     */
    @RequestMapping("/exportXls")
    public ModelAndView exportXls(@RequestParam(value = "realNameName", required = false) String realNameName,
                                  @RequestParam(value = "username", required = false) String username,
                                  @RequestParam(value = "idCard", required = false) String idCard,
                                  @RequestParam(value = "blackListType", required = false) String blackListType,
                                  HttpServletRequest request) {
        LoginUser sysUser= (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "未获取登录部门");
            return mv;
        }
        LambdaQueryWrapper<StaBlackList> staBlackListLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staBlackListLambdaQueryWrapper.eq(StaBlackList::getDelFlag, CommonConstant.DEL_FLAG_0);
        staBlackListLambdaQueryWrapper.orderByDesc(StaBlackList::getCreateTime);
        if (WxlConvertUtils.isNotEmpty(realNameName)) {
            staBlackListLambdaQueryWrapper.like(StaBlackList::getUserName, realNameName);
        }
        if (WxlConvertUtils.isNotEmpty(username)) {
            staBlackListLambdaQueryWrapper.eq(StaBlackList::getPhone, username);
        }
        if (WxlConvertUtils.isNotEmpty(idCard)) {
            staBlackListLambdaQueryWrapper.eq(StaBlackList::getIdCard, idCard);
        }
        if (WxlConvertUtils.isNotEmpty(blackListType)) {
            staBlackListLambdaQueryWrapper.eq(StaBlackList::getBlackListType, blackListType);
        }
        List<StaBlackList> list = staBlacklistService.list(staBlackListLambdaQueryWrapper);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, String> degreeStatus = new HashMap<String, String>() {{
            put("0", "无");
            put("1", "小学");
            put("2", "初中");
            put("3", "中专");
            put("4", "高中");
            put("5", "大专");
            put("6", "本科");
            put("7", "硕士");
            put("8", "博士");
        }};
        for (StaBlackList staBlackList : list) {
            //加入日期
            Date createTime = staBlackList.getCreateTime();
            String createDate = simpleDateFormat.format(createTime);
            staBlackList.setCreateDate(createDate);
            //性别
            if (WxlConvertUtils.isNotEmpty(staBlackList.getSex())) {
                staBlackList.setSexString(staBlackList.getSex() == 0 ? "男" : "女");
            }
            //黑名单类型
            if (WxlConvertUtils.isNotEmpty(staBlackList.getBlackListType())) {
                switch (staBlackList.getBlackListType()) {
                    case  CommonConstant.BLACLLISTTYPE0:
                        staBlackList.setBlackListType("开除");
                        break;
                    case  CommonConstant.BLACLLISTTYPE1:
                        staBlackList.setBlackListType("劝退");
                        break;
                    case  CommonConstant.BLACLLISTTYPE2:
                        staBlackList.setBlackListType("内盗");
                        break;
                    case  CommonConstant.BLACLLISTTYPE3:
                        staBlackList.setBlackListType("劳动争议");
                        break;
                    case  CommonConstant.BLACLLISTTYPE4:
                        staBlackList.setBlackListType("其他");
                        break;
                }
            }
            //学历
            if (WxlConvertUtils.isNotEmpty(staBlackList.getDegree())) {
                staBlackList.setDegree(degreeStatus.get(staBlackList.getDegree()));
            }
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "任务详情列表列表");
        mv.addObject(NormalExcelConstants.CLASS, StaBlackList.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("任务详情列表数据", "导出人:" + sysUser.getRealname(), "任务列表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;
    }
}
