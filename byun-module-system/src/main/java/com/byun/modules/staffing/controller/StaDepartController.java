package com.byun.modules.staffing.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.util.JwtUtil;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.model.StaDepartModel;
import com.byun.modules.staffing.model.StaDepartTreeModel;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaWorkService;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.service.ISysDepartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 部门
 * @date 2021/11/19 11:44
 */
@Api(tags="灵工部门相关接口")
@RestController
@RequestMapping("/staffing/sysDepart")
@Slf4j
public class StaDepartController {

    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private IStaWorkService staWorkService;

    @Autowired
    private ISysBaseAPI sysBaseAPI;
    /**
     * 分页列表查询
     *
     * @param
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "任务列表-分页列表查询按距离排序")
    @ApiOperation(value="任务列表-分页列表查询按距离排序", notes="任务列表-分页列表查询按距离排序")
    @GetMapping(value = "/listDepartDistance")
    public Result<?> listDepartDistance(StaDepartModel staDepartModel,
                                       @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                       @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                       HttpServletRequest req) {
        Result<IPage<StaDepartModel>> result = new Result<IPage<StaDepartModel>>();
        Page<StaDepartModel> pageList = new Page<StaDepartModel>(pageNo,pageSize);
        pageList = sysDepartService.listDepartDistancePage(pageList, staDepartModel);
        result.setResult(pageList);
        result.setSuccess(true);
        return result;
    }


    /**
     * 查询数据 查出我的部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/getModel", method = RequestMethod.GET)
    public Result<StaDepartModel> getStaDepartModelByid(@RequestParam(name = "id") String id) {
        Result<StaDepartModel> result = new Result<>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            SysDepart sysDepart = sysDepartService.getById(id);
            if(WxlConvertUtils.isNotEmpty(sysDepart)){
                StaDepartModel staDepartModel = new StaDepartModel(sysDepart);
                if(WxlConvertUtils.isNotEmpty(sysDepart.getParentId())){
                    SysDepart parent = sysDepartService.getById(sysDepart.getParentId());
                    if(WxlConvertUtils.isNotEmpty(parent)){
                        staDepartModel.setParentName(parent.getDepartName());
                    }
                }
                result.setResult(staDepartModel);
                result.setSuccess(true);
            }else{
                result.setSuccess(false);
            }

        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }

    /**
     * 修改任务
     * @param sysDepart
     * @param request
     * @return
     */
    @Transactional
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @CacheEvict(value= {CacheConstant.SYS_DEPARTS_CACHE,CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries=true)
    public Result<SysDepart> edit(@RequestBody SysDepart sysDepart, HttpServletRequest request) {
        String id = sysDepart.getId();
        SysDepart sysDepartEntity = sysDepartService.getById(id);
        String username = JwtUtil.getUserNameByToken(request);
        sysDepart.setUpdateBy(username);
        Result<SysDepart> result = new Result<SysDepart>();
        if (sysDepartEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = sysDepartService.updateDepartDataById(sysDepart, username);
            List<StaOrder> orderList = staOrderService.list(new QueryWrapper<StaOrder>().eq("company_name", sysDepartEntity.getDepartName()));
            List<StaWork> staWorklist = staWorkService.list(new QueryWrapper<StaWork>().lambda().eq(StaWork::getCompanyId, sysDepartEntity.getId()));
            List<StaOrder> updateStaOrderList = new ArrayList<>();
            List<StaWork> updateStaWorkList = new ArrayList<>();
            //修改任务
            if (WxlConvertUtils.isNotEmpty(staWorklist) && !staWorklist.isEmpty()) {
                for (StaWork staWork : staWorklist) {
                    staWork.setAddressLat(sysDepart.getAddressLat());
                    staWork.setAddressLng(sysDepart.getAddressLng());
                    staWork.setAddressName(sysDepart.getAddressName());
                    staWork.setAddress(sysDepart.getAddress());
                    updateStaWorkList.add(staWork);
                }
                staWorkService.updateBatchById(updateStaWorkList);
            }
            //修改任务单
            if (WxlConvertUtils.isNotEmpty(orderList) && !orderList.isEmpty()) {
                for (StaOrder staOrder : orderList) {
                    staOrder.setStoNe(sysDepart.getStoreNo()); //店编码
                    staOrder.setAddressLat(sysDepart.getAddressLat());
                    staOrder.setAddressLng(sysDepart.getAddressLng());
                    staOrder.setEnrollAddressLat(sysDepart.getAddressLat());
                    staOrder.setEnrollAddressLng(sysDepart.getAddressLng());
                    staOrder.setAddressName(sysDepart.getAddressName());
                    staOrder.setEnrollAddress(sysDepart.getAddress());
                    staOrder.setStoreNo(sysDepartEntity.getStoreNo());
                    updateStaOrderList.add(staOrder);
                }
                staOrderService.updateBatchById(updateStaOrderList);
            }
            if (ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 查询数据 查出我的部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public Result<SysDepart> getDepartByid(@RequestParam(name = "id") String id) {
        Result<SysDepart> result = new Result<>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            SysDepart sysDepart = sysDepartService.getById(id);
            result.setResult(sysDepart);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }


    /**
     * 添加新数据 添加用户新建的部门对象数据,并保存到数据库
     *
     * @param sysDepart
     * @return
     */
    //@RequiresRoles({"admin"})
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @CacheEvict(value= {CacheConstant.SYS_DEPARTS_CACHE,CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries=true)
    public Result<SysDepart> add(@RequestBody SysDepart sysDepart, HttpServletRequest request) {
        Result<SysDepart> result = new Result<SysDepart>();
        String username = JwtUtil.getUserNameByToken(request);
        List<SysDepart> list = sysDepartService.list();
        for (SysDepart s : list) {
            if ( s.getStoreNo().equals(sysDepart.getStoreNo())){
                result.error500("当前店编编号已存在！");
                return result;
            }
        }
        try {
            sysDepart.setCreateBy(username);
            sysDepartService.saveDepartData(sysDepart, username);
            StaOrder staOrder = staOrderService.getOne(new QueryWrapper<StaOrder>().eq("company_name", sysDepart.getDepartName()));
            if (staOrder != null){
                staOrder.setStoreNo(sysDepart.getStoreNo());
                staOrderService.updateById(staOrder);
            }
            //清除部门树内存
            // FindsDepartsChildrenUtil.clearSysDepartTreeList();
            // FindsDepartsChildrenUtil.clearDepartIdModel();
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            result.error500("操作失败");
        }
        return result;
    }




    /**
     * 查询数据 查出我的部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/queryMyDeptTreeList", method = RequestMethod.GET)
    public Result<List<StaDepartTreeModel>> queryStaMyDeptTreeList() {
        Result<List<StaDepartTreeModel>> result = new Result<>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
            if(WxlConvertUtils.isNotEmpty(departId)){
                //目前任务添加、代理添加，店铺管理会用到，不设置权限
//                Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(),departId);
//                boolean branchList = userPermissionSet.contains("staffing:branch:list");//门店列表
//                if(branchList){
                    if(StringUtils.isNotBlank(departId)){
                        List<StaDepartTreeModel> list = sysDepartService.queryStaMyDeptTreeList(departId);
                        result.setResult(list);
                    }
                    //update-end-:部门查询ids为空后的前端显示问题
                    result.setSuccess(true);
//                }else{
//                    result.setMessage("暂无权限");
//                    result.setSuccess(false);
//                }
            }else{
                result.setMessage("未找到当前登录公司");
                result.setSuccess(false);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            result.setMessage("查询失败");
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 查询数据 查出所有部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/queryTreeList", method = RequestMethod.GET)
    public Result<List<StaDepartTreeModel>> queryTreeList(@RequestParam(name = "ids", required = false) String ids) {
        Result<List<StaDepartTreeModel>> result = new Result<>();
        try {
            // 从内存中读取
//			List<SysDepartTreeModel> list =FindsDepartsChildrenUtil.getSysDepartTreeList();
//			if (CollectionUtils.isEmpty(list)) {
//				list = sysDepartService.queryTreeList();
//			}
            if(WxlConvertUtils.isNotEmpty(ids)){
                List<StaDepartTreeModel> departList = sysDepartService.queryStaTreeList(ids);
                result.setResult(departList);
            }else{
                List<StaDepartTreeModel> list = sysDepartService.queryStaTreeList();
                result.setResult(list);
            }
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return result;
    }


    /**
     * 分页列表查询
     *
     * @param sysDepart
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "门店-分页列表查询")
    @ApiOperation(value="门店-分页列表查询", notes="门店-分页列表查询")
    @GetMapping(value = "/companyList")
    public Result<?> queryPageCompanyList(SysDepart sysDepart,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        sysDepart.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
//        sysDepart.setOrgCategory(CommonConstant.DEPART_ORG_CATEGORY_2);
        QueryWrapper<SysDepart> queryWrapper = QueryGenerator.initQueryWrapper(sysDepart, req.getParameterMap());
        Page<SysDepart> page = new Page<SysDepart>(pageNo, pageSize);
        //queryWrapper.ne("orgCode","A04");
        IPage<SysDepart> pageList = sysDepartService.page(page, queryWrapper);
        List<SysDepart> collect = pageList.getRecords().stream().filter(sd -> !"A04".equals(sd.getOrgCode())).collect(Collectors.toList());
        return Result.OK(pageList.setRecords(collect));
    }

    /**
     * 店编查询单个店铺
     * @param storeNo
     * @return
     */
    @GetMapping("/queryUserListByStoreNo/{storeNo}")
    public Result queryUserListByStoreNo(@PathVariable("storeNo") String storeNo){
        Result result = new Result();
        SysDepart sysDepart = sysDepartService.getOne(new QueryWrapper<SysDepart>().eq("store_no", storeNo));
        if (WxlConvertUtils.isNotEmpty(sysDepart)){
            result.setSuccess(true);
            result.setResult(sysDepart);
            result.setMessage("操作成功");
        }else {
            result.setSuccess(false);
            result.setMessage("查无此店铺！");
        }
        return  result;
    }
}
