package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaDepartImage;
import com.byun.modules.staffing.service.IStaDepartImageService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 公司门店图片
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="公司门店图片")
@RestController
@RequestMapping("/staffing/staDepartImage")
@Slf4j
public class StaDepartImageController extends ByunExcelController<StaDepartImage, IStaDepartImageService> {
	@Autowired
	private IStaDepartImageService staDepartImageService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staDepartImage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "公司门店图片-分页列表查询")
	@ApiOperation(value="公司门店图片-分页列表查询", notes="公司门店图片-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaDepartImage staDepartImage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaDepartImage> queryWrapper = QueryGenerator.initQueryWrapper(staDepartImage, req.getParameterMap());
		Page<StaDepartImage> page = new Page<StaDepartImage>(pageNo, pageSize);
		IPage<StaDepartImage> pageList = staDepartImageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staDepartImage
	 * @return
	 */
	@AutoLog(value = "公司门店图片-添加")
	@ApiOperation(value="公司门店图片-添加", notes="公司门店图片-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaDepartImage staDepartImage) {
		staDepartImageService.save(staDepartImage);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staDepartImage
	 * @return
	 */
	@AutoLog(value = "公司门店图片-编辑")
	@ApiOperation(value="公司门店图片-编辑", notes="公司门店图片-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaDepartImage staDepartImage) {
		staDepartImageService.updateById(staDepartImage);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "公司门店图片-通过id删除")
	@ApiOperation(value="公司门店图片-通过id删除", notes="公司门店图片-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staDepartImageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "公司门店图片-批量删除")
	@ApiOperation(value="公司门店图片-批量删除", notes="公司门店图片-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staDepartImageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "公司门店图片-通过id查询")
	@ApiOperation(value="公司门店图片-通过id查询", notes="公司门店图片-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaDepartImage staDepartImage = staDepartImageService.getById(id);
		if(staDepartImage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staDepartImage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staDepartImage
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaDepartImage staDepartImage) {
        return super.exportXls(request, staDepartImage, StaDepartImage.class, "公司门店图片");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaDepartImage.class);
    }

}
