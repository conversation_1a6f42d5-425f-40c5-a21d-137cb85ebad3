package com.byun.modules.staffing.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.DySmsEnum;
import com.byun.common.util.DySmsHelper;
import com.byun.common.util.RedisUtil;
import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.byun.modules.staffing.entity.StaUserInfo;
import com.byun.modules.staffing.model.StaEnrollInfoModel;
import com.byun.modules.staffing.service.IStaEnrollInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.service.IStaUserInfoService;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 报名信息，用于任务报名
 * @Author: bai
 * @Date:   2021-11-21
 * @Version: V1.0
 */
@Api(tags="报名信息，用于任务报名")
@RestController
@RequestMapping("/staffing/staEnrollInfo")
@Slf4j
public class StaEnrollInfoController extends ByunExcelController<StaEnrollInfo, IStaEnrollInfoService> {
	@Autowired
	private IStaEnrollInfoService staEnrollInfoService;
	@Autowired
	private RedisUtil redisUtil;
	@Autowired
	private IStaUserInfoService staUserInfoService;
	@Autowired
	private ISysUserService sysUserService;
	 /**
	  * 短信验证码接口 验证报名信息
	  *
	  * @param jsonObject
	  * @return
	  */
	 @PostMapping(value = "/sms")
	 public Result<String> sms(@RequestBody JSONObject jsonObject) {
		 Result<String> result = new Result<String>();
		 LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 String mobile = jsonObject.get("mobile").toString();
		 if(WxlConvertUtils.isEmpty(mobile)){
			 result.setMessage("手机号不允许为空！");
			 result.setSuccess(false);
			 return result;
		 }
		 Object object = redisUtil.get("STAENROLLINFO_" +mobile);
		 if (object != null) {
			 result.setMessage("验证码5分钟内，仍然有效！");
			 result.setSuccess(false);
			 return result;
		 }

		 //随机数
		 String captcha = RandomUtil.randomNumbers(6);
		 JSONObject obj = new JSONObject();
		 obj.put("code", obj);
		 try {
			 boolean b = false;
			 LambdaQueryWrapper<StaEnrollInfo> queryWrapper = new LambdaQueryWrapper<>();
			 queryWrapper.eq(StaEnrollInfo::getPhone,mobile);
			 queryWrapper.eq(StaEnrollInfo::getSysUserId,user.getId());
			 int count =(int) staEnrollInfoService.count(queryWrapper);
			 if(count>0) {
				 result.error500("请查看报名列表，手机号已存在！");
				 return result;
			 }
			 b = DySmsHelper.sendSms(mobile, obj, DySmsEnum.STAENROLLINFO_CODE);
			 if (b == true) {
				 result.setMessage("短信验证码发送失败,请稍后重试");
				 result.setSuccess(false);
				 return result;
			 }
			 //验证码10分钟内有效
			 redisUtil.set("STAENROLLINFO_" +mobile, captcha, 600);
			 //update-begin
			 //result.setResult(captcha);
			 //update-end
			 result.setSuccess(true);

		 } catch (ClientException e) {
			 e.printStackTrace();
			 result.error500(" 短信接口未配置，请联系管理员！");
			 return result;
		 }
		 return result;
	 }


	 /**
	  * 获取当前用户的报名信息
	  *
	  * @return
	  */
	 @AutoLog(value = "报名信息，用于任务报名-通过id查询")
	 @ApiOperation(value="报名信息，用于任务报名-通过id查询", notes="报名信息，用于任务报名-通过id查询")
	 @GetMapping(value = "/getMyEnrollInfo")
	 public Result<?> getMyEnrollInfo() {
		 LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 LambdaQueryWrapper<StaEnrollInfo> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.eq(StaEnrollInfo::getUserRel,CommonConstant.USER_REL_0);
		 queryWrapper.eq(StaEnrollInfo::getSysUserId,user.getId());
		 queryWrapper.eq(StaEnrollInfo::getDelFlag,CommonConstant.DEL_FLAG_0);
		 StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(queryWrapper);
		 if(staEnrollInfo==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(staEnrollInfo);
	 }
	
	/**
	 * 分页列表查询
	 * @param sysUserId
	 * @return
	 */
	@AutoLog(value = "报名信息，用于任务报名-分页列表查询")
	@ApiOperation(value="报名信息，用于任务报名-分页列表查询", notes="报名信息，用于任务报名-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(@RequestParam("sysUserId") String sysUserId) {
		if (WxlConvertUtils.isEmpty(sysUserId)) {
			return Result.error("获取用户失败");
		}
		//根据用户id查找用户申请信息 没有则创建
		StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(new QueryWrapper<StaEnrollInfo>().eq("sys_user_id", sysUserId));
		if (WxlConvertUtils.isEmpty(staEnrollInfo)) {
			SysUser sysUser = sysUserService.getById(sysUserId);
			StaEnrollInfo staEnrollInfo2 = new StaEnrollInfo(sysUser);
			staEnrollInfo2.setPhoneVeri(CommonConstant.VERIFICATION_1);//手机验证码注册，则自动验证过手机
			staEnrollInfo2.setUserRel(CommonConstant.USER_REL_0);
			staEnrollInfoService.save(staEnrollInfo2);
			String id = staEnrollInfo2.getId();
			return Result.OK(staEnrollInfoService.getById(id));
		}else {
			//return WxlConvertUtils.isNotEmpty(staEnrollInfo) ? Result.OK(staEnrollInfo) : Result.error("获取申请信息失败");
			return Result.OK(staEnrollInfo);
		}
	}
	/**
	 *   添加
	 * @param staEnrollInfoModel
	 * @return
	 */
	@AutoLog(value = "报名信息，用于任务报名-添加")
	@ApiOperation(value="报名信息，用于任务报名-添加", notes="报名信息，用于任务报名-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaEnrollInfoModel staEnrollInfoModel) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		//TODO 添加短信验证码验证
		if(WxlConvertUtils.isEmpty(staEnrollInfoModel.getPhone())){
			return Result.error("手机号为空！");
		}
		StaEnrollInfo staEnrollInfo = new StaEnrollInfo(staEnrollInfoModel);
		Object code = redisUtil.get("STAENROLLINFO_" +staEnrollInfoModel.getPhone());
		if (WxlConvertUtils.isEmpty(staEnrollInfoModel.getCaptcha())||!staEnrollInfoModel.getCaptcha().equals(code)) {
			return Result.error("手机验证码错误！");
		}
		//查询用户下是否有相同手机号
		LambdaQueryWrapper<StaEnrollInfo> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(StaEnrollInfo::getSysUserId, user.getId());
		queryWrapper.eq(StaEnrollInfo::getPhone, staEnrollInfo.getPhone());
		int count = (int) staEnrollInfoService.count(queryWrapper);
		if(count>0){
			return Result.error("该手机号已存在请勿重复添加！");
		}
		//验证码验证手机成功
		staEnrollInfo.setPhoneVeri(CommonConstant.VERIFICATION_1);
		staEnrollInfo.setCreateTime(new Date());
		staEnrollInfo.setCreateBy(user.getUsername());
		staEnrollInfo.setSysUserId(user.getId());
		if(WxlConvertUtils.isEmpty(staEnrollInfo.getUserRel())){
			staEnrollInfo.setUserRel(CommonConstant.USER_REL_2);
		}
		staEnrollInfo.setDelFlag(CommonConstant.DEL_FLAG_0);
		if(WxlConvertUtils.isEmpty(staEnrollInfo.getIdCardVeri())){
			staEnrollInfo.setIdCardVeri(0);
		}
		if(WxlConvertUtils.isEmpty(staEnrollInfo.getPhoneVeri())){
			staEnrollInfo.setPhoneVeri(0);
		}
		staEnrollInfoService.save(staEnrollInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 * @param staEnrollInfo
	 * @return
	 */
	@AutoLog(value = "报名信息，用于任务报名-编辑")
	@ApiOperation(value="报名信息，用于任务报名-编辑", notes="报名信息，用于任务报名-编辑")
	@Transactional
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaEnrollInfo staEnrollInfo){
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		staEnrollInfo.setUpdateTime(new Date());
		staEnrollInfo.setUpdateBy(user.getUsername());
		boolean result1 = staEnrollInfoService.updateById(staEnrollInfo);//申请信息
		StaUserInfo staUserInfo = staUserInfoService.getOne(new LambdaQueryWrapper<StaUserInfo>().eq(StaUserInfo::getSysUserId,staEnrollInfo.getSysUserId()));//名片
		//TODO 暂时只修改、学历 、（现住址、现住址详情、经纬度）
		//staUserInfo.setDegree(staUserInfo.getDegree());
		staUserInfo.setAddress(staEnrollInfo.getAddress());
		staUserInfo.setAddressName(staEnrollInfo.getAddressName());
		staUserInfo.setAddressLat(staEnrollInfo.getAddressLat());
		staUserInfo.setAddressLat(staEnrollInfo.getAddressLat());
		boolean result2 = staUserInfoService.updateById(staUserInfo);
		if (result1 && result2){
			return Result.OK("编辑成功!");
		}
		return Result.OK("操作失败!");
	}

	 /**
	  * 用户修改学历
	  * @param
	  * @return
	  */
	@PostMapping("/editDegree")
	@Transactional
	public Result<?> editDegree(@RequestBody StaEnrollInfo staEnrollInfo) {
		String userId = "";
		if (WxlConvertUtils.isNotEmpty(staEnrollInfo.getSysUserId())) {
			userId = staEnrollInfo.getSysUserId();
		}else if (WxlConvertUtils.isNotEmpty(staEnrollInfo.getId())) {
			userId = staEnrollInfo.getId();
		}
		staEnrollInfoService.update(staEnrollInfo,new LambdaQueryWrapper<StaEnrollInfo>().eq(StaEnrollInfo::getSysUserId,userId));//修改申请信息
		StaUserInfo staUserInfo = staUserInfoService.getOne(new LambdaQueryWrapper<StaUserInfo>().eq(StaUserInfo::getSysUserId,userId));
		if (WxlConvertUtils.isNotEmpty(staUserInfo)) {
			staUserInfo.setDegree(staEnrollInfo.getDegree());//学历
			staUserInfo.setSchool(staEnrollInfo.getSchool());//学校名称
			staUserInfo.setMajor(staEnrollInfo.getMajor());//专业
			staUserInfo.setEnrollmentTime(staEnrollInfo.getEnrollmentTime());//入学日期
			staUserInfo.setGraduationTime(staEnrollInfo.getGraduationTime());//毕业日期
			staUserInfoService.updateById(staUserInfo);//修改名片
		}
		SysUser sysUser = sysUserService.getById(userId);
		sysUser.setDegree(staEnrollInfo.getDegree());//学历
		sysUser.setSchool(staEnrollInfo.getSchool());//学校名称
		sysUser.setMajor(staEnrollInfo.getMajor());//专业
		sysUser.setEnrollmentTime(staEnrollInfo.getEnrollmentTime());//入学日期
		sysUser.setGraduationTime(staEnrollInfo.getGraduationTime());//毕业日期
		sysUserService.updateById(sysUser);
		//修改用户信息
		return Result.OK("操作成功");
	}
	/**
	 *   通过id删除
	 *
	 * @param staEnrollInfo
	 * @return
	 */
	@AutoLog(value = "报名信息，用于任务报名-通过id删除")
	@ApiOperation(value="报名信息，用于任务报名-通过id删除", notes="报名信息，用于任务报名-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestBody StaEnrollInfo staEnrollInfo) {
		StaEnrollInfo sei = staEnrollInfoService.getById(staEnrollInfo.getId());
		if(WxlConvertUtils.isEmpty(sei)){
			return Result.error("未查询到报名信息!");
		}
		if(CommonConstant.USER_REL_0.equals(sei.getUserRel())){
			return Result.OK("不能删除自己的报名信息!");
		}
		staEnrollInfoService.removeById(staEnrollInfo.getId());
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "报名信息，用于任务报名-批量删除")
	@ApiOperation(value="报名信息，用于任务报名-批量删除", notes="报名信息，用于任务报名-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		System.out.println(ids);
		this.staEnrollInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报名信息，用于任务报名-通过id查询")
	@ApiOperation(value="报名信息，用于任务报名-通过id查询", notes="报名信息，用于任务报名-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaEnrollInfo staEnrollInfo = staEnrollInfoService.getById(id);
		if(staEnrollInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staEnrollInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staEnrollInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaEnrollInfo staEnrollInfo) {
        return super.exportXls(request, staEnrollInfo, StaEnrollInfo.class, "报名信息，用于任务报名");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaEnrollInfo.class);
    }

}
