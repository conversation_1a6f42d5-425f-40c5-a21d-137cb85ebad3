package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaFocusImage;
import com.byun.modules.staffing.service.IStaFocusImageService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 聚焦图
 * @Author: bai
 * @Date:   2021-11-17
 * @Version: V1.0
 */
@Api(tags="聚焦图")
@RestController
@RequestMapping("/staffing/focus")
@Slf4j
public class StaFocusImageController extends ByunExcelController<StaFocusImage, IStaFocusImageService> {
	@Autowired
	private IStaFocusImageService staFocusImageService;

	 @ApiOperation("管理-首页广告展示")
	 @RequestMapping(value = "/admin/index", method = RequestMethod.GET)
	 public Result<JSONObject> adminIlndexFocus() {
		 Result<JSONObject> result = new Result<>();

		 return result;
	 }
	 @ApiOperation("用户-首页广告展示")
	 @RequestMapping(value = "/index", method = RequestMethod.GET)
	 public Result<JSONObject> indexFocus() {
		 Result<JSONObject> result = new Result<>();

		 return result;
	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param staFocusImage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "聚焦图-分页列表查询")
	@ApiOperation(value="聚焦图-分页列表查询", notes="聚焦图-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaFocusImage staFocusImage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaFocusImage> queryWrapper = QueryGenerator.initQueryWrapper(staFocusImage, req.getParameterMap());
		Page<StaFocusImage> page = new Page<StaFocusImage>(pageNo, pageSize);
		IPage<StaFocusImage> pageList = staFocusImageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staFocusImage
	 * @return
	 */
	@AutoLog(value = "聚焦图-添加")
	@ApiOperation(value="聚焦图-添加", notes="聚焦图-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaFocusImage staFocusImage) {
		staFocusImageService.save(staFocusImage);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staFocusImage
	 * @return
	 */
	@AutoLog(value = "聚焦图-编辑")
	@ApiOperation(value="聚焦图-编辑", notes="聚焦图-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaFocusImage staFocusImage) {
		staFocusImageService.updateById(staFocusImage);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "聚焦图-通过id删除")
	@ApiOperation(value="聚焦图-通过id删除", notes="聚焦图-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staFocusImageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "聚焦图-批量删除")
	@ApiOperation(value="聚焦图-批量删除", notes="聚焦图-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staFocusImageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "聚焦图-通过id查询")
	@ApiOperation(value="聚焦图-通过id查询", notes="聚焦图-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaFocusImage staFocusImage = staFocusImageService.getById(id);
		if(staFocusImage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staFocusImage);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staFocusImage
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaFocusImage staFocusImage) {
        return super.exportXls(request, staFocusImage, StaFocusImage.class, "聚焦图");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaFocusImage.class);
    }

}
