package com.byun.modules.staffing.controller;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaGobsType;
import com.byun.modules.staffing.service.IStaGobsTypeService;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.service.ISysDepartService;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-4-25 9:39
 */
@RestController
@RequestMapping("/sys/gobsType")
public class StaGobsTypeController {
    /**
     * 获取工种列表
     * @param sysGobsType
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @Autowired
    private IStaGobsTypeService sysGobsTypeService;
    @Autowired
    private ISysDepartService sysDepartService;
    @GetMapping("/list")
    public Result<?> list(StaGobsType sysGobsType,
                                          @RequestParam(name = "jobsName",required = false) String jobsName,
                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                          HttpServletRequest req){
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        String orgCode = user.getOrgCode();
        Page<StaGobsType> page = new Page<>(pageNo,pageSize);
        LambdaQueryWrapper<StaGobsType> queryWrapper = new LambdaQueryWrapper<StaGobsType>()
                .eq(StaGobsType::getDelFlag, CommonConstant.DEL_FLAG_0)
                .likeRight(StaGobsType::getOrgCode,orgCode);
        if (WxlConvertUtils.isNotEmpty(jobsName)) {
            queryWrapper.like(StaGobsType::getJobsName,jobsName);
        }
        return Result.OK(sysGobsTypeService.page(page,queryWrapper));
    }
    @RequestMapping("/exportXls")
    public ModelAndView exportXls (@RequestParam(value = "jobsName",required = false) String jobsName) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String orgCode = user.getOrgCode();
        LambdaQueryWrapper<StaGobsType> queryWrapper = new LambdaQueryWrapper<StaGobsType>()
                .eq(StaGobsType::getDelFlag, CommonConstant.DEL_FLAG_0)
                .likeRight(StaGobsType::getOrgCode,orgCode);
        if (WxlConvertUtils.isNotEmpty(jobsName)) {
            queryWrapper.like(StaGobsType::getJobsName,jobsName);
        }
        List<StaGobsType> list = sysGobsTypeService.list(queryWrapper);
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "任务类型");
        mv.addObject(NormalExcelConstants.CLASS, StaGobsType.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("任务类型", "导出人:"+user.getRealname(), "任务类型"));
        mv.addObject(NormalExcelConstants.DATA_LIST,list);
        return mv;
    }
    @GetMapping("/queryList")
    public Result queryList(){
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        LambdaQueryWrapper<StaGobsType> queryWrapper = new LambdaQueryWrapper<StaGobsType>();
        String orgCode = user.getOrgCode();
        if (orgCode.length() == 3) { //总部
            //queryWrapper.eq(StaGobsType::getOrgCode,orgCode);
        }else if (orgCode.length() == 6){ //事业处
            queryWrapper.eq(StaGobsType::getOrgCode,orgCode);
        }else { //区域和门店
            orgCode = orgCode.substring(0,6);
            queryWrapper.eq(StaGobsType::getOrgCode,orgCode);
        }
        queryWrapper.eq(StaGobsType::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<StaGobsType> list = sysGobsTypeService.list(queryWrapper);
        return Result.OK(list);
    }
    @PostMapping("/save")
    public Result save(@RequestBody StaGobsType sysGobsType, HttpServletRequest req){
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        String companyId = sysGobsType.getCompanyId();
        if (WxlConvertUtils.isEmpty(companyId)) {
            return Result.error("事业处不能为空");
        }
        SysDepart sysDepart = sysDepartService.getById(companyId);
        switch (sysDepart.getStoreNo()) {
            case "1000" ://大卖场
                sysGobsType.setCompanyType(1000);
                break;
            case "8000" : //百货
                sysGobsType.setCompanyType(8000);
                break;
            case "3000" : //郑州便利店
                sysGobsType.setCompanyType(3000);
                break;
            case "2999" : //物流
                sysGobsType.setCompanyType(2999);
                break;
//            case "3600" : //洛阳便利店
//                sysGobsType.setCompanyType(3600);
//                break;
            default:
                return Result.error("请选择事业处");
        }
        sysGobsType.setCreateBy(user.getId());
        sysGobsType.setOrgCode(sysDepart.getOrgCode());
        sysGobsType.setCreateTime(new Date());
        sysGobsType.setUpdateTime(new Date());
        sysGobsType.setDelFlag(CommonConstant.DEL_FLAG_0);
        sysGobsType.setCompanyId(sysDepart.getId());
        sysGobsType.setCompanyName(sysDepart.getDepartName());
        sysGobsTypeService.save(sysGobsType);
        return Result.OK("操作成功");
    }
    @PostMapping("/edit")
    public Result edit(@RequestBody StaGobsType sysGobsType){
        sysGobsType.setUpdateTime(new Date());
        if (WxlConvertUtils.isEmpty(sysGobsType.getCompanyId())) {
            return Result.error("事业处丢失");
        }
        StaGobsType sysGobsTypeServiceById = sysGobsTypeService.getById(sysGobsType.getId());
        String companyId = sysGobsTypeServiceById.getCompanyId();
        SysDepart depart = sysDepartService.getById(companyId);
        SysDepart sysDepart = sysDepartService.getById(sysGobsType.getCompanyId());
        if (!depart.getId().equals(sysDepart.getId())) {
            return Result.error("事业处不允许修改");
        }
        sysGobsType.setCompanyName(sysDepart.getDepartName());
        sysGobsTypeService.updateById(sysGobsType);
        return Result.OK("操作成功");
    }
    @DeleteMapping("/remove")
    public Result remove(@RequestBody StaGobsType sysGobsType){
        sysGobsType.setDelFlag(CommonConstant.DEL_FLAG_1);
        sysGobsTypeService.updateById(sysGobsType);
        return Result.OK("操作成功");
    }
    @GetMapping("/getById")
    public Result getById(@RequestParam("id") String id) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(id)) {
            return Result.error("id丢失");
        }
        return Result.OK("操作成功",sysGobsTypeService.getById(id));
    }
}
