package com.byun.modules.staffing.controller;

import com.alibaba.fastjson.JSONObject;
import com.byun.common.api.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/16 13:34
 */
@RestController
@RequestMapping("/staffing/info")
@Api(tags="小程序消息")
@Slf4j
public class StaInfoController {
    @ApiOperation("管理-滚动消息")
    @RequestMapping(value = "/roll", method = RequestMethod.GET)
    public Result<JSONObject> roll() {
        Result<JSONObject> result = new Result<>();

        return result;
    }

    @ApiOperation("消息列表")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public Result<JSONObject> info() {
        Result<JSONObject> result = new Result<>();

        return result;
    }

}
