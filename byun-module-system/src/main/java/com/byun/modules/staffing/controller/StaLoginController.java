package com.byun.modules.staffing.controller;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.SymbolConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.util.JwtUtil;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.*;
import com.byun.common.util.encryption.AesEncryptUtil;
import com.byun.config.ByunBaseConfig;
import com.byun.modules.base.service.BaseCommonService;
import com.byun.modules.staffing.model.WechatAppletLoginModel;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/16 13:34
 */
@RestController
@RequestMapping("/staffing")
@Api(tags="用户登录")
@Slf4j
public class StaLoginController {
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private ISysLogService logService;
    @Autowired
    public RedisTemplate redisTemplate;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysTenantService sysTenantService;
    @Autowired
    private ISysDictService sysDictService;
    @Resource
    private BaseCommonService baseCommonService;
    @Autowired
    private ISysPermissionService sysPermissionService;
    @Autowired
    private ISysUserDepartService sysUserDepartService;
    @Autowired
    private ByunBaseConfig byunBaseConfig;
    @Autowired
    private IStaUserBankCardsService staUserBankCardsService;
    /**
     * 【小程序专用专用】获取用户信息
     */
    @RequestMapping(value = "/user/info", method = RequestMethod.GET)
    public Result<JSONObject> getUserInfo(HttpServletRequest request){
        Result<JSONObject> result = new Result<JSONObject>();
        String  username = JwtUtil.getUserNameByToken(request);
        if(WxlConvertUtils.isNotEmpty(username)) {
            // 根据用户名(手机号)查询用户信息
            SysUser sysUser = sysUserService.getUserByName(username);
            //用户登录信息
            Result<JSONObject> resultObj=userInfo(sysUser, result);
            JSONObject jsonObject=resultObj.getResult();
            JSONObject obj=new JSONObject();
            obj.put("token", jsonObject.get("token"));
            obj.put("userInfo",jsonObject.get("userInfo"));
            result.setResult(obj);
            result.success("");
        }
        return result;
    }
    @ApiOperation("登录接口")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public Result<JSONObject> login(@RequestBody WechatAppletLoginModel wechatAppletLoginModel){
        Result<JSONObject> result = new Result<JSONObject>();
        String username = wechatAppletLoginModel.getUsername();
        String password = wechatAppletLoginModel.getPassword();
        boolean contains = username.contains("@"); //邮箱
        //update-begin:校验验证码
        String captcha = wechatAppletLoginModel.getCaptcha();
        if(WxlConvertUtils.isEmpty(captcha)){
            result.error500("验证码无效");
            return result;
        }
        String lowerCaseCaptcha = captcha.toLowerCase();
        String realKey = MD5Util.MD5Encode(lowerCaseCaptcha+wechatAppletLoginModel.getCheckKey(), "utf-8");
        Object checkCode = redisUtil.get(realKey);
        //当进入登录页时，有一定几率出现验证码错误 #1714
        if(checkCode==null || !checkCode.toString().equals(lowerCaseCaptcha)) {
            result.error500("验证码错误");
            return result;
        }
        //update-end:校验验证码

        //1. 校验用户是否有效
        //update-begin: 登录代码验证用户是否注销bug，if条件永远为false
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        if (!contains){
            queryWrapper.eq(SysUser::getUsername,username); //手机号查询
        }else {
            queryWrapper.eq(SysUser::getEmail,username); //邮箱查询
        }
        SysUser sysUser = sysUserService.getOne(queryWrapper);
        //update-end: 登录代码验证用户是否注销bug，if条件永远为false
        result = sysUserService.checkUserIsEffective(sysUser);
        if(!result.isSuccess()) {
            return result;
        }
        //邮箱和手机号 加密的结果不同  邮箱登录(后台最终还是手机号登录)
        if (contains){
            username = sysUser.getUsername();
        }
        //2. 校验用户名或密码是否正确
        String userpassword = PasswordUtil.encrypt(username, password, sysUser.getSalt());
        String syspassword = sysUser.getPassword();
        if (WxlConvertUtils.isEmpty(syspassword)||!syspassword.equals(userpassword)) {
            result.error500("用户名或密码错误");
            //密码错误删除验证码
            redisUtil.del(realKey);
            return result;
        }

        //用户登录信息
        userInfo(sysUser, result);
        //update-begin：登录成功，删除redis中的验证码
        redisUtil.del(realKey);
        //update-begin：登录成功，删除redis中的验证码
        LoginUser loginUser = new LoginUser();
        BeanUtils.copyProperties(sysUser, loginUser);
        baseCommonService.addLog("用户名: " + username + ",sta小程序账户密码登录成功！", CommonConstant.LOG_TYPE_1, null,loginUser);
        //update-end：登录日志没有记录人员
        return result;
    }
    /**
     * 手机号
     * @param jsonObject
     * @return
     */
    @ApiOperation("手机号登录接口")
    @PostMapping("/phoneLogin")
    public Result<JSONObject> phoneLogin(@RequestBody JSONObject jsonObject) {
        Result<JSONObject> result = new Result<JSONObject>();
        String phone = jsonObject.getString("mobile");
        //boolean contains = phone.contains("@"); //等于true 邮箱登录
       // SysUser sysUser = new SysUser();
        //校验用户有效性
        //if (!contains){
        SysUser  sysUser = sysUserService.getUserByPhone(phone);
      //  }else {
             sysUser = sysUserService.getOne(new QueryWrapper<SysUser>().eq("email",phone).eq("del_flag",CommonConstant.DEL_FLAG_0));
      //  }
        result = sysUserService.checkUserIsEffective(sysUser);
        if(!result.isSuccess()) {
            return result;
        }
        String smscode = jsonObject.getString("captcha");
        Object code = redisUtil.get(phone);
        if (WxlConvertUtils.isEmpty(smscode)||!smscode.equals(code)) {
            result.setMessage("验证码错误");
            return result;
        }
        //用户信息
        userInfo(sysUser, result);
        //添加日志
        baseCommonService.addLog("用户名: " + sysUser.getUsername() + ",登录成功！", CommonConstant.LOG_TYPE_1, null);

        return result;
    }
    /**
     * 用户注册接口
     *
     * @param jsonObject
     * @param user
     * @return
     */
    @PostMapping("/register")
    public Result<JSONObject> userRegister(@RequestBody JSONObject jsonObject, SysUser user) {
        Result<JSONObject> result = new Result<JSONObject>();
        //招聘官id
        String recruiter = jsonObject.getString("recruiter");
        String realname = jsonObject.getString("realname");
        String phone = jsonObject.getString("phone");
        String smscode = jsonObject.getString("smscode");
        Object code = redisUtil.get(phone);
        //TODO 测试时不用短信
//        smscode="111111";
//        code="111111";
        String username = jsonObject.getString("username");
        //未设置用户名，则用手机号作为用户名
        if(WxlConvertUtils.isEmpty(username)){
            username = phone;
        }
        //未设置密码，则随机生成一个密码
        String password = jsonObject.getString("password");
        if(WxlConvertUtils.isEmpty(password)){
            password = RandomUtil.randomString(8);
        }
        String email = jsonObject.getString("email");
        SysUser sysUser1 = sysUserService.getUserByName(username);
        if (sysUser1 != null) {
            result.setMessage("用户名已注册");
            result.setSuccess(false);
            return result;
        }
        SysUser sysUser2 = sysUserService.getUserByPhone(phone);
        if (sysUser2 != null) {
            result.setMessage("该手机号已注册");
            result.setSuccess(false);
            return result;
        }

        if(WxlConvertUtils.isNotEmpty(email)){
            SysUser sysUser3 = sysUserService.getUserByEmail(email);
            if (sysUser3 != null) {
                result.setMessage("邮箱已被注册");
                result.setSuccess(false);
                return result;
            }
        }
        if(null == code){
            result.setMessage("手机验证码失效，请重新获取");
            result.setSuccess(false);
            return result;
        }
        if (!smscode.equals(code.toString())) {
            result.setMessage("手机验证码错误");
            result.setSuccess(false);
            return result;
        }

        try {
            user.setCreateTime(new Date());// 设置创建时间
            user.setUpdateTime(new Date());// 设置更新时间
            String salt = WxlConvertUtils.randomGen(8);
            String passwordEncode = PasswordUtil.encrypt(username, password, salt);
            user.setSalt(salt);
            user.setUsername(username);
            if(WxlConvertUtils.isNotEmpty(realname)){
                user.setRealname(realname);//真实姓名
            }else{
                user.setRealname("匿名用户");//真实姓名
            }
            user.setPassword(passwordEncode);
            user.setEmail(email);
            user.setPhone(phone);
            user.setHourlySalary(14);//小时薪资默认14
            user.setUserIdentity(CommonConstant.USER_IDENTITY_STAFFING_USER);//灵活用工普通用户
            user.setStatus(CommonConstant.USER_UNFREEZE);
            user.setDelFlag(CommonConstant.DEL_FLAG_0);
            user.setActivitiSync(CommonConstant.ACT_SYNC_0);
            //添加推荐人id|招聘官id
            user = addRecruiterUser(user,recruiter);
//            sysUserService.addUserWithRole(user,"ee8626f80f7c2619917b6236f3a7f02b");//默认临时角色 test
            //添加用户 、角色、 名片、报名信息
            sysUserService.addUserWithRolAndEnrollAndInfo(user,null);

            //1. 注册后重新校验用户是否有效
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getPhone,phone);
            SysUser sysUser = sysUserService.getOne(queryWrapper);
            //2. 注册后重新校验用户是否有效
            result = sysUserService.checkUserIsEffective(sysUser);
            if(!result.isSuccess()) {
                return result;
            }
            //3. 注册后登陆
            userInfo(sysUser, result);
            LoginUser loginUser = new LoginUser();
            BeanUtils.copyProperties(sysUser, loginUser);
            baseCommonService.addLog("用户名: " + phone + ",小程序注册后登陆成功！", CommonConstant.LOG_TYPE_1, null,loginUser);
            result.success("注册成功");

        } catch (Exception e) {
            result.error500("注册失败");
        }
        return result;
    }
    private SysUser addRecruiterUser(SysUser user,String recruiter) {
        //判断是否有推荐人id|招聘官id
        if(WxlConvertUtils.isNotEmpty(recruiter)){
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getPhone,recruiter);//小程序二维码参数长度有限制，使用手机号
            SysUser recruiterUser = sysUserService.getOne(queryWrapper);
            if(WxlConvertUtils.isNotEmpty(recruiterUser)){
//                if(!CommonConstant.USER_IDENTITY_3.equals(recruiterUser.getUserIdentity())){
//                String departId = sysBaseAPI.getDepartIdsByOrgCode(recruiterUser.getOrgCode());
                LambdaQueryWrapper<SysUserDepart> sysUserDepartLambdaQueryWrapper = new LambdaQueryWrapper<>();
                sysUserDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId,recruiterUser.getId());
                List<SysUserDepart> listSysUserDepart = sysUserDepartService.list(sysUserDepartLambdaQueryWrapper);

                for(SysUserDepart sysUserDepart:listSysUserDepart) {
                    if (WxlConvertUtils.isNotEmpty(sysUserDepart.getDepId())) {
                        Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(recruiterUser.getUsername(), sysUserDepart.getDepId());
                        boolean poolBind = userPermissionSet.contains("staffing:pool:bind");//绑定并管理用户的权限
                        if (poolBind) {
                            //TODO 目前招聘官绑定的用户不分公司
                            //如果是管理员或招聘官 则直接绑定该用户的管理者
                            user.setBindUserId(recruiterUser.getId());
                            break;
                        }
                    }
                }
                user.setPromoterId(recruiterUser.getId());
            }
        }
        return user;
    }
    @PostMapping("/bs/wxLogin")
    public Result<JSONObject> bsWxLogin(@RequestBody JSONObject jsonObject) {
        System.out.println(jsonObject);
        Result<JSONObject> result = new Result<JSONObject>();
        //招聘官id
        String encryptedData = jsonObject.get("encryptedData").toString();
        String iv = jsonObject.get("iv").toString();
        String errMsg = jsonObject.get("errMsg").toString();
        String openid = jsonObject.get("openid").toString();
        if(!"getPhoneNumber:ok".equals(errMsg)){
            result.error500("登录失败,用户拒绝授权!");
            return result;
        }
        if(WxlConvertUtils.isEmpty(openid)||openid.length()!=28){
            result.error500("openid失效!");
            return result;
        }
        String sessionKey = String.valueOf(redisUtil.get(CommonConstant.SESSION_KEY_CACHE+"_staffing_"+openid));
        //用session_key解析加密手机号
        String decodedStr="";
        try {
            decodedStr = AesEncryptUtil.base64Decrypt(encryptedData,sessionKey,iv);
        } catch (Exception e) {
            log.info("openid: " + openid + ",解析失败！");
            result.setMessage("解析失败!");
            result.setSuccess(false);
            return result;
        }
        JSONObject jo = JSONObject.parseObject(decodedStr);
        if(jo.containsKey("phoneNumber")){
            String phone = jo.getString("phoneNumber");
            //1. 校验用户是否有效
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getPhone,phone);
            SysUser sysUser = sysUserService.getOne(queryWrapper);
            //注册用户
            if (sysUser == null) {
                //未设置密码，则随机生成一个密码
                String password = jsonObject.getString("password");
                if(WxlConvertUtils.isEmpty(password)){
                    password = RandomUtil.randomString(8);
                }
                SysUser sysUser1 = sysUserService.getUserByName(phone);
                if (sysUser1 != null) {
                    result.setMessage("用户名已注册");
                    result.setSuccess(false);
                    return result;
                }
                SysUser sysUser2 = sysUserService.getUserByPhone(phone);
                if (sysUser2 != null) {
                    result.setMessage("该手机号已注册");
                    result.setSuccess(false);
                    return result;
                }
                try {
                    SysUser user = new SysUser();
                    user.setCreateTime(new Date());// 设置创建时间
                    user.setUpdateTime(new Date());// 设置更新时间
                    String salt = WxlConvertUtils.randomGen(8);
                    String passwordEncode = PasswordUtil.encrypt(phone, password, salt);
                    user.setSalt(salt);
                    user.setUsername(phone);
                    user.setOpenId(openid);
                    user.setRealname("匿名用户");//真实姓名
                    user.setPassword(passwordEncode);
                    user.setPhone(phone);
                    user.setHourlySalary(14);//小时薪资默认14
                    user.setStatus(CommonConstant.USER_UNFREEZE);
                    user.setDelFlag(CommonConstant.DEL_FLAG_0);
                    user.setActivitiSync(CommonConstant.ACT_SYNC_0);
                    //添加推荐人id|招聘官id
//            sysUserService.addUserWithRole(user,"ee8626f80f7c2619917b6236f3a7f02b");//默认临时角色 test
//                    添加用户 、角色、 名片、报名信息
                    sysUserService.addUserWithRolAndEnrollAndInfo(user,null);
                    result.success("注册成功");
                    //2. 注册后重新校验用户是否有效
                    sysUser = sysUserService.getOne(queryWrapper);
                } catch (Exception e) {
                    result.error500("注册失败");
                    return result;
                }
            }
            //补充微信openID
            if (!StringUtils.isNotEmpty(sysUser.getOpenId())){
                sysUser.setOpenId(openid);
                sysUserService.updateById(sysUser);
            }
            //检测用户是否有效
            result = sysUserService.checkUserIsEffective(sysUser);
            if(!result.isSuccess()) {
                return result;
            }
            //用户登录信息
            userInfo(sysUser, result);
            LoginUser loginUser = new LoginUser();
            BeanUtils.copyProperties(sysUser, loginUser);
            baseCommonService.addLog("用户名: " + phone + ",sta小程序微信快捷登录成功！", CommonConstant.LOG_TYPE_1, null,loginUser);
            return result;
        }else{
            result.setMessage("未获取到手机号!");
            result.setSuccess(false);
            return result;
        }
    }
    /**
     * @description: 微信快捷登录
     * <AUTHOR>
     * @date 2021/11/16 14:24
     * @version 1.0
     */
    @PostMapping(value = "/wxLogin")
    public Result<JSONObject> wxLogin(@RequestBody JSONObject jsonObject) {
        Result<JSONObject> result = new Result<JSONObject>();
        //招聘官id
        String recruiter = jsonObject.getString("recruiter");
        String encryptedData = jsonObject.get("encryptedData").toString();
        String iv = jsonObject.get("iv").toString();
        String errMsg = jsonObject.get("errMsg").toString();
        String openid = jsonObject.get("openid").toString();
//        String formId = jsonObject.get("formId").toString();//小程序用户消息推送
        if(!"getPhoneNumber:ok".equals(errMsg)){
            result.error500("登录失败,用户拒绝授权!");
            return result;
        }

        if(WxlConvertUtils.isEmpty(openid)||openid.length()!=28){
            result.error500("openid失效!");
            return result;
        }

        String sessionKey = String.valueOf(redisUtil.get(CommonConstant.SESSION_KEY_CACHE+"_staffing_"+openid));
        //用session_key解析加密手机号
        String decodedStr="";
        try {
            decodedStr = AesEncryptUtil.base64Decrypt(encryptedData,sessionKey,iv);
        } catch (Exception e) {
            log.info("openid: " + openid + ",解析失败！");
            result.setMessage("解析失败!");
            result.setSuccess(false);
            return result;
        }
        //System.out.println("解析后字符串："+decodedStr);
        JSONObject jo = JSONObject.parseObject(decodedStr);
        if(jo.containsKey("phoneNumber")){
            String phone = jo.getString("phoneNumber");
            //1. 校验用户是否有效
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUser::getPhone,phone);
            SysUser sysUser = sysUserService.getOne(queryWrapper);
            //注册用户
            if (sysUser == null) {
                //未设置密码，则随机生成一个密码
                String password = jsonObject.getString("password");
                if(WxlConvertUtils.isEmpty(password)){
                    password = RandomUtil.randomString(8);
                }
                SysUser sysUser1 = sysUserService.getUserByName(phone);
                if (sysUser1 != null) {
                    result.setMessage("用户名已注册");
                    result.setSuccess(false);
                    return result;
                }
                SysUser sysUser2 = sysUserService.getUserByPhone(phone);
                if (sysUser2 != null) {
                    result.setMessage("该手机号已注册");
                    result.setSuccess(false);
                    return result;
                }
                try {
                    SysUser user = new SysUser();
                    String username = phone;
                    user.setCreateTime(new Date());// 设置创建时间
                    user.setUpdateTime(new Date());// 设置更新时间
                    String salt = WxlConvertUtils.randomGen(8);
                    String passwordEncode = PasswordUtil.encrypt(username, password, salt);
                    user.setSalt(salt);
                    user.setUsername(username);
                    user.setOpenId(openid);//后期用于小程序微信支付
//                    user.setRealname(username);
                    user.setRealname("匿名用户");//真实姓名
                    user.setPassword(passwordEncode);
//                    user.setEmail(null);
                    user.setPhone(phone);
                    user.setHourlySalary(14);//小时薪资默认14
                    user.setUserIdentity(CommonConstant.USER_IDENTITY_STAFFING_USER);//灵活用工普通用户
                    user.setStatus(CommonConstant.USER_UNFREEZE);
                    user.setDelFlag(CommonConstant.DEL_FLAG_0);
                    user.setActivitiSync(CommonConstant.ACT_SYNC_0);
                    //添加推荐人id|招聘官id
                    user = addRecruiterUser(user,recruiter);
//            sysUserService.addUserWithRole(user,"ee8626f80f7c2619917b6236f3a7f02b");//默认临时角色 test
//                    添加用户 、角色、 名片、报名信息
                    sysUserService.addUserWithRolAndEnrollAndInfo(user,null);
                    result.success("注册成功");
                    //2. 注册后重新校验用户是否有效
                    sysUser = sysUserService.getOne(queryWrapper);
                } catch (Exception e) {
                    result.error500("注册失败");
                    return result;
                }
            }
             //补充微信openID 微信支付转账对象
            if (!StringUtils.isNotEmpty(sysUser.getOpenId())){
                sysUser.setOpenId(openid);
                sysUserService.updateById(sysUser);
            }
            result = sysUserService.checkUserIsEffective(sysUser);
            if(!result.isSuccess()) {
                return result;
            }
            //用户登录信息
            userInfo(sysUser, result);
            LoginUser loginUser = new LoginUser();
            BeanUtils.copyProperties(sysUser, loginUser);
            baseCommonService.addLog("用户名: " + phone + ",sta小程序微信快捷登录成功！", CommonConstant.LOG_TYPE_1, null,loginUser);
            return result;
        }else{
            result.setMessage("未获取到手机号!");
            result.setSuccess(false);
            return result;
        }
    }
    /**
     * 退出登录
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/logout")
    public Result<Object> logout(HttpServletRequest request, HttpServletResponse response) {
        //用户退出逻辑
        String token = request.getHeader(CommonConstant.X_ACCESS_TOKEN);
        if(WxlConvertUtils.isEmpty(token)) {
            return Result.error("退出登录失败！");
        }
        String username = JwtUtil.getUsername(token);
        LoginUser sysUser = sysBaseAPI.getUserByName(username);
        if(sysUser!=null) {
            //update-begin：登出日志没有记录人员
            baseCommonService.addLog("用户名: "+sysUser.getRealname()+",退出成功！", CommonConstant.LOG_TYPE_1, null,sysUser);
            //update-end：登出日志没有记录人员
            log.info(" 用户名:  "+sysUser.getRealname()+",退出成功！ ");
            //清空用户登录Token缓存
            redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
            //清空用户登录Shiro权限缓存
            redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + sysUser.getId());
            //清空用户的缓存信息（包括部门信息），例如sys:cache:user::<username>
            redisUtil.del(String.format("%s::%s", CacheConstant.SYS_USERS_CACHE, sysUser.getUsername()));
            //调用shiro的logout
            SecurityUtils.getSubject().logout();
            return Result.ok("退出登录成功！");
        }else {
            return Result.error("Token无效!");
        }
    }

    /**
     * 登陆成功选择用户当前部门
     * @param user
     * @return
     */
    @RequestMapping(value = "/selectDepart", method = RequestMethod.PUT)
    public Result<JSONObject> selectDepart(@RequestBody SysUser user) {
        Result<JSONObject> result = new Result<JSONObject>();
        String username = user.getUsername();
        if(WxlConvertUtils.isEmpty(username)) {
            LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
            username = sysUser.getUsername();
        }
        String orgCode= user.getOrgCode();
        this.sysUserService.updateUserDepart(username, orgCode);
        SysUser sysUser = sysUserService.getUserByName(username);
        //用户登录信息
        userInfo(sysUser, result);
        return result;
    }

    /**
     * 用户信息
     *
     * @param sysUser
     * @param result
     * @return
     */
    private Result<JSONObject> userInfo(SysUser sysUser, Result<JSONObject> result) {
        String syspassword = sysUser.getPassword();
        String username = sysUser.getUsername();
        // 获取用户部门信息
        JSONObject obj = new JSONObject();
        List<SysDepart> departs = sysDepartService.queryUserDeparts(sysUser.getId());
        obj.put("departs", departs);
        if (departs == null || departs.size() == 0) {
            obj.put("multi_depart", 0);
        } else if (departs.size() == 1) {
            sysUserService.updateUserDepart(username, departs.get(0).getOrgCode());
            obj.put("multi_depart", 1);
        } else {
            //查询当前是否有登录部门
            // update-begin：如果用戶为选择部门，数据库为存在上一次登录部门，则取一条存进去
            SysUser sysUserById = sysUserService.getById(sysUser.getId());
            if(WxlConvertUtils.isEmpty(sysUserById.getOrgCode())){
                sysUserService.updateUserDepart(username, departs.get(0).getOrgCode());
            }
            // update-end：如果用戶为选择部门，数据库为存在上一次登录部门，则取一条存进去
            obj.put("multi_depart", 2);
        }
        // update-begin：获取用户租户信息
        String tenantIds = sysUser.getRelTenantIds();
        if (WxlConvertUtils.isNotEmpty(tenantIds)) {
            List<Integer> tenantIdList = new ArrayList<>();
            for(String id: tenantIds.split(SymbolConstant.COMMA)){
                tenantIdList.add(Integer.valueOf(id));
            }
            // 该方法仅查询有效的租户，如果返回0个就说明所有的租户均无效。
            List<SysTenant> tenantList = sysTenantService.queryEffectiveTenant(tenantIdList);
            if (tenantList.size() == 0) {
                result.error500("与该用户关联的租户均已被冻结，无法登录！");
                return result;
            } else {
                obj.put("tenantList", tenantList);
            }
        }
        // update-end：获取用户租户信息

        //获取用户权限
        JSONArray menujsonArray = new JSONArray();
        Map authList = new HashMap();//权限列表
        JSONArray allauthjsonArray = new JSONArray();
        JSONArray children = new JSONArray();//菜单列表
        //直接获取公司权限,小程序目前只按公司权限判断
        SysUser sysUserById = sysUserService.getById(sysUser.getId());
        String orgCode = sysUserById.getOrgCode();//当前登录公司
        if(WxlConvertUtils.isNotEmpty(orgCode)) {
            String departId = sysBaseAPI.getDepartIdsByOrgCode(orgCode);
            if(WxlConvertUtils.isNotEmpty(departId)){
                List<SysPermission> metaList = sysPermissionService.queryStaffingByUserAndDepartId(username,departId);
                //菜单
                this.getPermissionJsonArray(menujsonArray, metaList, null);
                //一级菜单下的子菜单全部是隐藏路由，则一级菜单不显示
                this.handleFirstLevelMenuHidden(menujsonArray);
                //按钮或权限
                this.getAuthStringMap(authList, metaList);
                //查询灵工小程序所有的权限
                LambdaQueryWrapper<SysPermission> query = new LambdaQueryWrapper<SysPermission>();
                query.apply("perms"+" like {0}", "staffing%");
                query.eq(SysPermission::getDelFlag, CommonConstant.DEL_FLAG_0);
                query.eq(SysPermission::getMenuType, CommonConstant.MENU_TYPE_2);
                //query.eq(SysPermission::getStatus, "1");
                List<SysPermission> allAuthList = sysPermissionService.list(query);
                this.getAllAuthJsonArray(allauthjsonArray, allAuthList);
                if(menujsonArray.size()>0){//避免公司未设置权限时异常
                    JSONObject jo = menujsonArray.getJSONObject(0);
                    if (0 == "staffing/".compareTo(jo.getString("component"))) {
                        children = jo.getJSONArray("children");
                    }
                }
            }
        }
        //17700626323
        //判断用户是否已存在登陆token
        Collection<String> keys = redisTemplate.keys(CommonConstant.PREFIX_USER_TOKEN + "*");
        String token = null;
        for (String key : keys) {
            String tokenItem = (String)redisUtil.get(key);
            if (StringUtils.isNotEmpty(tokenItem)) {
                String tokenUsername = JwtUtil.getUsername(tokenItem);
                if(username.equals(tokenUsername)){
                    if(WxlConvertUtils.isEmpty(token)){
                        token=key.replaceAll(CommonConstant.PREFIX_USER_TOKEN,"");
                    }
                }
            }
        }
        // 生成token
        if(WxlConvertUtils.isEmpty(token)){
            token = JwtUtil.sign(username, syspassword);
            // 设置token缓存有效时间
            redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
            redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        }
        obj.put("token", token);
        StaUserBankCards staUserBankCards = staUserBankCardsService.getOne(new QueryWrapper<StaUserBankCards>().lambda().eq(StaUserBankCards::getUserId, sysUser.getId()));
        if (WxlConvertUtils.isNotEmpty(staUserBankCards)) {
            sysUser.setAccountType(staUserBankCards.getAccountType());
        }
        JSONObject userJson = (JSONObject) JSONObject.toJSON(sysUser);
        //部门选项
//        userJson.put("multi_depart",obj.get("multi_depart"));
//        if(ByunConvertUtils.isNotEmpty(orgCode)){
//            String departId = sysBaseAPI.getDepartIdsByOrgCode(orgCode);
//            if(ByunConvertUtils.isNotEmpty(departId)){
//                userJson.put("departId",departId);
//            }
//        }
        if(WxlConvertUtils.isNotEmpty(orgCode)){
            String departId = sysBaseAPI.getDepartIdsByOrgCode(orgCode);
            if(WxlConvertUtils.isNotEmpty(departId)){
                SysDepart byId = sysDepartService.getById(departId);
                if(WxlConvertUtils.isNotEmpty(byId)){
                    userJson.put("departId",byId.getId());
                    userJson.put("departName",byId.getDepartName());
                }
            }
        }

        //路由菜单
        userJson.put("menu", children);
        //按钮权限（用户拥有的权限集合）
        userJson.put("auth", authList);
        //全部权限配置集合（按钮权限，访问权限）
        userJson.put("allAuth", allauthjsonArray);
        userJson.put("sysSafeMode", byunBaseConfig.getSafeMode());
        obj.put("userInfo", userJson);
        obj.put("sysAllDictItems", sysDictService.queryAllDictItems());
        result.setResult(obj);
        result.success("登录成功");
        return result;
    }

    /**
     *  获取菜单JSON数组
     * @param jsonArray
     * @param metaList
     * @param parentJson
     */
    private void getPermissionJsonArray(JSONArray jsonArray, List<SysPermission> metaList, JSONObject parentJson) {
        for (SysPermission permission : metaList) {
            if (permission.getMenuType() == null) {
                continue;
            }
            String tempPid = permission.getParentId();
            JSONObject json = getPermissionJsonObject(permission);
            if(json==null) {
                continue;
            }
            if (parentJson == null && WxlConvertUtils.isEmpty(tempPid)) {
                //顶级菜单
                jsonArray.add(json);
                if (!permission.isLeaf()) {
                    getPermissionJsonArray(jsonArray, metaList, json);
                }
            } else if (parentJson != null && WxlConvertUtils.isNotEmpty(tempPid) && tempPid.equals(parentJson.getString("id"))) {
                // 类型( 0：一级菜单 1：子菜单 2：按钮 )
                if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_2)) {
                    JSONObject metaJson = parentJson.getJSONObject("meta");
                    if (metaJson.containsKey("permissionList")) {
                        metaJson.getJSONArray("permissionList").add(json);
                    } else {
                        JSONArray permissionList = new JSONArray();
                        permissionList.add(json);
                        metaJson.put("permissionList", permissionList);
                    }
                    // 类型( 0：一级菜单 1：子菜单 2：按钮 )
                } else if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_1) || permission.getMenuType().equals(CommonConstant.MENU_TYPE_0)) {
                    if (parentJson.containsKey("children")) {
                        parentJson.getJSONArray("children").add(json);
                    } else {
                        JSONArray children = new JSONArray();
                        children.add(json);
                        parentJson.put("children", children);
                    }

                    if (!permission.isLeaf()) {
                        getPermissionJsonArray(jsonArray, metaList, json);
                    }
                }
            }

        }
    }

    /**
     * 一级菜单的子菜单全部是隐藏路由，则一级菜单不显示
     * @param jsonArray
     */
    private void handleFirstLevelMenuHidden(JSONArray jsonArray) {
        jsonArray = jsonArray.stream().map(obj -> {
            JSONObject returnObj = new JSONObject();
            JSONObject jsonObj = (JSONObject)obj;
            if(jsonObj.containsKey("children")){
                JSONArray childrens = jsonObj.getJSONArray("children");
                childrens = childrens.stream().filter(arrObj -> !"true".equals(((JSONObject) arrObj).getString("hidden"))).collect(Collectors.toCollection(JSONArray::new));
                if(childrens==null || childrens.size()==0){
                    jsonObj.put("hidden",true);

                    //vue3版本兼容代码
                    JSONObject meta = new JSONObject();
                    meta.put("hideMenu",true);
                    jsonObj.put("meta", meta);
                }
            }
            return returnObj;
        }).collect(Collectors.toCollection(JSONArray::new));
    }

    /**
     *  获取权限JSON数组
     * @param jsonArray
     * @param metaList
     */
    private void getAuthJsonArray(JSONArray jsonArray,List<SysPermission> metaList) {
        for (SysPermission permission : metaList) {
            if(permission.getMenuType()==null) {
                continue;
            }
            JSONObject json = null;
            if(permission.getMenuType().equals(CommonConstant.MENU_TYPE_2) &&CommonConstant.STATUS_1.equals(permission.getStatus())) {
                json = new JSONObject();
                json.put("action", permission.getPerms());
                json.put("type", permission.getPermsType());
                json.put("describe", permission.getName());
                jsonArray.add(json);
            }
        }
    }

    /**
     *  获取权限String
     * @param map
     * @param metaList
     */
    private void getAuthStringMap(Map map, List<SysPermission> metaList) {
        for (SysPermission permission : metaList) {
            if(permission.getMenuType()==null) {
                continue;
            }
            if(permission.getMenuType().equals(CommonConstant.MENU_TYPE_2) &&CommonConstant.STATUS_1.equals(permission.getStatus())) {
                map.put(permission.getPerms(),true);
            }
        }
    }

    /**
     *  获取权限JSON数组
     * @param jsonArray
     * @param allList
     */
    private void getAllAuthJsonArray(JSONArray jsonArray,List<SysPermission> allList) {
        JSONObject json = null;
        for (SysPermission permission : allList) {
            json = new JSONObject();
            json.put("action", permission.getPerms());
            json.put("status", permission.getStatus());
            //1显示2禁用
            json.put("type", permission.getPermsType());
            json.put("describe", permission.getName());
            jsonArray.add(json);
        }
    }
    /**
     * 根据菜单配置生成路由json
     * @param permission
     * @return
     */
    private JSONObject getPermissionJsonObject(SysPermission permission) {
        JSONObject json = new JSONObject();
        // 类型(0：一级菜单 1：子菜单 2：按钮)
        if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_2)) {
            //json.put("action", permission.getPerms());
            //json.put("type", permission.getPermsType());
            //json.put("describe", permission.getName());
            return null;
        } else if (permission.getMenuType().equals(CommonConstant.MENU_TYPE_0) || permission.getMenuType().equals(CommonConstant.MENU_TYPE_1)) {
            json.put("id", permission.getId());
            if (permission.isRoute()) {
                json.put("route", "1");// 表示生成路由
            } else {
                json.put("route", "0");// 表示不生成路由
            }

            if (isWWWHttpUrl(permission.getUrl())) {
                json.put("path", MD5Util.MD5Encode(permission.getUrl(), "utf-8"));
            } else {
                json.put("path", permission.getUrl());
            }

            // 重要规则：路由name (通过URL生成路由name,路由name供前端开发，页面跳转使用)
            if (WxlConvertUtils.isNotEmpty(permission.getComponentName())) {
                json.put("name", permission.getComponentName());
            } else {
                json.put("name", urlToRouteName(permission.getUrl()));
            }

            JSONObject meta = new JSONObject();
            // 是否隐藏路由，默认都是显示的
            if (permission.isHidden()) {
                json.put("hidden", true);
                //vue3版本兼容代码
                meta.put("hideMenu",true);
            }
            // 聚合路由
            if (permission.isAlwaysShow()) {
                json.put("alwaysShow", true);
            }
            json.put("component", permission.getComponent());
            // 由用户设置是否缓存页面 用布尔值
            if (permission.isKeepAlive()) {
                meta.put("keepAlive", true);
            } else {
                meta.put("keepAlive", false);
            }

            /*update_begin:往菜单信息里添加外链菜单打开方式 */
            //外链菜单打开方式
            if (permission.isInternalOrExternal()) {
                meta.put("internalOrExternal", true);
            } else {
                meta.put("internalOrExternal", false);
            }
            /* update_end : 往菜单信息里添加外链菜单打开方式*/

            meta.put("title", permission.getName());

            //update-begin：路由缓存问题，关闭了tab页时再打开就不刷新 #842
            String component = permission.getComponent();
            if(WxlConvertUtils.isNotEmpty(permission.getComponentName()) || WxlConvertUtils.isNotEmpty(component)){
                meta.put("componentName", WxlConvertUtils.getString(permission.getComponentName(),component.substring(component.lastIndexOf("/")+1)));
            }
            //update-end：路由缓存问题，关闭了tab页时再打开就不刷新 #842

            if (WxlConvertUtils.isEmpty(permission.getParentId())) {
                // 一级菜单跳转地址
                json.put("redirect", permission.getRedirect());
                if (WxlConvertUtils.isNotEmpty(permission.getIcon())) {
                    meta.put("icon", permission.getIcon());
                }
            } else {
                if (WxlConvertUtils.isNotEmpty(permission.getIcon())) {
                    meta.put("icon", permission.getIcon());
                }
            }
            if (isWWWHttpUrl(permission.getUrl())) {
                meta.put("url", permission.getUrl());
            }
            // update-begin：新增适配vue3项目的隐藏tab功能
            if (permission.isHideTab()) {
                meta.put("hideTab", true);
            }
            // update-end：新增适配vue3项目的隐藏tab功能
            json.put("meta", meta);
        }

        return json;
    }

    /**
     * 判断是否外网URL 例如： http://localhost:8080/byun/swagger-ui.html#/ 支持特殊格式： {{
     * window._CONFIG['domianURL'] }}/druid/ {{ JS代码片段 }}，前台解析会自动执行JS代码片段
     *
     * @return
     */
    private boolean isWWWHttpUrl(String url) {
        if (url != null && (url.startsWith("http://") || url.startsWith("https://") || url.startsWith("{{"))) {
            return true;
        }
        return false;
    }

    /**
     * 通过URL生成路由name（去掉URL前缀斜杠，替换内容中的斜杠‘/’为-） 举例： URL = /isystem/role RouteName =
     * isystem-role
     *
     * @return
     */
    private String urlToRouteName(String url) {
        if (WxlConvertUtils.isNotEmpty(url)) {
            if (url.startsWith("/")) {
                url = url.substring(1);
            }
            url = url.replace("/", "-");

            // 特殊标记
            url = url.replace(":", "@");
            return url;
        } else {
            return null;
        }
    }

}
