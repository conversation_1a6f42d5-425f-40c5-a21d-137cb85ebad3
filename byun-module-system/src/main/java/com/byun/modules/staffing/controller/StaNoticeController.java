package com.byun.modules.staffing.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaNotice;
import com.byun.modules.staffing.service.IStaNoticeService;


import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.service.ISysDepartService;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 用于发布协议，入职须知等
 * @Author: bai
 * @Date:   2022-07-04
 * @Version: V1.0
 */
@Api(tags="用于发布协议，入职须知等")
@RestController
@RequestMapping("/staffing/notice")
@Slf4j
public class StaNoticeController extends ByunExcelController<StaNotice, IStaNoticeService> {
	@Autowired
	private IStaNoticeService staNoticeService;
	@Autowired
	private ISysDepartService sysDepartService;

	 /**
	  * 列表查询
	  *
	  * @param staNotice
	  * @param req
	  * @return
	  */
	 @AutoLog(value = "用于发布协议，入职须知等-分页列表查询")
	 @ApiOperation(value="用于发布协议，入职须知等-分页列表查询", notes="用于发布协议，入职须知等-分页列表查询")
	 @GetMapping(value = "/pcList")
	 public Result<?> queryListPc(StaNotice staNotice,
									HttpServletRequest req) {

		 LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 //查询所有负责部门
		 List<String> departIdArr = null;
		 //查询部门 和下级部门
		 if(WxlConvertUtils.isNotEmpty(user.getOrgCode())){
			 departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
		 }
		 if(WxlConvertUtils.isEmpty(departIdArr)||departIdArr.size()==0){
			 return Result.error("未获取登录部门");
		 }
		 QueryWrapper<StaNotice> queryWrapper = QueryGenerator.initQueryWrapper(staNotice, req.getParameterMap());
		 queryWrapper.in("company_id",departIdArr);
		 List<StaNotice> list = staNoticeService.list(queryWrapper);
		 return Result.OK(list);
	 }

	/**
	 * 分页列表查询
	 *
	 * @param staNotice
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用于发布协议，入职须知等-分页列表查询")
	@ApiOperation(value="用于发布协议，入职须知等-分页列表查询", notes="用于发布协议，入职须知等-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaNotice staNotice,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		//查询所有负责部门
		List<String> departIdArr = null;
		//查询部门 和下级部门
		if(WxlConvertUtils.isNotEmpty(user.getOrgCode())){
			departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
		}
		if(WxlConvertUtils.isEmpty(departIdArr)||departIdArr.size()==0){
			return Result.error("未获取登录部门");
		}
		QueryWrapper<StaNotice> queryWrapper = QueryGenerator.initQueryWrapper(staNotice, req.getParameterMap());
		if (WxlConvertUtils.isNotEmpty(staNotice.getCompanyId())){
			queryWrapper.eq("company_id",staNotice.getCompanyId());
		}
		if (WxlConvertUtils.isNotEmpty(staNotice.getNameFull())) {
			queryWrapper.like("name_full",staNotice.getNameFull());
		}
		queryWrapper.in("company_id",departIdArr);
		Page<StaNotice> page = new Page<StaNotice>(pageNo, pageSize);
		IPage<StaNotice> pageList = staNoticeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 * @param staNotice
	 * @return
	 */
	@AutoLog(value = "用于发布协议，入职须知等-添加")
	@ApiOperation(value="用于发布协议，入职须知等-添加", notes="用于发布协议，入职须知等-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaNotice staNotice,HttpServletRequest request) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if (WxlConvertUtils.isNotEmpty(staNotice.getCompanyId())) {
			SysDepart sysDepart = sysDepartService.getById(staNotice.getCompanyId());
			staNotice.setCompanyId(sysDepart.getId());
			staNotice.setCompanyName(sysDepart.getDepartName());
			staNotice.setFirmId(sysDepart.getId());
			staNotice.setFirmName(sysDepart.getDepartName());
			staNotice.setType(1);
			staNotice.setCreateTime(new Date());
			staNotice.setSysUserId(sysUser.getId());
			staNotice.setNameFull(staNotice.getNameNick());
		}
		staNoticeService.save(staNotice);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staNotice
	 * @return
	 */
	@AutoLog(value = "用于发布协议，入职须知等-编辑")
	@ApiOperation(value="用于发布协议，入职须知等-编辑", notes="用于发布协议，入职须知等-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaNotice staNotice) {
		if (WxlConvertUtils.isNotEmpty(staNotice.getId())) {
			StaNotice entityStaNotice = staNoticeService.getById(staNotice.getId());
			entityStaNotice.setContent(WxlConvertUtils.isNotEmpty(staNotice.getContent()) ? staNotice.getContent():entityStaNotice.getContent());
			entityStaNotice.setNameFull(staNotice.getNameNick());
			entityStaNotice.setNameNick(staNotice.getNameNick());
			staNoticeService.updateById(entityStaNotice);
			return Result.OK("编辑成功");
		}else {
			return Result.error("id丢失");
		}
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用于发布协议，入职须知等-通过id删除")
	@ApiOperation(value="用于发布协议，入职须知等-通过id删除", notes="用于发布协议，入职须知等-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staNoticeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用于发布协议，入职须知等-批量删除")
	@ApiOperation(value="用于发布协议，入职须知等-批量删除", notes="用于发布协议，入职须知等-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staNoticeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用于发布协议，入职须知等-通过id查询")
	@ApiOperation(value="用于发布协议，入职须知等-通过id查询", notes="用于发布协议，入职须知等-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaNotice staNotice = staNoticeService.getById(id);
		if(staNotice==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staNotice);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staNotice
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaNotice staNotice) {
        return super.exportXls(request, staNotice, StaNotice.class, "用于发布协议，入职须知等");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaNotice.class);
    }

}
