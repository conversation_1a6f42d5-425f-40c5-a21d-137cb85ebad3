package com.byun.modules.staffing.controller;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.DateUtils;
import com.byun.common.util.RedisUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.service.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.service.impl.StaLocationClockServiceImpl;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import com.sun.org.apache.xpath.internal.operations.Bool;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import com.byun.common.system.base.controller.ByunExcelController;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

/**
 * @Description: 排班表
 * @Author: bai
 * @Date: 2022-08-17
 * @Version: V1.0
 */
@Api(tags = "排班表")
@RestController
@RequestMapping("/staffing/schedule")
@Slf4j
public class StaScheduleController extends ByunExcelController<StaSchedule, IStaScheduleService> {
    @Autowired
    private IStaScheduleService staScheduleService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IStaLocationClockService staLocationClockService;
    @Autowired
    private IStaScheduleNoService staScheduleNoService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IStaWorkService staWorkService;
    @Autowired
    private RedisUtil redisUtil;
    /**
     * 获取顶级部门ID
     *
     * @param did 部门id
     * @return
     */
    public String getDepartTop(String did) {
        SysDepart byId = sysDepartService.getById(did);
        while (true) {
            if (WxlConvertUtils.isNotEmpty(byId.getParentId())) {
                byId = sysDepartService.getById(byId.getParentId());
            } else {
                break;
            }
        }
        return byId.getId();
    }

    /**
     * 网页单个添加排班或修改排班
     *
     * @param json
     * @return
     * @throws ParseException
     */
    @Transactional
    @PostMapping("/addSchedulingPc")
    @AutoLog(value = "排班操作",logType = 2,operateType = 1)
    public Result addPc(@RequestBody JSONObject json) throws ParseException {
        String oid = json.getString("oid");
        String scheduleDay = json.getString("scheduleDay");
        String code = json.getString("code");
        StaOrder staOrder = staOrderService.getById(oid);
        if (code.equals("///")) {//代表无排班
            List<StaSchedule> list = staScheduleService.list(new QueryWrapper<StaSchedule>().eq("sta_order_id", oid)
                    .likeRight("schedule_day", scheduleDay));
            if (!list.isEmpty()) {
                list.forEach(l -> l.setDelFlag(CommonConstant.DEL_FLAG_1));
                staScheduleService.removeByIds(list.stream().map(StaSchedule::getId).collect(Collectors.toList()));
            }
            return Result.OK();
        }
        List<StaScheduleNo> staScheduleNos = staScheduleNoService.list(new QueryWrapper<StaScheduleNo>()
                .eq("code", code).orderByAsc("start_time"));
        if (staScheduleNos == null || staScheduleNos.isEmpty()) {
            return Result.error("班别（" + code + "）不存在!");
        }
       /*
        Double thisWorkTime = 0.0;
        for (StaScheduleNo staScheduleNo : staScheduleNos) {
            thisWorkTime += staScheduleNo.getWorkTime();
        }
        //拿到任务单和当前日期就可以了，无需在意其他参数
        //TODO 工时管控
        Double monthWorkTime = staScheduleNoService.timeControl(staOrder,scheduleDay);
        Double totalMonthWorkTime = monthWorkTime + thisWorkTime;
        if (totalMonthWorkTime > 96) {
            return Result.error("排班失败本月工时超标:当前排班工时："+monthWorkTime +"  新增工时:"+thisWorkTime +"   合计工时"+totalMonthWorkTime,1000);
        }else if (totalMonthWorkTime < 95) {

        }else {
            List<StaSchedule> staSchedules = new ArrayList<>();
            SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
            for (StaScheduleNo staScheduleNo : staScheduleNos) {
                StaSchedule staSchedule = new StaSchedule();
                staSchedule.setStaOrderId(oid);
                staSchedule.setStaWorkId(staOrder.getStaWorkId());
                staSchedule.setCode(code);
                staSchedule.setDelFlag(CommonConstant.DEL_FLAG_0);
                staSchedule.setStartTime(staScheduleNo.getStartTime());
                staSchedule.setEndTime(staScheduleNo.getEndTime());
                //跨天
                staSchedule.setScheduleDay(ft.parse(scheduleDay));
                staSchedule.setUserIdCard(staOrder.getEnrollIdCard());
                staSchedule.setRealName(staOrder.getEnrollName());
                staSchedule.setSysUserId(staOrder.getSysUserId());
                staSchedules.add(staSchedule);
            }
            //保存或修改排班
            this.add(staSchedules, "1");
            List<String> scheduleIds = new ArrayList<>();
            for (StaSchedule staSchedule : staSchedules) {
                scheduleIds.add(staSchedule.getId());
            }
            List<StaSchedule> newStaSchedules = staScheduleService.listByIds(scheduleIds);
            //排班关联签到
            staScheduleService.addSchedule(oid, scheduleDay, newStaSchedules);
            return Result.OK("本月工时即将到达上限:当前排班工时："+monthWorkTime +"  新增工时:"+thisWorkTime +"  合计工时"+totalMonthWorkTime,2000);
        }
        **/
        List<StaSchedule> staSchedules = new ArrayList<>();
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        for (StaScheduleNo staScheduleNo : staScheduleNos) {
            StaSchedule staSchedule = new StaSchedule();
            staSchedule.setStaOrderId(oid);
            staSchedule.setStaWorkId(staOrder.getStaWorkId());
            staSchedule.setCode(code);
            staSchedule.setDelFlag(CommonConstant.DEL_FLAG_0);
            staSchedule.setStartTime(staScheduleNo.getStartTime());
            staSchedule.setEndTime(staScheduleNo.getEndTime());
            //跨天
            staSchedule.setScheduleDay(ft.parse(scheduleDay));
            staSchedule.setUserIdCard(staOrder.getEnrollIdCard());
            staSchedule.setRealName(staOrder.getEnrollName());
            staSchedule.setSysUserId(staOrder.getSysUserId());
            staSchedules.add(staSchedule);
        }
        //保存或修改排班
        this.add(staSchedules, "1");
        List<String> scheduleIds = new ArrayList<>();
        for (StaSchedule staSchedule : staSchedules) {
            scheduleIds.add(staSchedule.getId());
        }
        List<StaSchedule> newStaSchedules = staScheduleService.listByIds(scheduleIds);
        //排班关联签到
        staScheduleService.addSchedule(oid, scheduleDay, newStaSchedules);
        return Result.OK(2000);
    }
    /**
     * 添加排班表
     * @param list
     * @return
     */
    @AutoLog(value = "添加排班表AND批量排班")
    @ApiOperation(value = "添加排班表AND批量", notes = "添加排班表AND批量排班")
    @PostMapping(value = "/addBatch")
    public Result<String> add(@RequestBody List<StaSchedule> list, String type) throws ParseException {
        Result<String> result = new Result<String>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user) || WxlConvertUtils.isEmpty(user.getId())) {
            return result.error500("未获取到用户");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //订单id集合
        Set<String> setOids = list.stream().map(StaSchedule::getStaOrderId)
                .distinct().collect(Collectors.toSet());
        Set<Date> setScheduleDay = list.stream().map(StaSchedule::getScheduleDay).collect(Collectors.toSet());
        List<StaOrder> staOrders = staOrderService.listByIds(setOids);
//        int staLocationClocksCount = staLocationClockService.count(new QueryWrapper<StaLocationClock>()
//                .lambda()
//                .in(StaLocationClock::getStaOrderId, setOids)
//                .likeRight(StaLocationClock::getScheduleDay, setScheduleDay)
//                .eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0));
//        if (staLocationClocksCount > 0) {
//            return result.error500("已存在签到,无法修改排班");
//        }
        List<String> scheduleIds = new ArrayList<>();
        List<String> orderIdList = new ArrayList<>();
        List<Date> scheduleDayList = new ArrayList<>();
        for (StaSchedule staSchedule : list) { //添加任务ID
            if (staSchedule.getCode().equals("///")) {
                orderIdList.add(staSchedule.getStaOrderId());
                scheduleDayList.add(staSchedule.getScheduleDay());
            }
            staSchedule.setStaWorkId(staOrders.get(0).getStaWorkId());
            List<StaScheduleNo> staScheduleNos = staScheduleNoService.list(new QueryWrapper<StaScheduleNo>().lambda().eq(StaScheduleNo::getCode, staSchedule.getCode()));
            if (WxlConvertUtils.isEmpty(staScheduleNos) || staScheduleNos.isEmpty()) {
                if (staSchedule.getCode().equals("///")) { //删除排班
                }else {
                    String msg = "班别“" + staSchedule.getCode() + "”不存在";
                    return result.error500(msg);
                }
            }
        }
        if (WxlConvertUtils.isNotEmpty(list) && !list.isEmpty()) {
            String oid = list.get(0).getStaOrderId(); //任务单id
            String[] oids = oid.split(",");
            if (WxlConvertUtils.isNotEmpty(oid) || oids.length > 0) {
                for (int i = 0; i < oids.length; i++) {
                    int j = i;
                    //根据订单iD查询订单表
                    StaOrder order = staOrders.stream().filter(f -> f.getId().equals(oids[j])).findAny().get();
                    if (WxlConvertUtils.isNotEmpty(order)) {
                        for (StaSchedule ls : list) {
                            ls.setId("");
                            ls.setStaOrderId(oids[i]);
                            BeanUtils.copyProperties(ls, list);
                        }
                        Iterator<StaSchedule> iterator = list.iterator();
                        while (iterator.hasNext()) {//如果有元素返回true
                            StaSchedule schedule = iterator.next();//返回迭代的下一个参数
                            if (WxlConvertUtils.isNotEmpty(schedule.getScheduleDay())) {
                                schedule.setCreateBy(user.getUsername());
                                schedule.setUpdateBy(user.getUsername());
                                schedule.setCreateTime(new Date());
                                schedule.setUpdateTime(new Date());
                            } else {
                                iterator.remove();//删除刚返回的数据
                            }
                        }
                        if (list.size() > 0) {
                            staScheduleService.updateBatchScheduleForOrder(list, order);
                            if (type == null || !type.equals("1")) { //网页修改排班不干扰小程序修改
                                Map<Date, List<StaSchedule>> dateListMap = groupAndSortByDate(list);
                                //遍历
                                for (Map.Entry<Date, List<StaSchedule>> entry : dateListMap.entrySet()) {
                                    Date date = entry.getKey(); //key 排班日期
                                    List<StaSchedule> schedules = entry.getValue(); //排班数据
                                    for (StaSchedule schedule : schedules) {
                                        scheduleIds.add(schedule.getId());
                                    }
                                    List<StaSchedule> newStaSchedules = staScheduleService.listByIds(scheduleIds);
                                    staScheduleService.addSchedule(oid, sdf.format(date), newStaSchedules);
                                }
                            }
                        }
                    } else {
                        return result.error500("未找到任务单");
                    }
                }
            } else {
                return result.error500("未找到任务单id");
            }
            //删除排班编号未///的排班
            if (!orderIdList.isEmpty() && !scheduleDayList.isEmpty()) {
                List<StaSchedule> removeStaSchedules = staScheduleService.list(new LambdaQueryWrapper<StaSchedule>().in(StaSchedule::getStaOrderId, orderIdList)
                        .in(StaSchedule::getScheduleDay, scheduleDayList)
                );
                for (StaSchedule removeStaSchedule : removeStaSchedules) {
                    removeStaSchedule.setDelFlag(CommonConstant.DEL_FLAG_1);
                }
                staScheduleService.updateBatchById(removeStaSchedules);
            }
        }
        return Result.OK("添加成功");
    }

    private Map<Date, List<StaSchedule>> groupAndSortByDate(List<StaSchedule> list) {
        Map<Date, List<StaSchedule>> groupedByDate = list.stream()
                .collect(Collectors.groupingBy(obj -> truncateTime(obj.getScheduleDay())));
        Map<Date, List<StaSchedule>> sortedByDate = new TreeMap<>(groupedByDate);
        return sortedByDate;
    }

    // 将 Date 的时间部分截断，只保留日期部分
    private Date truncateTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当天的排班和签到
     *
     * @return
     */
//    @GetMapping("/getStaScheduleForOrderId/{orderId}")
//    @ApiOperation(value = "排班表-获取当天排班信息", notes = "排班表-获取当天排班信息")
//    public Result getStaScheduleForOrderId(@PathVariable("orderId") String orderId,HttpServletRequest request) throws ParseException {
//        if (WxlConvertUtils.isEmpty(orderId)){
//            return Result.error("获取签到信息失败");
//        }
//        //获取今天的日期yyyy-MM-dd格式
//        LocalDate today = LocalDate.now();
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        String scheduleDay = today.format(formatter);
//        LambdaQueryWrapper<StaSchedule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(StaSchedule::getStaOrderId,orderId);
//        lambdaQueryWrapper.eq(StaSchedule::getScheduleDay,scheduleDay);
//        lambdaQueryWrapper.eq(StaSchedule::getDelFlag,CommonConstant.DEL_FLAG_0);
//        //排班
//        List<StaSchedule> staSchedules = staScheduleService.list(lambdaQueryWrapper);
//        //签到
//        List<StaLocationClock> staLocationClocks = staLocationClockService.list(new LambdaQueryWrapper<StaLocationClock>()
//                .eq(StaLocationClock::getStaOrderId, orderId)
//                .likeRight(StaLocationClock::getTime,scheduleDay)
//                .isNotNull(StaLocationClock::getTime)
//                .orderByAsc(StaLocationClock::getTime));
//        if (staSchedules.size() > 0){
//            staSchedules = staScheduleService.getStaScheduleClock(staSchedules,staLocationClocks);
//        }else {
//            return Result.OK("2000",staLocationClocks);
//        }
//        return Result.OK(staSchedules);
//    }
    @ApiOperation(value = "排班表-订单查询", notes = "排班表-订单查询")
    @GetMapping(value = "/getListForOrder")
    public Result<List<StaSchedule>> getListForOrder(StaSchedule staSchedule,
                                                     HttpServletRequest req) throws ParseException {
        Result<List<StaSchedule>> result = new Result<List<StaSchedule>>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user) || WxlConvertUtils.isEmpty(user.getId())) {
            return result.error500("未获取到登录用户");
        }
        if (WxlConvertUtils.isEmpty(staSchedule.getStaOrderId())) {
            return result.error500("未获取到订单id");
        }
        Map<String, String[]> parameterMap = req.getParameterMap();
        String[] staOrderIds = parameterMap.get("staOrderId");
        String orderId = staOrderIds[0];
        QueryWrapper<StaSchedule> queryWrapper = QueryGenerator.initQueryWrapper(staSchedule, req.getParameterMap());
        //System.out.println(req.getParameterMap());
        queryWrapper.eq("del_flag", CommonConstant.DEL_FLAG_0);
        queryWrapper.orderByAsc("schedule_day,start_time");
        //排班
        List<StaSchedule> list = staScheduleService.list(queryWrapper);
        list = list.stream()
                .filter(s -> s.getStartTime() != null && s.getEndTime() != null)
                .collect(Collectors.toList());
        //签到
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>()
                .eq("sta_order_id", orderId).orderByAsc("time"));
        /**
         * 当前数据和以前的数据
         */
        Date currentDate = new Date();
        List<StaSchedule> filteredData = new ArrayList<>();
        for (StaSchedule schedule : list) {
            if (schedule.getScheduleDay().equals(currentDate) || schedule.getScheduleDay().before(currentDate)) {
                filteredData.add(schedule);
            }
        }
        //无签到记录
        if (staLocationClocks.size() == 0) {
            filteredData.forEach(l -> l.setClockStateFlag(CommonConstant.CLOCK_STATE_FLAG_0));
            return Result.OK(list);
        }
        //排班填充签到状态
        for (StaSchedule schedule : filteredData) {
            for (int i = 0; i < staLocationClocks.size(); i++) {
                //非空验证
                //Date timeExpect = staLocationClocks.get(i).getTimeExpect();
                if (schedule != null && staLocationClocks.get(i) != null
                        && schedule.getScheduleDay() != null
                        && staLocationClocks.get(i).getTimeExpect() != null
                        && !schedule.getScheduleDay().equals(null)
                        && !staLocationClocks.get(i).getTimeExpect().equals(null)) {
                    if (isSameDay(schedule.getScheduleDay(), staLocationClocks.get(i).getTimeExpect())) {
                        //同一天
                        switch (staLocationClocks.get(i).getStateFlag()) {
                            case 2: //迟到
                                schedule.setClockStateFlag(staLocationClocks.get(i).getStateFlag());
                                break;
                            case 3://早退
                                schedule.setClockStateFlag(staLocationClocks.get(i).getStateFlag());
                                break;
                            case 4: //外勤
                                schedule.setClockStateFlag(staLocationClocks.get(i).getStateFlag());
                                break;
                            case 5: //外勤 迟到
                                schedule.setClockStateFlag(staLocationClocks.get(i).getStateFlag());
                                break;
                            case 6: //外勤 早退
                                schedule.setClockStateFlag(staLocationClocks.get(i).getStateFlag());
                                break;
                            case 7: //补卡
                                schedule.setClockStateFlag(staLocationClocks.get(i).getStateFlag());
                                break;
                            case 1: //正常这里不做处理
                                break;
                            default: //缺卡 不可能走到这里
                                schedule.setClockStateFlag(CommonConstant.CLOCK_STATE_FLAG_0);
                                break;
                        }
                        continue;
                    }
                }
            }
            if (WxlConvertUtils.isEmpty(schedule.getClockStateFlag())) {
                /**
                 * 二次循环签到记录判断是缺卡还是正常签到
                 */
                for (StaLocationClock staLocationClock : staLocationClocks) {
                    if (schedule.getScheduleDay().equals(null) && staLocationClock.getTimeExpect().equals(null)) {
                        if (!isSameDay(schedule.getScheduleDay(), staLocationClock.getTimeExpect())) {
                            //缺卡
                            schedule.setClockStateFlag(CommonConstant.CLOCK_STATE_FLAG_0);
                        } else {
                            //全天考勤正常
                            schedule.setClockStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
                        }
                    }
                }
            }
        }
        return Result.OK(list);
    }

    /**
     * 对比两个java.util.date日期是否是同一天
     *
     * @param date1
     * @param date2
     * @return boolean
     */
    public static boolean isSameDay(Date date1, Date date2) {
        Calendar currentCalendar = Calendar.getInstance();
        Calendar otherCalendar = Calendar.getInstance();
        currentCalendar.setTime(date1);
        otherCalendar.setTime(date2);
        // 判断是否是同一天
        boolean isSameDay = currentCalendar.get(Calendar.YEAR) == otherCalendar.get(Calendar.YEAR)
                && currentCalendar.get(Calendar.MONTH) == otherCalendar.get(Calendar.MONTH)
                && currentCalendar.get(Calendar.DAY_OF_MONTH) == otherCalendar.get(Calendar.DAY_OF_MONTH);
        return isSameDay;
    }

    /**
     * 分页列表查询
     *
     * @param staSchedule
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "排班表-分页列表查询")
    @ApiOperation(value = "排班表-分页列表查询", notes = "排班表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<StaSchedule>> queryPageList(StaSchedule staSchedule,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        QueryWrapper<StaSchedule> queryWrapper = QueryGenerator.initQueryWrapper(staSchedule, req.getParameterMap());
        Page<StaSchedule> page = new Page<StaSchedule>(pageNo, pageSize);
        IPage<StaSchedule> pageList = staScheduleService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param staSchedule
     * @return
     */
    @AutoLog(value = "排班表-添加")
    @ApiOperation(value = "排班表-添加", notes = "排班表-添加")
    //@RequiresPermissions("com.byun.modules.demo:sta_schedule:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody StaSchedule staSchedule) {
        staScheduleService.save(staSchedule);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param staSchedule
     * @return
     */
    @AutoLog(value = "排班表-编辑")
    @ApiOperation(value = "排班表-编辑", notes = "排班表-编辑")
    //@RequiresPermissions("com.byun.modules.demo:sta_schedule:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody StaSchedule staSchedule) {
        staScheduleService.updateById(staSchedule);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "排班表-通过id删除")
    @ApiOperation(value = "排班表-通过id删除", notes = "排班表-通过id删除")
    //@RequiresPermissions("com.byun.modules.demo:sta_schedule:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        staScheduleService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "排班表-批量删除")
    @ApiOperation(value = "排班表-批量删除", notes = "排班表-批量删除")
    //@RequiresPermissions("com.byun.modules.demo:sta_schedule:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.staScheduleService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "排班表-通过id查询")
    @ApiOperation(value = "排班表-通过id查询", notes = "排班表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<StaSchedule> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<StaSchedule> result = new Result<StaSchedule>();
        StaSchedule staSchedule = staScheduleService.getById(id);
        if (staSchedule == null) {
            return result.error500("未找到对应数据");
        }
        return result.OK(staSchedule);
    }


    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaSchedule.class);
    }

    /**
     * 获取订单排班数据
     *
     * @param json
     * @return
     */
    @PostMapping("/scheduleList")
    public Result scheduleList(@RequestBody JSONObject json) {
        Result result = new Result();
        String oid = json.getString("oid");
        String startDay = json.getString("startDay");
        String endDay = json.getString("endDay");
        Boolean flag = false;
        if (WxlConvertUtils.isNotEmpty(startDay) && WxlConvertUtils.isNotEmpty(endDay)) flag = true;
        List<StaSchedule> staSchedules = staScheduleService.list(
                new QueryWrapper<StaSchedule>()
                        .eq("sta_order_id", oid)
                        .eq("del_flag", CommonConstant.DEL_FLAG_0)
                        .between(flag, "schedule_day", startDay, endDay)
                        .orderByAsc("schedule_day")
        );
        result.setSuccess(true);
        result.setMessage("操作成功");
        result.setResult(staSchedules);
        return result;
    }

    @PostMapping("/pcScheduleList")
    public Result pcScheduleList(@RequestBody JSONObject json) {
        String oid = json.getString("oid");
        String startDay = json.getString("startDay");
        String endDay = json.getString("endDay");
        Boolean flag = false;
        if (WxlConvertUtils.isNotEmpty(startDay) && WxlConvertUtils.isNotEmpty(endDay)) flag = true;
        List<StaSchedule> staSchedules = staScheduleService.list(
                new QueryWrapper<StaSchedule>()
                        .eq("sta_order_id", oid)
                        .eq("del_flag", CommonConstant.DEL_FLAG_0)
                        .between(flag, "schedule_day", startDay, endDay)
                        .orderByAsc("schedule_day")
        );
        return Result.OK("操作成功", staSchedules);
    }


}
