package com.byun.modules.staffing.controller;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaSchedule;
import com.byun.modules.staffing.entity.StaScheduleNo;
import com.byun.modules.staffing.entity.StaWorkAgentRel;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaScheduleNoService;
import com.byun.modules.staffing.service.IStaWorkAgentRelService;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 排班编号
 * @date : 2022-9-23 8:55
 */
@Api(tags = "排班编号表")
@RestController
@RequestMapping("/staffing/scheduleNo")
@Slf4j
public class StaScheduleNoController {
    @Autowired
    private IStaScheduleNoService staScheduleNoService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IStaWorkAgentRelService staWorkAgentRelService;
    @Autowired
    private IStaOrderService staOrderService;


    /**
     * 根据排班编号获取排班时间段
     *
     * @param code
     * @return
     */
    @ApiOperation(value = "灵活用工-根据排班编号查询排班日期", notes = "灵活用工-根据排班编号查询排班日期")
    @GetMapping("/getDateByCode/{code}/{oid}")
    public Result getDateByCode(@PathVariable("code") String code, @PathVariable("oid") String oid, HttpServletRequest req) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<StaScheduleNo> result = new Result<>();
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            return result.error("未获取登录部门");
        }
        String[] oids = oid.split(",");
        //登录部门需要订单中的部门对应
        StaOrder staOrder = staOrderService.getById(oids[0]); //订单
        SysDepart sysDepart = sysDepartService.getById(staOrder.getCompanyId()); //部门
        if (!WxlConvertUtils.isEmpty(sysDepart)) {
            List<StaScheduleNo> staScheduleNo = staScheduleNoService.list(
                    new QueryWrapper<StaScheduleNo>()
                            .eq("code", code));
            if (staScheduleNo.size() != 0) {
                return result.OK("操作成功", staScheduleNo);
            } else if (staScheduleNo.size() == 0) {
                List<StaScheduleNo> sn = staScheduleNoService.list(new
                        QueryWrapper<StaScheduleNo>()
                        .eq("code", code));
                if (!WxlConvertUtils.isEmpty(sn) && sn.size() != 0) {
                    return result.OK("操作成功", sn);
                } else {
                    return result.error("查无此编号");
                }
            } else {
                return result.error("查无此编号");
            }
        } else {
            return result.error("未查询到该订单");
        }
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "排班编号-分页列表查询(管理员)")
    @ApiOperation(value = "排班编号-分页列表查询(管理员)", notes = "排班编号-分页列表查询(管理员)")
    @GetMapping("/staScheduleNoListByAdmin")
    public Result<?> staScheduleNoListByAdmin(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                              HttpServletRequest req) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未获取登录部门");
        }
        Map<String, String[]> parameterMap = req.getParameterMap();
        String[] codes = parameterMap.get("code");
        String[] startTimes = parameterMap.get("startTime");
        String[] endTimes = parameterMap.get("endTime");
        LambdaQueryWrapper<StaScheduleNo> staScheduleNoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staScheduleNoLambdaQueryWrapper.orderByDesc(StaScheduleNo::getCreateTime);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat sdf2 = new SimpleDateFormat("HH:mm");
        if (WxlConvertUtils.isNotEmpty(codes) && codes.length > 0) {
            String code = codes[0];
            staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getCode, code);
        }
        if (WxlConvertUtils.isNotEmpty(startTimes) && startTimes.length > 0) {
            String startTime = startTimes[0];
            Date parse = sdf.parse(startTime);
            String format = sdf2.format(parse);
            staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getStartTime, sdf2.parse(format));
        }
        if (WxlConvertUtils.isNotEmpty(endTimes) && endTimes.length > 0) {
            String endTime = endTimes[0];
            Date parse = sdf.parse(endTime);
            String format = sdf2.format(parse);
            staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getEndTime, sdf2.parse(format));
        }
        IPage<StaScheduleNo> page = new Page<>(pageNo, pageSize);
        return Result.OK(staScheduleNoService.page(page, staScheduleNoLambdaQueryWrapper));
    }
    @ApiOperation(value = "排班班别-新增", notes = "排班班别-新增")
    @PostMapping("/add")
    @CacheEvict(value = {CacheConstant.SYS_DEPARTS_CACHE, CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries = true)
    public Result add(@RequestBody StaScheduleNo staScheduleNo, HttpServletRequest request) throws IOException, ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<StaScheduleNo> result = new Result<StaScheduleNo>();
        if (WxlConvertUtils.isEmpty(user)) {//未登录
            result.error500("登录失效");
            return result;
        }
        if (WxlConvertUtils.isEmpty(staScheduleNo.getCode())) {
            result.error500("版别代码不允许为空");
            return result;
        }
        int count = staScheduleNoService.count(new QueryWrapper<StaScheduleNo>()
                .lambda()
                .eq(StaScheduleNo::getCode, staScheduleNo.getCode()));
        if (count >= 2) {
            result.error500("当前班别代码已达到上限");
            return result;
        }
        staScheduleNo.setCreateBy(user.getUsername());
        staScheduleNo.setCreateTime(new DateTime());
        staScheduleNo.setDelFlag(CommonConstant.DEL_FLAG_0);
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String startTime = format.format(staScheduleNo.getStartTime());
        String endTime = format.format(staScheduleNo.getEndTime());
        staScheduleNo.setStartTime(format.parse(startTime));
        staScheduleNo.setEndTime(format.parse(endTime));
        //计算时长
        LocalTime sd = LocalTime.parse(startTime);
        LocalTime ed = LocalTime.parse(endTime);
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), sd);
        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), ed);
        // 如果结束时间在开始时间之前，表示时间跨越了一天
        if (endDateTime.isBefore(startDateTime)) {
            endDateTime = endDateTime.plusDays(1);
        }
        Duration duration = Duration.between(startDateTime, endDateTime);
        long hours = duration.toHours(); // 获取小时数
        long totalMinutes = duration.toMinutes(); // 获取总分钟数
        long minutes = totalMinutes % 60; // 获取剩余的分钟数
        double workTime = hours + (double) minutes / 60.0; // 将分钟转换为小时的小数部分
        staScheduleNo.setWorkTime(workTime);
        staScheduleNoService.save(staScheduleNo);
        result.setSuccess(true);
        result.setMessage("操作成功");
        return result;
    }
    @ApiOperation(value = "排班编号-修改", notes = "排班编号-修改")
    @PostMapping("/edit")
    @CacheEvict(value = {CacheConstant.SYS_DEPARTS_CACHE, CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries = true)
    public Result edit(@RequestBody StaScheduleNo staScheduleNo, HttpServletRequest request) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result result = new Result<>();
        if (WxlConvertUtils.isEmpty(user)) {//未登录
            result.error500("登录失效");
            return result;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        staScheduleNo.setUpdateTime(new Date());
        Date startTime = staScheduleNo.getStartTime();
        Date endTime = staScheduleNo.getEndTime();
        //计算时长
        LocalTime sd = LocalTime.parse(sdf.format(startTime));
        LocalTime ed = LocalTime.parse(sdf.format(endTime));
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), sd);
        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), ed);
        // 如果结束时间在开始时间之前，表示时间跨越了一天
        if (endDateTime.isBefore(startDateTime)) {
            endDateTime = endDateTime.plusDays(1);
        }
        Duration duration = Duration.between(startDateTime, endDateTime);
        long hours = duration.toHours(); // 获取小时数
        long totalMinutes = duration.toMinutes(); // 获取总分钟数
        long minutes = totalMinutes % 60; // 获取剩余的分钟数
        double workTime = hours + (double) minutes / 60.0; // 将分钟转换为小时的小数部分
        staScheduleNo.setWorkTime(workTime);
        staScheduleNoService.updateById(staScheduleNo);
        result.setSuccess(true);
        result.setMessage("操作成功");
        return result;
    }
    @ApiOperation(value = "排班编号-删除", notes = "排班编号-删除")
    @CacheEvict(value = {CacheConstant.SYS_DEPARTS_CACHE, CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries = true)
    @DeleteMapping("/remove")
    public Result remove(@RequestParam("id") String id) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<StaScheduleNo> result = new Result<StaScheduleNo>();
        if (WxlConvertUtils.isEmpty(user)) {//未登录
            result.error500("登录失效");
            return result;
        }
        staScheduleNoService.remove(new QueryWrapper<StaScheduleNo>().eq("id", id));
        return result.success("操作成功");
    }

    /**
     * 班别时间获取班别代码
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/getByDate")
    public Result<?> getByDate(@RequestParam("startTime") String startTime,
                               @RequestParam("endTime") String endTime){
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(startTime) && WxlConvertUtils.isEmpty(endTime)) {
            return  Result.error("开始时间和结束时间不能同时为空");
        }
        LambdaQueryWrapper<StaScheduleNo>  staScheduleNoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (WxlConvertUtils.isNotEmpty(startTime)) {
            staScheduleNoLambdaQueryWrapper.likeRight(StaScheduleNo::getStartTime,startTime);
        }
        if (WxlConvertUtils.isNotEmpty(endTime)) {
            staScheduleNoLambdaQueryWrapper.likeRight(StaScheduleNo::getEndTime,endTime);
        }
        staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getDelFlag,CommonConstant.DEL_FLAG_0);
        List<StaScheduleNo> list = staScheduleNoService.list(staScheduleNoLambdaQueryWrapper);
//        List<String> codeList = new ArrayList<>();
//        for (StaScheduleNo staScheduleNo : list) {
//            String code = staScheduleNo.getCode();
//            codeList.add(code);
//        }
       // if (!codeList.isEmpty()) {
            //list = staScheduleNoService.list(new QueryWrapper<StaScheduleNo>().lambda().in(StaScheduleNo::getCode, codeList));
//            Map<String, List<StaScheduleNo>> groupedByCode = list.stream()
//                    .collect(Collectors.groupingBy(StaScheduleNo::getCode));
//            // 获取 code 相同的列表
//            List<List<StaScheduleNo>> sameCodeLists = groupedByCode.values().stream()
//                    .filter(group -> group.size() > 1) // 只保留 code 相同的组
//                    .collect(Collectors.toList());
//            for (List<StaScheduleNo> sameCodeList : sameCodeLists) {
//
//            }
      //  }
        if (list.isEmpty()) {
            return Result.error("班别不存在");
        }
        return Result.OK("操作成功",list);
    }

    /**
     * 班别代码获取班别
     * @param code
     * @return
     */
    @GetMapping("/getListByCode")
    public Result getListByCode(@RequestParam("code") String code) {
        if (WxlConvertUtils.isEmpty(code)) {
            return Result.error("参数丢失");
        }
      return Result.OK("操作成功", staScheduleNoService.list(new QueryWrapper<StaScheduleNo>().lambda().eq(StaScheduleNo::getCode,code))) ;
    }
    /**
     * 排班编号导出
     * @param request
     * @param staScheduleNo
     * @return
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaScheduleNo staScheduleNo) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "未获取登录部门");
            return mv;
        }
        Map<String, String[]> parameterMap = request.getParameterMap();
        String[] codes = parameterMap.get("code");
        String[] startTimes = parameterMap.get("startTime");
        String[] endTimes = parameterMap.get("endTime");
        LambdaQueryWrapper<StaScheduleNo> staScheduleNoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staScheduleNoLambdaQueryWrapper.orderByDesc(StaScheduleNo::getCreateTime);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat sdf2 = new SimpleDateFormat("HH:mm");
        if (WxlConvertUtils.isNotEmpty(codes) && codes.length > 0) {
            String code = codes[0];
            staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getCode, code);
        }
        if (WxlConvertUtils.isNotEmpty(startTimes) && startTimes.length > 0) {
            String startTime = startTimes[0];
            Date parse = sdf.parse(startTime);
            String format = sdf2.format(parse);
            staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getStartTime, sdf2.parse(format));
        }
        if (WxlConvertUtils.isNotEmpty(endTimes) && endTimes.length > 0) {
            String endTime = endTimes[0];
            Date parse = sdf.parse(endTime);
            String format = sdf2.format(parse);
            staScheduleNoLambdaQueryWrapper.eq(StaScheduleNo::getEndTime, sdf2.parse(format));
        }
        List<StaScheduleNo> pageList = staScheduleNoService.list(staScheduleNoLambdaQueryWrapper);
        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "排班编号列表");
        mv.addObject(NormalExcelConstants.CLASS, StaScheduleNo.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("排班编号列表数据", "导出人:" + user.getRealname(), "排班编号"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }
}

