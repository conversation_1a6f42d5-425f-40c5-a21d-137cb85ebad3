package com.byun.modules.staffing.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import com.byun.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import com.byun.modules.staffing.entity.StaScheduleTemplate;
import com.byun.modules.staffing.vo.StaScheduleTemplatePage;
import com.byun.modules.staffing.service.IStaScheduleTemplateService;
import com.byun.modules.staffing.service.IStaScheduleTemplateItemService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 排班表模板
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Api(tags="排班表模板")
@RestController
@RequestMapping("/staffing/scheduleTemplate")
@Slf4j
public class StaScheduleTemplateController {
	@Autowired
	private IStaScheduleTemplateService staScheduleTemplateService;
	@Autowired
	private IStaScheduleTemplateItemService staScheduleTemplateItemService;

	 @ApiOperation(value="排班表模板-登录用户列表查询", notes="排班表模板-登录用户列表查询")
	 @GetMapping(value = "/getListForUser")
	 public Result<List<StaScheduleTemplate>> getListForUser(StaScheduleTemplate staScheduleTemplate,
															 HttpServletRequest req) {
		 Result<List<StaScheduleTemplate>> result = new  Result<List<StaScheduleTemplate>>();
		 LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 if(WxlConvertUtils.isEmpty(user)|| WxlConvertUtils.isEmpty(user.getId())){
		 	return result.error500("未获取到登录用户");
		 }
		 QueryWrapper<StaScheduleTemplate> queryWrapper = QueryGenerator.initQueryWrapper(staScheduleTemplate, req.getParameterMap());
		 //System.out.println(req.getParameterMap());
		 queryWrapper.eq("sys_user_id",user.getId());
		 List<StaScheduleTemplate> list = staScheduleTemplateService.list(queryWrapper);
		 return Result.OK(list);
	 }

	/**
	 * 分页列表查询
	 *
	 * @param staScheduleTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "排班表模板-分页列表查询")
	@ApiOperation(value="排班表模板-分页列表查询", notes="排班表模板-分页列表查询")
	 @GetMapping(value = "/list")
	 public Result<IPage<StaScheduleTemplate>> queryPageList(StaScheduleTemplate staScheduleTemplate,
															 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
															 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
															 HttpServletRequest req) {
		 QueryWrapper<StaScheduleTemplate> queryWrapper = QueryGenerator.initQueryWrapper(staScheduleTemplate, req.getParameterMap());
		 Page<StaScheduleTemplate> page = new Page<StaScheduleTemplate>(pageNo, pageSize);
		 IPage<StaScheduleTemplate> pageList = staScheduleTemplateService.page(page, queryWrapper);
		 return Result.OK(pageList);
	 }
	
	/**
	 *   添加
	 *
	 * @param staScheduleTemplatePage
	 * @return
	 */
	@AutoLog(value = "排班表模板-添加")
	@ApiOperation(value="排班表模板-添加", notes="排班表模板-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody StaScheduleTemplatePage staScheduleTemplatePage) {
		StaScheduleTemplate staScheduleTemplate = new StaScheduleTemplate();
		BeanUtils.copyProperties(staScheduleTemplatePage, staScheduleTemplate);
		staScheduleTemplateService.saveMain(staScheduleTemplate, staScheduleTemplatePage.getStaScheduleTemplateItemList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staScheduleTemplatePage
	 * @return
	 */
	@AutoLog(value = "排班表模板-编辑")
	@ApiOperation(value="排班表模板-编辑", notes="排班表模板-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody StaScheduleTemplatePage staScheduleTemplatePage) {
		Result<String> result = new Result<String>();
		StaScheduleTemplate staScheduleTemplate = new StaScheduleTemplate();
		BeanUtils.copyProperties(staScheduleTemplatePage, staScheduleTemplate);
		StaScheduleTemplate staScheduleTemplateEntity = staScheduleTemplateService.getById(staScheduleTemplate.getId());
		if(staScheduleTemplateEntity==null) {
			return result.error500("未找到对应数据");
		}
		staScheduleTemplateService.updateMain(staScheduleTemplate, staScheduleTemplatePage.getStaScheduleTemplateItemList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "排班表模板-通过id删除")
	@ApiOperation(value="排班表模板-通过id删除", notes="排班表模板-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		staScheduleTemplateService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "排班表模板-批量删除")
	@ApiOperation(value="排班表模板-批量删除", notes="排班表模板-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staScheduleTemplateService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "排班表模板-通过id查询")
	@ApiOperation(value="排班表模板-通过id查询", notes="排班表模板-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<StaScheduleTemplate> queryById(@RequestParam(name="id",required=true) String id) {
		Result<StaScheduleTemplate> result = new Result<StaScheduleTemplate>();
		StaScheduleTemplate staScheduleTemplate = staScheduleTemplateService.getById(id);
		if(staScheduleTemplate==null) {
			return result.error500("未找到对应数据");
		}
		return result.OK(staScheduleTemplate);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "排班模板选项通过主表ID查询")
	@ApiOperation(value="排班模板选项主表ID查询", notes="排班模板选项-通主表ID查询")
	@GetMapping(value = "/queryStaScheduleTemplateItemByMainId")
	public Result<List<StaScheduleTemplateItem>> queryStaScheduleTemplateItemListByMainId(@RequestParam(name="id",required=true) String id) {
		List<StaScheduleTemplateItem> staScheduleTemplateItemList = staScheduleTemplateItemService.selectByMainId(id);
		return Result.OK(staScheduleTemplateItemList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staScheduleTemplate
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaScheduleTemplate staScheduleTemplate) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<StaScheduleTemplate> queryWrapper = QueryGenerator.initQueryWrapper(staScheduleTemplate, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(WxlConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<StaScheduleTemplate> staScheduleTemplateList = staScheduleTemplateService.list(queryWrapper);

      // Step.3 组装pageList
      List<StaScheduleTemplatePage> pageList = new ArrayList<StaScheduleTemplatePage>();
      for (StaScheduleTemplate main : staScheduleTemplateList) {
          StaScheduleTemplatePage vo = new StaScheduleTemplatePage();
          BeanUtils.copyProperties(main, vo);
          List<StaScheduleTemplateItem> staScheduleTemplateItemList = staScheduleTemplateItemService.selectByMainId(main.getId());
          vo.setStaScheduleTemplateItemList(staScheduleTemplateItemList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "排班表模板列表");
      mv.addObject(NormalExcelConstants.CLASS, StaScheduleTemplatePage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("排班表模板数据", "导出人:"+sysUser.getRealname(), "排班表模板"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<StaScheduleTemplatePage> list = ExcelImportUtil.importExcel(file.getInputStream(), StaScheduleTemplatePage.class, params);
              for (StaScheduleTemplatePage page : list) {
                  StaScheduleTemplate po = new StaScheduleTemplate();
                  BeanUtils.copyProperties(page, po);
                  staScheduleTemplateService.saveMain(po, page.getStaScheduleTemplateItemList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
