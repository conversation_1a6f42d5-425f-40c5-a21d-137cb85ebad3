package com.byun.modules.staffing.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaTaskDeduction;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaTaskDeductionService;
import com.byun.modules.staffing.vo.StaTaskDeductionAddVo;
import com.byun.modules.staffing.vo.excel.exportXls.staTaskDeductioneXportXlsVo;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description TODO 待添加权限
 * @date : 2024-5-13 17:45
 */
@RestController
@RequestMapping("/staffing/order/deduction")
@Transactional
public class StaTaskDeductionController {
    @Autowired
    private IStaTaskDeductionService staTaskDeductionService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDepartService sysDepartService;
    /**
     * 待添加权限
     * @param staTaskDeductionAddVo request
     * @param request
     * @return
     */
    @PostMapping("/add")
    public Result add(StaTaskDeductionAddVo staTaskDeductionAddVo, HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(staTaskDeductionAddVo.getStaOrderId())) {
            return Result.error("任务单ID不能为空");
        }
        if (staTaskDeductionAddVo.getLateDeduction() == null && staTaskDeductionAddVo.getEarlyLeaveDeduction() == null && staTaskDeductionAddVo.getOtherDeduction() == null) {
            return Result.error("扣款金额至少三选一");
        }
        if (WxlConvertUtils.isEmpty(staTaskDeductionAddVo.getEmarks())) {
            return Result.error("扣款备注不能为空");
        }
        String staOrderId = staTaskDeductionAddVo.getStaOrderId();
        StaOrder staOrder = staOrderService.getById(staOrderId);
        if (WxlConvertUtils.isEmpty(staOrder)) {
            return Result.error("任务单不存在");
        }
        SysUser user = sysUserService.getById(sysUser.getId());
        //增加扣款数据
        StaTaskDeduction staTaskDeduction = new StaTaskDeduction();
        staTaskDeduction.setCreateTime(new Date());
        staTaskDeduction.setStaWorkId(staOrder.getStaWorkId());
        staTaskDeduction.setStaOrderId(staOrder.getId());
        staTaskDeduction.setCreateBy(WxlConvertUtils.isNotEmpty(user.getRealNameName()) ? user.getRealNameName() : user.getRealname());
        staTaskDeduction.setStaOrderCode(staOrder.getOrderCode());
        staTaskDeduction.setCreateById(sysUser.getId());
        staTaskDeduction.setStaOrderId(staOrderId);
        staTaskDeduction.setDeductionUserId(staOrder.getSysUserId());
        staTaskDeduction.setEmarks(staTaskDeductionAddVo.getEmarks());
        staTaskDeduction.setCompanyId(staOrder.getCompanyId());
        staTaskDeduction.setCompanyName(staOrder.getCompanyName());
        staTaskDeduction.setDeductionUserName(staOrder.getEnrollName());
        //staTaskDeduction.setDeductionDate(staTaskDeductionAddVo.getDeductionDate());
        staTaskDeduction.setWorkNameFull(WxlConvertUtils.isNotEmpty(staOrder.getWorkNameFull()) ? staOrder.getWorkNameFull() : staOrder.getWorkName());
        //增加事业处、区域、店编
        if (WxlConvertUtils.isNotEmpty(staOrder.getCompanyId())) {
            SysDepart storeDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, staOrder.getCompanyId())); //门店信息
            staTaskDeduction.setStoreNo(storeDepart.getStoreNo());
            if (WxlConvertUtils.isNotEmpty(storeDepart.getParentId())){ //获取区域
                SysDepart regionDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, storeDepart.getParentId()));
                staTaskDeduction.setStoreRegionName(regionDepart.getDepartName());
                if (WxlConvertUtils.isNotEmpty(regionDepart.getParentId())) { //获取事业处
                    SysDepart businessDepartmentDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, regionDepart.getParentId()));
                    staTaskDeduction.setBusinessDivisionName(businessDepartmentDepart.getDepartName());
                }
            }
        }
        BigDecimal totalDeduction = new BigDecimal(0.0);
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getLateDeduction())) {
            staTaskDeduction.setLateDeduction(staTaskDeductionAddVo.getLateDeduction());
            totalDeduction = totalDeduction.add(staTaskDeductionAddVo.getLateDeduction());
        }
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getEarlyLeaveDeduction())) {
            staTaskDeduction.setEarlyLeaveDeduction(staTaskDeductionAddVo.getEarlyLeaveDeduction());
            totalDeduction = totalDeduction.add(staTaskDeductionAddVo.getEarlyLeaveDeduction());
        }
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getOtherDeduction())) {
            staTaskDeduction.setOtherDeduction(staTaskDeductionAddVo.getOtherDeduction());
            totalDeduction = totalDeduction.add(staTaskDeductionAddVo.getOtherDeduction());
        }
        staTaskDeductionService.save(staTaskDeduction);
        return Result.OK("操作成功");
    }
    /**
     * 编辑扣款
     * @param staTaskDeductionAddVo
     * @param request
     * @return
     */
    @PostMapping("/edit")
    public Result edit(StaTaskDeductionAddVo staTaskDeductionAddVo, HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(staTaskDeductionAddVo.getId())) {
            return Result.error("扣款id丢失");
        }
        StaTaskDeduction staTaskDeduction = staTaskDeductionService.getById(staTaskDeductionAddVo.getId());
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getLateDeduction())) {
            staTaskDeduction.setLateDeduction(staTaskDeductionAddVo.getLateDeduction());
        }
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getEarlyLeaveDeduction())) {
            staTaskDeduction.setEarlyLeaveDeduction(staTaskDeductionAddVo.getEarlyLeaveDeduction());
        }
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getOtherDeduction())) {
            staTaskDeduction.setOtherDeduction(staTaskDeductionAddVo.getOtherDeduction());
        }
        if (WxlConvertUtils.isNotEmpty(staTaskDeductionAddVo.getEmarks())) {
            staTaskDeduction.setEmarks(staTaskDeductionAddVo.getEmarks());
        }
        staTaskDeduction.setUpdateTime(new Date());
        staTaskDeduction.setUpdateBy(sysUser.getUsername());
        staTaskDeductionService.updateById(staTaskDeduction);
        return Result.OK("操作成功","");
    }

    /**
     * 删除扣款
     * @param id
     * @return
     */
    @DeleteMapping("/remove")
    public Result remove(@RequestParam("id") String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(id)) {
            return Result.error("id丢失");
        }
        staTaskDeductionService.removeById(id);
        return Result.OK("操作成功","");
    }

    /**
     * 获取扣款列表
     * @param pageNo
     * @param pageSize
     * @param selecteddeparts
     * @param deptName
     * @param userName
     * @param idCard
     * @param request
     * @return
     */
    @GetMapping("/list")
    public Result list(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                       //@RequestParam(name = "selecteddeparts",required = false) String selecteddeparts,
                       @RequestParam(name = "deptName",required = false) String deptName,
                       @RequestParam(name = "userName",required = false) String userName,
                       @RequestParam(name = "idCard",required = false) String idCard,
                       HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效");
        }
        LambdaQueryWrapper<StaTaskDeduction> lambdaQueryWrapper = new LambdaQueryWrapper();
//        if (WxlConvertUtils.isNotEmpty(selecteddeparts)) {
//            SysDepart sysDepart = sysDepartService.getById(selecteddeparts);
//            lambdaQueryWrapper.likeRight(StaTaskDeduction::getStaOrderCode,sysDepart.getOrgCode());
//        }
        if (WxlConvertUtils.isNotEmpty(userName)) {
            lambdaQueryWrapper.likeRight(StaTaskDeduction::getDeductionUserName,userName);
        }
        if (WxlConvertUtils.isNotEmpty(idCard)) {
            lambdaQueryWrapper.eq(StaTaskDeduction::getDeductionUserName,userName);
        }
        Map<String, String[]> parameterMap = request.getParameterMap();
        String[] strings1 = parameterMap.get("userName");
        LambdaQueryWrapper<StaTaskDeduction> staTaskDeductionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (deptName != null) {
            staTaskDeductionLambdaQueryWrapper.eq(StaTaskDeduction::getCompanyName,deptName);
        }else {
            staTaskDeductionLambdaQueryWrapper.likeRight(StaTaskDeduction::getStaOrderCode, sysUser.getOrgCode());
        }
        if (WxlConvertUtils.isNotEmpty(strings1)) {
            staTaskDeductionLambdaQueryWrapper.like(StaTaskDeduction::getDeductionUserName, strings1[0]);
        }
        staTaskDeductionLambdaQueryWrapper.orderByDesc(StaTaskDeduction::getCreateTime);
        IPage<StaTaskDeduction> page = new Page<>(pageNo, pageSize);
        List<staTaskDeductioneXportXlsVo> staTaskDeductioneXportXlsVos = new ArrayList<>();
        IPage<StaTaskDeduction> staTaskDeductionIPage = staTaskDeductionService.page(page, staTaskDeductionLambdaQueryWrapper);
        IPage<staTaskDeductioneXportXlsVo>  staTaskDeductioneXportXlsVoIPage = new Page<>();
        for (StaTaskDeduction record : staTaskDeductionIPage.getRecords()) {
            staTaskDeductioneXportXlsVo staTaskDeductioneXportXlsVo = new staTaskDeductioneXportXlsVo();
            BeanUtils.copyProperties(record,staTaskDeductioneXportXlsVo);
            staTaskDeductioneXportXlsVos.add(staTaskDeductioneXportXlsVo);
        }
        staTaskDeductioneXportXlsVoIPage.setRecords(staTaskDeductioneXportXlsVos);
        staTaskDeductioneXportXlsVoIPage.setTotal(staTaskDeductionIPage.getTotal());
        staTaskDeductioneXportXlsVoIPage.setPages(staTaskDeductionIPage.getPages());
        staTaskDeductioneXportXlsVoIPage.setCurrent(staTaskDeductionIPage.getCurrent());
        staTaskDeductioneXportXlsVoIPage.setSize(staTaskDeductionIPage.getSize());
        return Result.OK(staTaskDeductioneXportXlsVoIPage);
    }
    /**
     * 扣款导出 TODO 还需要添加筛选条件
     * @param request
     * @return
     */
    @RequestMapping("/exportXls")
    public ModelAndView exportXls(@RequestParam(name = "selecteddeparts",required = false) String selecteddeparts,
                                 @RequestParam(name = "userName",required = false) String userName,HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "登录失效");
            return mv;
        }
        LambdaQueryWrapper<StaTaskDeduction> staTaskDeductionLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (WxlConvertUtils.isNotEmpty(selecteddeparts)) {
            SysDepart sysDepart = sysDepartService.getById(selecteddeparts);
            staTaskDeductionLambdaQueryWrapper.likeRight(StaTaskDeduction::getStaOrderCode,sysDepart.getOrgCode());
        }
        if (WxlConvertUtils.isNotEmpty(userName)) {
            staTaskDeductionLambdaQueryWrapper.likeRight(StaTaskDeduction::getDeductionUserName,userName);
        }
        staTaskDeductionLambdaQueryWrapper.orderByDesc(StaTaskDeduction::getCreateTime);
        List<StaTaskDeduction> staTaskDeductions = staTaskDeductionService.list(staTaskDeductionLambdaQueryWrapper);
        List<staTaskDeductioneXportXlsVo> pageList = new ArrayList<>();
        for (StaTaskDeduction staTaskDeduction : staTaskDeductions) {
            int index = staTaskDeduction.getCompanyName().indexOf("(");
            try {
                staTaskDeduction.setCompanyName(staTaskDeduction.getCompanyName().substring(0, index));
            } catch (Exception e) {}
            staTaskDeductioneXportXlsVo staTaskDeductioneXportXlsVo = new staTaskDeductioneXportXlsVo();
            BeanUtils.copyProperties(staTaskDeduction,staTaskDeductioneXportXlsVo);
            pageList.add(staTaskDeductioneXportXlsVo);
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "扣款列表");
        mv.addObject(NormalExcelConstants.CLASS, staTaskDeductioneXportXlsVo.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("扣款列表", "导出人:" + sysUser.getRealname(), "扣款"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }
}
