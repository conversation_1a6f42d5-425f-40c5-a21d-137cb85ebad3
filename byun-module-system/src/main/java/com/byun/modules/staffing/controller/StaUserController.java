package com.byun.modules.staffing.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.DateUtils;
import com.byun.common.util.RedisUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.base.service.BaseCommonService;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.model.*;
import com.byun.modules.staffing.service.*;
import com.byun.modules.staffing.utils.oss.AliOSSUtil;
import com.byun.modules.staffing.vo.StaUserDataVo;
import com.byun.modules.staffing.vo.SysUserBindingWorkPage;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.service.*;
import com.byun.modules.zh.service.IZhService;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户列表
 * @date 2021/11/17 9:15
 */
@RestController
@RequestMapping("/staffing/user")
@Api(tags = "用户操作")
@Slf4j
public class StaUserController {

    @Autowired
    private ISysUserDepartService sysUserDepartService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IStaUserInfoService staUserInfoService;
    @Autowired
    private IStaEnrollInfoService staEnrollInfoService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysDepartRoleService sysDepartRoleService;
    @Autowired
    private IStaWorkBrService staWorkBrService;
    @Autowired
    private IStaWorkCollectService staWorkCollectService;
    @Autowired
    private IStaWorkService staWorkService;
    @Autowired
    private IStaWorkAgentRelService staWorkAgentRelService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;
    @Autowired
    private ISysUserRelService sysUserRelService;
    @Autowired
    private ISysDepartRoleUserService sysDepartRoleUserService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Resource
    private BaseCommonService baseCommonService;
    @Autowired
    private RedisUtil redisUtil;
    @Value("${byun.oss.endpoint}")
    private String endpoint;
    @Value("${byun.oss.accessKey}")
    private String accessKeyId;
    @Value("${byun.oss.secretKey}")
    private String secretAccessKey;
    @Value("${byun.oss.bucketName}")
    private String bucketName;
    @Autowired
    private IZhService zhService;
    @RequestMapping(value = "/queryUserDepartRole", method = RequestMethod.GET)
    public Result<JSONObject> queryUserDepartRole(@RequestParam(name = "userid") String userid, @RequestParam(name = "departid", required = true) String departid) {
        Result<JSONObject> result = new Result<JSONObject>();
        List<String> list = new ArrayList<String>();
        //当前部门所有角色
        LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<SysDepartRole>();
        sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, departid);
        List<SysDepartRole> allRole = sysDepartRoleService.list(sysDepartRoleLambdaQueryWrapper);
        List<String> sysDepartRoleId = new ArrayList<String>();
        for (SysDepartRole sysDepartRole : allRole) {
            sysDepartRoleId.add(sysDepartRole.getId());
        }

        JSONObject obj = new JSONObject();
        JSONArray allRoleArr = new JSONArray();
        JSONArray userRoleArr = new JSONArray();
        if (WxlConvertUtils.isNotEmpty(userid)) {
            //用户所有角色
            LambdaQueryWrapper<SysDepartRoleUser> sysDepartRoleUserLambdaQueryWrapper = new LambdaQueryWrapper<SysDepartRoleUser>();
            sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getUserId, userid);
            sysDepartRoleUserLambdaQueryWrapper.in(SysDepartRoleUser::getDroleId, sysDepartRoleId);//当前部门
            List<SysDepartRoleUser> sysDepartRoleList = sysDepartRoleUserService.list(sysDepartRoleUserLambdaQueryWrapper);
            List<SysDepartRole> userRole = new ArrayList<>();
            for (SysDepartRoleUser sysDepartRoleUser : sysDepartRoleList) {
                for (SysDepartRole sysDepartRole : allRole) {
                    if (sysDepartRoleUser.getDroleId().equals(sysDepartRole.getId())) {
                        userRole.add(sysDepartRole);
                        break;
                    }
                }
            }
            userRoleArr.addAll(userRole);
            obj.put("userRole", userRoleArr);
        }
        allRoleArr.addAll(allRole);
        obj.put("allRole", allRoleArr);
        result.setResult(obj);
        result.setSuccess(true);
        return result;
    }

    /**
     * 启用代理
     *
     * @param staAgentModel
     * @return
     */
    @AutoLog(value = "启用代理")
    @ApiOperation(value = "启用代理", notes = "启用代理")
    @PostMapping(value = "/enableAgentByLogin")
    public Result<?> enableAgentByLogin(@RequestBody StaAgentModel staAgentModel) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(loginUser)) {
            return Result.error("登录失效");
        }
        SysUser user = sysUserService.getById(loginUser.getId());
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        if (WxlConvertUtils.isEmpty(staAgentModel.getUserId())) {
            return Result.error("未找到绑定的用户id");
        }
        SysUser byId = sysUserService.getById(staAgentModel.getUserId());
        if (WxlConvertUtils.isEmpty(byId)) {
            return Result.error("未找到绑定的用户");
        }
        if (WxlConvertUtils.isEmpty(loginUser.getOrgCode())) {
            return Result.error("未找到当前登录公司OrgCode");
        }
        String departId = sysBaseAPI.getDepartIdsByOrgCode(loginUser.getOrgCode());
        if (WxlConvertUtils.isEmpty(departId)) {
            return Result.error("未找到所属公司Id");
        }
        staAgentModel.setDepartId(departId);
        SysDepart depart = sysDepartService.getById(departId);
        if (WxlConvertUtils.isEmpty(depart)) {
            return Result.error("未找到所属公司");
        }
        LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //查询该公司下 代理角色
        sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, depart.getId());
        sysDepartRoleLambdaQueryWrapper.likeLeft(SysDepartRole::getRoleCode, CommonConstant.DEPART_ROLE_CODE_RECRUITER);
        SysDepartRole sysDepartRole = sysDepartRoleService.getOne(sysDepartRoleLambdaQueryWrapper);
        staAgentModel.setUpdateBy(loginUser.getUsername());
        staAgentModel.setUpdateTime(new Date());
        if (WxlConvertUtils.isEmpty(sysDepartRole)) {
            return Result.error("所在公司已没有招聘官角色");
        }
        try {
            sysUserService.enableAgentByUser(user, staAgentModel);
            return Result.OK("启用代理成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("启用代理失败！");
        }
    }

    /**
     * 停用代理
     *
     * @param staAgentModel
     * @return
     */
    @AutoLog(value = "停用代理")
    @ApiOperation(value = "停用代理", notes = "停用代理")
    @PostMapping(value = "/stopAgentByLogin")
    public Result<?> stopAgentByLogin(@RequestBody StaAgentModel staAgentModel) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(loginUser)) {
            return Result.error("登录失效");
        }
        SysUser user = sysUserService.getById(loginUser.getId());
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        if (WxlConvertUtils.isEmpty(staAgentModel.getUserId())) {
            return Result.error("未找到绑定的用户id");
        }
        SysUser byId = sysUserService.getById(staAgentModel.getUserId());
        if (WxlConvertUtils.isEmpty(byId)) {
            return Result.error("未找到绑定的用户");
        }
        if (WxlConvertUtils.isEmpty(loginUser.getOrgCode())) {
            return Result.error("未找到当前登录公司OrgCode");
        }
        String departId = sysBaseAPI.getDepartIdsByOrgCode(loginUser.getOrgCode());
        if (WxlConvertUtils.isEmpty(departId)) {
            return Result.error("未找到所属公司Id");
        }
        staAgentModel.setDepartId(departId);
        SysDepart depart = sysDepartService.getById(departId);
        if (WxlConvertUtils.isEmpty(depart)) {
            return Result.error("未找到所属公司");
        }
        staAgentModel.setUpdateBy(loginUser.getUsername());
        staAgentModel.setUpdateTime(new Date());
        try {
            sysUserService.stopAgentByUser(user, staAgentModel);
            return Result.OK("停用代理成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("停用代理失败！");
        }
    }

    /**
     * 删除代理
     *
     * @param staAgentModel
     * @return
     */
    @AutoLog(value = "删除代理")
    @ApiOperation(value = "删除代理", notes = "删除代理")
    @DeleteMapping(value = "/deleteAgentByLogin")
    public Result<?> deleteAgentByLogin(@RequestBody StaAgentModel staAgentModel) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(loginUser)) {
            return Result.error("登录失效");
        }
        SysUser user = sysUserService.getById(loginUser.getId());
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        if (WxlConvertUtils.isEmpty(staAgentModel.getUserId())) {
            return Result.error("未找到绑定的用户id");
        }
        SysUser byId = sysUserService.getById(staAgentModel.getUserId());
        if (WxlConvertUtils.isEmpty(byId)) {
            return Result.error("未找到绑定的用户");
        }
        if (WxlConvertUtils.isEmpty(loginUser.getOrgCode())) {
            return Result.error("未找到当前登录公司OrgCode");
        }
        String departId = sysBaseAPI.getDepartIdsByOrgCode(loginUser.getOrgCode());
        if (WxlConvertUtils.isEmpty(departId)) {
            return Result.error("未找到所属公司Id");
        }
        staAgentModel.setDepartId(departId);
        SysDepart depart = sysDepartService.getById(departId);
        if (WxlConvertUtils.isEmpty(depart)) {
            return Result.error("未找到所属公司");
        }
        staAgentModel.setUpdateBy(loginUser.getUsername());
        staAgentModel.setUpdateTime(new Date());
        try {
            sysUserService.deleteAgentByUser(user, staAgentModel);
            return Result.OK("删除代理成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("删除代理失败！");
        }
    }

    /**
     * 添加代理
     *
     * @param staAgentModel
     * @return
     */
    @AutoLog(value = "添加代理")
    @ApiOperation(value = "添加代理", notes = "添加代理")
    @PostMapping(value = "/addAgentByLogin")
    public Result<?> addAgentByLogin(@RequestBody StaAgentModel staAgentModel) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(loginUser)) {
            return Result.error("登录失效");
        }
        SysUser user = sysUserService.getById(loginUser.getId());
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未找到登录用户");
        }
        if (WxlConvertUtils.isEmpty(staAgentModel.getUserId())) {
            return Result.error("未找到绑定的用户id");
        }
        SysUser byId = sysUserService.getById(staAgentModel.getUserId());
        if (WxlConvertUtils.isEmpty(byId)) {
            return Result.error("未找到绑定的用户");
        }
        if (WxlConvertUtils.isEmpty(staAgentModel.getDepartId())) {
            return Result.error("未找到所属公司Id");
        }
        SysDepart depart = sysDepartService.getById(staAgentModel.getDepartId());
        if (WxlConvertUtils.isEmpty(depart)) {
            return Result.error("未找到所属公司");
        }
        LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //查询该公司下 代理角色
        sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, depart.getId());
        sysDepartRoleLambdaQueryWrapper.likeLeft(SysDepartRole::getRoleCode, CommonConstant.DEPART_ROLE_CODE_RECRUITER);
        SysDepartRole sysDepartRole = sysDepartRoleService.getOne(sysDepartRoleLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(sysDepartRole)) {
            return Result.error("所选公司没有招聘官角色");
        }

        //查询是否已是绑定的代理
        LambdaQueryWrapper<SysUserRel> userRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //被停用也算 被绑定
        userRelLambdaQueryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
        userRelLambdaQueryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        int count = sysUserRelService.count(userRelLambdaQueryWrapper);
        if (count > 0) {
            return Result.error("该代理已被绑定");
        }
        staAgentModel.setCreateBy(loginUser.getUsername());
        staAgentModel.setCreateTime(new Date());
        staAgentModel.setUpdateBy(loginUser.getUsername());
        staAgentModel.setUpdateTime(new Date());
        try {
            user.setCreateBy(loginUser.getUsername());
            user.setCreateTime(new Date());
            user.setUpdateBy(loginUser.getUsername());
            user.setUpdateTime(new Date());
            sysUserService.addAgentByUser(user, staAgentModel);
            return Result.OK("添加代理成功！");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("添加代理失败！");
        }

    }

    /**
     * 获取绑定任务的代理列表
     *
     * @param staAgentModel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @RequestMapping(value = "/listAgentByBindingWork", method = RequestMethod.GET)
    public Result<IPage<SysUserBindingWorkPage>> listAgentByBindingWork(StaAgentModel staAgentModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysUserBindingWorkPage>> result = new Result<IPage<SysUserBindingWorkPage>>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //当前登录部门
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isEmpty(departId)) {
            result.setSuccess(false);
            result.setMessage("未获取到当前公司");
            return result;
        }

//        staAgentModel.setUserId(sysUser.getId());
        Page<SysUser> list = new Page<SysUser>(pageNo, pageSize);
        StaUserRelModel staUserRelModel = new StaUserRelModel();
        staUserRelModel.setMeId(sysUser.getId());
        staUserRelModel.setSpareId(departId);
        staUserRelModel.setRelType(CommonConstant.SYS_USER_REL_3.toString());
        staUserRelModel.setDelFlag(staAgentModel.getDelFlag());
        list = sysUserService.getUserListByUserRelYou(list, staUserRelModel);
        Page<SysUserBindingWorkPage> pageList = new Page<SysUserBindingWorkPage>(pageNo, pageSize);
        pageList.setCurrent(list.getCurrent());
        pageList.setSize(list.getSize());
        pageList.setTotal(list.getTotal());
        pageList.setPages(list.getPages());
        List<SysUserBindingWorkPage> pageRecords = new ArrayList<>();
        for (SysUser su : list.getRecords()) {
            SysUserBindingWorkPage sysUserBindingWorkPage = new SysUserBindingWorkPage();
            sysUserBindingWorkPage.setId(su.getId());
            sysUserBindingWorkPage.setAvatar(su.getAvatar());
            sysUserBindingWorkPage.setPhone(su.getPhone());
            sysUserBindingWorkPage.setRealname(su.getRealname());
            LambdaQueryWrapper<StaWorkAgentRel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StaWorkAgentRel::getStaWorkId, staAgentModel.getWorkId());
            queryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
            queryWrapper.eq(StaWorkAgentRel::getSysUserId, su.getId());
            int count = staWorkAgentRelService.count(queryWrapper);
            if (count > 0) {
                sysUserBindingWorkPage.setChecked(true);
            } else {
                sysUserBindingWorkPage.setChecked(false);
            }

            pageRecords.add(sysUserBindingWorkPage);
        }
        pageList.setRecords(pageRecords);

        result.setResult(pageList);
        result.setSuccess(true);
        return result;
    }

    /**
     * 获取代理列表
     *
     * @param staAgentModel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @RequestMapping(value = "/agentList", method = RequestMethod.GET)
    public Result<IPage<SysUser>> agentList(StaAgentModel staAgentModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<IPage<SysUser>>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //当前登录部门
        String departId = sysBaseAPI.getDepartIdsByOrgCode(sysUser.getOrgCode());
        if (WxlConvertUtils.isEmpty(departId)) {
            result.setSuccess(false);
            result.setMessage("未获取到当前公司");
            return result;
        }
        staAgentModel.setDepartId(departId);
        if (WxlConvertUtils.isEmpty(staAgentModel.getUserId())) {
            result.setSuccess(false);
            result.setMessage("未获取到查询的用户");
            return result;
        }
        //查登陆用户改为，查指定用户
//        staAgentModel.setUserId(sysUser.getId());
        Page<SysUser> pageList = new Page<SysUser>(pageNo, pageSize);

        StaUserRelModel staUserRelModel = new StaUserRelModel();
        staUserRelModel.setMeId(staAgentModel.getUserId());
        staUserRelModel.setSpareId(staAgentModel.getDepartId());
        staUserRelModel.setRelType(CommonConstant.SYS_USER_REL_3.toString());
        staUserRelModel.setDelFlag(staAgentModel.getDelFlag());
        pageList = sysUserService.getUserListByUserRelYou(pageList, staUserRelModel);
        //TODO 暂时先查，后面封装对象
        for (SysUser su : pageList.getRecords()) {
            LambdaQueryWrapper<SysUserRel> userRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            //异常记录数据库脏数据造成的
            userRelLambdaQueryWrapper.eq(SysUserRel::getYouId, su.getId());
//            userRelLambdaQueryWrapper.eq(SysUserRel::getDelFlag,CommonConstant.DEL_FLAG_0);
            userRelLambdaQueryWrapper.eq(SysUserRel::getMeId, staAgentModel.getUserId());
            userRelLambdaQueryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
            userRelLambdaQueryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
            List<SysUserRel> list = sysUserRelService.list(userRelLambdaQueryWrapper);
            if (WxlConvertUtils.isNotEmpty(list) && list.size() == 1) {
                su.setDelFlag(list.get(0).getDelFlag());
            } else {
                su.setDelFlag(3);//异常
            }
        }
        result.setResult(pageList);
        result.setSuccess(true);
        return result;
    }
    /**
     * 获取推广的用户列表数据
     *
     * @param staUserRelModel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
//    @PermissionData(pageComponent = "system/UserList")
    @RequestMapping(value = "/promoterList", method = RequestMethod.GET)
    public Result<IPage<SysUser>> promoterList(StaUserRelModel staUserRelModel, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<IPage<SysUser>>();
        Page<SysUser> pageList = new Page<SysUser>(pageNo, pageSize);
        staUserRelModel.setRelType(CommonConstant.SYS_USER_REL_1.toString());
        pageList = sysUserService.getUserListByUserRelMe(pageList, staUserRelModel);
        result.setSuccess(true);
        result.setResult(pageList);
        log.info(pageList.toString());
        return result;
    }

    /**
     * 统计所有推广人的下级
     *
     * @param pageNo
     * @param pageSize
     * @param realNameName 真实姓名
     * @param username     手机号
     * @return IPage<SysUser>
     */
    @GetMapping("/allPromoterList")
    public Result<?> allUserPromotionList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       @RequestParam(name = "realNameName", required = false) String realNameName,
                                                       @RequestParam(name = "username", required = false) String username
    ) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String orgCode = loginUser.getOrgCode();
        SysDepart sysDepart = sysDepartService.getOne(new QueryWrapper<SysDepart>().lambda().eq(SysDepart::getOrgCode, orgCode));
        List<SysUserRel> userRels = sysUserRelService.list(new LambdaQueryWrapper<SysUserRel>()
                .eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_1)
                .eq(SysUserRel::getDepartId,sysDepart.getId())
                .eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_0).orderByDesc(SysUserRel::getCreateTime));
        //推广人
        List<String> meIds = userRels.stream().map(SysUserRel::getMeId).distinct().collect(Collectors.toList());
        if (WxlConvertUtils.isEmpty(meIds) || meIds.isEmpty()) {
            return Result.error("无数据");
        }
        IPage<SysUser> userPage = new Page(pageNo, pageSize);
        LambdaQueryWrapper<SysUser> userQueryWrapper = new LambdaQueryWrapper();
        if (WxlConvertUtils.isNotEmpty(realNameName)) {
            userQueryWrapper.like(SysUser::getRealNameName, realNameName);
        }
        if (WxlConvertUtils.isNotEmpty(username)) {
            userQueryWrapper.eq(SysUser::getUsername, username);
        }
        userQueryWrapper.in(SysUser::getId, meIds);
        userQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        IPage<SysUser> page = sysUserService.page(userPage, userQueryWrapper);
        List<SysUser> records = page.getRecords();
        Map<String, Integer> promotersMap = new HashMap<>();
        for (SysUserRel userRel : userRels) {
            String youId = userRel.getYouId();
            promotersMap.put(youId, promotersMap.getOrDefault(youId, 0) + 1);
        }
        for (SysUser record : records) {
            if (WxlConvertUtils.isEmpty(record.getSex()) && WxlConvertUtils.isNotEmpty(record.getIdCard())) {
                String idCardNo = record.getIdCard();
                String gender = Integer.parseInt(idCardNo.substring(16, 17)) % 2 == 1 ? "1" : "2";
                record.setSex(Integer.valueOf(gender));
            }
            String recordId = record.getId();
            record.setPromotersNum(promotersMap.getOrDefault(recordId, 0));
        }
        return Result.OK(page);
    }

    /**
     * 导出推广人及下级列表
     *
     * @param realNameName
     * @param username
     * @return ModelAndView
     */
    @RequestMapping("/promoterExport")
    public ModelAndView exportPromotion(@RequestParam(name = "realNameName", required = false) String realNameName,
                                        @RequestParam(name = "username", required = false) String username) {
        List<SysUserRel> userRels = sysUserRelService.list(new LambdaQueryWrapper<SysUserRel>()
                .eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_1)
                .eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_0).orderByDesc(SysUserRel::getCreateTime));
        List<String> youIds = userRels.stream().map(SysUserRel::getYouId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<SysUser> userQueryWrapper = new LambdaQueryWrapper();
        if (WxlConvertUtils.isNotEmpty(realNameName)) {
            userQueryWrapper.like(SysUser::getRealNameName, realNameName);
        }
        if (WxlConvertUtils.isNotEmpty(username)) {
            userQueryWrapper.eq(SysUser::getUsername, username);
        }
        userQueryWrapper.in(SysUser::getId, youIds);
        userQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<SysUser> sysUsers = sysUserService.list(userQueryWrapper);
        Map<String, Integer> promotersMap = new HashMap<>();
        for (SysUserRel userRel : userRels) {
            String youId = userRel.getYouId();
            promotersMap.put(youId, promotersMap.getOrDefault(youId, 0) + 1);
        }
        for (SysUser user : sysUsers) {
            if (WxlConvertUtils.isNotEmpty(user.getIdCard())) {
                String idCardNo = user.getIdCard();
                String gender = Integer.parseInt(idCardNo.substring(16, 17)) % 2 == 1 ? "1" : "2";
                user.setSex(Integer.valueOf(gender));
            }
            String recordId = user.getId();
            user.setPromotersNum(promotersMap.getOrDefault(recordId, 0));
        }
        //推广人
        List<SysUser> collectUsers = sysUsers.stream().sorted(Comparator.comparingInt(SysUser::getPromotersNum).reversed()).collect(Collectors.toList());
        int promotersMax = collectUsers.get(0).getPromotersNum();
        //表头
        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity e1 = new ExcelExportEntity("推广人", "realNameName");
        entityList.add(e1);
        ExcelExportEntity e2 = new ExcelExportEntity("手机号", "username");
        e2.setWidth(20);
        entityList.add(e2);
        for (int i = 1; i <= promotersMax; i++) {
            //key 推广人下级id
            ExcelExportEntity eday = new ExcelExportEntity("推广人" + i, String.valueOf(i));
            eday.setWidth(60);
            entityList.add(eday);
        }
        //数据
        List<Map<String, Object>> dataList = new ArrayList<>();
        List<String> uids = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
        List<SysUser> lowerUserList = sysUserService.list(new LambdaQueryWrapper<SysUser>()
                .in(SysUser::getPromoterId, uids)
                .eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0)
        );
        //根据promoterId进行分组
        Map<String, List<SysUser>> maps = lowerUserList.stream()
                .collect(Collectors.groupingBy(SysUser::getPromoterId,
                        Collectors.mapping(sysUser -> {
                            return sysUser;
                        }, Collectors.toList())
                ));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (SysUser collectUser : collectUsers) {
            Map<String, Object> map = new HashMap<>();
            map.put("realNameName", WxlConvertUtils.isNotEmpty(collectUser.getRealNameName()) ? collectUser.getRealNameName() : "未认证");
            map.put("username", collectUser.getUsername());
            Iterator<Map.Entry<String, List<SysUser>>> iterator = maps.entrySet().iterator();
            while (iterator.hasNext()){
                int i = 0;
                Map.Entry<String, List<SysUser>> sysUserS = iterator.next();
                if (sysUserS.getKey().equals(collectUser.getId())){
                    for (SysUser sysUser : sysUserS.getValue()) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(WxlConvertUtils.isNotEmpty(sysUser.getRealNameName()) ? sysUser.getRealNameName() : "未认证");
                        sb.append("--");
                        sb.append(sysUser.getUsername());
                        sb.append("--");
                        sb.append(DateUtils.date2Str(sysUser.getCreateTime(),simpleDateFormat));
                        int kay = ++i;
                        map.put(String.valueOf(kay), sb);
                    }
                    iterator.remove();
                    break;
                }
            }
            dataList.add(map);
        }
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "推广列表");
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("推广数据", "", "推广"));
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        mv.addObject(NormalExcelConstants.DATA_LIST, entityList);
        return mv;
    }

    /**
     * 获取推广人的下级列表
     *
     * @param sysUserId 用户id
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/lowerPromoterList")
    public Result<?> lowerPromoterList(@RequestParam("sysUserId") String sysUserId,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (WxlConvertUtils.isEmpty(sysUserId)) {
            return Result.error("用户丢失");
        }
        //用户ID(1557272709728190465)关系表中的脏数据用户库中不存在
        //promoter_id 推广人
        IPage<SysUser> page = new Page(pageNo, pageSize);
        IPage<SysUser> sysUserIPage = sysUserService.page(page, new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getPromoterId, sysUserId).eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0));
        return Result.OK(sysUserIPage);
    }

    @ApiOperation("查询用户相关数据量")
    @RequestMapping(value = "/queryUserDataNum", method = RequestMethod.GET)
    public Result<?> queryUserDataNum() {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        StaUserDataVo staUserDataVo = new StaUserDataVo();
        String orgCode = user.getOrgCode();
        String departId = sysBaseAPI.getDepartIdsByOrgCode(orgCode);
        List<String> departIdArr = null;
        Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
        boolean admin = userPermissionSet.contains("staffing:admin:simpledatanum");//管理员数据查询
        boolean recruiter = userPermissionSet.contains("staffing:recruiter:simpledatanum");//招聘官数据查询
        if (admin || recruiter) {//不是普通用户
            //查询当前登录部门
            //查询部门 和下级部门
            if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
                departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
            }
            //
            if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
                return Result.error("为获取到当前登录部门");
            }
        }
        if (admin) {
            //普通管理和上级管理
            //查询部门下发布任务数
            LambdaQueryWrapper<StaWork> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0);
            queryWrapper.eq(StaWork::getStateFlag, CommonConstant.WORK_STATUS_3);
            queryWrapper.in(StaWork::getCompanyId, departIdArr);
            List<StaWork> list = staWorkService.list(queryWrapper);
            if (WxlConvertUtils.isNotEmpty(list)) {
                staUserDataVo.setAdminPublishWorkNum(list.size());//发布任务数
            } else {
                staUserDataVo.setAdminPublishWorkNum(0);
            }
            int adminApplyWorkNum = 0;
            int adminProgressWorkNum = 0;
            for (StaWork sw : list) {
                //申请人数
                if (WxlConvertUtils.isNotEmpty(sw.getApplyNum())) {
                    adminApplyWorkNum += sw.getApplyNum();
                }
                //任务进行中 数
                if (sw.getStateFlag() >= 3) {
                    adminProgressWorkNum++;
                }
            }
            staUserDataVo.setAdminProgressWorkNum(adminProgressWorkNum);//进行中任务数
            staUserDataVo.setAdminApplyWorkNum(adminApplyWorkNum);//申请任务数
            LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysUserLambdaQueryWrapper.eq(SysUser::getBindUserId, user.getId());
            sysUserLambdaQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
            staUserDataVo.setPoolNum(sysUserService.count(sysUserLambdaQueryWrapper));//绑定人才
        } else if (recruiter) {
            //招聘官
            //查询部门下任务中任务数
            LambdaQueryWrapper<StaWork> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0);
            queryWrapper.eq(StaWork::getStateFlag, CommonConstant.WORK_STATUS_3);
            queryWrapper.in(StaWork::getCompanyId, departIdArr);
            //根据权限,查询关联的任务或任务下全部任务
            boolean positionlist = userPermissionSet.contains("staffing:position:list");//公司下全部任务
            boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//公司下自己绑定的任务
            if (positionmylist) {
                List<String> widList = new ArrayList<String>();
                LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<StaWorkAgentRel>();
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getDelFlag, CommonConstant.DEL_FLAG_0);
                staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId, user.getId());
                List<StaWorkAgentRel> list = staWorkAgentRelService.list(staWorkAgentRelLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
                    for (StaWorkAgentRel staWorkAgentRel : list) {
                        widList.add(staWorkAgentRel.getStaWorkId());
                    }
                    queryWrapper.in(StaWork::getId, widList);
                } else {
                    queryWrapper.in(StaWork::getId, "isnull");
                }
            } else if (positionlist) {

            } else {
                queryWrapper.in(StaWork::getId, "isnull");//没权限
            }
            staUserDataVo.setRecruiterProgressWorkNum(staWorkService.count(queryWrapper));//进行中任务
            //查询部门下完成任务数
            LambdaQueryWrapper<StaWork> staWorkLambdaQueryWrapper = new LambdaQueryWrapper<>();
            staWorkLambdaQueryWrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0);
            staWorkLambdaQueryWrapper.eq(StaWork::getStateFlag, CommonConstant.WORK_STATUS_1);
            staWorkLambdaQueryWrapper.in(StaWork::getCompanyId, departIdArr);
            staUserDataVo.setRecruiterCompleteWorkNum(staWorkService.count(staWorkLambdaQueryWrapper));//完成任务
            LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysUserLambdaQueryWrapper.eq(SysUser::getBindUserId, user.getId());
            sysUserLambdaQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
            staUserDataVo.setPoolNum(sysUserService.count(sysUserLambdaQueryWrapper));//绑定人才
        }
        //普通用户 头部数据
        LambdaQueryWrapper<StaWorkBr> staWorkBrLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staWorkBrLambdaQueryWrapper.eq(StaWorkBr::getSysUserId, user.getId());
        staUserDataVo.setVisitsNum(staWorkBrService.count(staWorkBrLambdaQueryWrapper));//浏览数
        LambdaQueryWrapper<StaWorkCollect> staWorkCollectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staWorkCollectLambdaQueryWrapper.eq(StaWorkCollect::getSysUserId, user.getId());
        staUserDataVo.setCollectNum(staWorkCollectService.count(staWorkCollectLambdaQueryWrapper));//收藏数
        LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staOrderLambdaQueryWrapper.eq(StaOrder::getRealUserId, user.getId());//真实用户id
        staOrderLambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        staOrderLambdaQueryWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_1);
        staUserDataVo.setCompleteWorkNum(staOrderService.count(staOrderLambdaQueryWrapper));
        return Result.OK(staUserDataVo);
    }

    /**
     * @description: 用户与招聘官解绑
     * <AUTHOR>
     * @date 2021/11/29 18:30
     * @version 1.0
     */
    @RequestMapping(value = "/recruiterUnbindUser", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<SysUser> recruiterUnbindUser(@RequestBody SysUser sysUser) {
        Result<SysUser> result = new Result<SysUser>();
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            SysUser oldSysUser = sysUserService.getById(sysUser.getId());
            String bindUserId = null;
            baseCommonService.addLog("编辑用户（解绑招聘官），id： " + sysUser.getId(), CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3);
            if (oldSysUser == null) {
                result.error500("未找到对应实体");
            } else {
                bindUserId = oldSysUser.getBindUserId();
                oldSysUser.setUpdateTime(new Date());
                oldSysUser.setUpdateBy(user.getUsername());
                oldSysUser.setBindUserId(null);
                sysUserService.updateBindUserById(oldSysUser);
                result.success("解绑成功!");
                //发送系统通知给招聘官
                try {
                    if (WxlConvertUtils.isNotEmpty(bindUserId)) {
                        SysUser bindUser = sysUserService.getById(bindUserId);
                        if (WxlConvertUtils.isNotEmpty(bindUser)) {
                            SysAnnouncement sysAnnouncement = new SysAnnouncement();
                            sysAnnouncement.setUserIds(bindUser.getId() + ",");//推送用户
                            String msgAbstract = "" + user.getRealname() + ",解绑了您与<" + sysUser.getRealname() + ">的招聘官绑定！";
                            sysAnnouncement.setMsgAbstract(msgAbstract);
                            String title = "招聘官解绑用户";
                            sysAnnouncement.setTitile(title);
                            sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
                            sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                            sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
                            sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                            sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                            sysAnnouncement.setSendTime(new Date());
                            sysAnnouncement.setSender(user.getRealname());
                            sysAnnouncement.setCreateBy(user.getUsername());
                            sysAnnouncement.setCreateTime(new Date());
                            sysAnnouncementService.saveAnnouncement(sysAnnouncement);
                        }
                    }
                } catch (Exception e) {
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }


    /**
     * @description: 用户与招聘官绑定
     * <AUTHOR>
     * @date 2021/11/29 18:31
     * @version 1.0
     */
    @RequestMapping(value = "/recruiterBindUser", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<SysUser> recruiterBindUser(@RequestBody SysUser sysUser) {
        Result<SysUser> result = new Result<SysUser>();
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            SysUser oldSysUser = sysUserService.getById(sysUser.getId());
            if (oldSysUser == null) {
                result.error500("未找到对应实体");
            } else {
                if (WxlConvertUtils.isNotEmpty(oldSysUser.getBindUserId())) {
                    result.error500("该用户已被其他招聘官绑定，请刷新后尝试！");
                    return result;
                }
                String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
                Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
                boolean poolBind = userPermissionSet.contains("staffing:pool:bind");
                if (!poolBind) {
                    result.error500("权限不足");
                    return result;
                }

                baseCommonService.addLog("编辑用户（绑定招聘官），id： " + sysUser.getId(), CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3);
                oldSysUser.setUpdateTime(new Date());
                oldSysUser.setUpdateBy(user.getUsername());
                oldSysUser.setBindUserId(sysUser.getBindUserId());
                sysUserService.updateBindUserById(oldSysUser);
                result.success("绑定成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }


    /**
     * 管理部门用户列表
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
//    @PermissionData(pageComponent = "system/UserList")
    @RequestMapping(value = "/adminList", method = RequestMethod.GET)
    public Result<IPage<SysUser>> queryPageAdminList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<IPage<SysUser>> result = new Result<IPage<SysUser>>();
        Page<SysUser> page = new Page<SysUser>(pageNo, pageSize);
        //查询所有负责部门
        List<String> subDepids = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            subDepids = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (subDepids != null && subDepids.size() > 0) {
            IPage<SysUser> pageList = sysUserService.getUserByDepIds(page, subDepids, null);
            //批量查询用户的所属部门
            //step.1 先拿到全部的 useids
            //step.2 通过 useids，一次性查询用户的所属部门名字
            List<String> userIds = pageList.getRecords().stream().map(SysUser::getId).collect(Collectors.toList());
            if (userIds != null && userIds.size() > 0) {
                Map<String, String> useDepNames = sysUserService.getDepNamesByUserIds(userIds);
                pageList.getRecords().forEach(item -> {
                    //批量查询用户的所属部门
                    item.setOrgCodeTxt(useDepNames.get(item.getId()));
                });

            }
            result.setSuccess(true);
            result.setResult(pageList);
        } else {
            result.setSuccess(true);
            result.setResult(null);
        }
        return result;
    }

    /**
     * 用户信息修改
     *
     * @param sysUser
     * @return
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<SysUser> edit(@RequestBody SysUser sysUser) {
        Result<SysUser> result = new Result<SysUser>();
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (0 != user.getId().compareTo(sysUser.getId())) {
                return result.error500("修改对象与当前登录用户不符");
            }
            SysUser oldSysUser = sysUserService.getById(sysUser.getId());
            baseCommonService.addLog("编辑用户，id： " + sysUser.getId(), CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3);
            if (oldSysUser == null) {
                result.error500("未找到对应实体");
            } else {
                oldSysUser.setUpdateTime(new Date());
                oldSysUser.setUpdateBy(user.getUsername());
                oldSysUser.setRealname(sysUser.getRealname());
                oldSysUser.setAvatar(sysUser.getAvatar());
                oldSysUser.setSex(sysUser.getSex());
                oldSysUser.setEmail(sysUser.getEmail());
                sysUserService.updateById(oldSysUser);
                result.success("修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 修改邮箱
     *
     * @return
     */
    @PutMapping("/editEmail")
    public Result<SysUser> editEmail(@RequestBody SysUser sysUser) {
        Result result = new Result();
        try {
            Integer code = null;
            boolean b = redisUtil.hasKey(sysUser.getEmail());
            if (b) {
                code = (Integer) redisUtil.get(sysUser.getEmail());
            }
            Integer code2 = Integer.parseInt(sysUser.getCode());
            if (!WxlConvertUtils.isEmpty(sysUser.getEmail())) {
                if (!WxlConvertUtils.isNotEmpty(sysUser.getEmail())) {
                    result.setSuccess(false);
                    result.setMessage("邮箱不能为空!");
                    return result;
                }
                if (!WxlConvertUtils.isNotEmpty(sysUser.getCode())) {
                    result.setSuccess(false);
                    result.setMessage("验证码不能为空!");
                    return result;
                }
                if (!b) {
                    result.setSuccess(false);
                    result.setMessage("验证码错误!");
                    return result;
                } else {
                    if (!code.equals(code2)) {
                        result.setSuccess(false);
                        result.setMessage("验证码错误!");
                        return result;
                    }
                }
            }
        } catch (NumberFormatException e) {
            result.setSuccess(false);
            result.setMessage("验证码格式错误!");
            return result;
        }
        //修改邮箱
        sysUserService.updateEmailById(sysUser.getId(), sysUser.getEmail());
        result.setSuccess(true);
        result.setMessage("修改成功");
        return result;
    }

    /**
     * 修改微信号
     *
     * @param sysUser
     * @return
     */
    @PutMapping("/editWechatId")
    @Transactional
    public Result editWechatId(@RequestBody SysUser sysUser) {
        Result result = new Result();
        if (sysUser.getId().length() <= 0) {
            result.setSuccess(false);
            result.setMessage("获取用户信息失败");
            return result;
        } else if (sysUser.getWechatId().length() <= 0) {
            result.setSuccess(false);
            result.setMessage("获取微信号失败");
            return result;
        }
        Boolean r = sysUserService.editWechatIdById(sysUser.getId(), sysUser.getWechatId());
        if (r) {
            //同步名片微信号信息
            Boolean ir = staUserInfoService.editWechatIdById(sysUser.getId(), sysUser.getWechatId());
            if (ir) {
                result.setSuccess(true);
                result.setMessage("修改成功");
            } else {
                result.setSuccess(false);
                result.setMessage("修改失败");
            }
        } else {
            result.setSuccess(false);
            result.setMessage("修改失败");
        }
        return result;
    }

    @AutoLog(value = "删除灵工小程序管理")
    @ApiOperation(value = "删除灵工小程序管理", notes = "删除灵工小程序管理")
    @DeleteMapping(value = "/deleteUserRoleAndDepart")
    public Result<?> deleteUserRoleAndDepart(@RequestBody StaAdminModel staAdminModel) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未获取到管理员信息！");
        }
        SysUser sysUser = sysUserService.getById(staAdminModel.getUserId());
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("未获取到用户信息！");
        }
        SysDepart firm = sysDepartService.getById(staAdminModel.getFirmId());
        if (WxlConvertUtils.isEmpty(firm)) {
            return Result.error("未获取到公司信息！");
        }
        if ("13613831601".equals(sysUser.getUsername())) {
            return Result.error("权限不足，不能删除超级管理员！");
        }
        sysUser.setUpdateBy(user.getUsername());
        sysUser.setUpdateTime(new Date());
        sysUserService.editUserWithDepartRole(sysUser, firm, "");
        return Result.OK("删除成功！");
    }

    //TODO 记录1
    @AutoLog(value = "用户角色添加或更新")
    @ApiOperation(value = "用户角色添加或更新", notes = "用户角色添加或更新")
    @PutMapping(value = "/userRoleAndDepart")
    public Result<?> userRoleAndDepart(@RequestBody JSONObject jsonObject) {
        Result<SysUser> result = new Result<SysUser>();
        LoginUser u = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(u)) {
            return Result.error("未获取到管理员信息！");
        }
        try {
            SysUser sysUser = sysUserService.getById(jsonObject.getString("id"));
            baseCommonService.addLog("编辑用户，id： " + jsonObject.getString("id"), CommonConstant.LOG_TYPE_2, CommonConstant.OPERATE_TYPE_3);
            if (sysUser == null) {
                result.error500("未找到对应实体");
            } else {
                SysUser user = JSON.parseObject(jsonObject.toJSONString(), SysUser.class);
                user.setUpdateTime(new Date());
                user.setPassword(sysUser.getPassword());
                /**
                 * TODO 目前就丹尼斯一家  角色暂时写死固定
                 */
                String roles = "f6817f48af4fb3af11b9e8bf182f618b";
                SysDepart one = sysDepartService.getOne(new QueryWrapper<SysDepart>().eq("id", jsonObject.getString("firmId")));
                String departs = one.getId();
                user.setOrgCode(one.getOrgCode());
                user.setDeptId(jsonObject.getString("firmId"));
                user.setUserIdentity(1);//管理员
                // 修改用户走一个service 保证事务
                sysUserService.editUser(user, roles, departs);
                result.success("修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @ApiOperation("通过手机号")
    @RequestMapping(value = "/queryListByPhone", method = RequestMethod.GET)
    public Result<?> queryListByPhone(@RequestParam(name = "phone", required = true) String phone) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getPhone, phone);
//        queryWrapper.eq(SysUser::getUserIdentity,CommonConstant.USER_IDENTITY_STAFFING_USER);
        queryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<SysUser> list = sysUserService.list(queryWrapper);
        if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
            return Result.OK(list);
        } else {
            return Result.error("未找到用户");
        }
    }

    /**
     * @param phone 身份证号码
     * @return
     */
    @ApiOperation("通过身份证号查询普通用户")
    @RequestMapping(value = "/queryUserListByPhone", method = RequestMethod.GET)
    public Result<?> queryUserListByPhone(@RequestParam(name = "phone", required = true) String phone) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        if (phone.length() == 18) {
            queryWrapper.eq(SysUser::getIdCard, phone);
        } else {
            queryWrapper.eq(SysUser::getPhone, phone);
        }
        queryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<SysUser> list = sysUserService.list(queryWrapper);
        //需要当前工作门店 和任务
        if (WxlConvertUtils.isNotEmpty(list) && list.size() > 0) {
            SysUser sysUser = list.get(0);
            LambdaQueryWrapper<StaOrder> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.eq(StaOrder::getSysUserId, sysUser.getId());
            orderWrapper.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_3);
            orderWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
            StaOrder staOrder = staOrderService.getOne(orderWrapper);
            if (WxlConvertUtils.isNotEmpty(staOrder)) {
                sysUser.setCurrentStores(staOrder.getFirmName());
                sysUser.setCurrentWorkName(staOrder.getWorkName());
                sysUser.setStaOrderId(staOrder.getId());
            }
        }
        return Result.OK(list);
    }
    /**
     * 查询指定用户和部门关联的数据（灵工）
     *
     * @param userId
     * @return
     */
    @RequestMapping(value = "/userDepartList", method = RequestMethod.GET)
    public Result<List<StaDepartIdModel>> getUserDepartsList(@RequestParam(name = "userId", required = true) String userId) {
        Result<List<StaDepartIdModel>> result = new Result<>();
        try {
            List<StaDepartIdModel> depIdModelList = this.sysUserDepartService.queryStaDepartIdsOfUser(userId);
            if (depIdModelList != null && depIdModelList.size() > 0) {
                result.setSuccess(true);
                result.setMessage("查找成功");
                result.setResult(depIdModelList);
            } else {
                result.setSuccess(false);
                result.setMessage("查找失败");
            }
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.setSuccess(false);
            result.setMessage("查找过程中出现了异常: " + e.getMessage());
            return result;
        }
    }
    /**
     * 获取黑名单用户
     *
     * @param pageNo
     * @param pageSize
     * @param idCard   身份证号码
     * @param req
     * @return
     */
    @GetMapping("/blackListUserInfo")
    public Result<IPage<SysUser>> blackListUserInfo(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @RequestParam(name = "idCard") String idCard, HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<IPage<SysUser>>();
        //场景1:获取所有没有输入身份证号码
        if (WxlConvertUtils.isEmpty(idCard)) {
            Page<SysUser> page = new Page<SysUser>(pageNo, pageSize);
            QueryWrapper<SysUser> wrapper = new QueryWrapper<SysUser>().eq("blacklisted", CommonConstant.BLACKLIST1);
            Page<SysUser> blacklisted = sysUserService.page(page, wrapper);
            result.setSuccess(true);
            result.setResult(blacklisted);
        }
        //场景2:输入身份证号码获取单个用户
        if (WxlConvertUtils.isNotEmpty(idCard)) {
            //固定第一页 身份证查询只能有一条记录
            Page<SysUser> page = new Page<SysUser>(1, 1);
            QueryWrapper<SysUser> wrapper = new QueryWrapper<SysUser>().eq("blacklisted", CommonConstant.BLACKLIST1).eq("id_card", idCard);
            Page<SysUser> blacklisted = sysUserService.page(page, wrapper);
            result.setSuccess(true);
            result.setResult(blacklisted);
        }
        return result;
    }

    /**
     * 添加黑名单用户
     *
     * @param userId 用户ID
     * @return
     */
    @GetMapping("addBlackList/{userId}/{blacklistType}")
    public Result addBlackList(@PathVariable("userId") String userId, @PathVariable("blacklistType") String blacklistType) {
        Result result = new Result();
        SysUser user = sysUserService.getById(userId);

        String msg = "";
        switch (blacklistType) {
            //开除、劝退、内盗、劳动争议、
            case "0":
                msg = "开除";
                break;
            case "1":
                msg = "劝退";
                break;
            case "2":
                msg = "内盗";
                break;
            case "3":
                msg = "劳动争议";
                break;
            case "4":
                msg = "其他";
                break;
        }
        if (user != null) {
            if (WxlConvertUtils.isEmpty(user.getBlacklisted())) {
                user.setBlacklisted(CommonConstant.BLACKLIST1);
                user.setBlacklistType(msg);
                user.setBlacklistedTime(new Date());
                sysUserService.updateById(user);
                result.setSuccess(true);
                result.setMessage("添加成功");
            } else if (user.getBlacklisted().equals(CommonConstant.BLACKLIST0)) {
                user.setBlacklisted(CommonConstant.BLACKLIST1);
                user.setBlacklistedTime(new Date());
                user.setBlacklistType(msg);
                sysUserService.updateById(user);
                result.setSuccess(true);
                result.setMessage("添加成功");
            } else {
                result.setSuccess(false);
                result.setMessage("当前用户已在黑名单中！");
                return result;
            }
        } else {
            result.setSuccess(false);
            result.setMessage("未查询到该用户！");
            return result;
        }
        return result;
    }

    /**
     * 移除黑名单
     * @param sysUser
     * @return
     */
    @PutMapping("/deleteBlackList")
    public Result deleteBlackList(@RequestBody SysUser sysUser) {
        Result result = new Result();
        SysUser user = sysUserService.getById(sysUser.getId());
        if (user != null && WxlConvertUtils.isNotEmpty(user.getBlacklisted())) {
            if (user.getBlacklisted().equals(CommonConstant.BLACKLIST1)) {
                user.setBlacklisted(CommonConstant.BLACKLIST0);
                user.setBlacklistType(null);
                sysUserService.updateById(user);
                result.setSuccess(true);
                result.setMessage("移除成功");
            } else {
                result.setSuccess(false);
                result.setMessage("未找到该用户！");
            }
        } else {
            result.setSuccess(false);
            result.setMessage("未找到该用户！");
        }
        return result;
    }

    /**
     * 获取供应商签约接口
     * @param userId
     * @return
     */
    @GetMapping("/getSingPerson")
    public Result getSingPerson(@RequestParam("uid") String userId) throws Exception {
        String accessToken = zhService.getToken(); //获取Token
        if (WxlConvertUtils.isEmpty(userId)){
            return Result.error("用户丢失!");
        }
        SysUser sysUser = sysUserService.getById(userId);
        JSONObject jsonObject = zhService.singPerson(sysUser, accessToken);
        Boolean success = jsonObject.getBoolean("success");
        String message = jsonObject.getString("resultMessage");
        if (success){
            JSONObject resultData = jsonObject.getJSONObject("resultData");
            Integer code = resultData.getInteger("code");
            if (code != 1) {
                String m = resultData.getString("message");
                return Result.error(WxlConvertUtils.isNotEmpty(m) ? m : "远程API请求失败");
            }
            JSONObject data = resultData.getJSONObject("data");
            Integer singStatus = data.getInteger("singStatus");
            if (singStatus == 2) { //已签署
                sysUser.setAgencyStatus(CommonConstant.AGENCY_STATUS1);
            }else  { //没有签署
                sysUser.setAgencyPdfUrl(data.getString("singUrl"));//签约连接
                sysUser.setSingNo(data.getString("singNo"));//签约编号
                sysUser.setAgencyStatus(CommonConstant.AGENCY_STATUS0);
            }
            sysUserService.updateById(sysUser);
            return Result.OK(message,data);
        }else {
            return Result.error(WxlConvertUtils.isNotEmpty(message) ? message :"获取签约失败");
        }
    }

    /**
     * @param userId
     * @return
     */
    @GetMapping("/completionOfSigning")
    public Result completionOfSigning(@RequestParam("userId") String userId) throws Exception {
        if (WxlConvertUtils.isEmpty(userId)) {
            return new Result().error500("用户丢失");
        }
        SysUser sysUser = sysUserService.getById(userId);
        String accessToken = zhService.getToken(); //获取Token
        JSONObject jsonObject = zhService.singPerson(sysUser, accessToken);
        Boolean success = jsonObject.getBoolean("success");
        if (success){
            JSONObject resultData = jsonObject.getJSONObject("resultData");
            JSONObject data = resultData.getJSONObject("data");
            Integer singStatus = data.getInteger("singStatus");
            if (singStatus == 2) { //已签署
                sysUser.setAgencyStatus(CommonConstant.AGENCY_STATUS1);
                sysUserService.updateById(sysUser);
                return Result.OK();
            }
            return Result.OK(data);
        }else {
            return Result.error("获取签约失败");
        }
    }
    /**
     * pdf手写签名
     * @param jsonObject {签名图片,用户id}
     * @return Result
     */
    @PostMapping("/contractSigning")
    public Result contractSigning(@RequestBody JSONObject jsonObject) throws Exception {
        Result result = new Result();
        //签名图片
        String signature = jsonObject.getString("signature");
        //用户ID
        String userId = jsonObject.getString("userId");
        //PDF模板
        String templateFilePath = "https://mcl666.oss-cn-zhangjiakou.aliyuncs.com/%E9%92%9F%E7%82%B9%E5%B7%A5%E5%8A%B3%E5%8A%A1%E5%90%88%E2%80%94%E9%B8%A3%E7%BF%A0%E6%9F%B3%E7%94%B5%E5%AD%90%E5%90%88%E5%90%8C.pdf";
        //保存PDF文件
        String pdfFilePath = System.getProperty("user.dir");
        pdfFilePath = pdfFilePath + "\\byun-module-system\\src\\main\\resources\\contract\\" + userId + ".pdf";
        // 表单数据
        HashMap<String, String> data = new HashMap<>();
        LocalDate currentDate = LocalDate.now();
        String year = String.valueOf(currentDate.getYear());
        String month = String.valueOf(currentDate.getMonthValue());
        String day = String.valueOf(currentDate.getDayOfMonth());
        data.put("year1", year);
        data.put("year2", year);
        data.put("month1", month);
        data.put("month2", month);
        data.put("day1", day);
        data.put("day2", day);
        // 图片数据
        HashMap<String, String> imageData = new HashMap<>();
        imageData.put("signature", signature);
        // 根据PDF模版生成PDF文件
        String pdfUrl = createPDF(templateFilePath, data, imageData, true, pdfFilePath, endpoint, accessKeyId, secretAccessKey, bucketName, userId);
        //System.out.println(pdfUrl);
        SysUser user = sysUserService.getById(userId);
        user.setAgencyStatus(CommonConstant.AGENCY_STATUS1);
        user.setAgencyPdfUrl(pdfUrl);
        sysUserService.updateById(user);
        result.setSuccess(true);
        result.setResult(user);
        result.setMessage("签约成功！");
        return result;
    }
    /**
     * 获取远程图像的字节数组
     * @param imageUrl 图像的 URL
     * @return 字节数组
     * @throws IOException 如果 URL 不可用或无法连接，则抛出 IOException
     */
    private static byte[] getImageBytesFromUrl(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        InputStream in = conn.getInputStream();
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = in.read(buffer)) != -1) {
            out.write(buffer, 0, length);
        }
        in.close();
        return out.toByteArray();
    }

    /**
     * 根据PDF模版生成PDF文件
     * @param templateFilePath PDF模版文件路径
     * @param data             表单数据
     * @param imageData        图片数据 VALUE为图片文件路径
     * @param formFlattening   false：生成后的PDF文件表单域仍然可编辑 true：生成后的PDF文件表单域不可编辑
     * @param pdfFilePath      生成PDF的文件路径
     */
    private static String createPDF(String templateFilePath, HashMap<String, String> data, HashMap<String, String> imageData,
                                    boolean formFlattening, String pdfFilePath, String endpoint, String accessKeyId, String secretAccessKey, String bucketName, String userId) throws Exception {
        PdfReader reader = null;
        ByteArrayOutputStream bos = null;
        PdfStamper pdfStamper = null;
        FileOutputStream fos = null;
        try {
            //读取PDF模版文件网络格式
            URL url = new URL(templateFilePath);
            reader = new PdfReader(url.openStream());
            // 输出流
            bos = new ByteArrayOutputStream();
            // 构建PDF对象
            pdfStamper = new PdfStamper(reader, bos);
            // 获取表单数据
            AcroFields form = pdfStamper.getAcroFields();
            // 使用中文字体 使用 AcroFields填充值的不需要在程序中设置字体，在模板文件中设置字体为中文字体 Adobe 宋体 std L
            BaseFont bfChinese = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            form.addSubstitutionFont(bfChinese);
            // 表单赋值
            for (String key : data.keySet()) {
                form.setField(key, data.get(key));
                // 也可以指定字体
                form.setFieldProperty(key, "textfont", bfChinese, null);
            }
            //添加图片
            if (null != imageData && imageData.size() > 0) {
                for (String key : imageData.keySet()) {
                    int pageNo = form.getFieldPositions(key).get(0).page;
                    Rectangle signRect = form.getFieldPositions(key).get(0).position;
                    float x = signRect.getLeft();
                    float y = signRect.getBottom();
                    // 从远程 URL 获取图像字节数组
                    byte[] imageBytes = getImageBytesFromUrl(imageData.get(key));
                    // 将字节数组转换为 Image 对象
                    Image image = Image.getInstance(imageBytes);
                    // 获取操作的页面
                    PdfContentByte under = pdfStamper.getOverContent(pageNo);
                    // 根据域的大小缩放图片
                    image.scaleToFit(signRect.getWidth(), signRect.getHeight());
                    // 添加图片
                    image.setAbsolutePosition(x, y);
                    under.addImage(image);
                }
            }
            // 如果为false那么生成的PDF文件还能编辑，一定要设为true
            pdfStamper.setFormFlattening(formFlattening);
            pdfStamper.close();
            String u = AliOSSUtil.uploadBytesFile("contract/" + userId + ".pdf", bos.toByteArray());
            return "https://" + u;
        } finally {
            if (null != fos) {
                try {
                    fos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (null != bos) {
                try {
                    bos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (null != reader) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
