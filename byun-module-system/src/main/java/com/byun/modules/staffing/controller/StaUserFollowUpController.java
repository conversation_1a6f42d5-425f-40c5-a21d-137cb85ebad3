package com.byun.modules.staffing.controller;

import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaUserFollowUp;
import com.byun.modules.staffing.service.IStaUserFollowUpService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 跟进用户记录
 * @Author: bai
 * @Date:   2021-12-01
 * @Version: V1.0
 */
@Api(tags="跟进用户记录")
@RestController
@RequestMapping("/staffing/staUserFollowUp")
@Slf4j
public class StaUserFollowUpController extends ByunExcelController<StaUserFollowUp, IStaUserFollowUpService> {
	@Autowired
	private IStaUserFollowUpService staUserFollowUpService;
	@Autowired
	private ISysUserService sysUserService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staUserFollowUp
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "跟进用户记录-分页列表查询")
	@ApiOperation(value="跟进用户记录-分页列表查询", notes="跟进用户记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaUserFollowUp staUserFollowUp,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaUserFollowUp> queryWrapper = QueryGenerator.initQueryWrapper(staUserFollowUp, req.getParameterMap());
		//当前公司下的跟进记录
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String orgCode = user.getOrgCode();
		if(WxlConvertUtils.isEmpty(orgCode)){
			return Result.error("登录部门为空");
		}

		queryWrapper.likeRight("sys_org_code",orgCode);
		Page<StaUserFollowUp> page = new Page<StaUserFollowUp>(pageNo, pageSize);
		IPage<StaUserFollowUp> pageList = staUserFollowUpService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staUserFollowUp
	 * @return
	 */
	@AutoLog(value = "跟进用户记录-添加")
	@ApiOperation(value="跟进用户记录-添加", notes="跟进用户记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaUserFollowUp staUserFollowUp) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if(WxlConvertUtils.isEmpty(user)) {
			return Result.error("未找到登录用户");
		}
		String sysUserId = staUserFollowUp.getSysUserId();
		String followUserId = staUserFollowUp.getFollowUserId();
		if(WxlConvertUtils.isEmpty(sysUserId)){
			return Result.error("未找到被跟进的用户id");
		}
		SysUser sysUser = sysUserService.getById(sysUserId);
		if(WxlConvertUtils.isEmpty(sysUser)){
			return Result.error("未找到被跟进的用户");
		}
		if(WxlConvertUtils.isEmpty(followUserId)){
			return Result.error("未找到操作人id");
		}
		SysUser followUser = sysUserService.getById(followUserId);
		if(WxlConvertUtils.isEmpty(followUser)){
			return Result.error("未找到操作人");
		}
		staUserFollowUp.setSysOrgCode(followUser.getOrgCode());
		staUserFollowUp.setSysUserName(sysUser.getRealname());
		staUserFollowUp.setSysUserPhone(sysUser.getPhone());
		staUserFollowUp.setFollowUserName(followUser.getRealname());
		staUserFollowUp.setCreateBy(user.getUsername());
		staUserFollowUp.setCreateTime(new Date());
		staUserFollowUp.setFollowTime(new Date());
		staUserFollowUpService.save(staUserFollowUp);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staUserFollowUp
	 * @return
	 */
	@AutoLog(value = "跟进用户记录-编辑")
	@ApiOperation(value="跟进用户记录-编辑", notes="跟进用户记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaUserFollowUp staUserFollowUp) {
		staUserFollowUpService.updateById(staUserFollowUp);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "跟进用户记录-通过id删除")
	@ApiOperation(value="跟进用户记录-通过id删除", notes="跟进用户记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staUserFollowUpService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "跟进用户记录-批量删除")
	@ApiOperation(value="跟进用户记录-批量删除", notes="跟进用户记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staUserFollowUpService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "跟进用户记录-通过id查询")
	@ApiOperation(value="跟进用户记录-通过id查询", notes="跟进用户记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaUserFollowUp staUserFollowUp = staUserFollowUpService.getById(id);
		if(staUserFollowUp==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staUserFollowUp);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staUserFollowUp
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaUserFollowUp staUserFollowUp) {
        return super.exportXls(request, staUserFollowUp, StaUserFollowUp.class, "跟进用户记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaUserFollowUp.class);
    }

}
