package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaUserInfoBr;
import com.byun.modules.staffing.service.IStaUserInfoBrService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 名片浏览记录
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="名片浏览记录")
@RestController
@RequestMapping("/staffing/staUserInfoBr")
@Slf4j
public class StaUserInfoBrController extends ByunExcelController<StaUserInfoBr, IStaUserInfoBrService> {
	@Autowired
	private IStaUserInfoBrService staUserInfoBrService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staUserInfoBr
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "名片浏览记录-分页列表查询")
	@ApiOperation(value="名片浏览记录-分页列表查询", notes="名片浏览记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaUserInfoBr staUserInfoBr,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaUserInfoBr> queryWrapper = QueryGenerator.initQueryWrapper(staUserInfoBr, req.getParameterMap());
		Page<StaUserInfoBr> page = new Page<StaUserInfoBr>(pageNo, pageSize);
		IPage<StaUserInfoBr> pageList = staUserInfoBrService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staUserInfoBr
	 * @return
	 */
	@AutoLog(value = "名片浏览记录-添加")
	@ApiOperation(value="名片浏览记录-添加", notes="名片浏览记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaUserInfoBr staUserInfoBr) {
		staUserInfoBrService.save(staUserInfoBr);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staUserInfoBr
	 * @return
	 */
	@AutoLog(value = "名片浏览记录-编辑")
	@ApiOperation(value="名片浏览记录-编辑", notes="名片浏览记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaUserInfoBr staUserInfoBr) {
		staUserInfoBrService.updateById(staUserInfoBr);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "名片浏览记录-通过id删除")
	@ApiOperation(value="名片浏览记录-通过id删除", notes="名片浏览记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staUserInfoBrService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "名片浏览记录-批量删除")
	@ApiOperation(value="名片浏览记录-批量删除", notes="名片浏览记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staUserInfoBrService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "名片浏览记录-通过id查询")
	@ApiOperation(value="名片浏览记录-通过id查询", notes="名片浏览记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaUserInfoBr staUserInfoBr = staUserInfoBrService.getById(id);
		if(staUserInfoBr==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staUserInfoBr);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staUserInfoBr
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaUserInfoBr staUserInfoBr) {
        return super.exportXls(request, staUserInfoBr, StaUserInfoBr.class, "名片浏览记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaUserInfoBr.class);
    }

}
