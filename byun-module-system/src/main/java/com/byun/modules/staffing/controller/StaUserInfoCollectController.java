package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaUserInfoCollect;
import com.byun.modules.staffing.service.IStaUserInfoCollectService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 用户名片收藏
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="用户名片收藏")
@RestController
@RequestMapping("/staffing/staUserInfoCollect")
@Slf4j
public class StaUserInfoCollectController extends ByunExcelController<StaUserInfoCollect, IStaUserInfoCollectService> {
	@Autowired
	private IStaUserInfoCollectService staUserInfoCollectService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staUserInfoCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户名片收藏-分页列表查询")
	@ApiOperation(value="用户名片收藏-分页列表查询", notes="用户名片收藏-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaUserInfoCollect staUserInfoCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaUserInfoCollect> queryWrapper = QueryGenerator.initQueryWrapper(staUserInfoCollect, req.getParameterMap());
		Page<StaUserInfoCollect> page = new Page<StaUserInfoCollect>(pageNo, pageSize);
		IPage<StaUserInfoCollect> pageList = staUserInfoCollectService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staUserInfoCollect
	 * @return
	 */
	@AutoLog(value = "用户名片收藏-添加")
	@ApiOperation(value="用户名片收藏-添加", notes="用户名片收藏-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaUserInfoCollect staUserInfoCollect) {
		staUserInfoCollectService.save(staUserInfoCollect);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staUserInfoCollect
	 * @return
	 */
	@AutoLog(value = "用户名片收藏-编辑")
	@ApiOperation(value="用户名片收藏-编辑", notes="用户名片收藏-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaUserInfoCollect staUserInfoCollect) {
		staUserInfoCollectService.updateById(staUserInfoCollect);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户名片收藏-通过id删除")
	@ApiOperation(value="用户名片收藏-通过id删除", notes="用户名片收藏-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staUserInfoCollectService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户名片收藏-批量删除")
	@ApiOperation(value="用户名片收藏-批量删除", notes="用户名片收藏-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staUserInfoCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户名片收藏-通过id查询")
	@ApiOperation(value="用户名片收藏-通过id查询", notes="用户名片收藏-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaUserInfoCollect staUserInfoCollect = staUserInfoCollectService.getById(id);
		if(staUserInfoCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staUserInfoCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staUserInfoCollect
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaUserInfoCollect staUserInfoCollect) {
        return super.exportXls(request, staUserInfoCollect, StaUserInfoCollect.class, "用户名片收藏");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaUserInfoCollect.class);
    }

}
