package com.byun.modules.staffing.controller;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.byun.modules.staffing.service.IStaEnrollInfoService;
import com.byun.modules.staffing.service.impl.StaEnrollInfoServiceImpl;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import com.byun.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaUserInfoVisits;
import com.byun.modules.staffing.entity.StaUserInfo;
import com.byun.modules.staffing.vo.StaUserInfoPage;
import com.byun.modules.staffing.service.IStaUserInfoService;
import com.byun.modules.staffing.service.IStaUserInfoVisitsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 用户灵活用工信息（名片）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="用户灵活用工信息（名片）")
@RestController
@RequestMapping("/staffing/staUserInfo")
@Slf4j
public class StaUserInfoController {
	@Autowired
	private IStaUserInfoService staUserInfoService;
	@Autowired
	private IStaUserInfoVisitsService staUserInfoVisitsService;
	@Autowired
	private ISysUserService sysUserService;
	@Autowired
	private ISysDepartService sysDepartService;
	 @Autowired
	 private ISysBaseAPI sysBaseAPI;
	@Autowired
	private IStaEnrollInfoService staEnrollInfoService;
	 /**
	  * 通过用户phone查询
	  * @return
	  */
	 @AutoLog(value = "用户灵活用工信息（名片）-通过phone查询")
	 @ApiOperation(value="用户灵活用工信息（名片）-通过phone查询", notes="用户灵活用工信息（名片）-通过phone查询")
	 @GetMapping(value = "/queryByPhone")
	 public Result<?> queryByPhone(@RequestParam(name="phone",required=true) String phone) {
		 if(WxlConvertUtils.isEmpty(phone)){
			 return Result.error("电话为空");
		 }
		 LambdaQueryWrapper<StaUserInfo> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.eq(StaUserInfo::getPhone,phone);
		 StaUserInfo staUserInfo = staUserInfoService.getOne(queryWrapper);
		 if(staUserInfo==null) {
			 return Result.error("该用户可能由他人报名还未注册");
		 }
		 return Result.OK(staUserInfo);
	 }

	 /**
	  * 通过用户id查询
	  * @return
	  */
	 @AutoLog(value = "用户灵活用工信息（名片）-通过userID查询")
	 @ApiOperation(value="用户灵活用工信息（名片）-通过userID查询", notes="用户灵活用工信息（名片）-通过userID查询")
	 @GetMapping(value = "/queryByUserId")
	 public Result<?> queryByUserId(@RequestParam(name="id",required=true) String id) {
	 	if(WxlConvertUtils.isEmpty(id)){
	 		return Result.error("id为空");
		}
		 LambdaQueryWrapper<StaUserInfo> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.eq(StaUserInfo::getSysUserId,id);
		 StaUserInfo staUserInfo = staUserInfoService.getOne(queryWrapper);
		 if(staUserInfo==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(staUserInfo);
	 }

	 /**
	  * 通过id查询
	  * @return
	  */
	 @AutoLog(value = "用户灵活用工信息（名片）-通过登录用户查询")
	 @ApiOperation(value="用户灵活用工信息（名片）-通过登录用户查询", notes="用户灵活用工信息（名片）-通过登录用户查询")
	 @GetMapping(value = "/queryByUser")
	 public Result<?> queryByUser() {
		 LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 if(WxlConvertUtils.isEmpty(user)){
			 return Result.error("登录用户为空");
		 }
		 LambdaQueryWrapper<StaUserInfo> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.eq(StaUserInfo::getSysUserId,user.getId());
		 StaUserInfo staUserInfo = staUserInfoService.getOne(queryWrapper);
		 if(staUserInfo==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(staUserInfo);

	 }
	
	/**
	 * 分页列表查询
	 *
	 * @param staUserInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户灵活用工信息（名片）-分页列表查询")
	@ApiOperation(value="用户灵活用工信息（名片）-分页列表查询", notes="用户灵活用工信息（名片）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaUserInfo staUserInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		QueryWrapper<StaUserInfo> queryWrapper = QueryGenerator.initQueryWrapper(staUserInfo, req.getParameterMap());
		String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
		if(WxlConvertUtils.isEmpty(departId)){
			return Result.error("未获取到登录部门");
		}
		Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(),departId);
		if(WxlConvertUtils.isNotEmpty(req.getParameterMap())){
			String[] strings = (String[]) req.getParameterMap().get("hasBindUser");
			if(WxlConvertUtils.isNotEmpty(strings)&&strings.length>0&&"0".equals(strings[0])){//查询没有招聘官的名片
				queryWrapper.isNull("bind_user_id");
//                queryWrapper.or().eq("bind_user_id","");
				boolean poolCommon = userPermissionSet.contains("staffing:pool:common");//公域人才 权限
				if(!poolCommon) {
					return Result.error("暂无权限");
				}
			}else if(WxlConvertUtils.isNotEmpty(strings)&&strings.length>0&&"1".equals(strings[0])){//查询所有已绑定用户
				queryWrapper.isNotNull("bind_user_id");
//                queryWrapper.ne("bind_user_id","");
				//获取部门
//				int userIdentity = user.getUserIdentity() != null?user.getUserIdentity():CommonConstant.USER_IDENTITY_3;//普通用户
//				if(ByunConvertUtils.isNotEmpty(userIdentity) && userIdentity == CommonConstant.USER_IDENTITY_2 ){//高级管理
				boolean poolManage = userPermissionSet.contains("staffing:pool:manage");//人才管理 权限
				if(poolManage){
					//改为获取当前登录公司下所
					List<String> subDepids = new ArrayList<>();
//					subDepids = sysDepartService.getMySubDepIdsByDepId(user.getDepartIds());
					if(WxlConvertUtils.isNotEmpty(departId)){
						subDepids.add(departId);
						List<SysUser> sysUsers = sysUserService.queryByDepIds(subDepids, null);
						//批量查询用户的所属部门
						//step.1 先拿到全部的 useids
						//step.2 通过 useids，一次性查询用户的所属部门名字
						List<String> userIds = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
						//只查询自己所负责部门的
						queryWrapper.in("bind_user_id",userIds);
					}else{
						return Result.error("未查到所属部门");
					}
				}else{
					return Result.error("暂无权限");
				}
			}else {
				if(WxlConvertUtils.isEmpty(staUserInfo.getBindUserId())){
					return Result.error("未获取到绑定者id");
				}
				boolean poolPrivate = userPermissionSet.contains("staffing:pool:private");//私域人才 权限
				if(!poolPrivate) {
					return Result.error("暂无权限");
				}
			}
		}
		Page<StaUserInfo> page = new Page<>(pageNo, pageSize);
		IPage<StaUserInfo> pageList = staUserInfoService.page(page, queryWrapper);
		if(WxlConvertUtils.isNotEmpty(pageList.getRecords())&&pageList.getRecords().size()>0){
			for (StaUserInfo sui :pageList.getRecords()){
				//放入招聘官名字
				if(WxlConvertUtils.isNotEmpty(sui.getBindUserId())){{
					SysUser su = sysUserService.getById(sui.getBindUserId());
					sui.setBindUserName(su.getRealname());
				}}
				//放入学历信息
				if (WxlConvertUtils.isNotEmpty(sui.getId())){
					List<StaEnrollInfo> staEnrollInfo = staEnrollInfoService.list(new QueryWrapper<StaEnrollInfo>().eq("sys_user_id", sui.getSysUserId()));
					staEnrollInfo.forEach( sta -> {
						sui.setDegree(sta.getDegree());
					});
				}
			}
		}
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staUserInfoPage
	 * @return
	 */
	@AutoLog(value = "用户灵活用工信息（名片）-添加")
	@ApiOperation(value="用户灵活用工信息（名片）-添加", notes="用户灵活用工信息（名片）-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaUserInfoPage staUserInfoPage) {
		StaUserInfo staUserInfo = new StaUserInfo();
		BeanUtils.copyProperties(staUserInfoPage, staUserInfo);
		staUserInfoService.saveMain(staUserInfo, staUserInfoPage.getStaUserInfoVisitsList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staUserInfoPage
	 * @return
	 */
	@AutoLog(value = "用户灵活用工信息（名片）-编辑")
	@ApiOperation(value="用户灵活用工信息（名片）-编辑", notes="用户灵活用工信息（名片）-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaUserInfoPage staUserInfoPage) {
		StaUserInfo staUserInfo = new StaUserInfo();
		BeanUtils.copyProperties(staUserInfoPage, staUserInfo);
		StaUserInfo staUserInfoEntity = staUserInfoService.getById(staUserInfo.getId());
		if(staUserInfoEntity==null) {
			return Result.error("未找到对应数据");
		}
		if (WxlConvertUtils.isEmpty(staUserInfo.getSalar())){
			staUserInfo.setSalar("无要求"); //用户不选择薪资范围 默认无要求
		}
		staUserInfoService.updateMain(staUserInfo, staUserInfoPage.getStaUserInfoVisitsList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户灵活用工信息（名片）-通过id删除")
	@ApiOperation(value="用户灵活用工信息（名片）-通过id删除", notes="用户灵活用工信息（名片）-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staUserInfoService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户灵活用工信息（名片）-批量删除")
	@ApiOperation(value="用户灵活用工信息（名片）-批量删除", notes="用户灵活用工信息（名片）-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staUserInfoService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户灵活用工信息（名片）-通过id查询")
	@ApiOperation(value="用户灵活用工信息（名片）-通过id查询", notes="用户灵活用工信息（名片）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaUserInfo staUserInfo = staUserInfoService.getById(id);
		if(staUserInfo==null) {
			return Result.error("未找到对应数据");
		}
		//封装学历
		StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(
				new QueryWrapper<StaEnrollInfo>()
						.eq("sys_user_id", staUserInfo.getSysUserId())
						.eq("phone",staUserInfo.getPhone()));
		if (WxlConvertUtils.isNotEmpty(staEnrollInfo)){
			staUserInfo.setDegree(staEnrollInfo.getDegree());//学历
		}
		return Result.OK(staUserInfo);
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "名片访问量通过主表ID查询")
	@ApiOperation(value="名片访问量主表ID查询", notes="名片访问量-通主表ID查询")
	@GetMapping(value = "/queryStaUserInfoVisitsByMainId")
	public Result<?> queryStaUserInfoVisitsListByMainId(@RequestParam(name="id",required=true) String id) {
		List<StaUserInfoVisits> staUserInfoVisitsList = staUserInfoVisitsService.selectByMainId(id);
		return Result.OK(staUserInfoVisitsList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staUserInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaUserInfo staUserInfo) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<StaUserInfo> queryWrapper = QueryGenerator.initQueryWrapper(staUserInfo, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //Step.2 获取导出数据
      List<StaUserInfo> queryList = staUserInfoService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<StaUserInfo> staUserInfoList = new ArrayList<StaUserInfo>();
      if(WxlConvertUtils.isEmpty(selections)) {
          staUserInfoList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          staUserInfoList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<StaUserInfoPage> pageList = new ArrayList<StaUserInfoPage>();
      for (StaUserInfo main : staUserInfoList) {
          StaUserInfoPage vo = new StaUserInfoPage();
          BeanUtils.copyProperties(main, vo);
          List<StaUserInfoVisits> staUserInfoVisitsList = staUserInfoVisitsService.selectByMainId(main.getId());
          vo.setStaUserInfoVisitsList(staUserInfoVisitsList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "用户灵活用工信息（名片）列表");
      mv.addObject(NormalExcelConstants.CLASS, StaUserInfoPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("用户灵活用工信息（名片）数据", "导出人:"+sysUser.getRealname(), "用户灵活用工信息（名片）"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<StaUserInfoPage> list = ExcelImportUtil.importExcel(file.getInputStream(), StaUserInfoPage.class, params);
              for (StaUserInfoPage page : list) {
                  StaUserInfo po = new StaUserInfo();
                  BeanUtils.copyProperties(page, po);
                  staUserInfoService.saveMain(po, page.getStaUserInfoVisitsList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
