package com.byun.modules.staffing.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaUserRecord;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaUserRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.service.IStaWorkService;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 用户记录表
 * @Author: bai
 * @Date:   2021-12-01
 * @Version: V1.0
 */
@Api(tags="用户记录表")
@RestController
@RequestMapping("/staffing/staUserRecord")
@Slf4j
public class StaUserRecordController extends ByunExcelController<StaUserRecord, IStaUserRecordService> {
	@Autowired
	private IStaUserRecordService staUserRecordService;
	@Autowired
    private IStaWorkService staWorkService;
	@Autowired
	private IStaOrderService staOrderService;

	/**
	 * 分页列表查询
	 *
	 * @param staUserRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户记录表-分页列表查询")
	@ApiOperation(value="用户记录表-分页列表查询", notes="用户记录表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaUserRecord staUserRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaUserRecord> queryWrapper = QueryGenerator.initQueryWrapper(staUserRecord, req.getParameterMap());
		Page<StaUserRecord> page = new Page<StaUserRecord>(pageNo, pageSize);
		IPage<StaUserRecord> pageList = staUserRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staUserRecord
	 * @return
	 */
	@AutoLog(value = "用户记录表-添加")
	@ApiOperation(value="用户记录表-添加", notes="用户记录表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaUserRecord staUserRecord) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if(WxlConvertUtils.isEmpty(user)){
			return  Result.error("未获取到登录用户");
		}

		if(CommonConstant.RECORD_TYPE_1.equals(staUserRecord.getRecordType())||CommonConstant.RECORD_TYPE_3.equals(staUserRecord.getRecordType())) {//浏览记录收藏记录
			if(WxlConvertUtils.isNotEmpty(staUserRecord.getJumpId())){
			//先查询
				LambdaQueryWrapper<StaUserRecord> queryWrapper = new LambdaQueryWrapper<>();
				queryWrapper.eq(StaUserRecord::getJumpType,staUserRecord.getJumpType());
				queryWrapper.eq(StaUserRecord::getJumpId,staUserRecord.getJumpId());
				queryWrapper.eq(StaUserRecord::getRecordType,staUserRecord.getRecordType());
				queryWrapper.eq(StaUserRecord::getSysUserId,user.getId());
				StaUserRecord one = staUserRecordService.getOne(queryWrapper);
				StaWork staWork = staWorkService.getById(staUserRecord.getJumpId());
				if(CommonConstant.RECORD_TYPE_1.equals(staUserRecord.getRecordType())){
					String string = "浏览了" + "<" + staWork.getCompanyName() + ">" + staWork.getNameNick();
					staUserRecord.setRecordContent(string);
				}else{
					String string = "收藏了" + "<" + staWork.getCompanyName() + ">" + staWork.getNameNick();
					staUserRecord.setRecordContent(string);
				}
				//若是空，以前没有记录则添加一条新的
				if(WxlConvertUtils.isEmpty(one)){
					staUserRecord.setCreateBy(user.getUsername());
					staUserRecord.setCreateTime(new Date());
					staUserRecord.setRecordTime(new Date());
					staUserRecord.setDelFlag(CommonConstant.DEL_FLAG_0);
					staUserRecord.setSysUserId(user.getId());
					staUserRecordService.save(staUserRecord);
					return Result.OK("添加成功！");
				}else{
					one.setUpdateBy(user.getUsername());
					one.setUpdateTime(new Date());
					one.setRecordTime(new Date());
					staUserRecordService.updateById(one);
					return Result.OK("更新成功！");
				}
			}
		}else if(CommonConstant.RECORD_TYPE_4.equals(staUserRecord.getRecordType())){
			if(WxlConvertUtils.isNotEmpty(staUserRecord.getJumpId())){
				StaWork staWork = staWorkService.getById(staUserRecord.getJumpId());
				String string = "申请了" + "<" + staWork.getCompanyName() + ">" + staWork.getNameNick();
				staUserRecord.setRecordContent(string);
				staUserRecord.setCreateBy(user.getUsername());
				staUserRecord.setCreateTime(new Date());
				staUserRecord.setRecordTime(new Date());
				staUserRecord.setDelFlag(CommonConstant.DEL_FLAG_0);
				staUserRecord.setSysUserId(user.getId());
				staUserRecordService.save(staUserRecord);
				return Result.OK("添加成功！");
			}
		}else if(CommonConstant.RECORD_TYPE_5.equals(staUserRecord.getRecordType())){
			if(WxlConvertUtils.isNotEmpty(staUserRecord.getJumpId())){
				StaWork staWork = staWorkService.getById(staUserRecord.getJumpId());
				String string = "撤销了" + "<" + staWork.getCompanyName() + ">" + staWork.getNameNick();
				staUserRecord.setRecordContent(string);
				staUserRecord.setCreateBy(user.getUsername());
				staUserRecord.setCreateTime(new Date());
				staUserRecord.setRecordTime(new Date());
				staUserRecord.setDelFlag(CommonConstant.DEL_FLAG_0);
				staUserRecord.setSysUserId(user.getId());
				staUserRecordService.save(staUserRecord);
				return Result.OK("添加成功！");
			}
		}else if(CommonConstant.RECORD_TYPE_6.equals(staUserRecord.getRecordType())){
			//任务开始记录
			if(WxlConvertUtils.isNotEmpty(staUserRecord.getJumpId())){
				StaWork staWork = staWorkService.getById(staUserRecord.getJumpId());
				LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
				staOrderLambdaQueryWrapper.eq(StaOrder::getStaWorkId,staWork.getId());
				staOrderLambdaQueryWrapper.eq(StaOrder::getDelFlag,CommonConstant.DEL_FLAG_0);
				staOrderLambdaQueryWrapper.in(StaOrder::getStateFlag,CommonConstant.ORDER_STATUS_3,CommonConstant.ORDER_STATUS_5);
				List<StaOrder> list = staOrderService.list(staOrderLambdaQueryWrapper);
				for(StaOrder staOrder: list){
					StaUserRecord sur = new StaUserRecord(staUserRecord);
					String string = "任务开始" + "<" + staWork.getCompanyName() + ">" + staWork.getNameNick();
					sur.setRecordContent(string);
					sur.setCreateBy(user.getUsername());
					sur.setCreateTime(new Date());
					sur.setRecordTime(new Date());
					sur.setDelFlag(CommonConstant.DEL_FLAG_0);
					sur.setSysUserId(staOrder.getSysUserId());
					staUserRecordService.save(sur);
				}
				return Result.OK("添加成功！");
			}
		}else if(CommonConstant.RECORD_TYPE_7.equals(staUserRecord.getRecordType())){
			//任务完成记录
			if(WxlConvertUtils.isNotEmpty(staUserRecord.getJumpId())){
				StaWork staWork = staWorkService.getById(staUserRecord.getJumpId());
				LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
				staOrderLambdaQueryWrapper.eq(StaOrder::getStaWorkId,staWork.getId());
				staOrderLambdaQueryWrapper.eq(StaOrder::getDelFlag,CommonConstant.DEL_FLAG_0);
				staOrderLambdaQueryWrapper.in(StaOrder::getStateFlag,CommonConstant.ORDER_STATUS_3,CommonConstant.ORDER_STATUS_1);
				List<StaOrder> list = staOrderService.list(staOrderLambdaQueryWrapper);
				for(StaOrder staOrder: list){
					StaUserRecord sur = new StaUserRecord(staUserRecord);
					String string = "任务完成" + "<" + staWork.getCompanyName() + ">" + staWork.getNameNick();
					sur.setRecordContent(string);
					sur.setCreateBy(user.getUsername());
					sur.setCreateTime(new Date());
					sur.setRecordTime(new Date());
					sur.setDelFlag(CommonConstant.DEL_FLAG_0);
					sur.setSysUserId(staOrder.getSysUserId());
					staUserRecordService.save(sur);
				}
				return Result.OK("添加成功！");
			}
		}else {
			return Result.error("未知类型！");
		}
		return Result.error("未知类型！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staUserRecord
	 * @return
	 */
	@AutoLog(value = "用户记录表-编辑")
	@ApiOperation(value="用户记录表-编辑", notes="用户记录表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaUserRecord staUserRecord) {
		staUserRecordService.updateById(staUserRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户记录表-通过id删除")
	@ApiOperation(value="用户记录表-通过id删除", notes="用户记录表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staUserRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户记录表-批量删除")
	@ApiOperation(value="用户记录表-批量删除", notes="用户记录表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staUserRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户记录表-通过id查询")
	@ApiOperation(value="用户记录表-通过id查询", notes="用户记录表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaUserRecord staUserRecord = staUserRecordService.getById(id);
		if(staUserRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staUserRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staUserRecord
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaUserRecord staUserRecord) {
        return super.exportXls(request, staUserRecord, StaUserRecord.class, "用户记录表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaUserRecord.class);
    }

}
