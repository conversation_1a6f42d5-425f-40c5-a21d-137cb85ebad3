package com.byun.modules.staffing.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.ImportExcelUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaUserSettlement;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaUserSettlementService;
import com.byun.modules.staffing.vo.excel.entity.BtchBiz;
import com.byun.modules.system.entity.StaUserBankCards;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.IStaUserBankCardsService;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-6-22 9:30
 */
@RestController
@RequestMapping("/sys/user/settlement")
public class StaUserSettlementController {
    @Autowired
    private IStaUserSettlementService staUserSettlementService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IStaUserBankCardsService staUserBankCardsService;

    /**
     * 获取结算列表数据
     *
     * @param staUserSettlement
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping("/list")
    public Result<?> list(StaUserSettlement staUserSettlement, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        Map<String, String[]> parameterMap = req.getParameterMap();
        QueryWrapper<StaUserSettlement> queryWrapper = QueryGenerator.initQueryWrapper(staUserSettlement, parameterMap);
        IPage<StaUserSettlement> iPage = new Page<>(pageNo, pageSize);
        IPage<StaUserSettlement> page = staUserSettlementService.page(iPage, queryWrapper);
        return Result.OK("操作成功", page);
    }

    /**
     * excel导出结算模板
     *
     * @return
     */
    @RequestMapping("/exportXlsModel")
    public ModelAndView exportXlsModel() {
        List<StaUserSettlement> staUserSettlements = new ArrayList<>();
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "结算导入模板");
        mv.addObject(NormalExcelConstants.CLASS, StaUserSettlement.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("结算导入模板(统一格式文本日期格式yyyy-MM-dd时间格式:HH:mm)", "结算"));
        mv.addObject(NormalExcelConstants.DATA_LIST, staUserSettlements);
        return mv;
    }

    /**
     * excel导入结算数据
     *
     * @param request
     * @param
     * @return
     */
    @Transactional
    @PostMapping("/importExcel")
    @AutoLog(value = "导入结算数据", operateType = 5)
    public synchronized Result<?> importExcel(HttpServletRequest request) throws Exception {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0;
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            params.setNeedSave(true);
            //try (InputStream inputStream = file.getInputStream()) {
            int row = 1;//读取的数据行
            List<StaUserSettlement> staUserSettlements = ExcelImportUtil.importExcel(file.getInputStream(), StaUserSettlement.class, params);
            Set<String> idCardS = staUserSettlements.stream().map(StaUserSettlement::getIdCard).collect(Collectors.toSet());//身份证ids
            //获取用户
            List<SysUser> sysUsers = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getIdCard, idCardS).eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0));
            //任务单
//                List<StaOrder> staOrders = staOrderService.list(new LambdaQueryWrapper<StaOrder>().in(StaOrder::getEnrollIdCard, idCardS)
//                        .notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_0, CommonConstant.ORDER_STATUS_1, CommonConstant.ORDER_STATUS_2))
//                        .eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0));
            List<String> uidList = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
            //结算账户
            List<StaUserBankCards> staUserBankCards = staUserBankCardsService.list(new LambdaQueryWrapper<StaUserBankCards>()
                    .in(StaUserBankCards::getUserId, uidList)); //.in(StaUserBankCards::getAccountType,Arrays.asList(1,2)
            for (StaUserSettlement staUserSettlement : staUserSettlements) {
                row++;
                SysDepart sysDepart = sysDepartService.getOne(new QueryWrapper<SysDepart>().lambda()
                        .eq(SysDepart::getStoreNo, staUserSettlement.getStoreNo())
                        .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0)
                        .eq(SysDepart::getOrgType, 4)
                );
                if (WxlConvertUtils.isEmpty(sysDepart)) {
                    errorLines++;
                    errorMessage.add("第" + row + "行出现错误" + "店别不存在：店编-" + staUserSettlement.getStoreNo() + "  店别-" + staUserSettlement.getCompanyName());
                    return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                }
                SysDepart sysDepartRegion = sysDepartService.getById(sysDepart.getParentId());
                staUserSettlement.setRegionId(sysDepartRegion.getId());
                staUserSettlement.setBusinessDivisionId(sysDepartRegion.getParentId());
                String idCard = staUserSettlement.getIdCard();
                List<SysUser> sysUserList = sysUsers.stream().filter(s -> s.getIdCard().equals(idCard)).collect(Collectors.toList());
                if (WxlConvertUtils.isEmpty(sysUserList) || sysUserList.isEmpty()) {
                    errorLines++;
                    errorMessage.add("第" + row + "行出现错误" + "用户不存在：姓名-" + staUserSettlement.getUserName() + ",身份证-" + staUserSettlement.getIdCard());
                    return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                } else {
                    if (sysUsers.get(0).getAgencyStatus() == null || sysUsers.get(0).getAgencyStatus() != 1) {
                        errorLines++;
                        errorMessage.add("第" + row + "行出现错误" + "用户未签约：姓名-" + staUserSettlement.getUserName() + ",身份证-" + staUserSettlement.getIdCard());
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    } else {
                        staUserSettlement.setSysUserId(sysUserList.get(0).getId());
                        staUserSettlement.setOrgCode(sysDepart.getOrgCode());
                        staUserSettlement.setMobile(sysUserList.get(0).getUsername());
                    }
                    //任务单可能多个
                    //List<StaOrder> staOrderList = staOrders.stream().filter(s -> s.getEnrollIdCard().equals(idCard) && s.getCompanyId().equals(sysDepart.getId())).collect(Collectors.toList());
//                    if (staOrderList.isEmpty()) {
//                        errorLines++;
//                        errorMessage.add("第"+row+"行出现错误"+"任务单不存在或已提交结算：姓名-" + staUserSettlement.getUserName() + " 店编-" + sysDepart.getStoreNo() + " -店别" + sysDepart.getDepartName());
//                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
//                    }
                    //staUserSettlement.setOrgCode(staOrderList.get(0).getSysOrgCode());
                    //结算账户
                    // if (WxlConvertUtils.isNotEmpty(staUserSettlement.getBankNo())) {
                    //sysUserList
                    List<StaUserBankCards> staUserBankCards1 = staUserBankCards.stream().filter(s -> s.getUserId().equals(sysUserList.get(0).getId())).collect(Collectors.toList());
                    if (WxlConvertUtils.isNotEmpty(staUserBankCards1) && !staUserBankCards1.isEmpty()) {
                        if (staUserBankCards1.get(0).getAccountType() == CommonConstant.ACCOUNTTYPE1) {//银行卡
                            staUserSettlement.setBankName(staUserBankCards1.get(0).getBankName());
                            staUserSettlement.setTaskNo(CommonConstant.TASKNO1);
                            staUserSettlement.setAccountType("1");
                            staUserSettlement.setBankNo(staUserBankCards1.get(0).getCardNumber());
                        } else {//支付宝
                            staUserSettlement.setAccountType("2");
                            staUserSettlement.setTaskNo(CommonConstant.TASKNO2);
                            staUserSettlement.setBankName(staUserBankCards1.get(0).getBankName());
                            staUserSettlement.setBankNo(staUserBankCards1.get(0).getCardNumber());
                        }
                    } else {
                        //System.out.println("aaaa");
                    }
                    //    }
                    //if (WxlConvertUtils.isEmpty(staUserBankCards1) || staUserBankCards1.isEmpty()) {
//                            errorLines++;
//                            errorMessage.add("第"+row+"行出现错误"+"结算账户错误或和认证不一致：姓名-" + staUserSettlement.getUserName());
//                            return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
//                        }
//                        if (staUserSettlement.getAccountType() == String.valueOf(CommonConstant.ACCOUNTTYPE1)) {//银行卡
//                            staUserSettlement.setBankName(staUserBankCards1.get(0).getBankName());
//                            staUserSettlement.setTaskNo(CommonConstant.TASKNO1);
//                        } else {//支付宝
//                            staUserSettlement.setTaskNo(CommonConstant.TASKNO2);
//                        }
                }
                //StringBuilder sb = new StringBuilder();
//                    for (StaOrder staOrder : staOrderList) {
//                        sb.append(staOrder.getId());
//                        sb.append(",");
//                    }
                //staUserSettlement.setStaOrderIds(sb.toString());//任务单集合
                staUserSettlement.setCompanyId(sysDepart.getId());
                staUserSettlement.setStatus(CommonConstant.SETTLETANACCOUNSTATUS0);
                String date = sdf.format(new Date());
                String randomNum = String.format("%010d", Math.abs(new Random().nextInt()));//10位随机数
                String batchBizId = "MCL" + date + randomNum;//批次号
                staUserSettlement.setBizId(batchBizId);
                staUserSettlement.setCreateTime(new Date());
                staUserSettlement.setCreateBy(user.getId());
                successLines++;
            }
            //List<StaUserSettlement> collect = staUserSettlements.stream().filter(s -> s.getAccountType() == null).collect(Collectors.toList());
            staUserSettlementService.saveBatch(staUserSettlements);
//                if (!staUserSettlements.isEmpty()) {
//                    staUserSettlementService.saveBatch(staUserSettlements);
//                    //修改任务单状态修改为待结算
//                    for (StaOrder staOrder : staOrders) {
//                        staOrderService.agreeResign(staOrder.getId(), "", "3");
//                    }
//                }
//            } catch (Exception e) {
//                System.out.println(e.getMessage());
//                errorMessage.add("发生异常：" + e.getMessage());
//            }
        }
        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
    }

    /**
     * excel导出结算列表
     *
     * @param staUserSettlement
     * @param request
     * @return
     */
    @RequestMapping("/exportXls")
    public ModelAndView exportXls(StaUserSettlement staUserSettlement, HttpServletRequest request) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, String[]> parameterMap = request.getParameterMap();
        String selections = request.getParameter("selections");
        QueryWrapper<StaUserSettlement> queryWrapper = QueryGenerator.initQueryWrapper(staUserSettlement, parameterMap);
        List<StaUserSettlement> queryList = staUserSettlementService.list(queryWrapper);
        //过滤选中数据
        List<StaUserSettlement> staUserSettlements = new ArrayList<StaUserSettlement>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staUserSettlements = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staUserSettlements = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        for (StaUserSettlement userSettlement : staUserSettlements) {
            if (WxlConvertUtils.isNotEmpty(userSettlement.getTaskStartDate())) {
                userSettlement.setTaskStartDate(userSettlement.getTaskStartDate().substring(0, 10));
            }
            if (WxlConvertUtils.isNotEmpty(userSettlement.getTaskEndDate())) {
                userSettlement.setTaskEndDate(userSettlement.getTaskEndDate().substring(0, 10));
            }
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "结算列表");
        mv.addObject(NormalExcelConstants.CLASS, StaUserSettlement.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("结算列表", "导出人:" + user.getRealname(), "结算列表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, staUserSettlements);
        return mv;
    }

    /**
     * 提现推送
     */
    @PostMapping("/doWithdraw")
    @Transactional
    @AutoLog(value = "提现推送", operateType = 3)
    public synchronized Result doWithdraw(StaUserSettlement staUserSettlement, HttpServletRequest request) throws Exception {
        Map<String, String[]> parameterMap = request.getParameterMap();
        String selections = request.getParameter("selections");
        if (WxlConvertUtils.isEmpty(staUserSettlement.getIds())) {
            return Result.error("参数丢失");
        }
        List<String> ids = staUserSettlement.getIds();
        List<String> settlementIds = new ArrayList<>();
        for (String id : ids) {
            String noQuotes = id.replaceAll("\"", "");
            String s = noQuotes.replaceAll("[\\[\\]]", "");
            settlementIds.add(s);
        }
        QueryWrapper<StaUserSettlement> queryWrapper = QueryGenerator.initQueryWrapper(staUserSettlement, parameterMap);
        queryWrapper.lambda().in(StaUserSettlement::getId, settlementIds);
        List<StaUserSettlement> queryList = staUserSettlementService.list(queryWrapper);
        //过滤选中数据
        List<StaUserSettlement> staUserSettlements = new ArrayList<StaUserSettlement>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staUserSettlements = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staUserSettlements = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        Result<?> result = staUserSettlementService.doWithdraw(staUserSettlements);
        return result;
    }

    /**
     * 编辑结算数据
     *
     * @param staUserSettlement
     * @return
     */
    @PostMapping("/edit")
    public Result edit(@RequestBody StaUserSettlement staUserSettlement) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(staUserSettlement)) {
            return Result.error("参数丢失");
        }
        StaUserSettlement settlement = staUserSettlementService.getById(staUserSettlement.getId());
//        if (WxlConvertUtils.isNotEmpty(settlement)) {
//            if (staUserSettlement.get)
//        }
        BeanUtils.copyProperties(staUserSettlement, settlement, getNullPropertyNames(staUserSettlement));
        staUserSettlementService.updateById(settlement);
        return Result.OK("操作成功", "");
    }

    /**
     * 拷贝的时候 忽略null值
     *
     * @param source
     * @return
     */
    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return (String[]) emptyNames.toArray(result);
    }

    /**
     * 获取批次号导入模板
     *
     * @return
     */
    @RequestMapping("/getbBtchBizIdModel")
    public ModelAndView getbBtchBizIdModel() {
        List<BtchBiz> btchBizs = new ArrayList<>();
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "批次号导入模板(统一格式文本日期格式yyyy-MM-dd时间格式:HH:mm)");
        mv.addObject(NormalExcelConstants.CLASS, BtchBiz.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("批次号导入模板", "批次号"));
        mv.addObject(NormalExcelConstants.DATA_LIST, btchBizs);
        return mv;
    }

    /**
     * excel导入提现结果,同步任务单
     *
     * @param request
     * @return
     */
    @PostMapping("/importExcelBatchBiz")
    public Result<?> importExcelBatchBiz(HttpServletRequest request) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0;
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try (InputStream inputStream = file.getInputStream()) {
                List<BtchBiz> btchBizList = ExcelImportUtil.importExcel(inputStream, BtchBiz.class, params);
                if (btchBizList == null || btchBizList.isEmpty())
                    return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                int num = 2;
                for (BtchBiz btchBiz : btchBizList) {
                    num++;
                    //检查身份证格式
                    boolean idCardNumberValid = IdCardUtil.isIdCardNumberValid(btchBiz.getIdCard());
                    if (!idCardNumberValid) {
                        errorLines++;
                        errorMessage.add("第" + num + "行出现错误" + "姓名-" + btchBiz.getUserName() + "，身份证格式错误");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    if (btchBiz.getStatus().equals(CommonConstant.SETTLETANACCOUNSTATUS1) || btchBiz.getStatus().equals(CommonConstant.SETTLETANACCOUNSTATUS99)) {
                    } else {
                        errorMessage.add("第" + num + "行出现错误" + "姓名-" + btchBiz.getUserName() + "状态格式错误");
                        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
                    }
                    successLines++; //成功行数
                }
                Map<String, List<BtchBiz>> groupedBtchBizList = btchBizList.stream()
                        .collect(Collectors.groupingBy(BtchBiz::getStatus));
                processBtchBizList(groupedBtchBizList.get(CommonConstant.SETTLETANACCOUNSTATUS1), CommonConstant.SETTLETANACCOUNSTATUS1);
                processBtchBizList(groupedBtchBizList.get(CommonConstant.SETTLETANACCOUNSTATUS99), CommonConstant.SETTLETANACCOUNSTATUS99);
            } catch (Exception e) {
                errorMessage.add("异常" + file.getOriginalFilename());
            }
        }
        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
    }

    /**
     * @param btchBizList
     * @param status      1成功数据 99 失败数据
     */
    @Transactional
    public void processBtchBizList(List<BtchBiz> btchBizList, String status) {
        if (btchBizList == null || btchBizList.isEmpty()) {
            return;
        }
        Set<String> batchBizIds = btchBizList.stream().map(BtchBiz::getBtchBizId).collect(Collectors.toSet());
        List<String> bizIds = btchBizList.stream().map(BtchBiz::getBizId).collect(Collectors.toList());
        List<String> idCards = btchBizList.stream().map(BtchBiz::getIdCard).collect(Collectors.toList());
        Set<String> userNames = btchBizList.stream().map(BtchBiz::getUserName).collect(Collectors.toSet());
        LambdaQueryWrapper<StaUserSettlement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StaUserSettlement::getBatchBizId, batchBizIds);
        queryWrapper.in(StaUserSettlement::getBizId, bizIds);
        queryWrapper.in(StaUserSettlement::getIdCard, idCards);
        queryWrapper.in(StaUserSettlement::getUserName, userNames);
        queryWrapper.eq(StaUserSettlement::getStatus, CommonConstant.SETTLETANACCOUNSTATUS2);
        List<StaUserSettlement> settlements = staUserSettlementService.list(queryWrapper);
        List<String> idCardList = new ArrayList<>();
        for (StaUserSettlement settlement : settlements) {
            settlement.setStatus(status);
            if (settlement.getStatus().equals(CommonConstant.SETTLETANACCOUNSTATUS1)) { //获取提现成功的用户身份证
                idCardList.add(settlement.getIdCard());
            }
        }
        //待结算的任务单
        List<StaOrder> staOrderList = staOrderService.list(new QueryWrapper<StaOrder>()
                .lambda()
                .in(StaOrder::getEnrollIdCard, idCardList)
                .eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_4));
        if (WxlConvertUtils.isNotEmpty(staOrderList)) {
            for (StaOrder staOrder : staOrderList) {
                staOrder.setStateFlag(CommonConstant.ORDER_STATUS_1);
            }
            staOrderService.updateBatchById(staOrderList);
        }
        staUserSettlementService.updateBatchById(settlements);
    }

    /**
     * 生成结算单
     *
     * @param
     */
    @PostMapping("/createPay")
    @Transactional
    @AutoLog(value = "创建结算单",logType = 2,operateType = 2)
    public Result<?> createPay(@RequestBody StaOrder staOrder) {
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(staOrder)) {
            return Result.error("任务单不存在");
        }
        if (!staOrder.getStateFlag().equals(CommonConstant.ORDER_STATUS_4)) {
            return Result.error("任务单不是待结算状态");
        }
        StaUserSettlement staUserSettlement = new StaUserSettlement();
        staUserSettlement.setSysUserId(staOrder.getSysUserId());
        staUserSettlement.setCreateTime(new Date());
        staUserSettlement.setCreateBy(user.getId());
        staUserSettlement.setStaOrderIds(staOrder.getId());
        staUserSettlement.setUserName(staOrder.getUserName());//姓名
        staUserSettlement.setIdCard(staOrder.getUserIdCard());//身份证
        staUserSettlement.setBusinessDivisionName(staOrder.getBusinessDivisionName());
        staUserSettlement.setBusinessDivisionId(staOrder.getBusinessDivisionId());
        staUserSettlement.setRegionName(staOrder.getRegionName());
        staUserSettlement.setRegionId(staOrder.getRegionId());
        staUserSettlement.setStoreNo(staOrder.getStoreNo());
        staUserSettlement.setCompanyName(staOrder.getCompanyName());
        staUserSettlement.setCompanyId(staOrder.getCompanyId());
        staUserSettlement.setStoreNo(staOrder.getStoreNo());
        staUserSettlement.setMobile(staOrder.getEnrollPhone());
        //工时
        if (WxlConvertUtils.isNotEmpty(staOrder.getTotalWorkingHours())) {
            staUserSettlement.setTaskManHour(staOrder.getTotalWorkingHours());
        }else {
            staUserSettlement.setTaskManHour(0.0);
        }
        Date timeOfResignation = staOrder.getTimeOfResignation();
        staUserSettlement.setMonth(sdf.format(timeOfResignation));//月份
        staUserSettlement.setTaskStartDate(sb.format(staOrder.getEntryDate()));
        staUserSettlement.setTaskEndDate(sb.format(staOrder.getTimeOfResignation()));
        staUserSettlement.setTaskDetail(staOrder.getWorkName());
        staUserSettlement.setHourlySalary(staOrder.getHourlySalary());
        staUserSettlement.setOrgCode(staOrder.getSysOrgCode());
        staUserSettlement.setBizId(UUID.randomUUID().toString().replace("-", ""));
        String sysUserId = staOrder.getSysUserId();
        StaUserBankCards staUserBankCards = staUserBankCardsService.getOne(new LambdaQueryWrapper<StaUserBankCards>().eq(StaUserBankCards::getUserId, sysUserId));
        if (WxlConvertUtils.isNotEmpty(staUserBankCards)) {
            if (staUserBankCards.getAccountType() == CommonConstant.ACCOUNTTYPE1) { //银行卡
                staUserSettlement.setAccountType(CommonConstant.ACCOUNTTYPE1.toString());
                staUserSettlement.setTaskNo(CommonConstant.TASKNO1);
                staUserSettlement.setBankName(staUserBankCards.getBankName());
            } else {//支付宝
                staUserSettlement.setAccountType(CommonConstant.ACCOUNTTYPE2.toString());
                staUserSettlement.setTaskNo(CommonConstant.TASKNO2);
                staUserSettlement.setBankName("支付宝");
            }
            staUserSettlement.setBankNo(staUserBankCards.getCardNumber());
        }
        staUserSettlement.setStatus("0");//待提现
        if (staOrder.getTotalWorkingHours() != null && !staOrder.getTotalWorkingHours().equals(0.0)) {
            BigDecimal workingHours = new BigDecimal(staOrder.getTotalWorkingHours());
            workingHours = workingHours.multiply(staOrder.getHourlySalary()).setScale(1, RoundingMode.DOWN);
            staUserSettlement.setTotalSalary(workingHours);
            staUserSettlement.setPayableAmount(workingHours);
            //TODO 扣款错误
            if (staOrder.getTaskDeductionMoney() != null) {
                staUserSettlement.setTaskDeductionMoney(staOrder.getTaskDeductionMoney());//扣款金额
                BigDecimal actualAmountPaidCount = workingHours;
                actualAmountPaidCount = actualAmountPaidCount.subtract(staOrder.getTaskDeductionMoney()).setScale(1, RoundingMode.DOWN);
                staUserSettlement.setActualAmountPaidCount(actualAmountPaidCount);
            } else {
                staUserSettlement.setActualAmountPaidCount(workingHours != null ? workingHours : new BigDecimal("0.0"));
            }
        } else {
            staUserSettlement.setTotalSalary(new BigDecimal("0.0"));
            staUserSettlement.setPayableAmount(new BigDecimal("0.0"));
            staUserSettlement.setTaskDeductionMoney(new BigDecimal("0.0"));
            staUserSettlement.setActualAmountPaidCount(new BigDecimal("0.0"));
        }
        staUserSettlementService.save(staUserSettlement);
        staOrder.setStateFlag(CommonConstant.ORDER_STATUS_11); //结算中
        staOrderService.updateById(staOrder);
        return Result.OK("操作成功","");
    }
    //测试生成随机批次号 MCL+当天日期+10位随机数 MCL202406271405637303
    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String date = sdf.format(new Date());
        String randomNum = String.format("%10d", Math.abs(new Random().nextInt()));//10位随机数
        String batchBizId = "MCL" + date + randomNum;//批次号
        System.out.println(batchBizId);
    }
}
