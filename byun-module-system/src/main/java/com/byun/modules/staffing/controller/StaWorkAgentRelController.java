package com.byun.modules.staffing.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.aspect.annotation.AutoLog;
import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkAgentRel;
import com.byun.modules.staffing.model.StaAgentModel;
import com.byun.modules.staffing.service.IStaWorkAgentRelService;
import com.byun.modules.staffing.service.IStaWorkService;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 任务代理人关系表
 * @Author: bai
 * @Date:   2021-12-13
 * @Version: V1.0
 */
@Api(tags="任务代理人关系表")
@RestController
@RequestMapping("/staffing/staWorkAgentRel")
@Slf4j
public class StaWorkAgentRelController extends ByunExcelController<StaWorkAgentRel, IStaWorkAgentRelService> {
	@Autowired
	private IStaWorkAgentRelService staWorkAgentRelService;
	 @Autowired
	 private ISysUserService sysUserService;
	 @Autowired
	 private IStaWorkService staWorkService;

	 /**
	  *  批量代理绑定任务
	  *
	  * @param staAgentModel
	  * @return
	  */
	 @AutoLog(value = "批量代理绑定任务")
	 @ApiOperation(value="批量代理绑定任务", notes="批量代理绑定任务")
	 @PutMapping(value = "/batchAgentBindWork")
	 public Result<?> batchAgentBindWork(@RequestBody StaAgentModel staAgentModel) {
		 LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		 if (WxlConvertUtils.isEmpty(loginUser)){
			 return Result.error("登录失效");
		 }
		 SysUser user = sysUserService.getById(loginUser.getId());
		 if(WxlConvertUtils.isEmpty(user)){
			 return Result.error("未找到登录用户");
		 }
		 String[] addUserIdArr = new String[0];
		 if(WxlConvertUtils.isNotEmpty(staAgentModel.getUserIds())) {
			 addUserIdArr = staAgentModel.getUserIds().split(",");
			 for (String id : addUserIdArr) {
				 if (WxlConvertUtils.isNotEmpty(id)) {
					 SysUser byId = sysUserService.getById(id);
					 if (WxlConvertUtils.isEmpty(byId)) {
						 return Result.error("存在失效用户");
					 }
				 } else {
					 return Result.error("存在异常id");
				 }
			 }
		 }
		 if(WxlConvertUtils.isEmpty(staAgentModel.getAllUserIds())){
			 return Result.error("未找到用户id");
		 }
		 List<String> deleteUserIdList = new ArrayList<>();
		 String[] allUserIdArr =  staAgentModel.getAllUserIds().split(",");
		 for(String id : allUserIdArr){
			 if(WxlConvertUtils.isNotEmpty(id)){
				 SysUser byId = sysUserService.getById(id);
				 if(WxlConvertUtils.isEmpty(byId)){
					 return Result.error("代理列表存在失效用户");
				 }
				 boolean bl = true;//如果不在添加列表则放入删除列表
				 for(String addId : addUserIdArr){
					 if(id.equals(addId)){
						 bl = false;
					 }
				 }
				 if(bl){
					 deleteUserIdList.add(id);//将不再列表中的id 添加到待删集合中
				 }
			 }else{
				 return Result.error("代理列表存在异常id");
			 }
		 }
		 if(WxlConvertUtils.isEmpty(staAgentModel.getWorkId())){
			 return Result.error("未找到任务Id");
		 }
		 StaWork staWork = staWorkService.getById(staAgentModel.getWorkId());
		 if(WxlConvertUtils.isEmpty(staWork)){
			 return Result.error("未找到任务");
		 }
		 try {
			 staWorkAgentRelService.batchAgentBindWork(user,staWork,addUserIdArr,deleteUserIdList);
			 return Result.OK("绑定代理成功!");
		 } catch (Exception e) {
			 e.printStackTrace();
			 return Result.error("绑定代理失败!");
		 }
	 }


	/**
	 * 分页列表查询
	 *
	 * @param staWorkAgentRel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务代理人关系表-分页列表查询")
	@ApiOperation(value="任务代理人关系表-分页列表查询", notes="任务代理人关系表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkAgentRel staWorkAgentRel,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaWorkAgentRel> queryWrapper = QueryGenerator.initQueryWrapper(staWorkAgentRel, req.getParameterMap());
		Page<StaWorkAgentRel> page = new Page<StaWorkAgentRel>(pageNo, pageSize);
		IPage<StaWorkAgentRel> pageList = staWorkAgentRelService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务代理人关系表-通过id删除")
	@ApiOperation(value="任务代理人关系表-通过id删除", notes="任务代理人关系表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkAgentRelService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务代理人关系表-批量删除")
	@ApiOperation(value="任务代理人关系表-批量删除", notes="任务代理人关系表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkAgentRelService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务代理人关系表-通过id查询")
	@ApiOperation(value="任务代理人关系表-通过id查询", notes="任务代理人关系表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkAgentRel staWorkAgentRel = staWorkAgentRelService.getById(id);
		if(staWorkAgentRel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkAgentRel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkAgentRel
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkAgentRel staWorkAgentRel) {
        return super.exportXls(request, staWorkAgentRel, StaWorkAgentRel.class, "任务代理人关系表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkAgentRel.class);
    }

}
