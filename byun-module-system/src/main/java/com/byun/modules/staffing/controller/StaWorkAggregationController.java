package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaWorkAggregation;
import com.byun.modules.staffing.service.IStaWorkAggregationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 任务聚合
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="任务聚合")
@RestController
@RequestMapping("/staffing/staWorkAggregation")
@Slf4j
public class StaWorkAggregationController extends ByunExcelController<StaWorkAggregation, IStaWorkAggregationService> {
	@Autowired
	private IStaWorkAggregationService staWorkAggregationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staWorkAggregation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务聚合-分页列表查询")
	@ApiOperation(value="任务聚合-分页列表查询", notes="任务聚合-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkAggregation staWorkAggregation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaWorkAggregation> queryWrapper = QueryGenerator.initQueryWrapper(staWorkAggregation, req.getParameterMap());
		Page<StaWorkAggregation> page = new Page<StaWorkAggregation>(pageNo, pageSize);
		IPage<StaWorkAggregation> pageList = staWorkAggregationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staWorkAggregation
	 * @return
	 */
	@AutoLog(value = "任务聚合-添加")
	@ApiOperation(value="任务聚合-添加", notes="任务聚合-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkAggregation staWorkAggregation) {
		staWorkAggregationService.save(staWorkAggregation);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkAggregation
	 * @return
	 */
	@AutoLog(value = "任务聚合-编辑")
	@ApiOperation(value="任务聚合-编辑", notes="任务聚合-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkAggregation staWorkAggregation) {
		staWorkAggregationService.updateById(staWorkAggregation);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务聚合-通过id删除")
	@ApiOperation(value="任务聚合-通过id删除", notes="任务聚合-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkAggregationService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务聚合-批量删除")
	@ApiOperation(value="任务聚合-批量删除", notes="任务聚合-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkAggregationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务聚合-通过id查询")
	@ApiOperation(value="任务聚合-通过id查询", notes="任务聚合-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkAggregation staWorkAggregation = staWorkAggregationService.getById(id);
		if(staWorkAggregation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkAggregation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkAggregation
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkAggregation staWorkAggregation) {
        return super.exportXls(request, staWorkAggregation, StaWorkAggregation.class, "任务聚合");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkAggregation.class);
    }

}
