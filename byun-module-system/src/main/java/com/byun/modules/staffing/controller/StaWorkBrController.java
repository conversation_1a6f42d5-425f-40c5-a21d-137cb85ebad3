package com.byun.modules.staffing.controller;

import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkBr;
import com.byun.modules.staffing.service.IStaWorkBrService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 任务浏览记录
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="任务浏览记录")
@RestController
@RequestMapping("/staffing/staWorkBr")
@Slf4j
public class StaWorkBrController extends ByunExcelController<StaWorkBr, IStaWorkBrService> {
	@Autowired
	private IStaWorkBrService staWorkBrService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staWorkBr
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务浏览记录-分页列表查询")
	@ApiOperation(value="任务浏览记录-分页列表查询", notes="任务浏览记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkBr staWorkBr,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
//		QueryWrapper<StaWorkBr> queryWrapper = QueryGenerator.initQueryWrapper(staWorkBr, req.getParameterMap());
//		Page<StaWorkBr> page = new Page<StaWorkBr>(pageNo, pageSize);
//		IPage<StaWorkBr> pageList = staWorkBrService.page(page, queryWrapper);
//		return Result.OK(pageList);

		Result<IPage<StaWork>> result = new Result<IPage<StaWork>>();
		LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		String userId = sysUser.getId();
		Page<StaWork> pageList = new Page<StaWork>(pageNo,pageSize);
		pageList = staWorkBrService.getMyWorkBrPage(pageList, userId);
		result.setResult(pageList);
		result.setSuccess(true);
		return result;
	}
	
	/**
	 *   添加
	 *
	 * @param staWorkBr
	 * @return
	 */
	@AutoLog(value = "任务浏览记录-添加")
	@ApiOperation(value="任务浏览记录-添加", notes="任务浏览记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkBr staWorkBr) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if(WxlConvertUtils.isEmpty(user)){
			return Result.OK("未获取到当前用户！");
		}
		if(WxlConvertUtils.isEmpty(staWorkBr.getStaWorkId())){
			return Result.OK("未获取到当前任务id！");
		}
		LambdaQueryWrapper<StaWorkBr> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(StaWorkBr::getSysUserId, user.getId());
		queryWrapper.eq(StaWorkBr::getStaWorkId, staWorkBr.getStaWorkId());
		StaWorkBr one = staWorkBrService.getOne(queryWrapper);
		if(WxlConvertUtils.isEmpty(one)){
			staWorkBr.setSysUserId(user.getId());
			staWorkBr.setBrTime(new Date());
			staWorkBrService.save(staWorkBr);
			return Result.OK("浏览记录添加成功！");

		}else {
			one.setBrTime(new Date());
			staWorkBrService.updateById(one);
			return Result.OK("浏览记录更新成功!");
		}
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkBr
	 * @return
	 */
	@AutoLog(value = "任务浏览记录-编辑")
	@ApiOperation(value="任务浏览记录-编辑", notes="任务浏览记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkBr staWorkBr) {
		staWorkBrService.updateById(staWorkBr);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务浏览记录-通过id删除")
	@ApiOperation(value="任务浏览记录-通过id删除", notes="任务浏览记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkBrService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务浏览记录-批量删除")
	@ApiOperation(value="任务浏览记录-批量删除", notes="任务浏览记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkBrService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务浏览记录-通过id查询")
	@ApiOperation(value="任务浏览记录-通过id查询", notes="任务浏览记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkBr staWorkBr = staWorkBrService.getById(id);
		if(staWorkBr==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkBr);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkBr
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkBr staWorkBr) {
        return super.exportXls(request, staWorkBr, StaWorkBr.class, "任务浏览记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkBr.class);
    }

}
