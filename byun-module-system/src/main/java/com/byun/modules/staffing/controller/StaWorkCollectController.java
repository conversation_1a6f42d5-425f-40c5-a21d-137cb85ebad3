package com.byun.modules.staffing.controller;

import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkCollect;
import com.byun.modules.staffing.service.IStaWorkCollectService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.service.IStaWorkService;
import com.byun.modules.staffing.service.IStaWorkVisitsService;
import com.byun.modules.system.entity.SysAnnouncement;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysAnnouncementService;
import com.byun.modules.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 用户收藏任务
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="用户收藏任务")
@RestController
@RequestMapping("/staffing/staWorkCollect")
@Slf4j
public class StaWorkCollectController extends ByunExcelController<StaWorkCollect, IStaWorkCollectService> {
	@Autowired
	private IStaWorkCollectService staWorkCollectService;
	@Autowired
	private ISysAnnouncementService sysAnnouncementService;
	@Autowired
	private ISysUserService sysUserService;
	@Autowired
	private IStaWorkService staWorkService;
	@Autowired
	private IStaWorkVisitsService staWorkVisitsService;


	/**
	 * 分页列表查询
	 *
	 * @param staWorkCollect
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户收藏任务-分页列表查询")
	@ApiOperation(value="用户收藏任务-分页列表查询", notes="用户收藏任务-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkCollect staWorkCollect,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
//		QueryWrapper<StaWorkCollect> queryWrapper = QueryGenerator.initQueryWrapper(staWorkCollect, req.getParameterMap());
//		Page<StaWorkCollect> page = new Page<StaWorkCollect>(pageNo, pageSize);
//		IPage<StaWorkCollect> pageList = staWorkCollectService.page(page, queryWrapper);
//		return Result.OK(pageList);
		Result<IPage<StaWork>> result = new Result<IPage<StaWork>>();
		LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		String userId = sysUser.getId();
		Page<StaWork> pageList = new Page<StaWork>(pageNo,pageSize);
		pageList = staWorkCollectService.getMyWorkCollectPage(pageList, userId);
		result.setResult(pageList);
		result.setSuccess(true);
		return result;
	}
	
	/**
	 *   添加
	 *
	 * @param staWorkCollect
	 * @return
	 */
	@AutoLog(value = "用户收藏任务-添加")
	@ApiOperation(value="用户收藏任务-添加", notes="用户收藏任务-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkCollect staWorkCollect) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if(WxlConvertUtils.isNotEmpty(user)&& WxlConvertUtils.isNotEmpty(user.getId())&& WxlConvertUtils.isNotEmpty(staWorkCollect.getStaWorkId())){
			StaWork staWork = staWorkService.getById(staWorkCollect.getStaWorkId());
			if(WxlConvertUtils.isEmpty(staWork)){
				return Result.error("未找到任务!");
			}
			LambdaQueryWrapper<StaWorkCollect> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(StaWorkCollect::getSysUserId, user.getId());
			queryWrapper.eq(StaWorkCollect::getStaWorkId, staWorkCollect.getStaWorkId());
			int count = staWorkCollectService.count(queryWrapper);
			if(count>0){
				return Result.OK("已收藏!");
			}else{
				staWorkCollect.setCollectTime(new Date());
				staWorkCollect.setSysUserId(user.getId());
				staWorkCollectService.save(staWorkCollect);
				//发送系统通知给招聘官
				try {
					if(WxlConvertUtils.isNotEmpty(user.getBindUserId())){
						SysUser sysUser = sysUserService.getById(user.getBindUserId());
						if(WxlConvertUtils.isNotEmpty(sysUser)){
							SysAnnouncement sysAnnouncement = new SysAnnouncement();
							sysAnnouncement.setUserIds(sysUser.getId()+",");//推送用户
							String msgAbstract = "" + user.getRealname()+user.getPhone() + ",收藏了<"+staWork.getNameNick()+">！";
							sysAnnouncement.setMsgAbstract(msgAbstract);
							String title = "绑定用户收藏";
							sysAnnouncement.setTitile(title);
							sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
							sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
							sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
							sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
							sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
							sysAnnouncement.setSendTime(new Date());
							sysAnnouncement.setSender(user.getRealname());
							sysAnnouncement.setCreateBy(user.getRealname());
							sysAnnouncement.setCreateTime(new Date());
							sysAnnouncementService.saveAnnouncement(sysAnnouncement);
						}
					}
				} catch (Exception e) {
				}
				return Result.OK("收藏成功!");
			}

		}else{
			return Result.error("未找到目标!");
		}
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkCollect
	 * @return
	 */
	@AutoLog(value = "用户收藏任务-编辑")
	@ApiOperation(value="用户收藏任务-编辑", notes="用户收藏任务-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkCollect staWorkCollect) {
		staWorkCollectService.updateById(staWorkCollect);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param staWorkCollect
	 * @return
	 */
	@AutoLog(value = "用户收藏任务-通过id删除")
	@ApiOperation(value="用户收藏任务-通过id删除", notes="用户收藏任务-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestBody StaWorkCollect staWorkCollect) {
		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if(WxlConvertUtils.isNotEmpty(user)&& WxlConvertUtils.isNotEmpty(user.getId())&& WxlConvertUtils.isNotEmpty(staWorkCollect.getStaWorkId())){
			LambdaQueryWrapper<StaWorkCollect> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(StaWorkCollect::getStaWorkId,staWorkCollect.getStaWorkId());
			queryWrapper.eq(StaWorkCollect::getSysUserId,user.getId());
			staWorkCollectService.remove(queryWrapper);
			return Result.OK("取消收藏!");
		}else{
			return Result.error("未找到目标!");
		}
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户收藏任务-批量删除")
	@ApiOperation(value="用户收藏任务-批量删除", notes="用户收藏任务-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkCollectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户收藏任务-通过id查询")
	@ApiOperation(value="用户收藏任务-通过id查询", notes="用户收藏任务-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkCollect staWorkCollect = staWorkCollectService.getById(id);
		if(staWorkCollect==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkCollect);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkCollect
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkCollect staWorkCollect) {
        return super.exportXls(request, staWorkCollect, StaWorkCollect.class, "用户收藏任务");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkCollect.class);
    }

}
