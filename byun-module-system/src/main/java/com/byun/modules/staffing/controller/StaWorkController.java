package com.byun.modules.staffing.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.exception.ByunBootException;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.util.JwtUtil;
import com.byun.common.util.DateUtils;
import com.byun.common.util.RedisUtil;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.model.StaWorkModel;
import com.byun.modules.staffing.service.*;
import com.byun.modules.staffing.vo.StaTaskDateVO;
import com.byun.modules.staffing.vo.StaWorkVoPage;
import com.byun.modules.staffing.util.AdminListCacheUtil;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.staffing.entity.StaGobsType;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.IStaUserBankCardsService;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.staffing.service.IStaGobsTypeService;
import com.byun.modules.system.service.ISysUserService;
import io.swagger.annotations.ApiModelProperty;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import com.byun.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.vo.StaWorkPage;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

/**
 * @Description: 任务列表
 * @Author: baiyun
 * @Date: 2021-11-14
 * @Version: V1.0
 */
@Api(tags = "任务列表")
@RestController
@RequestMapping("/staffing/staWork")
@Slf4j
public class StaWorkController {
    @Autowired
    private IStaWorkService staWorkService;
    @Autowired
    private IStaWorkImageService staWorkImageService;
    @Autowired
    private IStaWorkEnrollService staWorkEnrollService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IStaWorkCollectService staWorkCollectService;
    @Autowired
    private IStaWorkVisitsService staWorkVisitsService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IStaWorkBrService staWorkBrService;
    @Autowired
    private IStaWorkAgentRelService staWorkAgentRelService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    IStaOrderService staOrderService;
    @Autowired
    private IStaLocationClockService staLocationClockService;
    @Autowired
    private IStaScheduleService staScheduleService;
    @Autowired
    private IStaGobsTypeService sysGobsTypeService;
    @Autowired
    private IStaUserBankCardsService staUserBankCardsService;
    @Autowired
    private IStaNoticeService staNoticeService;
    @Autowired
    private IStaTaskDateService staTaskDateService;
    @Autowired
    private AdminListCacheUtil adminListCacheUtil;

    /**
     * 任务置顶
     *
     * @param staWork
     * @param request
     * @return
     */
    @RequestMapping(value = "/changeTopFlagUpdate", method = RequestMethod.PUT)
    public Result<StaWork> changeTopFlagUpdate(@RequestBody StaWork staWork, HttpServletRequest request) {
        String username = JwtUtil.getUserNameByToken(request);
        Result<StaWork> result = new Result<StaWork>();
        StaWork staWorkEntity = staWorkService.getById(staWork.getId());
        if (staWorkEntity == null) {
            result.error500("未找到对应实体");
        } else {
            if (CommonConstant.DEL_FLAG_1.equals(staWorkEntity.getDelFlag())) {
                return result.error500("任务已删除");
            }
            if (!CommonConstant.WORK_STATUS_2.equals(staWorkEntity.getStateFlag())) {
                return result.error500("任务状态不是发布中,无法修改置顶");
            }
            LambdaUpdateWrapper<StaWork> updateWrapper = new UpdateWrapper().lambda();
            updateWrapper.eq(StaWork::getId, staWorkEntity.getId());
            updateWrapper.set(StaWork::getUpdateBy, username);
            updateWrapper.set(StaWork::getUpdateTime, new Date());
            if (WxlConvertUtils.isNotEmpty(staWork.getTopFlagUpdate())) {
                updateWrapper.set(StaWork::getTopFlagUpdate, new Date());
            } else {
                updateWrapper.set(StaWork::getTopFlagUpdate, null);
            }
            boolean ok = staWorkService.update(updateWrapper);
            if (ok) {
                if (WxlConvertUtils.isNotEmpty(staWork.getTopFlagUpdate())) {
                    result.success("置顶成功!");
                } else {
                    result.success("取消置顶成功!");
                }
            } else {
                result.error500("失败");
            }
        }
        return result;
    }

    /**
     * 分页列表查询
     *
     * @param staWorkModel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "任务列表-分页列表查询按距离排序")
    @ApiOperation(value = "任务列表-分页列表查询按距离排序", notes = "任务列表-分页列表查询按距离排序")
    @GetMapping(value = "/listOrderDistance")
    public Result<?> listOrderDistance(StaWorkModel staWorkModel,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       HttpServletRequest req) {
        Result<IPage<StaWorkModel>> result = new Result<IPage<StaWorkModel>>();
        Page<StaWorkModel> pageList = new Page<StaWorkModel>(pageNo, pageSize);
        pageList = staWorkService.listOrderDistancePage(pageList, staWorkModel);
        result.setResult(pageList);
        result.setSuccess(true);
        return result;
    }

    @AutoLog(value = "任务列表-紧急任务")
    @ApiOperation(value = "任务列表-紧急任务", notes = "任务列表-紧急任务")
    @GetMapping(value = "/listOrderUrgent")
    public Result<?> listOrderUrgent(StaWorkModel staWorkModel,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                     HttpServletRequest req) {
        IPage<StaWork> page = new Page<>();
        LambdaQueryWrapper<StaWork> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0);
        wrapper.eq(StaWork::getStateFlag, CommonConstant.WORK_STATUS_3);
        wrapper.isNotNull(StaWork::getTopFlagUpdate);
        IPage<StaWork> staWorkPage = staWorkService.page(page, wrapper);
        return Result.OK(staWorkPage);
    }

    /**
     * 任务上下架
     * TODO 待补充权限
     *
     * @param staWork
     * @return
     */
    @AutoLog(value = "任务单-下架 ")
    @ApiOperation(value = "任务单-下架 ", notes = "任务单-下架 ")
    @PutMapping(value = "/taskDownAndUp")
    public Result<?> taskDown(@RequestBody StaWork staWork) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) return Result.error("未获取到登录用户");
        if (WxlConvertUtils.isEmpty(staWork) || WxlConvertUtils.isEmpty(staWork.getStateFlag())) {
            return Result.error("未获取到任务");
        } else if (staWork.getStateFlag() == CommonConstant.WORK_STATUS_3) {
            //执行下架
            staWork.setStateFlag(CommonConstant.WORK_STATUS_5);
        } else if (staWork.getStateFlag() == CommonConstant.WORK_STATUS_5) {
            //执行上架
            staWork.setStateFlag(CommonConstant.WORK_STATUS_3);
        }
        staWorkService.updateById(staWork);
        return Result.OK("操作成功");
    }

    /**
     * @description: 将任务设为删除状态
     * <AUTHOR>
     * @date 2021/12/1 13:51
     * @version 1.0
     */
    @AutoLog(value = "任务列表-设为删除状态 ")
    @ApiOperation(value = "任务列表-设为删除状态 ", notes = "任务列表-设为删除状态 ")
    @DeleteMapping(value = "/deleteWork")
    public Result<?> deleteWork(@RequestBody StaWork sw) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("未获取到登录用户");
        }
        if (WxlConvertUtils.isEmpty(sw) || WxlConvertUtils.isEmpty(sw.getId())) {
            return Result.error("未获取到任务");
        }
        staWorkService.removeById(sw.getId());
        if (redisUtil.hasKey(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + sw.getId())) {
            redisUtil.del(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + sw.getId());
        }
        return Result.OK("删除成功!", "删除成功!");
    }

    /**
     * 管理员分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param deptNo    店编
     * @param deptName  店别
     * @param stateFlag 状态
     * @param nameFull  任务类型名称
     * @param req
     * @return
     */
    @AutoLog(value = "任务列表-管理员分页列表查询")
    @ApiOperation(value = "任务列表-分页列表查询", notes = "任务列表-管理员分页列表查询")
    @GetMapping(value = "/adminList")
    public Result<?> queryPageAdminList(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "deptNo", required = false) String deptNo,
            @RequestParam(name = "deptName", required = false) String deptName,
            @RequestParam(name = "stateFlag", required = false) String stateFlag,
            @RequestParam(name = "nameFull", required = false) String nameFull,
            HttpServletRequest req) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return Result.error("用户未登录");
        }

        // 优化：缓存用户权限信息，避免重复查询
        String orgCode = user.getOrgCode();
        if (WxlConvertUtils.isEmpty(orgCode)) {
            return Result.error("用户部门信息缺失");
        }

        //查询所有负责部门 - 优化：使用缓存
        List<String> departIdArr = adminListCacheUtil.getDeptHierarchyFromCache(orgCode);
        if (departIdArr == null) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(orgCode);
            if (WxlConvertUtils.isNotEmpty(departIdArr)) {
                adminListCacheUtil.setDeptHierarchyCache(orgCode, departIdArr);
            }
        }
        if (WxlConvertUtils.isEmpty(departIdArr)) {
            return Result.error("未获取登录部门");
        }

        //当前登录账户根据部门code获取部门id
        String departId = sysBaseAPI.getDepartIdsByOrgCode(orgCode);

        //用户权限 - 优化：使用缓存
        Set<String> userPermissionSet = adminListCacheUtil.getUserPermissionFromCache(user.getUsername(), departId);
        if (userPermissionSet == null) {
            userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
            if (userPermissionSet != null) {
                adminListCacheUtil.setUserPermissionCache(user.getUsername(), departId, userPermissionSet);
            }
        }

        boolean positionlist = userPermissionSet != null && userPermissionSet.contains("staffing:position:list");
        if (!positionlist) {
            return Result.error("暂无权限");
        }
        QueryWrapper<StaWork> queryWrapper = new QueryWrapper<>();
        Page<StaWork> page = new Page<StaWork>(pageNo, pageSize);
        if (WxlConvertUtils.isNotEmpty(stateFlag)) {
            queryWrapper.eq("state_flag", stateFlag);
        }
        if (WxlConvertUtils.isNotEmpty(nameFull)) {
            queryWrapper.lambda().eq(StaWork::getNameFull, nameFull);
        }
        //权限下所有数据
        if (WxlConvertUtils.isEmpty(deptName)) {
            queryWrapper.in("company_id", departIdArr);
        } else {
            // 优化：简化部门名称查询逻辑，减少不必要的数据库查询
            boolean isNumeric = deptName.matches("^\\d+$");
            if (isNumeric) {
                // 如果是纯数字，按店编查询
                queryWrapper.like("store_no", deptName);
            } else {
                // 如果不是纯数字，按门店名称查询
                queryWrapper.like("company_name", deptName);
            }

            // 添加店编条件（如果提供）
            if (WxlConvertUtils.isNotEmpty(deptNo)) {
                queryWrapper.eq("store_no", deptNo);
            }

            // 确保在用户权限范围内
            queryWrapper.likeRight("sys_org_code", orgCode);
        }
        queryWrapper.eq("del_flag", CommonConstant.DEL_FLAG_0);
        queryWrapper.orderByDesc("create_time", "store_region_name");
        IPage<StaWork> pageList = staWorkService.page(page, queryWrapper);
        if (pageList.getRecords().isEmpty()) {
            return Result.OK("无数据", new StaWorkVoPage());
        }

        // 优化：使用Map避免嵌套循环，提高性能
        List<String> staWorkIds = pageList.getRecords().stream().map(StaWork::getId).collect(Collectors.toList());
        List<StaTaskDate> staTaskDates = staTaskDateService.list(new LambdaQueryWrapper<StaTaskDate>().in(StaTaskDate::getStaWorkId, staWorkIds));

        // 将任务时间段按任务ID分组，避免嵌套循环
        Map<String, List<StaTaskDate>> taskDateMap = staTaskDates.stream()
                .collect(Collectors.groupingBy(StaTaskDate::getStaWorkId));

        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sb = new SimpleDateFormat("HH:mm");

        // 批量处理，避免重复创建对象
        for (StaWork record : pageList.getRecords()) {
            List<StaTaskDate> recordTaskDates = taskDateMap.get(record.getId());
            List<StaTaskDateVO> staTaskDateVOS = new ArrayList<>();

            if (recordTaskDates != null) {
                for (StaTaskDate staTaskDate : recordTaskDates) {
                    StaTaskDateVO staTaskDateVO = new StaTaskDateVO();
                    staTaskDateVO.setId(staTaskDate.getId());
                    staTaskDateVO.setTaskStartDate(sd.format(staTaskDate.getTaskStartDate()));
                    staTaskDateVO.setTaskEndDate(sd.format(staTaskDate.getTaskEndDate()));
                    staTaskDateVO.setTaskStartTime(sb.format(staTaskDate.getTaskStartTime()));
                    staTaskDateVO.setTaskEndTime(sb.format(staTaskDate.getTaskEndTime()));
                    staTaskDateVO.setTaskStartTime2(WxlConvertUtils.isNotEmpty(staTaskDate.getTaskStartTime2()) ? sb.format(staTaskDate.getTaskStartTime2()) : null);
                    staTaskDateVO.setTaskEndTime2(WxlConvertUtils.isNotEmpty(staTaskDate.getTaskEndTime2()) ? sb.format(staTaskDate.getTaskEndTime2()) : null);
                    staTaskDateVO.setShiftCode(staTaskDate.getShiftCode());
                    staTaskDateVO.setExpectNum(String.valueOf(staTaskDate.getExpectNum()));
                    staTaskDateVO.setRemainingNum(staTaskDate.getRemainingNum());
                    staTaskDateVOS.add(staTaskDateVO);
                }
            }
            record.setStaTaskDateAndTimes(staTaskDateVOS);
        }
        IPage<StaWorkVoPage> staWorkVoPageIPage = this.taskList(pageList);
        return Result.OK("操作成功", staWorkVoPageIPage);
    }

    /**
     * 任务结束
     *
     * @param staWork
     * @param request
     * @return
     */
    @RequestMapping(value = "/completeWork", method = RequestMethod.PUT)
    public Result completeWork(@RequestBody StaWork staWork, HttpServletRequest request) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            Result.error("为获取到登录用户");
        }
        staWorkService.completeWork(staWork);
        return Result.OK("操作成功");
    }

    @RequestMapping(value = "/startWork", method = RequestMethod.PUT)
    public Result<StaWork> startWork(@RequestBody StaWork staWork, HttpServletRequest request) {
        String username = JwtUtil.getUserNameByToken(request);
        Result<StaWork> result = new Result<StaWork>();
        StaWork staWorkEntity = staWorkService.getById(staWork.getId());
        if (staWorkEntity == null) {
            result.error500("未找到对应实体");
        } else {
            if (CommonConstant.DEL_FLAG_1.equals(staWorkEntity.getDelFlag())) {
                return result.error500("任务已删除");
            }
            if (!CommonConstant.WORK_STATUS_2.equals(staWorkEntity.getStateFlag())) {
                return result.error500("任务状态不是发布中");
            }
            staWorkEntity.setUpdateBy(username);
            staWorkEntity.setUpdateTime(new Date());
            boolean ok = staWorkService.startWork(staWorkEntity, username);
            if (ok) {
                result.success("任务开始成功!");
            } else {
                result.error500("失败");
            }
        }
        return result;
    }

    /**
     * 查询数据 查出我的部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @RequestMapping(value = "/getModel", method = RequestMethod.GET)
    public Result<StaWorkModel> getStaDepartModelByid(@RequestParam(name = "id") String id) {
        Result<StaWorkModel> result = new Result<>();
        try {
            StaWork staWork = staWorkService.getById(id);
            if (WxlConvertUtils.isNotEmpty(staWork)) {
                StaWorkModel staWorkModel = new StaWorkModel();
                result.setResult(staWorkModel);
                result.setSuccess(true);
            } else {
                result.setSuccess(false);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * @description: 修改任务内容
     * <AUTHOR>
     * @date 2021/11/23 15:47
     * @version 1.0
     */
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @AutoLog(value = "编辑任务", logType = 2, operateType = 3)
    //@CacheEvict(value = {CacheConstant.SYS_DEPARTS_CACHE, CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries = true)
    @Transactional
    public Result<StaWork> edit(@RequestBody StaWork staWork, HttpServletRequest request) throws ParseException {
        String username = JwtUtil.getUserNameByToken(request);
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Result<StaWork> result = new Result<StaWork>();
        StaWork staWorkEntity = staWorkService.getById(staWork.getId());
        if (staWorkEntity == null) {
            result.error500("未找到对应实体");
        } else {
            if (staWork.getRequestType().equals(CommonConstant.CLIENT_TYPE_0) || staWork.getRequestType().equals(CommonConstant.CLIENT_TYPE_1)) {
                //门店暂不支持修改(修改后关联效应 需要修改 任务表、排班表、 签到表、 订单表、浏览表、收藏表、工作记录表、)
                //需要人数
                staWorkEntity.setExpectNum(staWork.getExpectNum() != null ? staWork.getExpectNum() : staWorkEntity.getExpectNum());
                //任务名称
                staWorkEntity.setNameFull(staWork.getNameFull() != null ? staWork.getNameFull() : staWorkEntity.getNameFull());
                //任务简称
                staWorkEntity.setNameNick(staWork.getNameFull() != null ? staWork.getNameFull() : staWorkEntity.getNameNick());
                //任务图片
                staWorkEntity.setImageUrl(staWork.getImageUrl() != null ? staWork.getImageUrl() : staWorkEntity.getImageUrl());
                //联系人姓名
                staWorkEntity.setLiaison(staWork.getLiaison() != null ? staWork.getLiaison() : staWorkEntity.getLiaison());
                //联系人电话
                staWorkEntity.setLiaisonTp(staWork.getLiaisonTp() != null ? staWork.getLiaisonTp() : staWorkEntity.getLiaisonTp());
                //任务描述
                staWorkEntity.setContent(staWork.getContent() != null ? staWork.getContent() : staWorkEntity.getContent());
                //任务开始时间
//                staWorkEntity.setTaskStartTime(staWork.getTaskStartTime() != null ? staWork.getTaskStartTime() :staWorkEntity.getTaskStartTime());
//                //任务结束时间
//                staWorkEntity.setTaskEndTime(staWork.getTaskEndTime() != null ? staWork.getTaskEndTime() :staWorkEntity.getTaskEndTime());
                //任务开始-结束 日期
                if (WxlConvertUtils.isNotEmpty(staWork.getTaskDate())) {
                    String[] split = staWork.getTaskDate().split(",");
                    Date startDate = format.parse(split[0]);
                    Date endData = format.parse(split[1]);
                    String format1 = format.format(startDate);
                    String format2 = format.format(endData);
                    staWork.setTaskStartDate(format.parse(format1));
                    staWork.setTaskEndDate(format.parse(format2));
                    staWorkEntity.setTaskStartDate(format.parse(format1));
                    staWorkEntity.setTaskEndDate(format.parse(format2));
                }
                //任务天数
                if (staWork.getTaskStartDate() != null && staWork.getTaskEndDate() != null) {
                    String format1 = format.format(staWork.getTaskStartDate());
                    String format2 = format.format(staWork.getTaskEndDate());
                    staWork.setTaskStartDate(format.parse(format1));
                    staWork.setTaskEndDate(format.parse(format2));
                    long dateTotal = DateUtils.calculateDateDifference(format1, format2);
                    staWorkEntity.setTaskDaysTotal((int) dateTotal);
                }
                //任务类型
                if (WxlConvertUtils.isNotEmpty(staWork.getJobsId())) {
                    StaGobsType sysGobsType = sysGobsTypeService.getById(staWork.getJobsId());
                    staWorkEntity.setJobsId(sysGobsType.getId());
                    staWorkEntity.setJobsName(sysGobsType.getJobsName());
                    staWorkEntity.setJobsSalary(sysGobsType.getJobsHourlyWages());
                }
                //结算标准
                if (staWork.getWorkSalary() != null && staWork.getWorkSalary().compareTo(BigDecimal.ZERO) > 0) {
                    staWorkEntity.setWorkSalary(staWork.getWorkSalary());
                }
                SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
                SimpleDateFormat sb = new SimpleDateFormat("HH:mm");
                List<StaTaskDateVO> staTaskDateAndTimes = staWork.getStaTaskDateAndTimes();
                String staWorkEntityId = staWorkEntity.getId();
                List<StaTaskDate> taskDateList = staTaskDateService.list(new QueryWrapper<StaTaskDate>().lambda()
                        .eq(StaTaskDate::getStaWorkId, staWorkEntityId)); //当前任务时间段
                List<StaTaskDateVO> staTaskDateVOStreamAdd = staTaskDateAndTimes.stream()
                        .filter(s -> WxlConvertUtils.isEmpty(s.getId())).collect(Collectors.toList());//id为空是新增
                List<StaTaskDateVO> staTaskDateVOStreamEdit = staTaskDateAndTimes.stream()
                        .filter(s -> WxlConvertUtils.isNotEmpty(s.getId())).collect(Collectors.toList());//id不为空是编辑
                //删除任务时间段
                Set<String> staTaskDateVOStreamCodeList = staTaskDateVOStreamEdit.stream().map(StaTaskDateVO::getId).collect(Collectors.toSet());
                List<StaTaskDate> removeDate = taskDateList.stream()
                        .filter(t -> !staTaskDateVOStreamCodeList.contains(t.getId())).collect(Collectors.toList());
                if (!removeDate.isEmpty()) {
                    staTaskDateVOStreamEdit = staTaskDateVOStreamEdit.stream()
                            .filter(s -> staTaskDateVOStreamCodeList.contains(s.getId())).collect(Collectors.toList());
                    staTaskDateService.removeByIds(removeDate.stream().map(StaTaskDate::getId).collect(Collectors.toList()));
                }
                //新增时间段
                if (WxlConvertUtils.isNotEmpty(staTaskDateVOStreamAdd)) {
                    List<StaTaskDate> staTaskDateList = new ArrayList<>();
                    for (StaTaskDateVO staTaskDateVO : staTaskDateVOStreamAdd) {
                        StaTaskDate staTaskDate = new StaTaskDate();
                        staTaskDate.setCreateById(user.getId());//创建人
                        staTaskDate.setStaWorkId(staWork.getId());
                        staTaskDate.setCreateDate(new Date());
                        staTaskDate.setTaskStartDate(sd.parse(staTaskDateVO.getTaskStartDate()));//开始日期
                        staTaskDate.setTaskEndDate(sd.parse(staTaskDateVO.getTaskEndDate()));//结束日期
                        staTaskDate.setTaskStartTime(sb.parse(staTaskDateVO.getTaskStartTime()));//开始时间
                        staTaskDate.setTaskEndTime(sb.parse(staTaskDateVO.getTaskEndTime()));//结束时间
                        staTaskDate.setTaskStartTime2(WxlConvertUtils.isNotEmpty(staTaskDateVO.getTaskStartTime2()) ? sb.parse(staTaskDateVO.getTaskStartTime2()) : null);
                        staTaskDate.setTaskEndTime2(WxlConvertUtils.isNotEmpty(staTaskDateVO.getTaskEndTime2()) ? sb.parse(staTaskDateVO.getTaskEndTime2()) : null);
                        staTaskDate.setShiftCode(staTaskDateVO.getShiftCode());
                        staTaskDate.setExpectNum(Integer.valueOf(staTaskDateVO.getExpectNum()));
                        staTaskDate.setRemainingNum(staTaskDateVO.getRemainingNum());
                        staTaskDateList.add(staTaskDate);
                    }
                    staTaskDateService.saveBatch(staTaskDateList);
                }
                //编辑时间段
                if (WxlConvertUtils.isNotEmpty(staTaskDateVOStreamEdit)) {
                    List<StaTaskDate> staTaskDateList = new ArrayList<>();
                    for (StaTaskDateVO staTaskDateVO : staTaskDateVOStreamEdit) {
                        StaTaskDate staTaskDate = staTaskDateService.getById(staTaskDateVO.getId());
                        String taskStartDate = staTaskDateVO.getTaskStartDate();
                        String taskEndDate = staTaskDateVO.getTaskEndDate();
                        String taskStartTime = staTaskDateVO.getTaskStartTime();
                        String taskEndTime = staTaskDateVO.getTaskEndTime();
                        String expectNum = staTaskDateVO.getExpectNum();
                        String shiftCode = staTaskDateVO.getShiftCode();
                        String taskStartTime2 = staTaskDateVO.getTaskStartTime2();
                        String taskEndTime2 = staTaskDateVO.getTaskEndTime2();
                        staTaskDate.setTaskStartDate(sd.parse(taskStartDate));
                        staTaskDate.setTaskEndDate(sd.parse(taskEndDate));
                        staTaskDate.setTaskStartTime(sb.parse(taskStartTime));
                        staTaskDate.setTaskEndTime(sb.parse(taskEndTime));
                        staTaskDate.setTaskStartTime2(WxlConvertUtils.isNotEmpty(taskStartTime2) ? sb.parse(taskStartTime2) : null);
                        staTaskDate.setTaskEndTime2(WxlConvertUtils.isNotEmpty(taskEndTime2) ? sb.parse(taskEndTime2) : null);
                        staTaskDate.setExpectNum(Integer.valueOf(expectNum));
                        staTaskDate.setShiftCode(shiftCode);
                        staTaskDateList.add(staTaskDate);
                    }
                    for (StaTaskDate staTaskDate : staTaskDateList) {
                        int count = staOrderService.count(new LambdaQueryWrapper<StaOrder>()
                                .eq(StaOrder::getStaTaskDateId, staTaskDate.getId())
                                .notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_0
                                        , CommonConstant.ORDER_STATUS_1
                                        , CommonConstant.ORDER_STATUS_2
                                        , CommonConstant.ORDER_STATUS_4
                                        , CommonConstant.ORDER_STATUS_6)
                                )
                        );
                        if (staTaskDate.getExpectNum() < count) {
                            return result.error500("实际人数大于修改人数");
                        }
                        staTaskDate.setRemainingNum(staTaskDate.getExpectNum() - count);
                    }
                    staTaskDateService.updateBatchById(staTaskDateList);
                }
                if (WxlConvertUtils.isNotEmpty(staWork.getTopFlag()) && staWork.getTopFlag().equals("Y")) {
                    staWorkEntity.setTopFlagUpdate(new Date());
                } else if (WxlConvertUtils.isNotEmpty(staWork.getTopFlag()) && staWork.getTopFlag().equals("N")) {
                    staWorkEntity.setTopFlagUpdate((Date) null);
                }
                if (WxlConvertUtils.isNotEmpty(staWork.getIdHealth()) && staWork.getIdHealth().equals("Y")) {
                    staWorkEntity.setNeedIdHealth(CommonConstant.GENNERASTATUS0);
                } else if (WxlConvertUtils.isNotEmpty(staWork.getIdHealth()) && staWork.getIdHealth().equals("N")) {
                    staWorkEntity.setNeedIdHealth(CommonConstant.GENNERASTATUS1);
                }
                boolean ok = staWorkService.updateWorkDataById(staWorkEntity, username);
                if (ok) {
                    StaWork work = staWorkService.getById(staWorkEntity.getId());
                    redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWorkEntity.getId(), work.getAdoptNum());
                    return result.success("修改成功!");

                }
                return result.error500("修改失败！");
            } else {
                return result.error500("请求错误");
            }
        }
        return result;
    }

    /**
     * 查询当前任务周期是否有人员正在申请中
     *
     * @param id 任务周期id
     * @return
     */
    @GetMapping("/queryTaskListBasedTaskTime")
    public Result<?> queryTaskListBasedTaskTime(@RequestParam("id") String id) {
        if (WxlConvertUtils.isEmpty(id)) {
            return Result.error("参数丢失");
        }
        int count = staOrderService.count(new QueryWrapper<StaOrder>()
                .lambda().eq(StaOrder::getStaTaskDateId, id)
                .eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2));//申请中
        if (count > 0) {
            return Result.error("当前时间段已有人员申请无法删除");
        }
        return Result.OK();
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    //@CacheEvict(value = {CacheConstant.SYS_DEPARTS_CACHE, CacheConstant.SYS_DEPART_IDS_CACHE}, allEntries = true)
    @AutoLog(value = "发布任务", logType = 2, operateType = 2)
    public Result<StaWork> add(@RequestBody JSONObject jsonObject, HttpServletRequest request) throws ParseException {
        Result<StaWork> result = new Result<>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            result.error500("登录失效");
            return result;
        }
        String staWorkStr = jsonObject.toJSONString();
        StaWork staWork = JSON.parseObject(staWorkStr, StaWork.class);
        if (!(WxlConvertUtils.isNotEmpty(staWork.getSelecteddeparts()) || WxlConvertUtils.isNotEmpty(staWork.getCompanyId()))) {
            result.error500("获取门店失败");
            return result;
        }
        if (WxlConvertUtils.isNotEmpty(staWork.getLiaisonTp())) {
            String liaisonTp = staWork.getLiaisonTp(); //联系人电话
            boolean matches = this.validatePhoneNumber(liaisonTp);
            if (!matches) {
                return result.error500("联系人手机格式错误！");
            }
        }
        if (WxlConvertUtils.isNotEmpty(staWork.getSelecteddeparts())) {
            //TODO 部门名称重复
            SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, staWork.getSelecteddeparts()));
            if (WxlConvertUtils.isEmpty(sysDepart.getStoreNo())) {
                result.error500("当前只支持门店发布任务！");
                return result;
            }
        }
        if (WxlConvertUtils.isNotEmpty(staWork.getCompanyId())) {
            String sysDeptId = staWork.getCompanyId();
            SysDepart sysDepart = sysDepartService.getById(sysDeptId);
            if (WxlConvertUtils.isEmpty(sysDepart.getStoreNo())) {
                result.error500("当前只支持门店发布任务！");
                return result;
            }
        }
        List<StaTaskDate> staTaskDates = new ArrayList<>(); //任务对应服务周期和时间段
        if (WxlConvertUtils.isNotEmpty(staWork)) {
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sb = new SimpleDateFormat("HH:mm");
            List<StaTaskDateVO> staTaskDateAndTimes = staWork.getStaTaskDateAndTimes();
            for (StaTaskDateVO staTaskDateAndTime : staTaskDateAndTimes) {
                StaTaskDate staTaskDate = new StaTaskDate();
                String taskStartDate = staTaskDateAndTime.getTaskStartDate();
                String taskEndDate = staTaskDateAndTime.getTaskEndDate();
                String taskStartTime = staTaskDateAndTime.getTaskStartTime();
                String taskEndTime = staTaskDateAndTime.getTaskEndTime();
                String expectNum = staTaskDateAndTime.getExpectNum();
                String shiftCode = staTaskDateAndTime.getShiftCode();
                staTaskDate.setCreateDate(new Date());
                staTaskDate.setTaskStartDate(sd.parse(taskStartDate));
                staTaskDate.setTaskEndDate(sd.parse(taskEndDate));
                staTaskDate.setTaskStartTime(sb.parse(taskStartTime));
                staTaskDate.setTaskEndTime(sb.parse(taskEndTime));
                staTaskDate.setExpectNum(Integer.valueOf(expectNum));
                //staTaskDate.setRemainingNum(Integer.valueOf(expectNum));
                staTaskDate.setShiftCode(shiftCode);
                if (WxlConvertUtils.isNotEmpty(staTaskDateAndTime.getTaskStartTime2()) && WxlConvertUtils.isNotEmpty(staTaskDateAndTime.getTaskEndTime2())) {
                    String taskStartTime2 = staTaskDateAndTime.getTaskStartTime2();
                    String taskEndTime2 = staTaskDateAndTime.getTaskEndTime2();
                    staTaskDate.setTaskStartTime2(sb.parse(taskStartTime2));
                    staTaskDate.setTaskEndTime2(sb.parse(taskEndTime2));
                }
                staTaskDates.add(staTaskDate);
            }
        }
        try {
            SysUser sysUser = sysUserService.getById(user.getId());
            if (staWork.getRequestType().equals(CommonConstant.CLIENT_TYPE_1) || staWork.getRequestType().equals(CommonConstant.CLIENT_TYPE_0)) {//小程序
                if (WxlConvertUtils.isNotEmpty(staWork.getJobsId())) {
                    StaGobsType sysGobs = sysGobsTypeService.getById(staWork.getJobsId());
                    staWork.setJobsId(sysGobs.getId()); //任务类型id
                    staWork.setJobsName(sysGobs.getJobsName()); //任务类型名称
                    staWork.setWorkSalary(sysGobs.getJobsHourlyWages()); //薪资标准
                    staWork.setNameFull(sysGobs.getJobsName());//任务名称
                    staWork.setNameNick(sysGobs.getJobsName());//任务名称
                }
                StaWork sw = this.workAssignment(staWork, sysUser, CommonConstant.CLIENT_TYPE_0);
                String staWorkId = staWorkService.saveWorkData2(sw, user.getUsername());
                for (StaTaskDate s : staTaskDates) {
                    s.setStaWorkId(staWorkId);
                    s.setDelFlag(CommonConstant.DEL_FLAG_0);
                    s.setCreateById(user.getId());
                    s.setRemainingNum(s.getExpectNum());
                }
                staTaskDateService.saveBatch(staTaskDates);
                result.setSuccess(true);
                result.setMessage("添加成功");
                return result;
            } else {
                return result.error500("请求错误！");
            }
        } catch (Exception e) {
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 验证手机号码
     *
     * @param phoneNumber
     * @return
     */
    public boolean validatePhoneNumber(String phoneNumber) {
        //String regex = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";
        String regex = "^(13[0-9]|14[0-9]|15[0-9]|16[2567]|17[0-8]|18[0-9]|19[0-9])\\d{8}$";
        if (phoneNumber.length() != 11) {
            regex = "^(?:\\d{3,4}-?)?\\d{7,8}$";
        }
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(phoneNumber);
        // 执行匹配操作
        return matcher.matches();
    }

    /**
     * 添加或修改任务赋值
     *
     * @param staWorkVo
     * @return
     */
    public StaWork workAssignment(StaWork staWorkVo, SysUser sysUser, String type) throws ParseException {
        StaWork staWork = new StaWork();
        BeanUtils.copyProperties(staWorkVo, staWork);
        SysDepart sysDepart = new SysDepart();
        if (WxlConvertUtils.isNotEmpty(staWorkVo.getSelecteddeparts())) {
            sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, staWorkVo.getSelecteddeparts()));
        } else {
            sysDepart = sysDepartService.getById(staWorkVo.getCompanyId());
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart.getStoreNo())) {
            staWork.setStoreNo(sysDepart.getStoreNo());
        }
        //未输入联系人
        if (WxlConvertUtils.isEmpty(staWorkVo.getLiaison())) {
            staWork.setLiaison(sysUser.getRealNameName());//真实姓名
        }
        //未输入联系人电话
        if (WxlConvertUtils.isEmpty(staWorkVo.getLiaisonTp())) {
            staWork.setLiaisonTp(sysUser.getUsername());//电话号码
        }
        staWork.setCompanyId(sysDepart.getId()); //店铺ID
        staWork.setCreateBy(sysUser.getUsername()); //创建人
        staWork.setSysUserId(sysUser.getId());//发布人id
        staWork.setExamineFlag(CommonConstant.GENNERASTATUS1);//需要审核
        staWork.setLcNeedFlag(CommonConstant.GENNERASTATUS1);//需要签到
        staWork.setListShow(CommonConstant.GENNERASTATUS1);//列表展示
        staWork.setRcNeedFlag(CommonConstant.GENNERASTATUS0);//不使用点名
        staWork.setCompanyName(sysDepart.getDepartName());//门店名称
        staWork.setFirmName(sysDepart.getDepartName()); //门店名称
        staWork.setFirmId(sysDepart.getId()); //店铺Ids
        staWork.setNameNick(staWorkVo.getNameFull()); //任务名称
        staWork.setAddress(sysDepart.getAddress());//位置
        staWork.setAddressName(sysDepart.getAddressName()); //位置名称
        staWork.setAddressLng(sysDepart.getAddressLng());//精度
        staWork.setAddressLat(sysDepart.getAddressLat());//维度
        staWork.setSysOrgCode(sysDepart.getOrgCode());//部门code
        staWork.setUpdateTime(new Date());//修改时间
        /**
         * 添加 区域、事业处
         */
        if (WxlConvertUtils.isNotEmpty(sysDepart.getParentId())) {
            SysDepart storeRegionDeptrt = sysDepartService.getById(sysDepart.getParentId());
            staWork.setStoreRegionId(storeRegionDeptrt.getId());
            staWork.setStoreRegionName(storeRegionDeptrt.getDepartName());
            if (WxlConvertUtils.isNotEmpty(storeRegionDeptrt.getParentId())) {
                SysDepart storeBusinessDivision = sysDepartService.getById(storeRegionDeptrt.getParentId());
                staWork.setStoreBusinessDivisionName(storeBusinessDivision.getDepartName());
                staWork.setStoreBusinessDivisionId(storeBusinessDivision.getId());
            }
        }
        if (type.equals(CommonConstant.CLIENT_TYPE_1)) {//网页
            staWork.setNeedIdHealth(staWorkVo.getIdHealth().equals("N") ? CommonConstant.GENNERASTATUS0 : CommonConstant.GENNERASTATUS1);
            //置顶标记
            staWork.setTopFlagUpdate(staWork.getTopFlag().equals("Y") ? new Date() : null);
        }
        staWork.setUpdateBy(sysUser.getUsername());
        /**
         * 预计需求时数、预计总薪资、 任务时段、日期、工作天数
         */
        String taskDate = staWork.getTaskDate();
        if (WxlConvertUtils.isNotEmpty(taskDate)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String[] split = taskDate.split(",");
            Date startDate = format.parse(split[0]);
            Date endData = format.parse(split[1]);
            String format1 = format.format(startDate);
            String format2 = format.format(endData);
            staWork.setTaskStartDate(format.parse(format1));
            staWork.setTaskEndDate(format.parse(format2));
            long dateTotal = DateUtils.calculateDateDifference(format1, format2);
            staWork.setTaskDaysTotal((int) dateTotal);
        }
//        double monthlyHours = 0;
//        Date taskStartTime = staWork.getTaskStartTime();
//        Date taskEndTime = staWork.getTaskEndTime();
//        monthlyHours += DateUtils.getHourDifferenceBetweenTimes(taskStartTime, taskEndTime);
//        staWork.setTaskTimeTotal(monthlyHours);
        return staWork;
    }

    /**
     * 分页列表查询
     *
     * @param staWork
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "任务列表-分页列表查询")
    @ApiOperation(value = "任务列表-分页列表查询", notes = "任务列表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(StaWork staWork,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) throws ParseException {
        Map<String, String[]> parameterMap = req.getParameterMap();
        /**
         * 距离查询
         */
        if (WxlConvertUtils.isNotEmpty(parameterMap.get("currentLat")) && WxlConvertUtils.isNotEmpty(parameterMap.get("currentLng"))) {
            String[] currentLat = parameterMap.get("currentLat");
            String[] currentLng = parameterMap.get("currentLng");
            if (currentLat.length > 0 && currentLng.length > 0) {
                StaWorkModel staWorkModel = new StaWorkModel();
                staWorkModel.setStateFlag(CommonConstant.WORK_STATUS_3); //执行中
                BigDecimal bigDecimalLng = new BigDecimal(currentLng[0]);
                staWorkModel.setCurrentLng(bigDecimalLng);
                BigDecimal bigDecimalLat = new BigDecimal(currentLat[0]);
                staWorkModel.setCurrentLat(bigDecimalLat);
                Page<StaWorkModel> distancePage = new Page<>(pageNo, pageSize);
                //距离排序
                distancePage = staWorkService.listOrderDistancePage(distancePage, staWorkModel);
                for (StaWorkModel record : distancePage.getRecords()) {
                    if (record.getServiceStartTime() != null) {
                        Date startTime = record.getServiceStartTime();
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startTime);
                        Calendar currentCalendar = Calendar.getInstance();
                        calendar.set(Calendar.YEAR, currentCalendar.get(Calendar.YEAR));
                        calendar.set(Calendar.MONTH, currentCalendar.get(Calendar.MONTH));
                        calendar.set(Calendar.DAY_OF_MONTH, currentCalendar.get(Calendar.DAY_OF_MONTH));
                        Date completeDateTime = calendar.getTime();
                        record.setServiceStartTime(completeDateTime);
                    }
                    if (record.getServiceEndTime() != null) {
                        Date startTime = record.getServiceEndTime();
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startTime);
                        Calendar currentCalendar = Calendar.getInstance();
                        calendar.set(Calendar.YEAR, currentCalendar.get(Calendar.YEAR));
                        calendar.set(Calendar.MONTH, currentCalendar.get(Calendar.MONTH));
                        calendar.set(Calendar.DAY_OF_MONTH, currentCalendar.get(Calendar.DAY_OF_MONTH));
                        Date completeDateTime = calendar.getTime();
                        record.setServiceEndTime(completeDateTime);
                    }
                }
                return Result.OK(distancePage);
            }
        }
        /**
         * 区域查询
         */
        if (WxlConvertUtils.isNotEmpty(parameterMap.get("selectTheRegion"))) {
            String[] selectTheRegion = parameterMap.get("selectTheRegion");
            if (selectTheRegion.length > 0 && selectTheRegion != null) {
                LambdaQueryWrapper<StaWork> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                IPage<StaWork> page = new Page<>(pageNo, pageSize);
                lambdaQueryWrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0);
                lambdaQueryWrapper.eq(StaWork::getStateFlag, CommonConstant.WORK_STATUS_3);
                lambdaQueryWrapper.like(StaWork::getAddress, selectTheRegion[0]);
                IPage<StaWork> pageList = staWorkService.page(page, lambdaQueryWrapper);
                return Result.OK(pageList);
            }
        }
        /**
         * 周期 几天内
         */
        if (WxlConvertUtils.isNotEmpty(parameterMap.get("publishTime"))) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String[] publishTime = parameterMap.get("publishTime");
            LocalDate today = LocalDate.now();
            LocalDate nextDay = today.plusDays(1);
            Date endDate = java.sql.Date.valueOf(nextDay);
            String endD = sdf.format(endDate);
            LambdaQueryWrapper<StaWork> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            IPage<StaWork> page = new Page<>(pageNo, pageSize);
            lambdaQueryWrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0);
            lambdaQueryWrapper.eq(StaWork::getStateFlag, CommonConstant.WORK_STATUS_3);
            lambdaQueryWrapper.ge(StaWork::getCreateTime, publishTime[0]).le(StaWork::getCreateTime, endD);
            IPage<StaWork> pageList = staWorkService.page(page, lambdaQueryWrapper);
            return Result.OK(pageList);
        }
        //发布时间
        QueryWrapper<StaWork> queryWrapper = QueryGenerator.initQueryWrapper(staWork, parameterMap);
        String[] taskAndDep = parameterMap.get("taskAndDep"); //任务名称 或门店名称
        Page<StaWork> page = new Page<StaWork>(pageNo, pageSize);
        queryWrapper.eq("state_flag", CommonConstant.WORK_STATUS_3);
        if (WxlConvertUtils.isNotEmpty(taskAndDep)) {
            String a = taskAndDep[0];
            queryWrapper.and(wapper -> wapper.like("name_full", taskAndDep[0]).or().like("company_name", taskAndDep[0]));
        }
        IPage<StaWork> pageList = staWorkService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "任务列表-通过id删除")
    @ApiOperation(value = "任务列表-通过id删除", notes = "任务列表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        staWorkService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "任务列表-批量删除")
    @ApiOperation(value = "任务列表-批量删除", notes = "任务列表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.staWorkService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "任务列表-通过id查询")
    @ApiOperation(value = "任务列表-通过id查询", notes = "任务列表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id, HttpServletRequest request) {
        StaWork staWork = staWorkService.getById(id);
        if (staWork == null) {
            return Result.error("未找到对应数据");
        }
        if (!CommonConstant.DEL_FLAG_0.equals(staWork.getDelFlag())) {
            return Result.error("任务已被删除");
        }
        StaWorkPage vo = new StaWorkPage();
        BeanUtils.copyProperties(staWork, vo);
        LambdaQueryWrapper<StaWorkCollect> staWorkCollectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staWorkCollectLambdaQueryWrapper.eq(StaWorkCollect::getStaWorkId, id);
        int collections = staWorkCollectService.count(staWorkCollectLambdaQueryWrapper);
        LambdaQueryWrapper<StaWorkBr> staWorkBrLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staWorkBrLambdaQueryWrapper.eq(StaWorkBr::getStaWorkId, id);
        int visits = staWorkBrService.count(staWorkBrLambdaQueryWrapper);
        vo.setVisits(visits);//访问量
        vo.setCollections(collections);//收藏量
        //任务描述
        if (WxlConvertUtils.isNotEmpty(vo.getStaNoticeId())) {
            StaNotice staNotice = staNoticeService.getById(vo.getStaNoticeId());
            if (WxlConvertUtils.isNotEmpty(staNotice)) {
                vo.setStaNoticeContext(staNotice.getContent());
            }
        }
        //未登录时
        try {
            String accessToken = request.getHeader("X-Access-Token");
            if (WxlConvertUtils.isNotEmpty(accessToken)) {
                String username = JwtUtil.getUserNameByToken(request);
                if (WxlConvertUtils.isNotEmpty(username)) {
                    SysUser user = sysUserService.getUserByName(username);
                    vo.setHasCollections(0);
                    if (WxlConvertUtils.isNotEmpty(user)) {
                        LambdaQueryWrapper<StaWorkCollect> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(StaWorkCollect::getSysUserId, user.getId());
                        queryWrapper.eq(StaWorkCollect::getStaWorkId, id);
                        int count = staWorkCollectService.count(queryWrapper);
                        if (count > 0) {
                            vo.setHasCollections(1);//当前用户是否收藏
                        }
                    }
                }
            }

        } catch (ByunBootException e) {
        }
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sb = new SimpleDateFormat("HH:mm");
        String staWorkId = vo.getId();
        List<StaTaskDateVO> staTaskDateVOS = new ArrayList<>();
        List<StaTaskDate> list = staTaskDateService.list(new QueryWrapper<StaTaskDate>().lambda().eq(StaTaskDate::getStaWorkId, staWorkId));
        for (StaTaskDate staTaskDate : list) {
            StaTaskDateVO staTaskDateVO = new StaTaskDateVO();
            staTaskDateVO.setId(staTaskDate.getId());
            staTaskDateVO.setTaskStartDate(sd.format(staTaskDate.getTaskStartDate()));
            staTaskDateVO.setTaskEndDate(sd.format(staTaskDate.getTaskEndDate()));
            staTaskDateVO.setTaskStartTime(sb.format(staTaskDate.getTaskStartTime()));
            staTaskDateVO.setTaskEndTime(sb.format(staTaskDate.getTaskEndTime()));
            staTaskDateVO.setShiftCode(staTaskDate.getShiftCode());
            staTaskDateVO.setExpectNum(String.valueOf(staTaskDate.getExpectNum()));
            staTaskDateVO.setRemainingNum(staTaskDate.getRemainingNum());
            staTaskDateVO.setTaskStartTime2(staTaskDate.getTaskStartTime2() != null ? sb.format(staTaskDate.getTaskStartTime2()) : null);
            staTaskDateVO.setTaskEndTime2(staTaskDate.getTaskEndTime2() != null ? sb.format(staTaskDate.getTaskEndTime2()) : null);
            staTaskDateVOS.add(staTaskDateVO);
        }
        vo.setStaTaskDateAndTimes(staTaskDateVOS);
        return Result.OK(vo);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "任务图片通过主表ID查询")
    @ApiOperation(value = "任务图片主表ID查询", notes = "任务图片-通主表ID查询")
    @GetMapping(value = "/queryStaWorkImageByMainId")
    public Result<?> queryStaWorkImageListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<StaWorkImage> staWorkImageList = staWorkImageService.selectByMainId(id);
        return Result.OK(staWorkImageList);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "访问量、收藏量通过主表ID查询")
    @ApiOperation(value = "访问量、收藏量主表ID查询", notes = "访问量、收藏量-通主表ID查询")
    @GetMapping(value = "/queryStaWorkVisitsByMainId")
    public Result<?> queryStaWorkVisitsListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<StaWorkVisits> staWorkVisitsList = staWorkVisitsService.selectByMainId(id);
        return Result.OK(staWorkVisitsList);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新通过主表ID查询")
    @ApiOperation(value = "任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新主表ID查询", notes = "任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新-通主表ID查询")
    @GetMapping(value = "/queryStaWorkEnrollByMainId")
    public Result<?> queryStaWorkEnrollListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<StaWorkEnroll> staWorkEnrollList = staWorkEnrollService.selectByMainId(id);
        return Result.OK(staWorkEnrollList);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param staWork
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(
            @RequestParam(name = "deptName", required = false) String deptName,
            @RequestParam(name = "stateFlag", required = false) String status,
            @RequestParam(name = "nameFull", required = false) String nameFull,
            HttpServletRequest request, StaWork staWork) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, String[]> parameterMap = request.getParameterMap();
        String[] selecteddeparts = parameterMap.get("selecteddeparts");
        String[] firm = parameterMap.get("firm");
        //查询所有负责部门
        List<String> departIdArr = null;
        //查询部门 和下级部门
        if (WxlConvertUtils.isNotEmpty(user.getOrgCode())) {
            departIdArr = sysDepartService.getSubDepIdsByOrgCode(user.getOrgCode());
        }
        if (WxlConvertUtils.isEmpty(departIdArr) || departIdArr.size() == 0) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "未获取登录部门");
            return mv;
        }
        String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
        //用户权限
        Set<String> userPermissionSet = sysBaseAPI.getUserPermissionSet(user.getUsername(), departId);
        boolean positionlist = userPermissionSet.contains("staffing:position:list");//查询所有任务列表的权限
        boolean positionmylist = userPermissionSet.contains("staffing:position:mylist");//查询自己任务列表的权限
        QueryWrapper<StaWork> queryWrapper = new QueryWrapper<>();
        List<StaWork> queryList = new ArrayList<>();
        if (positionlist) {
            if (WxlConvertUtils.isNotEmpty(selecteddeparts) && !selecteddeparts[0].isEmpty()) {
                SysDepart sysDepart = sysDepartService.getById(selecteddeparts[0]);
                queryWrapper.likeRight("sys_org_code", sysDepart.getOrgCode());
            } else {
                //查询当前登录部门 和下级部门
                queryWrapper.in("company_id", departIdArr);
            }
            if (WxlConvertUtils.isNotEmpty(firm)) {
                queryWrapper.like("firm_name", firm[0]);
            }
            //名字或店编搜索
            //级联查询
            if (WxlConvertUtils.isNotEmpty(deptName)) {
                //当前选中的部门
                SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, deptName));
                if (WxlConvertUtils.isEmpty(sysDepart)) { //小程序
                    queryWrapper.like("company_name", deptName);
                    queryWrapper.likeRight("sys_org_code", user.getOrgCode());
                } else {
                    queryWrapper.like("sys_org_code", sysDepart.getOrgCode());
                }
            }
            if (WxlConvertUtils.isNotEmpty(status)) {
                queryWrapper.eq("state_flag", status);
            }
            if (WxlConvertUtils.isNotEmpty(nameFull)) {
                queryWrapper.lambda().eq(StaWork::getNameFull, nameFull);
            }
            queryWrapper.eq("del_flag", CommonConstant.DEL_FLAG_0);
            queryWrapper.orderByDesc("create_time", "store_region_name");
            List<StaWork> pageList = staWorkService.list(queryWrapper);
            queryList = pageList;
        } else {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "暂无权限");
            return mv;
        }
        //Step.2 获取导出数据
        //List<StaWork> queryList = staWorkService.list(queryWrapper);
        // 过滤选中数据
        String selections = request.getParameter("selections");
        List<StaWork> staWorkList = new ArrayList<StaWork>();
        if (WxlConvertUtils.isEmpty(selections)) {
            staWorkList = queryList;
        } else {
            List<String> selectionList = Arrays.asList(selections.split(","));
            staWorkList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        }
        // Step.3 组装pageList
        //List<StaWorkVoPage> pageList = new ArrayList<StaWorkVoPage>();
        List<StaWorkVoPage> pageList = this.taskExportXls(staWorkList);
        Map<Integer, String> map = new HashMap<Integer, String>() {{
            put(0, "未发布");
            put(1, "完成");
            put(2, "发布中");
            put(3, "服务中");
            put(4, "结算中");
            put(5, "已下架");
        }};
        for (StaWorkVoPage staWorkVoPage : pageList) {
            int index = staWorkVoPage.getCompanyName().indexOf("(");
            try {
                staWorkVoPage.setCompanyName(staWorkVoPage.getCompanyName().substring(0, index));
            } catch (Exception e) {
                //捕获 录入门店个别格式  正常格式 门店(店编) 错误格式 门店（店编） 店编门店
                //System.err.println(main.getFirmName());
            }
            //状态
            staWorkVoPage.setStateFlagStr(map.get(staWorkVoPage.getStateFlag()));
        }
        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "任务详情列表列表");
        mv.addObject(NormalExcelConstants.CLASS, StaWorkVoPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("任务详情列表数据", "导出人:" + user.getRealname(), "任务列表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入任务
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<StaWorkPage> list = ExcelImportUtil.importExcel(file.getInputStream(), StaWorkPage.class, params);
                for (StaWorkPage page : list) {
                    StaWork po = new StaWork();
                    BeanUtils.copyProperties(page, po);
                    //TODO 暂时删除
                    //staWorkService.saveMain(po, page.getStaWorkImageList(),page.getStaWorkVisitsList(),page.getStaWorkEnrollList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }

    @GetMapping("/listWorkByStores/{firmId}")
    public Result getWorkByStores(@PathVariable("firmId") String firmId) {
        if (WxlConvertUtils.isEmpty(firmId)) {
            return Result.error("部门丢失");
        }
        LambdaQueryWrapper<StaWork> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StaWork::getDelFlag, CommonConstant.DEL_FLAG_0)
                .eq(StaWork::getFirmId, firmId);
        List<StaWork> list = staWorkService.list(lambdaQueryWrapper);
        if (!list.isEmpty()) {
            return Result.OK(list);
        } else {
            return Result.error("当前门店无任务");
        }
    }

    /**
     * 获取任务列表
     *
     * @param pageList
     * @return
     */
    private IPage<StaWorkVoPage> taskList(IPage<StaWork> pageList) {
        List<StaWorkVoPage> staWorkVoPages = new ArrayList<>();
        for (StaWork record : pageList.getRecords()) {
            StaWorkVoPage staWorkVoPage = new StaWorkVoPage();
            BeanUtils.copyProperties(record, staWorkVoPage);
            staWorkVoPages.add(staWorkVoPage);
        }
        IPage<StaWorkVoPage> staWorkVoPageIPage = new Page<StaWorkVoPage>();
        BeanUtils.copyProperties(pageList, staWorkVoPageIPage, "records");
        staWorkVoPageIPage.setRecords(staWorkVoPages);
        return staWorkVoPageIPage;
    }

    /**
     * 导出任务列表组装数据
     *
     * @param list
     * @return
     */
    private List<StaWorkVoPage> taskExportXls(List<StaWork> list) {
        List<StaWorkVoPage> staWorkVoPages = new ArrayList<>();
        for (StaWork record : list) {
            StaWorkVoPage staWorkVoPage = new StaWorkVoPage();
            BeanUtils.copyProperties(record, staWorkVoPage);
            staWorkVoPages.add(staWorkVoPage);
        }
        List<StaWorkVoPage> staWorkVoPageIPage = new ArrayList<>();
        BeanUtils.copyProperties(list, staWorkVoPageIPage, "records");
        staWorkVoPageIPage.addAll(staWorkVoPages);
        if (!staWorkVoPageIPage.isEmpty()) {
            Map<String, List<StaOrder>> staWorkOrders = new HashMap<>();
            //置顶 健康证
            for (StaWorkVoPage record : staWorkVoPageIPage) {
                record.setIdHealth(record.getNeedIdHealth() == CommonConstant.GENNERASTATUS0 ? "Y" : "N");//是否需要健康证
                record.setTopFlag(record.getTopFlagUpdate() != null ? "Y" : "N");//置顶
            }
            //任务成本和时数计算,实际打卡计算
            List<String> workIds = staWorkVoPageIPage.stream().map(StaWorkVoPage::getId).collect(Collectors.toList());
            //任务单
            List<StaOrder> staOrders = staOrderService.list(new QueryWrapper<StaOrder>().lambda()
                    .eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0)
                    .in(StaOrder::getStaWorkId, workIds));
            staWorkOrders = staOrders.stream().collect(Collectors.groupingBy(StaOrder::getStaWorkId));
            List<String> ordierIds = staOrders.stream().map(StaOrder::getId).collect(Collectors.toList());
            if (!ordierIds.isEmpty()) {
                //打卡记录
                List<StaLocationClock> staLocationClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>()
                        .lambda()
                        .in(StaLocationClock::getStaOrderId, ordierIds)
                        .isNotNull(StaLocationClock::getTimeExpect)
                        .eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0));
                if (!staLocationClocks.isEmpty()) {
                    //拆分打卡集合(key:任务id、value:打卡数据)
                    Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
                    for (StaLocationClock staLocationClock : staLocationClocks) {
                        String staOrderId = staLocationClock.getStaOrderId();
                        if (!locationClocksMap.containsKey(staOrderId)) {
                            locationClocksMap.put(staOrderId, new ArrayList<>());
                        }
                        locationClocksMap.get(staOrderId).add(staLocationClock);
                    }
                    Map<String, Double> workingHoursMap = new HashMap<>();//任务单时数
                    //根据天拆分打卡(后续几天每天时数)
                    for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                        Double workingHours = 0.0; //当前任务单时数
                        String staOrderId = entry.getKey();
                        List<StaLocationClock> value = entry.getValue();
                        //key签到日期，value签到数据
                        Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                                .collect(Collectors.groupingBy(clock -> {
                                    Calendar cal = Calendar.getInstance();
                                    cal.setTime(clock.getTime());
                                    cal.set(Calendar.HOUR_OF_DAY, 0);
                                    cal.set(Calendar.MINUTE, 0);
                                    cal.set(Calendar.SECOND, 0);
                                    cal.set(Calendar.MILLISECOND, 0);
                                    return cal.getTime();
                                }));
                        //升序排序
                        for (List<StaLocationClock> sublist : mapByDay.values()) {
                            sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                        }
                        //时数计算
                        for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                            List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                            List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());//打卡时间
                            List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());//要求打卡时间
                            long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                            long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                            if (totalWorkDuration > shiftDuration) {
                                totalWorkDuration = shiftDuration;
                            }
                            Long hours = totalWorkDuration / 60;//小时
                            Long remainingMinutes = totalWorkDuration % 60; //分钟
                            String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                            double workHours = Double.parseDouble(workHoursStr); //当天时数
                            if (workHours > 0) {
                                workingHours += workHours;
                            }
                        }
                        workingHoursMap.put(staOrderId, workingHours);
                    }
                    for (Map.Entry<String, List<StaOrder>> entry : staWorkOrders.entrySet()) {
                        List<StaOrder> orders = entry.getValue();
                        Double num = 0.0; //任务时数
                        for (StaOrder order : orders) {
                            if (workingHoursMap.get(order.getId()) != null) {
                                num = num + workingHoursMap.get(order.getId());
                            }
                        }
                        for (StaWorkVoPage record : staWorkVoPageIPage) {
                            if (record.getId().equals(entry.getKey())) {
                                record.setTotalTime(num); //任务产生的实际时数
                                //时数*结算标准计算实际产生金额
                                if (record.getWorkSalary() != null) {
                                    BigDecimal bigDecimal = new BigDecimal(num); //时数
                                    record.setWorkSalary(record.getWorkSalary().multiply(bigDecimal).setScale(1, RoundingMode.DOWN)); //金额
                                }
                            }

                        }
                    }
                }
            }
        }
        return staWorkVoPageIPage;
    }

    @GetMapping("/getTaskByDeptIdList")
    public Result getTaskByDeptIdList(@RequestParam("storeId") String storeId) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效");
        }
        Result result = new Result();
        List<StaWork> staWorks = staWorkService.list(new QueryWrapper<StaWork>().lambda().eq(StaWork::getFirmId, storeId));
        result.setSuccess(true);
        result.setResult(staWorks);
        //result.setMessage("");
        return result;
    }

    @GetMapping("/getUsersByTaskId")
    public Result getUsersByTaskId(@RequestParam("taskId") String taskId) {
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd");
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效");
        }
        Result result = new Result();
        List<StaOrder> staOrders = staOrderService.list(new QueryWrapper<StaOrder>()
                .lambda()
                .eq(StaOrder::getStaWorkId, taskId)
                .notIn(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_0, CommonConstant.ORDER_STATUS_1, CommonConstant.ORDER_STATUS_6)
        );
        if (WxlConvertUtils.isNotEmpty(staOrders) && !staOrders.isEmpty()) {
            List<String> taskDateIdList = staOrders.stream().map(StaOrder::getStaTaskDateId).collect(Collectors.toList());
            List<StaTaskDate> staTaskDateList = staTaskDateService.list(new LambdaQueryWrapper<StaTaskDate>().in(StaTaskDate::getId, taskDateIdList));
            for (StaOrder staOrder : staOrders) {
                List<StaTaskDate> collect = staTaskDateList.stream().filter(f -> f.getId().equals(staOrder.getStaTaskDateId())).collect(Collectors.toList());
                if (WxlConvertUtils.isNotEmpty(collect) && !collect.isEmpty()) {
                    staOrder.setSubtractStartDate(sb.format(collect.get(0).getTaskStartDate()));
                    staOrder.setSubtractEndDate(sb.format(collect.get(0).getTaskEndDate()));
                }
            }
        }
        result.setSuccess(true);
        result.setResult(staOrders);
        return result;
    }
}
