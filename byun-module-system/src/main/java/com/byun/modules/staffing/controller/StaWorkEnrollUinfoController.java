package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaWorkEnrollUinfo;
import com.byun.modules.staffing.service.IStaWorkEnrollUinfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新")
@RestController
@RequestMapping("/staffing/staWorkEnrollUinfo")
@Slf4j
public class StaWorkEnrollUinfoController extends ByunExcelController<StaWorkEnrollUinfo, IStaWorkEnrollUinfoService> {
	@Autowired
	private IStaWorkEnrollUinfoService staWorkEnrollUinfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staWorkEnrollUinfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-分页列表查询")
	@ApiOperation(value="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-分页列表查询", notes="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkEnrollUinfo staWorkEnrollUinfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaWorkEnrollUinfo> queryWrapper = QueryGenerator.initQueryWrapper(staWorkEnrollUinfo, req.getParameterMap());
		Page<StaWorkEnrollUinfo> page = new Page<StaWorkEnrollUinfo>(pageNo, pageSize);
		IPage<StaWorkEnrollUinfo> pageList = staWorkEnrollUinfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staWorkEnrollUinfo
	 * @return
	 */
	@AutoLog(value = "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-添加")
	@ApiOperation(value="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-添加", notes="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkEnrollUinfo staWorkEnrollUinfo) {
		staWorkEnrollUinfoService.save(staWorkEnrollUinfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkEnrollUinfo
	 * @return
	 */
	@AutoLog(value = "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-编辑")
	@ApiOperation(value="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-编辑", notes="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkEnrollUinfo staWorkEnrollUinfo) {
		staWorkEnrollUinfoService.updateById(staWorkEnrollUinfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-通过id删除")
	@ApiOperation(value="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-通过id删除", notes="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkEnrollUinfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-批量删除")
	@ApiOperation(value="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-批量删除", notes="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkEnrollUinfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-通过id查询")
	@ApiOperation(value="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-通过id查询", notes="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkEnrollUinfo staWorkEnrollUinfo = staWorkEnrollUinfoService.getById(id);
		if(staWorkEnrollUinfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkEnrollUinfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkEnrollUinfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkEnrollUinfo staWorkEnrollUinfo) {
        return super.exportXls(request, staWorkEnrollUinfo, StaWorkEnrollUinfo.class, "任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkEnrollUinfo.class);
    }

}
