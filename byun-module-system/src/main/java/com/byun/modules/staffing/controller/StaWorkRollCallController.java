package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaWorkRollCall;
import com.byun.modules.staffing.service.IStaWorkRollCallService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 报道点名表（用户是否到场）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="报道点名表（用户是否到场）")
@RestController
@RequestMapping("/staffing/staWorkRollCall")
@Slf4j
public class StaWorkRollCallController extends ByunExcelController<StaWorkRollCall, IStaWorkRollCallService> {
	@Autowired
	private IStaWorkRollCallService staWorkRollCallService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staWorkRollCall
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "报道点名表（用户是否到场）-分页列表查询")
	@ApiOperation(value="报道点名表（用户是否到场）-分页列表查询", notes="报道点名表（用户是否到场）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkRollCall staWorkRollCall,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaWorkRollCall> queryWrapper = QueryGenerator.initQueryWrapper(staWorkRollCall, req.getParameterMap());
		Page<StaWorkRollCall> page = new Page<StaWorkRollCall>(pageNo, pageSize);
		IPage<StaWorkRollCall> pageList = staWorkRollCallService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staWorkRollCall
	 * @return
	 */
	@AutoLog(value = "报道点名表（用户是否到场）-添加")
	@ApiOperation(value="报道点名表（用户是否到场）-添加", notes="报道点名表（用户是否到场）-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkRollCall staWorkRollCall) {
		staWorkRollCallService.save(staWorkRollCall);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkRollCall
	 * @return
	 */
	@AutoLog(value = "报道点名表（用户是否到场）-编辑")
	@ApiOperation(value="报道点名表（用户是否到场）-编辑", notes="报道点名表（用户是否到场）-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkRollCall staWorkRollCall) {
		staWorkRollCallService.updateById(staWorkRollCall);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报道点名表（用户是否到场）-通过id删除")
	@ApiOperation(value="报道点名表（用户是否到场）-通过id删除", notes="报道点名表（用户是否到场）-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkRollCallService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "报道点名表（用户是否到场）-批量删除")
	@ApiOperation(value="报道点名表（用户是否到场）-批量删除", notes="报道点名表（用户是否到场）-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkRollCallService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报道点名表（用户是否到场）-通过id查询")
	@ApiOperation(value="报道点名表（用户是否到场）-通过id查询", notes="报道点名表（用户是否到场）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkRollCall staWorkRollCall = staWorkRollCallService.getById(id);
		if(staWorkRollCall==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkRollCall);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkRollCall
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkRollCall staWorkRollCall) {
        return super.exportXls(request, staWorkRollCall, StaWorkRollCall.class, "报道点名表（用户是否到场）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkRollCall.class);
    }

}
