package com.byun.modules.staffing.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.staffing.entity.StaWorkTemplate;
import com.byun.modules.staffing.service.IStaWorkTemplateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 任务模板（方便管理添加）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="任务模板（方便管理添加）")
@RestController
@RequestMapping("/staffing/staWorkTemplate")
@Slf4j
public class StaWorkTemplateController extends ByunExcelController<StaWorkTemplate, IStaWorkTemplateService> {
	@Autowired
	private IStaWorkTemplateService staWorkTemplateService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staWorkTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务模板（方便管理添加）-分页列表查询")
	@ApiOperation(value="任务模板（方便管理添加）-分页列表查询", notes="任务模板（方便管理添加）-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(StaWorkTemplate staWorkTemplate,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<StaWorkTemplate> queryWrapper = QueryGenerator.initQueryWrapper(staWorkTemplate, req.getParameterMap());
		Page<StaWorkTemplate> page = new Page<StaWorkTemplate>(pageNo, pageSize);
		IPage<StaWorkTemplate> pageList = staWorkTemplateService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param staWorkTemplate
	 * @return
	 */
	@AutoLog(value = "任务模板（方便管理添加）-添加")
	@ApiOperation(value="任务模板（方便管理添加）-添加", notes="任务模板（方便管理添加）-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkTemplate staWorkTemplate) {
		staWorkTemplateService.save(staWorkTemplate);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkTemplate
	 * @return
	 */
	@AutoLog(value = "任务模板（方便管理添加）-编辑")
	@ApiOperation(value="任务模板（方便管理添加）-编辑", notes="任务模板（方便管理添加）-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkTemplate staWorkTemplate) {
		staWorkTemplateService.updateById(staWorkTemplate);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务模板（方便管理添加）-通过id删除")
	@ApiOperation(value="任务模板（方便管理添加）-通过id删除", notes="任务模板（方便管理添加）-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkTemplateService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务模板（方便管理添加）-批量删除")
	@ApiOperation(value="任务模板（方便管理添加）-批量删除", notes="任务模板（方便管理添加）-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkTemplateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务模板（方便管理添加）-通过id查询")
	@ApiOperation(value="任务模板（方便管理添加）-通过id查询", notes="任务模板（方便管理添加）-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkTemplate staWorkTemplate = staWorkTemplateService.getById(id);
		if(staWorkTemplate==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkTemplate);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkTemplate
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkTemplate staWorkTemplate) {
        return super.exportXls(request, staWorkTemplate, StaWorkTemplate.class, "任务模板（方便管理添加）");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StaWorkTemplate.class);
    }

}
