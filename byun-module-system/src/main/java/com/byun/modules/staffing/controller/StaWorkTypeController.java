package com.byun.modules.staffing.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaWorkType;
import com.byun.modules.staffing.service.IStaWorkTypeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 任务类型
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="任务类型")
@RestController
@RequestMapping("/staffing/staWorkType")
@Slf4j
public class StaWorkTypeController extends ByunExcelController<StaWorkType, IStaWorkTypeService> {
	@Autowired
	private IStaWorkTypeService staWorkTypeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param staWorkType
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务类型-分页列表查询")
	@ApiOperation(value="任务类型-分页列表查询", notes="任务类型-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<?> queryPageList(StaWorkType staWorkType,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
        if(hasQuery != null && "true".equals(hasQuery)){
            QueryWrapper<StaWorkType> queryWrapper =  QueryGenerator.initQueryWrapper(staWorkType, req.getParameterMap());
            List<StaWorkType> list = staWorkTypeService.queryTreeListNoPage(queryWrapper);
            IPage<StaWorkType> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = staWorkType.getPid();
            if (WxlConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            staWorkType.setPid(null);
            QueryWrapper<StaWorkType> queryWrapper = QueryGenerator.initQueryWrapper(staWorkType, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("pid", parentId);
            Page<StaWorkType> page = new Page<StaWorkType>(pageNo, pageSize);
            IPage<StaWorkType> pageList = staWorkTypeService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
	}

	 /**
      * 获取子数据
      * @param staWorkType
      * @param req
      * @return
      */
	@AutoLog(value = "任务类型-获取子数据")
	@ApiOperation(value="任务类型-获取子数据", notes="任务类型-获取子数据")
	@GetMapping(value = "/childList")
	public Result<?> queryPageList(StaWorkType staWorkType,HttpServletRequest req) {
		QueryWrapper<StaWorkType> queryWrapper = QueryGenerator.initQueryWrapper(staWorkType, req.getParameterMap());
		List<StaWorkType> list = staWorkTypeService.list(queryWrapper);
		IPage<StaWorkType> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
		return Result.OK(pageList);
	}

    /**
      * 批量查询子节点
      * @param parentIds 父ID（多个采用半角逗号分割）
      * @return 返回 IPage
      * @param parentIds
      * @return
      */
	@AutoLog(value = "任务类型-批量获取子数据")
    @ApiOperation(value="任务类型-批量获取子数据", notes="任务类型-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<StaWorkType> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<StaWorkType> list = staWorkTypeService.list(queryWrapper);
            IPage<StaWorkType> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }
	
	/**
	 *   添加
	 *
	 * @param staWorkType
	 * @return
	 */
	@AutoLog(value = "任务类型-添加")
	@ApiOperation(value="任务类型-添加", notes="任务类型-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody StaWorkType staWorkType) {
		staWorkTypeService.addStaWorkType(staWorkType);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param staWorkType
	 * @return
	 */
	@AutoLog(value = "任务类型-编辑")
	@ApiOperation(value="任务类型-编辑", notes="任务类型-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody StaWorkType staWorkType) {
		staWorkTypeService.updateStaWorkType(staWorkType);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务类型-通过id删除")
	@ApiOperation(value="任务类型-通过id删除", notes="任务类型-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		staWorkTypeService.deleteStaWorkType(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务类型-批量删除")
	@ApiOperation(value="任务类型-批量删除", notes="任务类型-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.staWorkTypeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务类型-通过id查询")
	@ApiOperation(value="任务类型-通过id查询", notes="任务类型-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		StaWorkType staWorkType = staWorkTypeService.getById(id);
		if(staWorkType==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(staWorkType);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param staWorkType
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StaWorkType staWorkType) {
		return super.exportXls(request, staWorkType, StaWorkType.class, "任务类型");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, StaWorkType.class);
    }

}
