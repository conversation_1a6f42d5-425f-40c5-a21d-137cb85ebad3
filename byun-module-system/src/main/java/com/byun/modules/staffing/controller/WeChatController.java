package com.byun.modules.staffing.controller;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.RedisUtil;
import com.byun.common.util.RestUtil;
import com.byun.common.util.wechat.AppletUtil;
import com.byun.modules.bosong.utils.WechatQrcodeUtil;
import com.byun.modules.system.entity.SysApplet;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.ISysAppletService;
import com.byun.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 微信相关接口
 * <AUTHOR>
 * @date 2021/11/15 23:29
 * @version 1.0
 */
@Api(tags="微信相关接口")
@RestController
@RequestMapping("/staffing/wechat")
@Slf4j
public class WeChatController {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysAppletService sysAppletService;
    @Autowired
    private ISysUserService sysUserService;

    @ApiOperation("获取小程序Openid")
    @RequestMapping(value = "/openid", method = RequestMethod.POST)
    public Result<JSONObject> getAppletOpenid(@RequestParam(name = "code") String code){
        try {
            //获取openid,session_key
            String requestUrl = AppletUtil.getCode2SessionUrl(code);//通过自定义工具类组合出小程序需要的登录凭证 code
            ResponseEntity<String> response  = RestUtil.request(requestUrl,  HttpMethod.GET, null, null, null, String.class);
            // 封装返回结果
            Result<JSONObject> result = new Result<>();
            int statusCode = response.getStatusCodeValue();
            result.setCode(statusCode);
            result.setSuccess(statusCode == 200);
            JSONObject parse = JSONObject.parseObject(response.getBody());
            String openid = parse.getString("openid");
            if(WxlConvertUtils.isEmpty(openid)){
                result.setMessage("获取openid失败");
                result.setSuccess(false);
                return result;
            }
            String sessionKey = parse.getString("session_key");
            if(WxlConvertUtils.isEmpty(sessionKey)){
                result.setMessage("获取sessionKey失败");
                result.setSuccess(false);
                return result;
            }
            //TODO 改用数据库存取？
            redisUtil.set(CommonConstant.SESSION_KEY_CACHE + "_staffing_"+openid, sessionKey);
            //session_key 不传客户端
            parse.remove("session_key");
            result.setResult(parse);
            return result;
        } catch (Exception e) {
            log.debug("获取小程序用户数据失败", e);
            return Result.error(e.getMessage(),null);
        }
    }

    @ApiOperation("获取小程序分享码")
    @RequestMapping(value = "/qrcode", method = RequestMethod.POST)
    public Result<?> qrcode(@RequestBody JSONObject json) {
        //LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        JSONObject params = new JSONObject();
        String url = json.getString("url");
        String scene = json.getString("scene");
        LambdaQueryWrapper<SysApplet> sysAppletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysAppletLambdaQueryWrapper.eq(SysApplet::getAppid,AppletUtil.getStaAppid());
        SysApplet sysApplet = sysAppletService.getOne(sysAppletLambdaQueryWrapper);
        params.put("scene","9");//该值不能为空，或空字符串
        //需要缩短 任务id ，
        if(WxlConvertUtils.isNotEmpty(scene)){
            String[] split = scene.split(",");
            scene = split[0];
            if(split.length==2){
                SysUser userByName = sysUserService.getUserByName(split[1]);
                //所有用户都可绑定推广者  //&&!CommonConstant.USER_IDENTITY_STAFFING_USER.equals(userByName.getUserIdentity())
                if(WxlConvertUtils.isNotEmpty(userByName)){
                    scene+=","+split[1];
                }
            }
            params.put("scene",scene);
        }else{
        }
        //小程序页面路径
        if(WxlConvertUtils.isNotEmpty(url)){
            params.put("page",url);
        }
        //params.put("check_path",true);//TODO 测试用 检查page 是否存在 true 必须是已发布,发布后注释掉
        params.put("env_version",AppletUtil.getQrcodeVersion());//要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
        String wxacodeUrl = AppletUtil.getwxacodeunlimit(sysApplet.getAccessToken());
        ResponseEntity<byte[]> response  = RestUtil.request(wxacodeUrl,  HttpMethod.POST, null, null, params, byte[].class);

        // 使用工具类处理微信API响应
        return WechatQrcodeUtil.processWechatQrcodeResponse(response);
    }

//    @ApiOperation("管理-数据展示")
//    @RequestMapping(value = "/admin/home/<USER>", method = RequestMethod.GET)
//    public Result<JSONObject> adminHomeShownum() {
//        Result<JSONObject> result = new Result<>();
//
//        return result;
//    }
//
//    @ApiOperation("用户-数据展示")
//    @RequestMapping(value = "/home/<USER>", method = RequestMethod.GET)
//    public Result<JSONObject> homeShownum() {
//        Result<JSONObject> result = new Result<>();
//
//        return result;
//    }
//public static void main(String[] args) {
//    List<String> initList = Arrays.asList("张三", "李四", "周一", "刘四", "李强", "李白");
//    System.out.println(initList.stream().filter(e -> !e.startsWith("李")).collect(Collectors.toList()));
//    }
}
