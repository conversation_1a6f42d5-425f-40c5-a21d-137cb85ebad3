package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 黑名单
 * @date : 2024-5-24 10:11
 */
@TableName("sta_black_list")
@Data
public class StaBlackList {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人id
     */
    private String createById;
    /**
     * 创建人
     */
    @Excel(name = "创建人",width = 15)
    private String createByName;
    /**
     * 创建时间
     */
//    @Excel(name = "加入时间",width = 15)
    private Date createTime;
    /**
     * 加入时间
     */
    @Excel(name = "加入时间",width = 15)
    @TableField(exist = false)
    private String createDate;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户姓名
     */
    @Excel(name = "姓名",width = 15)
    private String userName;
    /**
     * 身份证
     */
    @Excel(name = "身份证",width = 30)
    private String idCard;
    /**
     * 性别
     */
    //@Excel(name = "性别",width = 10)
    private int sex;
    @TableField(exist = false)
    @Excel(name = "性别",width = 15)
    private String sexString;
    /**
     * 年龄
     */
    @Excel(name = "年龄",width = 10)
    private String age;
    /**
     * 电话
     */
    @Excel(name = "电话",width = 15)
    private String phone;
    /**
     * 学历
     */
    @Excel(name = "学历",width = 10)
    private String degree;
    /**
     * 黑名单类型
     */
    @Excel(name = "黑名单类型",width = 10)
    private String blackListType;
    /**
     * 删除标记
     */
    private int delFlag;

}
