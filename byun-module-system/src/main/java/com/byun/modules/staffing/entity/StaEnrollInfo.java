package com.byun.modules.staffing.entity;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.byun.modules.system.entity.SysUser;
import io.swagger.models.auth.In;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
 * @Description: 报名信息，用于任务报名
 * @Author: bai
 * @Date:   2021-11-21
 * @Version: V1.0
 */
@Data
@TableName("sta_enroll_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_enroll_info对象", description="报名信息，用于任务报名")
public class StaEnrollInfo implements Serializable {
    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**手机*/
	@Excel(name = "手机", width = 15)
    @ApiModelProperty(value = "手机")
    private java.lang.String phone;
	/**是否短信验证*/
	@Excel(name = "是否短信验证", width = 15)
    @ApiModelProperty(value = "是否短信验证")
    private java.lang.Integer phoneVeri;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**身份证*/
	@Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    private java.lang.String idCard;
	/**身份证是否验证*/
	@Excel(name = "身份证是否验证", width = 15)
    @ApiModelProperty(value = "身份证是否验证")
    private java.lang.Integer idCardVeri;
	/**性别*/
	@Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private java.lang.Integer sex;
	/**年龄*/
	@Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private java.lang.Integer age;
	/**生日*/
	@Excel(name = "生日", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "生日")
    private java.util.Date birthday;
	/**灵活用工用户信息id*/
	@Excel(name = "灵活用工用户信息id", width = 15)
    @ApiModelProperty(value = "灵活用工用户信息id")
    private java.lang.String sysUserId;
	/**用户与报名人的关系0自己1家人2朋友*/
	@Excel(name = "用户与报名人的关系0自己1家人2朋友", width = 15)
    @ApiModelProperty(value = "用户与报名人的关系0自己1家人2朋友")
    private java.lang.Integer userRel;
	/**删除状态(0-正常,1-已删除)*/
	@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常,1-已删除)")
    private java.lang.Integer delFlag;
    @Excel(name = "毕业证编号", width = 15)
    @ApiModelProperty(value = "毕业证编号")
    private java.lang.String idDiploma;
    @Excel(name = "健康证编号", width = 15)
    @ApiModelProperty(value = "健康证编号")
    private java.lang.String idHealth;
    @Excel(name = "一寸相片", width = 15)
    @ApiModelProperty(value = "一寸相片")
    private java.lang.String myPhoto;
    @ApiModelProperty(value = "健康码图片")
    private java.lang.String healthPhoto;
    /**
     * 身份证有效期
     */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "身份证有效期")
    private String idCardStartDate;
    /**
     * 身份证有效期
     */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "身份证有效期")
    private String idCardEndDate;
    /**
     * 健康证体检日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "健康证体检日期")
    private java.util.Date idHealthStartDate;
    /**
     * 健康证有效期`
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "健康证有效期")
    private java.util.Date idHealthEndDate;
    @ApiModelProperty(value = "身份证地址")
    private String idCardAddress;
    @ApiModelProperty(value = "现住地址")
    private String address;
    /**现住址坐标*/
    @ApiModelProperty(value = "现住址坐标")
    private java.math.BigDecimal addressLat;
    /**现住址坐标*/
    @ApiModelProperty(value = "现住址坐标")
    private java.math.BigDecimal addressLng;
    @ApiModelProperty(value = "现住地址名")
    private String addressName;

    @ApiModelProperty(value = "健康证办理单位")
    private String idHealthCompany;
    @ApiModelProperty(value = "紧急联系人手机")
    private java.lang.String contactNumber;
    @ApiModelProperty(value = "紧急联系人")
    private String contact;
    @Excel(name = "身份证是否在有效期内", width = 15)
    @ApiModelProperty(value = "身份证是否在有效期内")
    private java.lang.Integer idCardValidityDate;
    @Excel(name = "是否有毕业证", width = 15)
    @ApiModelProperty(value = "是否有毕业证")
    private java.lang.Integer idDiplomaValidity;
    @Excel(name = "是否有健康证", width = 15)
    @ApiModelProperty(value = "是否有健康证")
    private java.lang.Integer idHealthValidity;
    @Excel(name = "是否有体检表", width = 15)
    @ApiModelProperty(value = "是否有体检表")
    private int physicalExaminationValidity;
    @Excel(name = "健康证是否在有效期内", width = 15)
    @ApiModelProperty(value = "健康证是否在有效期内")
    private java.lang.Integer idHealthValidityDate;
    @Excel(name = "求职类型", width = 15)
    @ApiModelProperty(value = "求职类型")
    private String workClass;
    //薪资范围
    @ApiModelProperty(value = "薪资范围")
    @Excel(name = "薪资范围", width = 15)
    private String salar;
    //人员类别
    @ApiModelProperty(value = "人员类别")
    @Excel(name = "人员类别", width = 15)
    private String personnelCategory;
    @ApiModelProperty(value = "文化程度")
    private String degree;
    //学校名称
    @ApiModelProperty(value = "学校名称")
    @Excel(name = "学校名称", width = 15)
    private String school;
    //专业
    @ApiModelProperty(value = "专业")
    @Excel(name = "专业", width = 15)
    private String major;
    @ApiModelProperty(value = "入学日期")
    @JsonFormat(pattern = "yyyy-MM")
    private Date enrollmentTime;
    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty(value = "毕业日期")
    private Date graduationTime;
    /**
     * 身高
     */
    private String height;
    /**
     * 体重
     */
    private String weight;
    public StaEnrollInfo(SysUser sysUser){
        this.phone = sysUser.getPhone();
        this.name = sysUser.getRealname();
        this.sex = sysUser.getSex();
        this.sysUserId = sysUser.getId();
        this.delFlag=sysUser.getDelFlag();
    }
    public StaEnrollInfo(){

    }
    public StaEnrollInfo(StaEnrollInfo staEnrollInfo) {
        this.salar = staEnrollInfo.getSalar();
        this.workClass = staEnrollInfo.getWorkClass();
        this.id = staEnrollInfo.getId();
        this.createBy = staEnrollInfo.getCreateBy();
        this.createTime = staEnrollInfo.getCreateTime();
        this.updateBy = staEnrollInfo.getUpdateBy();
        this.updateTime = staEnrollInfo.getUpdateTime();
        this.phone = staEnrollInfo.getPhone();
        this.phoneVeri = staEnrollInfo.getPhoneVeri();
        this.name = staEnrollInfo.getName();
        this.idCard = staEnrollInfo.getIdCard();
        this.idCardVeri = staEnrollInfo.getIdCardVeri();
        this.sex = staEnrollInfo.getSex();
        this.age = staEnrollInfo.getAge();
        this.birthday = staEnrollInfo.getBirthday();
        this.sysUserId = staEnrollInfo.getSysUserId();
        this.userRel = staEnrollInfo.getUserRel();
        this.delFlag = staEnrollInfo.getDelFlag();
        this.idDiploma = staEnrollInfo.getIdDiploma();
        this.idHealth = staEnrollInfo.getIdHealth();
        this.myPhoto = staEnrollInfo.getMyPhoto();
        this.healthPhoto = staEnrollInfo.getHealthPhoto();
        this.idCardStartDate = staEnrollInfo.getIdCardStartDate();
        this.idCardEndDate = staEnrollInfo.getIdCardEndDate();
        this.idHealthStartDate = staEnrollInfo.getIdHealthStartDate();
        this.idHealthEndDate = staEnrollInfo.getIdHealthEndDate();
        this.idCardAddress = staEnrollInfo.getIdCardAddress();
        this.address = staEnrollInfo.getAddress();
        this.addressLat = staEnrollInfo.getAddressLat();
        this.addressLng = staEnrollInfo.getAddressLng();
        this.addressName = staEnrollInfo.getAddressName();
        this.degree = staEnrollInfo.getDegree();
        this.idHealthCompany = staEnrollInfo.getIdHealthCompany();
        this.contactNumber = staEnrollInfo.getContactNumber();
        this.contact = staEnrollInfo.getContact();
        this.idCardValidityDate = staEnrollInfo.getIdCardValidityDate();
        this.idDiplomaValidity = staEnrollInfo.getIdDiplomaValidity();
        this.idHealthValidity = staEnrollInfo.getIdHealthValidity();
        this.idHealthValidityDate = staEnrollInfo.getIdHealthValidityDate();
    }
}
