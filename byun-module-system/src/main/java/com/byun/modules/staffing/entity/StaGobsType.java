package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-4-25 9:30
 */
@Data
@TableName(value = "sta_jobs_type")
public class StaGobsType {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 工种名称
     */
    @Excel(name = "任务类型",width = 20)
    private String jobsName;
    /**
     * 时薪
     */
    @Excel(name = "时薪",width = 20)
    private BigDecimal jobsHourlyWages;
    /**
     * 部门id
     */
    private String companyId;
    /**
     * 部门名称
     */
    @Excel(name = "事业处",width = 20)
    private String companyName;
    /**
     * 部门code
     */
    private String orgCode;
    /**
     * 部门类型 1000大卖场事业处 2000百货事业处 3000便利店事业处 4000物流事业处
     */
    private int companyType;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间",width = 20,format = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 修改时间
     */
    @Excel(name = "修改时间",width = 20,format = "yyyy-MM-dd")
    private Date updateTime;
    /**
     * 创建人
     */
    @Excel(name = "创建人",width = 20)
    private String createBy;
    /**
     * 修改人
     */
    @Excel(name = "修改人",width = 20)
    private String updateBy;

    private Integer delFlag;

}
