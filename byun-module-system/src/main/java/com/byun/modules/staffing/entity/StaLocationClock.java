package com.byun.modules.staffing.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 签到定位时间
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@ApiModel(value="sta_location_clock对象", description="签到定位时间")
@Data
@TableName("sta_location_clock")
public class StaLocationClock implements Serializable {
    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**签到类型(1签到2签退）)*/
	@Excel(name = "签到类型(1签到2签退）)", width = 15)
    @ApiModelProperty(value = "签到类型(1签到2签退）)")
    private java.lang.Integer timeType;
	/**要求签到时间*/
	@Excel(name = "要求签到时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "要求签到时间")
    private java.util.Date timeExpect;
	/**签到时间*/
	@Excel(name = "签到时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到时间")
    private java.util.Date time;
    @ApiModelProperty(value = "签到凭证图片")
    private String clockImg;
    @TableField(exist = false)
    private LocalDate clockTime;
	/**任务地点*/
	@Excel(name = "任务地点", width = 15)
    @ApiModelProperty(value = "任务地点")
    private java.lang.String address;
	/**任务地点坐标Lat*/
	@Excel(name = "任务地点坐标Lat", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lat")
    private java.math.BigDecimal addressLat;
    /**任务地点坐标Lng*/
    @Excel(name = "任务地点坐标Lng", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lng")
    private java.math.BigDecimal addressLng;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String description;
	/**签到状态*/
	@Excel(name = "签到状态", width = 15)
    @ApiModelProperty(value = "签到状态")
    private java.lang.Integer stateFlag;
	/**任务id*/
	@Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private java.lang.String staWorkId;
	/**任务单id*/
    @ApiModelProperty(value = "任务单id")
    private java.lang.String staOrderId;
	/**用户id*/
	@Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private java.lang.String sysUserId;
	/**删除状态*/
	@Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    
    private java.lang.Integer delFlag;
    /**签到地点*/
    @Excel(name = "签到地点", width = 15)
    @ApiModelProperty(value = "签到地点")
    private java.lang.String lcAddress;
    /**签到地点坐标Lat*/
    @Excel(name = "签到地点坐标Lat", width = 15)
    @ApiModelProperty(value = "签到地点坐标Lat")
    private java.math.BigDecimal lcAddressLat;
    /**签到地点坐标Lng*/
    @Excel(name = "签到地点坐标Lng", width = 15)
    @ApiModelProperty(value = "签到地点坐标Lng")
    private java.math.BigDecimal lcAddressLng;
    /**对应排班表id*/
    @Excel(name = "对应排班表id", width = 15)
    @ApiModelProperty(value = "对应排班表id")
    private java.lang.String staScheduleId;
    /**店编*/
    @Excel(name = "店编", width = 15)
    @TableField(exist = false)
    private String  storeNo;
    @TableField(exist = false)
    @Excel(name = "所属企业", width = 15)
    private String  companyName;
    /**任务名全称*/
    @TableField(exist = false)
    @Excel(name = "任务名全称", width = 15)
    private java.lang.String nameFull;
    /*排班日期*/
    @Excel(name = "排班日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "排班日期")
    private Date scheduleDay;
    @TableField(exist = false)
    @Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String enrollName;
    /**报名联系人电话*/
    @TableField(exist = false)
    @Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
    private java.lang.String enrollPhone;
    @ApiModelProperty(value = "公司Id")
    private String companyId;
    //公司code
    private String orgCode;
    @ApiModelProperty(value = "迟到时长")
    private Integer lengthOfTardiness;
    @ApiModelProperty(value = "早退时长")
    private Integer earlyLeaveDuration;
    @TableField(exist = false)
    private String lateDisplay;
    @TableField(exist = false)
    private String leaveEarly;
    //姓名
    @TableField(exist = false)
    private String realName;
    //身份证
    @TableField(exist = false)
    private String userIdCard;
    //班别代码
    @TableField(exist = false)
    private String shiftCode;
    //签到时间
    @TableField(exist = false)
    private String clockInTime1; //签到
    @TableField(exist = false)
    private String clockInTime2; //签退
    @TableField(exist = false)
    private String clockInTime3; //签到
    @TableField(exist = false)
    private String clockInTime4; //签退

    //要求签到时间
    @TableField(exist = false)
    private String clockInTime1Expect; //签到
    @TableField(exist = false)
    private String clockInTime2Expect; //签退
    @TableField(exist = false)
    private String clockInTime3Expect; //签到
    @TableField(exist = false)
    private String clockInTime4Expect; //签退
    //签到状态
    @TableField(exist = false)
    private Integer clockInStatus1;
    @TableField(exist = false)
    private Integer clockInStatus2;
    @TableField(exist = false)
    private Integer clockInStatus3;
    @TableField(exist = false)
    private Integer clockInStatus4;
    @Excel(name = "签到1", width = 20,type = 2)
    @TableField(exist = false)
    private String clockImg1;
    @Excel(name = "签到2", width = 20,type = 2)
    @TableField(exist = false)
    private String clockImg2;
    @Excel(name = "签到3", width = 20,type = 2)
    @TableField(exist = false)
    private String clockImg3;
    @Excel(name = "签到4", width = 20,type = 2)
    @TableField(exist = false)
    private String clockImg4;
    //单日时数
    @TableField(exist = false)
    private Double dailyWorkingHours;
    @TableField(exist = false)
    private String businessDivisionName; //事业处
    @TableField(exist = false)
    private String regionName; //区域
    @TableField(exist = false)
    private Integer status;
}
