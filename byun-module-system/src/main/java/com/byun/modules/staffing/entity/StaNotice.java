package com.byun.modules.staffing.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用于发布协议，入职须知等
 * @Author: bai
 * @Date:   2022-07-04
 * @Version: V1.0
 */
@Data
@TableName("sta_notice")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_notice对象", description="用于发布协议，入职须知等")
public class StaNotice implements Serializable {
    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**企业id（depart）*/
	@Excel(name = "企业id（depart）", width = 15)
    @ApiModelProperty(value = "企业id（depart）")
    private String companyId;
	/**企业名称*/
	@Excel(name = "企业名称", width = 15)
    @ApiModelProperty(value = "企业名称")
    private String companyName;
	/**上级公司id（depart）*/
	@Excel(name = "上级公司id（depart）", width = 15)
    @ApiModelProperty(value = "上级公司id（depart）")
    private String firmId;
	/**上级公司名称*/
	@Excel(name = "上级公司名称", width = 15)
    @ApiModelProperty(value = "上级公司名称")
    private String firmName;
	/**通知标题*/
	@Excel(name = "通知标题", width = 15)
    @ApiModelProperty(value = "通知标题")
    private String nameFull;
	/**通知简称*/
	@Excel(name = "通知简称", width = 15)
    @ApiModelProperty(value = "通知简称")
    private String nameNick;
	/**内容*/
	@Excel(name = "内容", width = 15)
    @ApiModelProperty(value = "内容")
    private String content;
	/**通知类型*/
	@Excel(name = "通知类型", width = 15)
    @ApiModelProperty(value = "通知类型")
    private Integer type;
	/**发布人*/
	@Excel(name = "发布人", width = 15)
    @ApiModelProperty(value = "发布人")
    private String sysUserId;
}
