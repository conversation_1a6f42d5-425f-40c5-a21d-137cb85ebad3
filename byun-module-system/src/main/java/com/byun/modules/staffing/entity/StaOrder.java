package com.byun.modules.staffing.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.byun.common.aspect.annotation.Dict;
import com.byun.common.constant.CommonConstant;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 任务单
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@ApiModel(value="sta_order对象", description="任务单")
@Data
@TableName("sta_order")
public class StaOrder implements Serializable {
    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**订单编码*/
	@Excel(name = "订单编码", width = 15)
    @ApiModelProperty(value = "订单编码")
    private java.lang.String orderCode;
	/**下单时间*/
	@Excel(name = "下单时间", width = 15)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下单时间")
    private java.util.Date orderDate;
    /**简历过期时间默认3天*/
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "简历过期时间默认3天", width = 15)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "简历过期时间")
    private Date expirationTime;
	/**任务简称*/
	@Excel(name = "任务简称", width = 15)
    @ApiModelProperty(value = "任务简称")
    private java.lang.String workName;
	/**任务id*/
	@Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private java.lang.String staWorkId;
	/**任务状态*/
	@Excel(name = "任务状态", width = 15)
    @ApiModelProperty(value = "任务状态")
    private java.lang.Integer stateFlag;
	/**用户id*/
	@Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private java.lang.String sysUserId;
	/**报名类型(1自己2他人)*/
	@Excel(name = "报名类型(1自己2他人)", width = 15)
    @ApiModelProperty(value = "报名类型(1自己2他人)")
    private java.lang.Integer enrollType;
	/**用户灵活用工名片*/
	@Excel(name = "用户灵活用工名片", width = 15)
    @ApiModelProperty(value = "用户灵活用工名片")
    private java.lang.String staUserInfoId;
	/**推荐人_id*/
	@Excel(name = "推荐人_id", width = 15)
    @ApiModelProperty(value = "推荐人_id")
    private java.lang.String recommenderId;
	/**推荐人名称*/
	@Excel(name = "推荐人名称", width = 15)
    @ApiModelProperty(value = "推荐人名称")
    private java.lang.String recommenderName;
	/**删除状态*/
	@Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    private java.lang.Integer delFlag;
    //推荐人公司id
    private String  recommenderDeptId;
    //推荐人公司名称
    private String  recommenderDeptName;
    //推荐人公司code
    private String  recommenderDeptOrgCode;
    /**门店名称*/
    @Excel(name = "门店名称", width = 15)
    @ApiModelProperty(value = "门店名称")
    private java.lang.String companyName;
    /**报道时间*/
    @Excel(name = "报道时间", width = 15, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "报道时间")
    private java.util.Date reportDate;
    /**任务完成时间*/
    @Excel(name = "用工结束日期", width = 15, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "用工结束日期")
    private java.util.Date endDate;
    @Excel(name = "地址名称", width = 15)
    @ApiModelProperty(value = "地址名称")
    private java.lang.String addressName;
    /**任务地点坐标Lat*/
    @Excel(name = "任务地点坐标Lat", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lat")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal addressLat;
    /**任务地点坐标Lng*/
    @Excel(name = "任务地点坐标Lng", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lng")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal addressLng;
    /**任务种类（1兼职2专职）*/
    @Excel(name = "任务种类（1兼职2专职）", width = 15)
    @ApiModelProperty(value = "任务种类（1兼职2专职）")
    private java.lang.Integer workClass;
    /**是否点名*/
    @Excel(name = "是否点名", width = 15)
    @ApiModelProperty(value = "是否点名")
    private java.lang.Integer rcStateFlag;
    /**是否需要签到*/
    @Excel(name = "是否需要签到", width = 15)
    @ApiModelProperty(value = "是否需要签到")
    private java.lang.Integer lcStateFlag;
    /**任务名全称*/
    @Excel(name = "任务名全称", width = 15)
    @ApiModelProperty(value = "任务名全称")
    private java.lang.String workNameFull;
    /**姓名*/
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String enrollName;
    /**身份证*/
    @Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    private java.lang.String enrollIdCard;
    /**身份证是否验证*/
    @Excel(name = "身份证是否验证", width = 15)
    @ApiModelProperty(value = "身份证是否验证")
    private java.lang.Integer enrollIdCardVeri;
    /**手机*/
    @Excel(name = "手机", width = 15)
    @ApiModelProperty(value = "手机")
    private java.lang.String enrollPhone;
    /**是否短信验证*/
    @Excel(name = "是否短信验证", width = 15)
    @ApiModelProperty(value = "是否短信验证")
    private java.lang.Integer enrollPhoneVeri;
    /**性别 男1 女2*/
    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private java.lang.Integer enrollSex;
    /**年龄*/
    @Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private java.lang.Integer enrollAge;
    /**用户报名信息id*/
    @Excel(name = "用户报名信息id", width = 15)
    @ApiModelProperty(value = "用户报名信息id")
    private java.lang.String staEnrollInfo;
    /**是否点名*/
    @Excel(name = "是否点名", width = 15)
    @ApiModelProperty(value = "是否点名")
    private java.lang.Integer rollCallFlag;
    /**点名时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "点名时间")
    private java.util.Date rollCallTime;
    /**实际用户id*/
    @Excel(name = "实际用户id", width = 15)
    @ApiModelProperty(value = "实际用户id")
    private java.lang.String realUserId;
    /**报名联系人*/
    @Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String reportLiaison;
    /**报名联系人电话*/
    @Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
    private java.lang.String reportLiaisonTp;
    @Excel(name = "毕业证编号", width = 15)
    @ApiModelProperty(value = "毕业证编号")
    private java.lang.String enrollIdDiploma;
    @Excel(name = "健康证编号", width = 15)
    @ApiModelProperty(value = "健康证编号")
    private java.lang.String enrollIdHealth;
    @Excel(name = "一寸相片", width = 15)
    @ApiModelProperty(value = "一寸相片")
    private java.lang.String enrollMyPhoto;
    /**入职须知id*/
    @Excel(name = "入职须知id", width = 15)
    @ApiModelProperty(value = "入职须知id")
    private java.lang.String staNoticeId;
    /**入职须知阅读标记 0未读 1已读*/
    @Excel(name = "入职须知阅读标记 ", width = 15)
    @ApiModelProperty(value = "入职须知阅读标记 ")
    private java.lang.Integer noticeReadFlag;
    @ApiModelProperty(value = "健康码图片")
    private java.lang.String enrollHealthPhoto;
    /**
     * 身份证有效期
     */
    @ApiModelProperty(value = "身份证有效期")
    private String enrollIdCardStartDate;
    /**
     * 身份证有效期
     */
    @ApiModelProperty(value = "身份证有效期")
    private String enrollIdCardEndDate;
    /**
     * 健康证有效期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "健康证有效期")
    private java.util.Date enrollIdHealthStartDate;
    /**
     * 健康证有效期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "健康证有效期")
    private java.util.Date enrollIdHealthEndDate;

    @ApiModelProperty(value = "身份证地址")
    private String enrollIdCardAddress;
    @ApiModelProperty(value = "现住地址")
    private String enrollAddress;
    /**现住址坐标*/
    @ApiModelProperty(value = "现住址坐标")
    private java.math.BigDecimal enrollAddressLat;
    /**现住址坐标*/
    @ApiModelProperty(value = "现住址坐标")
    private java.math.BigDecimal enrollAddressLng;
    @ApiModelProperty(value = "现住地址名")
    private String enrollAddressName;
    @Dict(dicCode = "admin_degree")
    @ApiModelProperty(value = "文化程度")
    private String enrollDegree;
    @ApiModelProperty(value = "健康证办理单位")
    private String enrollIdHealthCompany;
    @ApiModelProperty(value = "紧急联系人手机")
    private java.lang.String enrollContactNumber;
    @ApiModelProperty(value = "紧急联系人")
    private String enrollContact;
    /**所属部门code*/
    @ApiModelProperty(value = "所属部门code")
    private java.lang.String sysOrgCode;
    /**门店id（depart）*/
    @Excel(name = "门店id（depart）", width = 15)
    @ApiModelProperty(value = "门店id（depart）")
    private java.lang.String companyId;
    /**公司id（depart）*/
    @Excel(name = "公司id（depart）", width = 15)
    @ApiModelProperty(value = "公司id（depart）")
    private java.lang.String firmId;
    /**门店名称*/
    @Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private java.lang.String firmName;
    @Excel(name = "身份证是否在有效期内", width = 15)
    @ApiModelProperty(value = "身份证是否在有效期内")
    private java.lang.Integer enrollIdCardValidityDate;
    @Excel(name = "是否有毕业证", width = 15)
    @ApiModelProperty(value = "是否有毕业证")
    private java.lang.Integer enrollIdDiplomaValidity;
    @Excel(name = "是否有健康证", width = 15)
    @ApiModelProperty(value = "是否有健康证")
    private java.lang.Integer enrollIdHealthValidity;
    @Excel(name = "健康证是否在有效期内", width = 15)
    @ApiModelProperty(value = "健康证是否在有效期内")
    private java.lang.Integer enrollIdHealthValidityDate;
    @Excel(name = "店编", width = 15)
    @ApiModelProperty(value = "甲方店编")
    private String StoreNo;
    /**
     * 任务完成凭证类型
     */
    private String taskVoucherType;
    /**
     * 任务完成凭证(图片或视频) 多张图片,分割
     */
    private String taskVoucher;
    /**
     * 任务凭证
     * 0未上传  1 已上传  2 审核通过  3 审核未通过
     */
    @ApiModelProperty(value = "任务标识")
    @TableField(value = "mission_flag")
    private Integer missionFlag;
    /**
     * 是否打分 0 -  1
     */
    private Integer scoreStatus;
    /**
     * 任务评分 1 - 5分
     */
    private Integer taskScore;
    /**
     * 任务凭证 审核未通过 详细信息
     */
    private String taskRefuseContext;
    //结算标准
    private BigDecimal hourlySalary;
    //扣款id
    private String staTaskDeductionId;
    //任务时数
    private Double taskManHour;
    //任务金额
    private BigDecimal taskAmount;
    /**离职店铺**/
    @Excel(name = "离职店铺", width = 15)
    private String dimissionShop;
    @Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    private Date timeOfResignation;
    @TableField(exist = false)
    private String stoNe;//店编
    @TableField(exist = false)
    private String deptName;//部门
    @TableField(exist = false)
    private String jobName;//工作名称
    @TableField(exist = false)
    private String userName;//名字
    @TableField(exist = false)
    private String idCard;//身份证
    @TableField(exist = false)
    private Integer age;//年龄
    @TableField(exist = false)
    private String sex;//性别
    //单日时数
    @TableField(exist = false)
    private Double dailyWorkingHours;
    //当前工作时数
    @TableField(exist = false)
    private Double workingHours;
    @TableField(exist = false)
    private Double totalWorkingHours;

    //本周工作时长
    @TableField(exist = false)
    private Double weeklyWorkingHours;
    //本月工作时长
    @TableField(exist = false)
    private Double monthlyWorkingHours;
    //年时数
    @TableField(exist = false)
    private Double annualWorkHours;
    //上半月时数
    @TableField(exist = false)
    private Double monthStart;
    //下半月时数
    @TableField(exist = false)
    private Double monthEnd;
    //月时数
    @TableField(exist = false)
    private Double month;
    //筛选时数
    @TableField(exist = false)
    private Double customizeTime;
    @TableField(exist = false)
    private String presentStatus;// 当下状态
    @TableField(exist = false)
    @Excel(name = "出生日期", width = 15)
    private String dateOfBirth;
    /**
     * 入职日期 第一次签到记录中的时间填充入职日期
     */
    @Excel(name = "开始日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "开始日期")
    @TableField(value = "entry_date",updateStrategy = FieldStrategy.IGNORED)
    private Date entryDate;
    /**
     * 用工结束时间
     */
    @Excel(name = "用工结束时间", width = 15, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "用工结束时间")
    @TableField(value = "work_end_date")
    private Date workEndDate;
    //扣款金额
    private BigDecimal staTaskDeductionMoney;
    //款款金额 生成结算单使用
    @TableField(exist = false)
    private BigDecimal taskDeductionMoney;
    //扣款备注
    private String staTaskDeductionRemark;
    //0任务完成申请  1取消任务完成申请
    @TableField(exist = false)
    private java.lang.String status;
    @TableField(exist = false)
    private String avatar;
    /**
     * 区域id
     */
    private String regionId;
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 事业处id
     */
    private String businessDivisionId;
    /**
     * 事业处名称
     */
    private String businessDivisionName;
    /*----------------------异动-----------------------------*/
    private String newShopId;
    private String newShopName;
    private String newWorkId;
    private String newWorkName;
    @TableField (updateStrategy = FieldStrategy.IGNORED)
    private Date variationTime;
    private Integer variationFlag;
    /**
     * 结算单生成状态 0 未生成  1 已生成
     */
//    private int payStatusFlag;
    /**
     * 司龄
     */
    @TableField(exist = false)
    private int companyServiceDays;
    /**
     * 人员类别
     */
    @TableField(exist = false)
    private String staUserType;
    /**
     * 身高
     */
    @TableField(exist = false)
    private String height;
    @TableField(exist = false)
    private String userIdCard;
    /**
     * 体重
     */
    @TableField(exist = false)
    private String weight;
    @TableField(exist = false)
    private BigDecimal salary;
    @TableField(exist = false)
    private BigDecimal totalSalary;
    private String staTaskDateId;
    //结算批次号
    private String batchBizId;
    @TableField(exist = false)
    private String taskEndDateAndTime;
    @TableField(exist = false)
    private String taskStartDate;
    @TableField(exist = false)
    private String taskEndDate;
    @TableField(exist = false)
    private String shiftCode;//班别代码
    @TableField(exist = false)
    private String subtractStartDate;
    @TableField(exist = false)
    private String subtractEndDate;
    @TableField(exist = false)
    private List<StaSchedule> staScheduleList;
    @TableField(exist = false)
    private List<StaLocationClock> staLocationClocks;
    @TableField(exist = false)
    private String taskStartDateAndEndDateStr;
    public StaOrder(){}
    public StaOrder(StaWork staWork, StaEnrollInfo staEnrollInfo){
	    this.workName = staWork.getNameNick();
	    this.staWorkId = staWork.getId();
	    this.companyName = staWork.getCompanyName();
	    this.workNameFull=staWork.getNameFull();
	    this.reportDate = staWork.getReportDate();
	    this.addressLat = staWork.getAddressLat();
	    this.addressLng = staWork.getAddressLng();
	    this.addressName = staWork.getAddressName();
	    this.rcStateFlag = staWork.getRcNeedFlag();
	    this.lcStateFlag = staWork.getLcNeedFlag();
	    this.workClass = staWork.getWorkClass();
        this.reportLiaison = staWork.getLiaison();
        this.reportLiaisonTp = staWork.getLiaisonTp();
        this.staNoticeId = staWork.getStaNoticeId();
        this.sysOrgCode = staWork.getSysOrgCode();
        this.companyId = staWork.getCompanyId();
        this.firmId = staWork.getFirmId();
        this.firmName = staWork.getFirmName();
        this.noticeReadFlag = 0;
	    this.enrollName = staEnrollInfo.getName();
	    this.enrollIdCard = staEnrollInfo.getIdCard();
	    this.enrollIdCardVeri = staEnrollInfo.getIdCardVeri();
	    this.enrollPhone = staEnrollInfo.getPhone();
	    this.enrollPhoneVeri = staEnrollInfo.getPhoneVeri();
	    this.enrollSex = staEnrollInfo.getSex();
	    this.enrollAge =staEnrollInfo.getAge();
        this.enrollIdDiploma=staEnrollInfo.getIdDiploma();
        this.enrollIdHealth=staEnrollInfo.getIdHealth();
        this.enrollMyPhoto=staEnrollInfo.getMyPhoto();
        this.enrollHealthPhoto=staEnrollInfo.getHealthPhoto();
        this.enrollIdCardStartDate=staEnrollInfo.getIdCardStartDate();
        this.enrollIdCardEndDate=staEnrollInfo.getIdCardEndDate();
        this.enrollIdHealthStartDate=staEnrollInfo.getIdHealthStartDate();
        this.enrollIdHealthEndDate=staEnrollInfo.getIdHealthEndDate();
        this.enrollIdCardAddress=staEnrollInfo.getIdCardAddress();
        this.enrollAddressLat=staEnrollInfo.getAddressLat();
        this.enrollAddressLng=staEnrollInfo.getAddressLng();
        this.enrollAddressName=staEnrollInfo.getAddressName();
        this.enrollAddress=staEnrollInfo.getAddress();
        this.enrollDegree=staEnrollInfo.getDegree();
        this.enrollIdHealthCompany=staEnrollInfo.getIdHealthCompany();
        this.enrollContactNumber=staEnrollInfo.getContactNumber();
        this.enrollContact=staEnrollInfo.getContact();
        this.enrollIdCardValidityDate=staEnrollInfo.getIdCardValidityDate();
        this.enrollIdDiplomaValidity=staEnrollInfo.getIdDiplomaValidity();
        this.enrollIdHealthValidity=staEnrollInfo.getIdHealthValidity();
        this.enrollIdHealthValidityDate=staEnrollInfo.getIdHealthValidityDate();
	    this.staEnrollInfo = staEnrollInfo.getId();
	    this.sysUserId = staEnrollInfo.getSysUserId();
        if(CommonConstant.USER_REL_0.equals(staEnrollInfo.getUserRel())){
            this.enrollType=CommonConstant.ENROLL_TYPE_1;//自己
            this.realUserId = staEnrollInfo.getSysUserId();
        }else{
            this.enrollType=CommonConstant.ENROLL_TYPE_2;//他人
        }
    }
}
