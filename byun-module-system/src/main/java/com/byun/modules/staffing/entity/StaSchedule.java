package com.byun.modules.staffing.entity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.models.auth.In;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
 * @Description: 排班表
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Data
@TableName("sta_schedule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_schedule对象", description="排班表")
public class StaSchedule implements Serializable {
    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**订单id*/
	@Excel(name = "订单id", width = 15)
    @ApiModelProperty(value = "订单id")
    private String staOrderId;
    /**任务id*/
    @Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private String staWorkId;
	/**排班日期*/
	@Excel(name = "排班日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "排班日期")
    private Date scheduleDay;
	/**排班编号*/
	@Excel(name = "排班编号", width = 15)
    @ApiModelProperty(value = "排班编号")
    private String code;
	/**班次开始时间*/
	@Excel(name = "班次开始时间", width = 20, format = "HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @ApiModelProperty(value = "班次开始时间")
    private Date startTime;
	/**班次结束时间*/
	@Excel(name = "班次结束时间", width = 20, format = "HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @ApiModelProperty(value = "班次结束时间")
    private Date endTime;
	/**删除标记*/
	@Excel(name = "删除标记", width = 15)
    @ApiModelProperty(value = "删除标记")
    private Integer delFlag;
    /**签到状态标记*/
    @Excel(name = "签到状态标记", width = 15)
    @ApiModelProperty(value = "签到状态标记")
    private Integer lcStateFlag;
    /**店编*/
    private String  storeNo;
    /*所属企业*/
    @Excel(name = "所属企业", width = 15)
    private String  companyName;
    /**部门code*/
    private String  orgCode;
    /**任务名全称*/
    @TableField(exist = false)
    @Excel(name = "任务名全称", width = 15)
    private java.lang.String nameFull;
    //事业处
    private String businessDivisionName;
    //区域
    private String regionName;
    @TableField(exist = false)
    private Integer clockStateFlag;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @TableField(exist = false)
    private Date startDay;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @TableField(exist = false)
    private Date endDay;
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startClock;
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endClock;
    private String realName;
    private String userIdCard;
    private String sysUserId;
    /**
     * 签到地点
     */
    @TableField(exist = false)
    private String address;
}
