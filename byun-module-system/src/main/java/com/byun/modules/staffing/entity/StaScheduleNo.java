package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description  排班编号表
 * @date : 2022-9-23 8:43
 */
@Data
@TableName("sta_schedule_no")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_schedule_no对象", description="排班编号表")
public class StaScheduleNo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**创建人*/
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**门店名称*/
   // @TableField(exist = false)
//    @Excel(name = "门店名称", width = 15)
//    @ApiModelProperty(value = "门店名称")
//    private java.lang.String firmName;
    /**班别代码*/
    @TableField(value = "code", updateStrategy = FieldStrategy.NEVER)
    @Excel(name = "班别代码", width = 15)
    @ApiModelProperty(value = "班别代码")
    private String code;
    /**班次开始时间*/
    @Excel(name = "班次开始时间", width = 20, format = "HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @ApiModelProperty(value = "班次开始时间")
    private Date startTime;
    /**班次结束时间*/
    @Excel(name = "班次结束时间", width = 20, format = "HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @ApiModelProperty(value = "班次结束时间")
    private Date endTime;


    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    @TableField(exist = false)
    @ApiModelProperty(value = "订单id")
    private String staOrderId;
    @ApiModelProperty(value = "门店id")
    private java.lang.String companyId;
    /**
     * 部门code
     */
    private String orgCode;
    //时长
    @Excel(name = "时长",width = 15)
    private Double workTime;
    @Excel(name = "班别描述", width = 20)
    @ApiModelProperty(value = "班别描述")
    private String description;
    /**删除标记*/
    @ApiModelProperty(value = "删除标记")
    @TableLogic(value = "0",delval = "1")
    private Integer delFlag;

    //餐费 0 -1
    private Integer isMealMoney;
    //企业名称选中数据
    @TableField(exist = false)
    private String selecteddeparts;

}
