package com.byun.modules.staffing.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 排班模板选项
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@ApiModel(value="sta_schedule_template_item对象", description="排班模板选项")
@Data
@TableName("sta_schedule_template_item")
public class StaScheduleTemplateItem implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**排班模板*/
    @ApiModelProperty(value = "排班模板")
    private String staScheduleTemplateId;
	/**排班日期*/
	@Excel(name = "排班日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "排班日期")
    private Date scheduleDay;
	/**排班编号*/
	@Excel(name = "排班编号", width = 15)
    @ApiModelProperty(value = "排班编号")
    private String code;
	/**班次开始时间*/
	@Excel(name = "班次开始时间", width = 20, format = "HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @ApiModelProperty(value = "班次开始时间")
    private Date startTime;
	/**班次结束时间*/
	@Excel(name = "班次结束时间", width = 20, format = "HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @ApiModelProperty(value = "班次结束时间")
    private Date endTime;
}
