package com.byun.modules.staffing.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Email;
import java.util.Date;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 满足条件待发送邮件实体
 * @date : 2023-1-4 15:41
 */
@ApiModel(value="sta_send_email", description="邮箱")
@Data
@TableName("sta_send_email")
public class StaSendEmail {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    @ApiModelProperty(value = "接收人ID")
    private String userId;
    @ApiModelProperty(value = "订单ID")
    private String orderId;
    @ApiModelProperty(value = "任务名称")
    private String workName;
    @ApiModelProperty(value = "报道人姓名")
    private String enrollName;
    @ApiModelProperty(value = "报道地址名称")
    private String addressName;
    @ApiModelProperty(value = "报道联系人姓名")
    private String reportLiaisonTp;
    @ApiModelProperty(value = "报道联系人电话")
    private String reportLiaison;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入职时间")
    private Date workStartDate;
    @ApiModelProperty(value = "邮箱")
    @Email
    private String email;
    @ApiModelProperty(value = "发送状态 0 未发送 1 已发送")
    private Integer sendStatus;
}
