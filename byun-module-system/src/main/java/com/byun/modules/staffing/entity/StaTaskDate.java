package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 任务周期和任务时间
 * @date : 2024-6-18 18:38
 */
@Data
@TableName("sta_task_date")
public class StaTaskDate {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    private Date createDate;//创建时间
    private String createById;//创建人Id
    private String staWorkId;//任务id
    private String staOrderId;//任务单id
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date manTaskStartDate;//总服务开始时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date manTaskEndDate;//总服务结束时间
    private Date taskStartDate;//服务开始日期
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskEndDate;//服务结束日期
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    private Date taskStartTime;//服务开始时间
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    private Date taskEndTime;//服务结束时间2
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @TableField(updateStrategy = FieldStrategy.IGNORED)//忽略空值判断
    private Date taskStartTime2;//服务开始时间2
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    @TableField(updateStrategy = FieldStrategy.IGNORED) //忽略空值判断
    private Date taskEndTime2;//服务结束时间
    private String shiftCode;//班别代码
    private int expectNum;//需要人数
    private int applyNum;//申请人数
    private int adoptNum; //通过人数
    private int remainingNum;//剩余人数
    private int delFlag;//删除状态
}
