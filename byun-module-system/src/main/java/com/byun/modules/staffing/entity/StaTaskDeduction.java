package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 扣款表
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-5-13 17:33
 */
@Data
@TableName("sta_task_deduction")
public class StaTaskDeduction {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 扣款发布人
     */
    private String createBy;
    /**
     * 扣款人id
     */
    private String createById;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 修改人
     */
    private String updateBy;
    /**
     * 任务单结算标准
     */
    private BigDecimal taskSalary;
    /**
     * 迟到扣款
     */
    private BigDecimal lateDeduction;
    /***
     * 早退扣款
     */
    private BigDecimal earlyLeaveDeduction;
    /**
     * 其他扣款
     */
    private BigDecimal otherDeduction;
    /**
     * 扣款备注
     */
    private String emarks;
    /**
     * 扣款任务单
     */
    private String staOrderId;
    /**
     * 扣款订单code
     */
    private  String  staOrderCode;
    /**
     * 扣款任务
     */
    private String staWorkId;
    /**
     * 扣款任务名称
     */
    private String workNameFull;
    /**
     * 扣款日期
     */
    //private Date deductionDate;
    /**
     * 被扣款人
     */
    private String deductionUserId;
    private String deductionUserName;
    private String companyName; //门店
    private String companyId; //门店id
    private String businessDivisionName;//事业处
    private String storeRegionName;//区域
    private String storeNo;//店编
}
