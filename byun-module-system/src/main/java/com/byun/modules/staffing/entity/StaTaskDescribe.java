package com.byun.modules.staffing.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 任务描述 TODO
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-5-11 11:39
 */
@Data
@TableName("sta_task_describe")
public class StaTaskDescribe {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;

}
