package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 服务周期内时间表
 * @date : 2024-6-18 19:06
 */
@TableName("sta_task_time")
public class StaTaskTime {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    private Date createDate;//创建时间
    private String createById;//创建人Id
    private String staWorkId; //任务id
    private String staTaskDateId;//服务周期id
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    private Date taskStartTime;//服务开始时间
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    private Date taskEndTime;//服务结束时间
    private String shiftCode;//班别代码
    private int delFlag;//删除状态
}
