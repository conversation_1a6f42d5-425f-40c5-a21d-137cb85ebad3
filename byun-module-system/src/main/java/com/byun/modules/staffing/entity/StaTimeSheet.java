package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 用户时数
 * @date : 2023-10-27 14:57
 */
@lombok.Data
@TableName("sta_time_sheet")
public class StaTimeSheet {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    //门店Id
    private String firmId;
    //部门code
    private String orgCode;
    //工作id
    private String staWorkId;
    //订单id
    private String staOrderId;
    //排班id
    private String staScheduleId;
    @Excel(name = "排班日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "排班日期")
    private Date scheduleDay;
    //用户id
    private String sysUserId;
    //要求签到时间
    private Date timeExpectOne;
    private Date timeExpectTow;
    private Date timeExpectThree;
    private Date timeExpectFour;
    //签到时间
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date clockOne;
    //签到状态
    private Integer clockOneStatus;
    //签到2
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date clockTow;
    private Integer clockTowStatus;
    //签到3
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date clockThree;
    private Integer clockThreeStatus;
    //签到4
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date clockFour;
    private Integer clockFourStatus;
    //迟到时长
    private String lateDurationOne;
    private String lateDurationTow;
    //早退时长
    private String earlyDurationOne;
    private String earlyDurationTow;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    //删除标识
    private Integer delFlag;

}
