package com.byun.modules.staffing.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 跟进用户记录
 * @Author: bai
 * @Date:   2021-12-01
 * @Version: V1.0
 */
@Data
@TableName("sta_user_follow_up")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_user_follow_up对象", description="跟进用户记录")
public class StaUserFollowUp implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**被跟进的用户id*/
	@Excel(name = "被跟进的用户id", width = 15)
    @ApiModelProperty(value = "被跟进的用户id")
    private java.lang.String sysUserId;
	/**被跟进的用户姓名*/
	@Excel(name = "被跟进的用户姓名", width = 15)
    @ApiModelProperty(value = "被跟进的用户姓名")
    private java.lang.String sysUserName;
	/**被跟进的用户电话*/
	@Excel(name = "被跟进的用户电话", width = 15)
    @ApiModelProperty(value = "被跟进的用户电话")
    private java.lang.String sysUserPhone;
	/**进行跟进人的id*/
	@Excel(name = "进行跟进人的id", width = 15)
    @ApiModelProperty(value = "进行跟进人的id")
    private java.lang.String followUserId;
	/**进行跟进人*/
	@Excel(name = "进行跟进人", width = 15)
    @ApiModelProperty(value = "进行跟进人")
    private java.lang.String followUserName;
	/**期望任务城市*/
	@Excel(name = "期望任务城市", width = 15)
    @ApiModelProperty(value = "期望任务城市")
    private java.lang.String needCity;
	/**期望任务*/
	@Excel(name = "期望任务", width = 15)
    @ApiModelProperty(value = "期望任务")
    private java.lang.String needWork;
	/**期望薪资*/
	@Excel(name = "期望薪资", width = 15)
    @ApiModelProperty(value = "期望薪资")
    private java.lang.String needSalary;
	/**求职紧急程度*/
	@Excel(name = "求职紧急程度", width = 15)
    @ApiModelProperty(value = "求职紧急程度")
    private java.lang.Integer needLevelType;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String description;
    /**跟进时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "跟进时间")
    private java.util.Date followTime;
}
