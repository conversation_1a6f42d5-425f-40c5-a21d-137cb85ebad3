package com.byun.modules.staffing.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户记录表
 * @Author: bai
 * @Date:   2021-12-01
 * @Version: V1.0
 */
@Data
@TableName("sta_user_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_user_record对象", description="用户记录表")
public class StaUserRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**用户id*/
	@Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private java.lang.String sysUserId;
	/**记录类型1、浏览记录2、任职记录3、收藏记录*/
	@Excel(name = "记录类型1、浏览记录2、任职记录3、收藏记录", width = 15)
    @ApiModelProperty(value = "记录类型1、浏览记录2、任职记录3、收藏记录")
    private java.lang.Integer recordType;
	/**记录内容*/
	@Excel(name = "记录内容", width = 15)
    @ApiModelProperty(value = "记录内容")
    private java.lang.String recordContent;
	/**跳转页面类型*/
	@Excel(name = "跳转页面类型", width = 15)
    @ApiModelProperty(value = "跳转页面类型")
    private java.lang.Integer jumpType;
	/**跳转用到的id*/
	@Excel(name = "跳转用到的id", width = 15)
    @ApiModelProperty(value = "跳转用到的id")
    private java.lang.String jumpId;
	/**删除状态(0-正常，1-已删除)*/
	@Excel(name = "删除状态(0-正常，1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常，1-已删除)")
    private java.lang.Integer delFlag;
    /**记录时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "记录时间")
    private java.util.Date recordTime;

    public StaUserRecord(){

    }
    public StaUserRecord(StaUserRecord staUserRecord){
        this.id = staUserRecord.getId();
        this.createBy = staUserRecord.getCreateBy();
        this.createTime = staUserRecord.getCreateTime();
        this.updateBy = staUserRecord.getUpdateBy();
        this.updateTime = staUserRecord.getUpdateTime();
        this.sysUserId = staUserRecord.getSysUserId();
        this.recordType = staUserRecord.getRecordType();
        this.recordContent = staUserRecord.getRecordContent();
        this.jumpType = staUserRecord.getJumpType();
        this.jumpId = staUserRecord.getJumpId();
        this.delFlag = staUserRecord.getDelFlag();
        this.recordTime = staUserRecord.getRecordTime();
    }
}
