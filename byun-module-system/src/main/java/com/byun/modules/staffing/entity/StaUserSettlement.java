package com.byun.modules.staffing.entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 结算表
 * @date : 2024-6-22 9:06
 */
@TableName("sta_user_settlement")
@Data
public class StaUserSettlement {
    private static final long serialVersionUID = 1L;
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @TableField(exist = false)
    private List<String> ids;
    private String sysUserId;//用户id
    @Excel(name = "事业处",width = 25,height = 20)
    private String businessDivisionName;//事业处
    private String businessDivisionId;//事业处id1
    @Excel(name = "区域",width = 25)
    private String regionName;//区域
    private String regionId;//区域id
    @Excel(name = "店编",width = 20)
    private String storeNo;
    @Excel(name = "店别",width = 20)
    private String companyName;
    private String companyId;//门店id
    @Excel(name = "月份")
    private String month;//月份
    @Excel(name = "姓名",width = 20)
    private String userName;
    @Excel(name = "身份证",width = 30)
    private String idCard;
    @Excel(name = "手机",width = 20)
    private String mobile;
    @Excel(name = "任务开始日期",width = 30)
    private String taskStartDate;
    @Excel(name = "任务结束日期",width = 30)
    private String taskEndDate;
    @Excel(name = "任务详情",width = 25)
    private String taskDetail;
    @Excel(name = "时薪",width = 15)
    private BigDecimal hourlySalary;
    @Excel(name = "时数",width = 15)
    private Double taskManHour;//工时
    @Excel(name = "结算类型(1银行卡2支付宝)",width = 25)
    private String accountType;//1银行卡 2支付宝
    @Excel(name = "银行名(结算类型银行卡填写银行名、支付宝填写支付宝)",width = 30)
    private String bankName;//银行名称
    @Excel(name = "结算账户",width = 15)
    private String bankNo;
    @Excel(name = "工资总额",width = 15)
    private BigDecimal totalSalary;
    @Excel(name = "应发总额",width = 15)
    private BigDecimal payableAmount;
    @Excel(name = "扣款总额",width = 15)
    private BigDecimal taskDeductionMoney;
    @Excel(name = "实发总额",width = 15)
    private BigDecimal actualAmountPaidCount;
    //@Excel(name = "提现状态",width = 20)
    private String status;//0待提现，1提现成功，2提现中，99提现失败
    private String bizId;//客户提供的唯一ID 默认长度50
    private String batchBizId;//批次号默认长度限制为50
    private String staOrderIds;//任务单id集合
    private String taskNo;//签约编号
    private String orgCode;//所属code
    private Date createTime;//创建时间
    private String createBy;//创建人
}
