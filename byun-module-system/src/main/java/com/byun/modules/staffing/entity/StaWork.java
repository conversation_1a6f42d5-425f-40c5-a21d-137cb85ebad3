package com.byun.modules.staffing.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.byun.modules.staffing.vo.StaTaskDateVO;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 任务列表
 * @Author: baiyun
 * @Date: 2021-11-14
 * @Version: V1.0
 */
@ApiModel(value = "sta_work对象", description = "任务列表")
@Data
@TableName("sta_work")
public class StaWork implements Serializable, Comparable<StaWork> {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 所属部门code
     */
    @ApiModelProperty(value = "所属部门code")
    private java.lang.String sysOrgCode;
    /**
     * 门店id（depart）
     */
    @Excel(name = "门店id（depart）", width = 15)
    @ApiModelProperty(value = "门店id（depart）")
    private java.lang.String companyId;
    /**
     * 门店名称
     */
    @Excel(name = "门店名称", width = 15)
    @ApiModelProperty(value = "门店名称")
    private java.lang.String companyName;
    /**
     * 门店区域
     */
    @Excel(name = "门店区域", width = 15)
    @ApiModelProperty(value = "门店区域")
    private String storeRegionName;
    /**
     * 门店区域id
     */
    private String storeRegionId;
    /**
     * 门店事业处
     */
    @Excel(name = "门店事业处", width = 15)
    @ApiModelProperty(value = "门店事业处")
    private String storeBusinessDivisionName;
    /**
     * 门店事业处id
     */
    private String storeBusinessDivisionId;
    /**
     * 公司id（depart）
     */
    @Excel(name = "公司id（depart）", width = 15)
    @ApiModelProperty(value = "公司id（depart）")
    private java.lang.String firmId;
    @ApiModelProperty(value = "选中部门id")
    @TableField(exist = false)
    private String selecteddeparts;
    /**
     * 门店名称
     */
    @Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private java.lang.String firmName;
    /**
     * 任务名全称
     */
    @Excel(name = "任务名全称", width = 15)
    @ApiModelProperty(value = "任务名全称")
    private java.lang.String nameFull;
    /**
     * 任务名简称
     */
    @Excel(name = "任务名简称", width = 15)
    @ApiModelProperty(value = "任务名简称")
    private java.lang.String nameNick;
    /**
     * 任务描述
     */
    @Excel(name = "任务描述", width = 15)
    @ApiModelProperty(value = "任务描述")
    private java.lang.String content;

    /**
     * 报道时间
     */
    @Excel(name = "报道时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "报道时间")
    private java.util.Date reportDate;
    /**
     * 任务开始时间
     */
    @Excel(name = "任务开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd ")
    @ApiModelProperty(value = "任务开始时间")
    private java.util.Date workDateStart;
    /**
     * 任务结束时间
     */
    @Excel(name = "任务结束时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "任务结束时间")
    private java.util.Date workDateEnd;
    /**
     * 任务地址
     */
    @Excel(name = "任务地址", width = 15)
    @ApiModelProperty(value = "任务地址")
    private java.lang.String address;
    /**
     * 任务地点坐标Lat
     */
    @Excel(name = "任务地点坐标Lat", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lat")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal addressLat;
    /**
     * 任务地点坐标Lng
     */
    @Excel(name = "任务地点坐标Lng", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lng")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal addressLng;
    /**
     * 所在区域编码
     */
    @Excel(name = "所在区域编码", width = 15)
    @ApiModelProperty(value = "所在区域编码")
    private java.lang.String regionCode;
    /**
     * 要求最小年龄段
     */
    @Excel(name = "要求最小年龄段", width = 15)
    @ApiModelProperty(value = "要求最小年龄段")
    private java.lang.Integer needAgeMin;
    /**
     * 要求最大年龄段
     */
    @Excel(name = "要求最大年龄段", width = 15)
    @ApiModelProperty(value = "要求最大年龄段")
    private java.lang.Integer needAgeMax;
    /**
     * 要求性别
     */
    @Excel(name = "要求性别", width = 15)
    @ApiModelProperty(value = "要求性别")
    private java.lang.Integer needSex;
    /**
     * 要求学历
     */
    @Excel(name = "要求学历", width = 15)
    @ApiModelProperty(value = "要求学历")
    private java.lang.Integer needDegree;
    /**
     * 要求经验
     */
    @Excel(name = "要求经验", width = 15)
    @ApiModelProperty(value = "要求经验")
    private java.lang.String needExp;
    /**
     * 其他要求
     */
    @Excel(name = "其他要求", width = 15)
    @ApiModelProperty(value = "其他要求")
    private java.lang.String needOther;
    /**
     * 福利待遇
     */
    @Excel(name = "福利待遇", width = 15)
    @ApiModelProperty(value = "福利待遇")
    private java.lang.String benefit;
    /**
     * 标签code(label1,label2)
     */
    @Excel(name = "标签code(label1,label2)", width = 15)
    @ApiModelProperty(value = "标签code(label1,label2)")
    private java.lang.String labelCode;
    /**
     * 标签(label1,label2)
     */
    @Excel(name = "标签(label1,label2)", width = 15)
    @ApiModelProperty(value = "标签(label1,label2)")
    private java.lang.String labelName;
    /**
     * 工种编码组
     */
    @Excel(name = "工种编码组", width = 15)
    @ApiModelProperty(value = "工种编码组")
    private java.lang.String workTypeCode;
    /**
     * 工种名称组
     */
    @Excel(name = "工种名称组", width = 15)
    @ApiModelProperty(value = "工种名称组")
    private java.lang.String workTypeName;
    /**
     * 展示开始时间
     */
    @Excel(name = "展示开始时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "展示开始时间")
    private java.util.Date showDateStart;
    /**
     * 展示结束时间
     */
    @Excel(name = "展示结束时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "展示结束时间")
    private java.util.Date showDateEnd;
    /**
     * 删除状态
     */
    @Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    private java.lang.Integer delFlag;
    /**
     * 任务种类（1兼职2专职）
     */
    @Excel(name = "任务种类（1兼职2专职）", width = 15)
    @ApiModelProperty(value = "任务种类（1兼职2专职）")
    private java.lang.Integer workClass;
    /**
     * 是否需要审核
     */
    @Excel(name = "是否需要审核", width = 15)
    @ApiModelProperty(value = "是否需要审核")
    private java.lang.Integer examineFlag;
    /**
     * 聚合id
     */
    @Excel(name = "聚合id", width = 15)
    @ApiModelProperty(value = "聚合id")
    private java.lang.String aggregationId;
    /**
     * 联系人
     */
    @Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String liaison;
    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
    private java.lang.String liaisonTp;
    /**
     * 集合地址
     */
    @Excel(name = "集合地址", width = 15)
    @ApiModelProperty(value = "集合地址")
    private java.lang.String addressJoin;
    /**
     * 适合人群code
     */
    @Excel(name = "适合人群code", width = 15)
    @ApiModelProperty(value = "适合人群code")
    private java.lang.String crowdCode;
    /**
     * 适合人群
     */
    @Excel(name = "适合人群", width = 15)
    @ApiModelProperty(value = "适合人群")
    private java.lang.String crowd;
    /**
     * 任务时间要求
     */
    @Excel(name = "任务时间要求", width = 15)
    @ApiModelProperty(value = "任务时间要求")
    private java.lang.String needDate;
    /**
     * 状态0未发布1完成2发布中3任务中4结算中
     */
    @Excel(name = "状态0未发布1完成2发布中3任务中4结算中", width = 15)
    @ApiModelProperty(value = "状态0未发布1完成2发布中3任务中4结算中")
    private java.lang.Integer stateFlag;
    /**
     * 是否点名
     */
    @Excel(name = "是否点名", width = 15)
    @ApiModelProperty(value = "是否点名")
    private java.lang.Integer rcNeedFlag;
    /**
     * 是否在列表展示
     */
    @Excel(name = "是否在列表展示", width = 15)
    @ApiModelProperty(value = "是否在列表展示")
    private java.lang.Integer listShow;
    /**
     * 需要人数
     */
    @Excel(name = "需要人数", width = 15)
    @ApiModelProperty(value = "需要人数")
    private java.lang.Integer expectNum;
    /**
     * 申请人数
     */
    @Excel(name = "申请人数", width = 15)
    @ApiModelProperty(value = "申请人数")
    private java.lang.Integer applyNum;
    /**
     * 通过人数
     */
    @Excel(name = "通过人数", width = 15)
    @ApiModelProperty(value = "通过人数")
    private java.lang.Integer adoptNum;
    /**
     * 所在区域
     */
    @Excel(name = "所在区域", width = 15)
    @ApiModelProperty(value = "所在区域")
    private java.lang.String region;
    /**
     * 是否需要签到
     */
    @Excel(name = "是否需要签到", width = 15)
    @ApiModelProperty(value = "是否需要签到")
    private java.lang.Integer lcNeedFlag;
    @Excel(name = "图片路径", width = 15)
    @ApiModelProperty(value = "图片路径")
    private java.lang.String imageUrl;
    @Excel(name = "地址名称", width = 15)
    @ApiModelProperty(value = "地址名称")
    private java.lang.String addressName;
    /**
     * 发布人id
     */
    @Excel(name = "发布人id", width = 15)
    @ApiModelProperty(value = "发布人id")
    private java.lang.String sysUserId;

    /**
     * 是否需要毕业证书编号
     */
    @Excel(name = "是否需要毕业证书编号", width = 15)
    @ApiModelProperty(value = "是否需要毕业证书编号")
    private java.lang.Integer needIdDiploma;
    /**
     * 是否需要健康证编号
     */
    @Excel(name = "是否需要健康证编号", width = 15)
    @ApiModelProperty(value = "是否需要健康证编号")
    private java.lang.Integer needIdHealth;
    //健康证
    @TableField(exist = false)
    private java.lang.String idHealth;
    /**
     * 是否需要本人免冠蓝底或红底证件照
     */
    @Excel(name = "是否需要本人免冠蓝底或红底证件照", width = 15)
    @ApiModelProperty(value = "是否需要本人免冠蓝底或红底证件照")
    private java.lang.Integer needMyPhoto;
    /**
     * 是否需要健康码
     */
    @Excel(name = "是否需要健康码", width = 15)
    @ApiModelProperty(value = "是否需要健康码")
    private java.lang.Integer needHealthPhoto;
    /**
     * 入职须知id
     */
    @Excel(name = "入职须知id", width = 15)
    @ApiModelProperty(value = "入职须知id")
    private java.lang.String staNoticeId;
    /**
     * 入职须知 内容
     */
    @TableField(exist = false)
    private java.lang.String staNoticeContext;

    @Excel(name = "置顶标记更新时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "置顶标记更新时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.util.Date topFlagUpdate;
    //置顶
    @TableField(exist = false)
    private java.lang.String topFlag;
    @Excel(name = "结算类型", width = 15)
    @ApiModelProperty(value = "结算类型")
    private String salar;
    @ApiModelProperty(value = "店编")
    private String storeNo;
    //工种id
    private String jobsId;
    //工种名称
    private String jobsName;
    /**
     * 弃用jobsSalary(
     * 任务结算标准大于工种结算标准
     * 任务结算标准为空 使用工种结算标准 不为空使用任务结算标准
     * )
     * 统一结算标准  workSalary
     */
    //工种结算标准
    private BigDecimal jobsSalary;
    //任务结算标准 统一结算标准
    private BigDecimal workSalary;
    /**
     * 任务对应的服务周期和服务时间段
     */
    @TableField(exist = false)
    List<StaTaskDateVO> staTaskDateAndTimes;
    /**
     * 任务开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date taskStartDate;
    /**
     * 任务结束日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date taskEndDate;
    /**
     * 任务开始-结束日期任务添加使用
     */
    @TableField(exist = false)
    private String taskDate;
    /**
     * 任务天数
     */
    private int taskDaysTotal;
    /**
     * 任务开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "任务开始时间")
    private Date taskStartTime;
    /**
     * 任务结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "任务结束时间")
    private Date taskEndTime;
    /**
     * 任务时长
     */
    private Double taskTimeTotal;
    /**
     * 任务预计总时数
     */
    private Double taskEstimatedTotalTime;
    /**
     * 任务预计总金额
     */
    private BigDecimal taskEstimatedTotalAmount;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "服务开始时间")
    private Date serviceStartTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "服务结束时间")
    private Date serviceEndTime;
    /**
     * 预计产生的金额
     */
    @TableField(exist = false)
    private BigDecimal totalSalary;
    /**
     * 预计产生的时数
     */
    @TableField(exist = false)
    private Double totalTime;
    @TableField(exist = false)
    private Double settlementCriteria;
    /**
     * 排班id
     */
    @TableField(exist = false)
    private List<String> sids;
    //请求类型
    @TableField(exist = false)
    private String requestType;
    @TableField(exist = false)
    private String searchField;

    @Override
    public int compareTo(StaWork work) {
        return work.getExpectNum() - this.getExpectNum();
    }
}
