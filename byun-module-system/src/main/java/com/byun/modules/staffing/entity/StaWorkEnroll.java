package com.byun.modules.staffing.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@ApiModel(value="sta_work_enroll对象", description="任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新")
@Data
@TableName("sta_work_enroll")
public class StaWorkEnroll implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**任务id*/
    @ApiModelProperty(value = "任务id")
    private java.lang.String staWorkId;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**身份证*/
	@Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    private java.lang.String idCard;
	/**身份证是否验证*/
	@Excel(name = "身份证是否验证", width = 15)
    @ApiModelProperty(value = "身份证是否验证")
    private java.lang.Integer idCardVeri;
	/**手机*/
	@Excel(name = "手机", width = 15)
    @ApiModelProperty(value = "手机")
    private java.lang.String phone;
	/**是否短信验证*/
	@Excel(name = "是否短信验证", width = 15)
    @ApiModelProperty(value = "是否短信验证")
    private java.lang.Integer phoneVeri;
	/**性别*/
	@Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private java.lang.String sex;
	/**年龄*/
	@Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private java.lang.String age;
	/**报名提交人id*/
	@Excel(name = "报名提交人id", width = 15)
    @ApiModelProperty(value = "报名提交人id")
    private java.lang.String sysUserId;
	/**报名类型(1自己2他人)*/
	@Excel(name = "报名类型(1自己2他人)", width = 15)
    @ApiModelProperty(value = "报名类型(1自己2他人)")
    private java.lang.Integer enrollType;
	/**用户报名信息id*/
	@Excel(name = "用户报名信息id", width = 15)
    @ApiModelProperty(value = "用户报名信息id")
    private java.lang.String staEnrollInfo;
	/**删除状态(0-正常,1-已删除)*/
	@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常,1-已删除)")
    private java.lang.Integer delFlag;
	/**报名状态0拒绝1通过2待审核3待面试*/
	@Excel(name = "报名状态0拒绝1通过2待审核3待面试", width = 15)
    @ApiModelProperty(value = "报名状态0拒绝1通过2待审核3待面试")
    private java.lang.Integer applyStateFlag;
	/**用户订单id*/
	@Excel(name = "用户订单id", width = 15)
    @ApiModelProperty(value = "用户订单id")
    private java.lang.String staOrderId;

    public StaWorkEnroll(){

    }

    public StaWorkEnroll(StaEnrollInfo staEnrollInfo){
        this.setName(staEnrollInfo.getName());
        this.setIdCard(staEnrollInfo.getIdCard());
        this.setIdCardVeri(staEnrollInfo.getIdCardVeri());
        this.setPhone(staEnrollInfo.getPhone());
        this.setPhoneVeri(staEnrollInfo.getPhoneVeri());
        if(staEnrollInfo.getAge()!=null){
            this.setAge(staEnrollInfo.getAge().toString());
        }
        if(staEnrollInfo.getUserRel()==0){
            this.setEnrollType(1);
        }
    }
}
