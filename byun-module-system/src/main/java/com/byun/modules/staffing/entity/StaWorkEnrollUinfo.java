package com.byun.modules.staffing.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@TableName("sta_work_enroll_uinfo")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_work_enroll_uinfo对象", description="任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新")
public class StaWorkEnrollUinfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**任务id*/
	@Excel(name = "任务id", width = 15)
    @ApiModelProperty(value = "任务id")
    private java.lang.String staWorkId;
	/**名片绑定用户*/
	@Excel(name = "名片绑定用户", width = 15)
    @ApiModelProperty(value = "名片绑定用户")
    private java.lang.String sysUserId;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**电话*/
	@Excel(name = "电话", width = 15)
    @ApiModelProperty(value = "电话")
    private java.lang.String phone;
	/**是否短信验证第一个电话*/
	@Excel(name = "是否短信验证第一个电话", width = 15)
    @ApiModelProperty(value = "是否短信验证第一个电话")
    private java.lang.Integer phoneVeri;
	/**电话2*/
	@Excel(name = "电话2", width = 15)
    @ApiModelProperty(value = "电话2")
    private java.lang.String phoneTwo;
	/**微信号*/
	@Excel(name = "微信号", width = 15)
    @ApiModelProperty(value = "微信号")
    private java.lang.String wechatId;
	/**id_card*/
	@Excel(name = "id_card", width = 15)
    @ApiModelProperty(value = "id_card")
    private java.lang.String idCard;
	/**sex*/
	@Excel(name = "sex", width = 15)
    @ApiModelProperty(value = "sex")
    private java.lang.Integer sex;
	/**简介*/
	@Excel(name = "简介", width = 15)
    @ApiModelProperty(value = "简介")
    private java.lang.String brief;
	/**生日*/
	@Excel(name = "生日", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "生日")
    private java.util.Date birthday;
	/**年龄*/
	@Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private java.lang.Integer age;
	/**求职意向，sta_work_type工种code*/
	@Excel(name = "求职意向，sta_work_type工种code", width = 15)
    @ApiModelProperty(value = "求职意向，sta_work_type工种code")
    private java.lang.String intentionCode;
	/**所在区域code*/
	@Excel(name = "所在区域code", width = 15)
    @ApiModelProperty(value = "所在区域code")
    private java.lang.String regionCode;
	/**所在区域*/
	@Excel(name = "所在区域", width = 15)
    @ApiModelProperty(value = "所在区域")
    private java.lang.String region;
	/**地址*/
	@Excel(name = "地址", width = 15)
    @ApiModelProperty(value = "地址")
    private java.lang.String address;
    /**任务地点坐标Lat*/
    @Excel(name = "任务地点坐标Lat", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lat")
    private java.math.BigDecimal addressLat;
    /**任务地点坐标Lng*/
    @Excel(name = "任务地点坐标Lng", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lng")
    private java.math.BigDecimal addressLng;
	/**名片id*/
	@Excel(name = "名片id", width = 15)
    @ApiModelProperty(value = "名片id")
    private java.lang.String staUserInfoId;
	/**身份证是否验证*/
	@Excel(name = "身份证是否验证", width = 15)
    @ApiModelProperty(value = "身份证是否验证")
    private java.lang.Integer idCardVeri;
	/**用户订单id*/
	@Excel(name = "用户订单id", width = 15)
    @ApiModelProperty(value = "用户订单id")
    private java.lang.String staOrderId;
	/**任务报名人id*/
	@Excel(name = "任务报名人id", width = 15)
    @ApiModelProperty(value = "任务报名人id")
    private java.lang.String staWorkEnrollId;
}
