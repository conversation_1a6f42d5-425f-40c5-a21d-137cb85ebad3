package com.byun.modules.staffing.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 任务图片
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@ApiModel(value="sta_work_image对象", description="任务图片")
@Data
@TableName("sta_work_image")
public class StaWorkImage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**任务id*/
    @ApiModelProperty(value = "任务id")
    private java.lang.String staWorkId;
	/**图片路径*/
	@Excel(name = "图片路径", width = 15)
    @ApiModelProperty(value = "图片路径")
    private java.lang.String imageUrl;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sortOrder;
}
