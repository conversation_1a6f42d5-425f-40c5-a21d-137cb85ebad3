package com.byun.modules.staffing.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 任务模板（方便管理添加）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@TableName("sta_work_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sta_work_template对象", description="任务模板（方便管理添加）")
public class StaWorkTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**模板级别0系统1公司2门店3个人*/
	@Excel(name = "模板级别0系统1公司2门店3个人", width = 15)
    @ApiModelProperty(value = "模板级别0系统1公司2门店3个人")
    private java.lang.Integer templateLevel;
	/**公司id（depart）*/
	@Excel(name = "公司id（depart）", width = 15)
    @ApiModelProperty(value = "门店id（depart）")
    private java.lang.String companyId;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "门店名称")
    private java.lang.String companyName;
	/**门店id（depart）*/
	@Excel(name = "门店id（depart）", width = 15)
    @ApiModelProperty(value = "公司id（depart）")
    private java.lang.String firmId;
	/**门店名称*/
	@Excel(name = "门店名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    private java.lang.String firmName;
	/**任务名全称*/
	@Excel(name = "任务名全称", width = 15)
    @ApiModelProperty(value = "任务名全称")
    private java.lang.String nameFull;
	/**任务名简称*/
	@Excel(name = "任务名简称", width = 15)
    @ApiModelProperty(value = "任务名简称")
    private java.lang.String nameNick;
	/**任务描述*/
	@Excel(name = "任务描述", width = 15)
    @ApiModelProperty(value = "任务描述")
    private java.lang.String content;
    /**薪资类型*/
    @Excel(name = "薪资类型", width = 15)
    @ApiModelProperty(value = "薪资类型,1、年薪2、月薪3、日薪4、时薪")
    private java.lang.Integer salaryType;
	/**最小薪资*/
	@Excel(name = "最小薪资", width = 15)
    @ApiModelProperty(value = "最小薪资")
    private java.math.BigDecimal salaryMin;
	/**最大薪资*/
	@Excel(name = "最大薪资", width = 15)
    @ApiModelProperty(value = "最大薪资")
    private java.math.BigDecimal salaryMax;
	/**任务周期*/
	@Excel(name = "任务周期", width = 15)
    @ApiModelProperty(value = "任务周期")
    private java.lang.String needWorkCycle;
	/**报道时间*/
	@Excel(name = "报道时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "报道时间")
    private java.util.Date reportDate;
	/**任务开始时间*/
	@Excel(name = "任务开始时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "任务开始时间")
    private java.util.Date workDateStart;
	/**任务结束时间*/
	@Excel(name = "任务结束时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "任务结束时间")
    private java.util.Date workDateEnd;
	/**任务地址*/
	@Excel(name = "任务地址", width = 15)
    @ApiModelProperty(value = "任务地址")
    private java.lang.String address;
    /**任务地点坐标Lat*/
    @Excel(name = "任务地点坐标Lat", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lat")
    private java.math.BigDecimal addressLat;
    /**任务地点坐标Lng*/
    @Excel(name = "任务地点坐标Lng", width = 15)
    @ApiModelProperty(value = "任务地点坐标Lng")
    private java.math.BigDecimal addressLng;
	/**所在区域编码*/
	@Excel(name = "所在区域编码", width = 15)
    @ApiModelProperty(value = "所在区域编码")
    private java.lang.String regionCode;
    @Excel(name = "所在区域名称", width = 15)
    @ApiModelProperty(value = "所在区域名称")
    private java.lang.String regionName;
	/**要求最小年龄段*/
	@Excel(name = "要求最小年龄段", width = 15)
    @ApiModelProperty(value = "要求最小年龄段")
    private java.lang.Integer needAgeMin;
	/**要求最大年龄段*/
	@Excel(name = "要求最大年龄段", width = 15)
    @ApiModelProperty(value = "要求最大年龄段")
    private java.lang.Integer needAgeMax;
	/**要求性别*/
	@Excel(name = "要求性别", width = 15)
    @ApiModelProperty(value = "要求性别")
    private java.lang.Integer needSex;
	/**要求学历*/
	@Excel(name = "要求学历", width = 15)
    @ApiModelProperty(value = "要求学历")
    private java.lang.Integer needDegree;
	/**要求经验*/
	@Excel(name = "要求经验", width = 15)
    @ApiModelProperty(value = "要求经验")
    private java.lang.String needExp;
	/**其他要求*/
	@Excel(name = "其他要求", width = 15)
    @ApiModelProperty(value = "其他要求")
    private java.lang.String needOther;
	/**福利待遇*/
	@Excel(name = "福利待遇", width = 15)
    @ApiModelProperty(value = "福利待遇")
    private java.lang.String benefit;
	/**标签code(label1,label2)*/
	@Excel(name = "标签code(label1,label2)", width = 15)
    @ApiModelProperty(value = "标签code(label1,label2)")
    private java.lang.String labelCode;
	/**标签(label1,label2)*/
	@Excel(name = "标签(label1,label2)", width = 15)
    @ApiModelProperty(value = "标签(label1,label2)")
    private java.lang.String labelName;
	/**工种编码组*/
	@Excel(name = "工种编码组", width = 15)
    @ApiModelProperty(value = "工种编码组")
    private java.lang.String workTypeCode;
	/**工种名称组*/
	@Excel(name = "工种名称组", width = 15)
    @ApiModelProperty(value = "工种名称组")
    private java.lang.String workTypeName;
	/**展示开始时间*/
	@Excel(name = "展示开始时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "展示开始时间")
    private java.util.Date showDateStart;
	/**展示结束时间*/
	@Excel(name = "展示结束时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "展示结束时间")
    private java.util.Date showDateEnd;
	/**删除状态*/
	@Excel(name = "删除状态", width = 15)
    @ApiModelProperty(value = "删除状态")
    private java.lang.Integer delFlag;
	/**任务种类（1兼职2专职）*/
	@Excel(name = "任务种类（1兼职2专职）", width = 15)
    @ApiModelProperty(value = "任务种类（1兼职2专职）")
    private java.lang.Integer workClass;
	/**是否需要审核*/
	@Excel(name = "是否需要审核", width = 15)
    @ApiModelProperty(value = "是否需要审核")
    private java.lang.Integer examineFlag;
	/**聚合id*/
	@Excel(name = "聚合id", width = 15)
    @ApiModelProperty(value = "聚合id")
    private java.lang.String aggregationId;
	/**联系人*/
	@Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String liaison;
	/**联系人电话*/
	@Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
    private java.lang.String liaisonTp;
	/**集合地址*/
	@Excel(name = "集合地址", width = 15)
    @ApiModelProperty(value = "集合地址")
    private java.lang.String addressJoin;
	/**适合人群code*/
	@Excel(name = "适合人群code", width = 15)
    @ApiModelProperty(value = "适合人群code")
    private java.lang.String crowdCode;
	/**适合人群*/
	@Excel(name = "适合人群", width = 15)
    @ApiModelProperty(value = "适合人群")
    private java.lang.String crowd;
	/**任务时间要求*/
	@Excel(name = "任务时间要求", width = 15)
    @ApiModelProperty(value = "任务时间要求")
    private java.lang.String needDate;
	/**是否点名*/
	@Excel(name = "是否点名", width = 15)
    @ApiModelProperty(value = "是否点名")
    private java.lang.Integer rcNeedFlag;
	/**是否在列表展示*/
	@Excel(name = "是否在列表展示", width = 15)
    @ApiModelProperty(value = "是否在列表展示")
    private java.lang.Integer listShow;
	/**需要人数*/
	@Excel(name = "需要人数", width = 15)
    @ApiModelProperty(value = "需要人数")
    private java.lang.Integer expectNum;
	/**申请人数*/
	@Excel(name = "申请人数", width = 15)
    @ApiModelProperty(value = "申请人数")
    private java.lang.Integer applyNum;
	/**通过人数*/
	@Excel(name = "通过人数", width = 15)
    @ApiModelProperty(value = "通过人数")
    private java.lang.Integer adoptNum;
	/**所在区域*/
	@Excel(name = "所在区域", width = 15)
    @ApiModelProperty(value = "所在区域")
    private java.lang.String region;
	/**是否需要签到*/
	@Excel(name = "是否需要签到", width = 15)
    @ApiModelProperty(value = "是否需要签到")
    private java.lang.Integer lcNeedFlag;
    @Excel(name = "图片路径", width = 15)
    @ApiModelProperty(value = "图片路径")
    private java.lang.String imageUrl;
    @Excel(name = "地址名称", width = 15)
    @ApiModelProperty(value = "地址名称")
    private java.lang.String addressName;
	/**用户id*/
	@Excel(name = "用户id", width = 15)
    @ApiModelProperty(value = "用户id")
    private java.lang.String sysUserId;
}
