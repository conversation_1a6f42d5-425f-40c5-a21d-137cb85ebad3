package com.byun.modules.staffing.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 访问量、收藏量
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@ApiModel(value="sta_work_visits对象", description="访问量、收藏量")
@Data
@TableName("sta_work_visits")
public class StaWorkVisits implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**访问量*/
	@Excel(name = "访问量", width = 15)
    @ApiModelProperty(value = "访问量")
    private java.lang.Integer visits;
	/**收藏量*/
	@Excel(name = "收藏量", width = 15)
    @ApiModelProperty(value = "收藏量")
    private java.lang.Integer collections;
	/**任务id*/
    @ApiModelProperty(value = "任务id")
    private java.lang.String staWorkId;
}
