package com.byun.modules.staffing.mapper;

import java.util.List;
import com.byun.modules.staffing.entity.StaLocationClock;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 签到定位时间
 * @Author: b<PERSON><PERSON>
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaLocationClockMapper extends BaseMapper<StaLocationClock> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<StaLocationClock> selectByMainId(@Param("mainId") String mainId);
}
