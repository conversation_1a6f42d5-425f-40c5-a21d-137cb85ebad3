package com.byun.modules.staffing.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.byun.modules.system.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 任务单
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Mapper
public interface StaOrderMapper extends BaseMapper<StaOrder> {

    Page<SysUser> getBankPage(Page<SysUser>page, @Param("deptName") String deptName, @Param("deptNo") String deptNo, @Param("userName") String userName);
    List<SysUser> getBankPage(@Param("deptName")String deptName, @Param("deptNo") String deptNo, @Param("userName") String userName);
}
