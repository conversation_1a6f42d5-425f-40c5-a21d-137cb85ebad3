package com.byun.modules.staffing.mapper;

import java.util.List;
import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 排班模板选项
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
public interface StaScheduleTemplateItemMapper extends BaseMapper<StaScheduleTemplateItem> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<StaScheduleTemplateItem>
   */
	public List<StaScheduleTemplateItem> selectByMainId(@Param("mainId") String mainId);
}
