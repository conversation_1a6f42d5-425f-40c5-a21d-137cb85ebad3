package com.byun.modules.staffing.mapper;

import com.byun.modules.staffing.entity.StaUserInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 用户灵活用工信息（名片）
 * @Author: b<PERSON>yun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaUserInfoMapper extends BaseMapper<StaUserInfo> {

    Boolean editWechatIdById(@Param("id") String id, @Param("wechatId") String wechatId);

    void updateIdCarByUserId(@Param("idCard") String idCard, @Param("id") String id);
}
