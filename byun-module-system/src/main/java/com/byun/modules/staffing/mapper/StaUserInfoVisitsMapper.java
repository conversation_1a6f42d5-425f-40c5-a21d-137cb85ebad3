package com.byun.modules.staffing.mapper;

import java.util.List;
import com.byun.modules.staffing.entity.StaUserInfoVisits;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 名片访问量
 * @Author: b<PERSON>yun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaUserInfoVisitsMapper extends BaseMapper<StaUserInfoVisits> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<StaUserInfoVisits> selectByMainId(@Param("mainId") String mainId);
}
