package com.byun.modules.staffing.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaWork;
import org.apache.ibatis.annotations.Param;
import com.byun.modules.staffing.entity.StaWorkCollect;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 用户收藏任务
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaWorkCollectMapper extends BaseMapper<StaWorkCollect> {

    /**
     * @description:  查询我的收藏
     * @param pageList
     * @param userId
     * @return: java.util.List<com.byun.modules.staffing.entity.StaWork>
     * <AUTHOR>
     * @date: 2021/12/4 21:17
     */
    List<StaWork> getMyWorkCollectList(Page<StaWork> pageList,@Param("userId") String userId);
}
