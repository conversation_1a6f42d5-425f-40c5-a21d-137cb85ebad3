package com.byun.modules.staffing.mapper;

import java.util.List;
import com.byun.modules.staffing.entity.StaWorkEnroll;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaWorkEnrollMapper extends BaseMapper<StaWorkEnroll> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<StaWorkEnroll> selectByMainId(@Param("mainId") String mainId);
}
