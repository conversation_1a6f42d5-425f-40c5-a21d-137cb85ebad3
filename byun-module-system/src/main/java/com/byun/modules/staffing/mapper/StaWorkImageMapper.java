package com.byun.modules.staffing.mapper;

import java.util.List;
import com.byun.modules.staffing.entity.StaWorkImage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 任务图片
 * @Author: b<PERSON><PERSON>
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaWorkImageMapper extends BaseMapper<StaWorkImage> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<StaWorkImage> selectByMainId(@Param("mainId") String mainId);
}
