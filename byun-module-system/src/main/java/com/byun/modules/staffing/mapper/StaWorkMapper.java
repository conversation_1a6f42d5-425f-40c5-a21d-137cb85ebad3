package com.byun.modules.staffing.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.model.StaWorkModel;
import org.apache.ibatis.annotations.Param;
import com.byun.modules.staffing.entity.StaWork;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 任务列表
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaWorkMapper extends BaseMapper<StaWork> {
    /**
     * @description: 查询任务列表按距离排序
     * @param pageList
     * @param staWorkModel
     * @return: java.util.List<com.byun.modules.staffing.model.StaWorkModel>
     * <AUTHOR>
     * @date: 2021/12/5 10:13
     */
    List<StaWorkModel> listOrderDistance(Page<StaWorkModel> pageList,@Param("staWorkModel") StaWorkModel staWorkModel);

    //IPage<StaWork> pageDistance(@Param(Constants.WRAPPER) QueryWrapper<StaWork> queryWrapper, @Param("lat") String lat, @Param("lnt") String lnt, @Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize);

    /**
     *   根据用户查询用户灵工小程序权限
     */
//    public List<StaWork> queryPageListByStateOrderDistance(@Param("storesId")  Integer state,@Param("addressLag")  BigDecimal lat,@Param("addressLng")  BigDecimal lnt);

}
