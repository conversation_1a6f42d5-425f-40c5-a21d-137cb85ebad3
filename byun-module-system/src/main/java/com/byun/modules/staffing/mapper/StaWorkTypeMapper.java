package com.byun.modules.staffing.mapper;

import org.apache.ibatis.annotations.Param;
import com.byun.modules.staffing.entity.StaWorkType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 任务类型
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaWorkTypeMapper extends BaseMapper<StaWorkType> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

}
