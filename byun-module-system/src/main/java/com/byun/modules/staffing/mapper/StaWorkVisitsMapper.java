package com.byun.modules.staffing.mapper;

import java.util.List;
import com.byun.modules.staffing.entity.StaWorkVisits;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 访问量、收藏量
 * @Author: b<PERSON>yun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface StaWorkVisitsMapper extends BaseMapper<StaWorkVisits> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<StaWorkVisits> selectByMainId(@Param("mainId") String mainId);
}
