<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.staffing.mapper.StaOrderMapper">
    <select id="getBankPage" resultType="com.byun.modules.system.entity.SysUser">
        SELECT
        DISTINCT su.id,su.username,su.real_name_name,su.id_card,su.current_stores_id
        FROM
        sys_user su
        INNER JOIN
        sta_order so ON su.id = so.sys_user_id
        WHERE
        1=1
        <if test="deptName != null and deptName != ''">
            AND so.company_name = #{deptName}
        </if>
        <if test="deptNo != null and deptNo != ''">
            AND so.store_no = #{deptNo}
        </if>
        <if test="userName != null and userName != ''">
            AND so.enroll_name LIKE CONCAT('%', #{userName}, '%')
        </if>
    </select>
</mapper>