<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.staffing.mapper.StaScheduleMapper">
    <select id="getCompanyServiceYears" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT schedule_day) FROM sta_schedule
        WHERE del_flag = 0
        <if test="oid != null and oid != '' and oid.trim() != ''">
        AND sta_order_id = #{oid}
        </if>
    </select>
</mapper>