<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.staffing.mapper.StaUserInfoVisitsMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  sta_user_info_visits 
		WHERE
			 sta_user_info_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.byun.modules.staffing.entity.StaUserInfoVisits">
		SELECT * 
		FROM  sta_user_info_visits
		WHERE
			 sta_user_info_id = #{mainId} 	</select>
</mapper>
