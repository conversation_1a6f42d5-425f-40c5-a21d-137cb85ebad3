<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.staffing.mapper.StaWorkBrMapper">

    <select id="getMyWorkBrList" parameterType="String"  resultType="com.byun.modules.staffing.entity.StaWork">
        select
            sw.*
        from sta_work sw
        left join sta_work_br swb ON swb.sta_work_id = sw.id
        where swb.sys_user_id = #{userId}
        order by swb.br_time desc
    </select>
</mapper>