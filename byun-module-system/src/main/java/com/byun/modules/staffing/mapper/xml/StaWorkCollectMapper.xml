<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.staffing.mapper.StaWorkCollectMapper">

    <select id="getMyWorkCollectList" parameterType="String"  resultType="com.byun.modules.staffing.entity.StaWork">
        select
            sw.*
        from sta_work sw
        left join sta_work_collect swc ON swc.sta_work_id = sw.id
        where swc.sys_user_id = #{userId}
        order by swc.collect_time desc
    </select>
</mapper>