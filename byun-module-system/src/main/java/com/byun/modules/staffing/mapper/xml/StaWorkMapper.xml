<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.staffing.mapper.StaWorkMapper">
    <resultMap id="StaWorkModel" type="com.byun.modules.staffing.model.StaWorkModel" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="firm_id" property="firmId" jdbcType="VARCHAR"/>
        <result column="firm_name" property="firmName" jdbcType="VARCHAR"/>
        <result column="name_full" property="nameFull" jdbcType="VARCHAR"/>
        <result column="name_nick" property="nameNick" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="salary_type" property="salaryType" jdbcType="TINYINT"/>
        <result column="salary_min" property="salaryMin" jdbcType="DECIMAL"/>
        <result column="salary_max" property="salaryMax" jdbcType="DECIMAL"/>
        <result column="need_work_cycle" property="needWorkCycle" jdbcType="VARCHAR"/>
        <result column="report_date" property="reportDate" jdbcType="DATE"/>
        <result column="work_date_start" property="workDateStart" jdbcType="DATE"/>
        <result column="work_date_end" property="workDateEnd" jdbcType="DATE"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="address_lat" property="addressLat" jdbcType="DECIMAL"/>
        <result column="address_lng" property="addressLng" jdbcType="DECIMAL"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="need_age_min" property="needAgeMin" jdbcType="INTEGER"/>
        <result column="need_age_max" property="needAgeMax" jdbcType="INTEGER"/>
        <result column="need_sex" property="needSex" jdbcType="TINYINT"/>
        <result column="need_degree" property="needDegree" jdbcType="VARCHAR"/>
        <result column="need_exp" property="needExp" jdbcType="VARCHAR"/>
        <result column="need_other" property="needOther" jdbcType="VARCHAR"/>
        <result column="benefit" property="benefit" jdbcType="VARCHAR"/>
        <result column="label_code" property="labelCode" jdbcType="VARCHAR"/>
        <result column="label_name" property="labelName" jdbcType="VARCHAR"/>
        <result column="work_type_code" property="workTypeCode" jdbcType="VARCHAR"/>
        <result column="work_type_name" property="workTypeName" jdbcType="VARCHAR"/>
        <result column="show_date_start" property="showDateStart" jdbcType="DATE"/>
        <result column="show_date_end" property="showDateEnd" jdbcType="DATE"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
        <result column="work_class" property="workClass" jdbcType="TINYINT"/>
        <result column="examine_flag" property="examineFlag" jdbcType="TINYINT"/>
        <result column="aggregation_id" property="aggregationId" jdbcType="VARCHAR"/>
        <result column="liaison" property="liaison" jdbcType="VARCHAR"/>
        <result column="liaison_tp" property="liaisonTp" jdbcType="VARCHAR"/>
        <result column="address_join" property="addressJoin" jdbcType="VARCHAR"/>
        <result column="crowd_code" property="crowdCode" jdbcType="VARCHAR"/>
        <result column="crowd" property="crowd" jdbcType="VARCHAR"/>
        <result column="need_date" property="needDate" jdbcType="VARCHAR"/>
        <result column="state_flag" property="stateFlag" jdbcType="TINYINT"/>
        <result column="rc_need_flag" property="rcNeedFlag" jdbcType="TINYINT"/>
        <result column="list_show" property="listShow" jdbcType="TINYINT"/>
        <result column="expect_num" property="expectNum" jdbcType="INTEGER"/>
        <result column="apply_num" property="applyNum" jdbcType="INTEGER"/>
        <result column="adopt_num" property="adoptNum" jdbcType="INTEGER"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="lc_need_flag" property="lcNeedFlag" jdbcType="TINYINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="address_name" property="addressName" jdbcType="VARCHAR"/>
        <result column="sys_user_id" property="sysUserId" jdbcType="VARCHAR"/>
        <!--        距离-->
        <result column="distance" property="distance" jdbcType="DECIMAL"/>
        <result column="need_id_diploma" property="needIdDiploma" jdbcType="TINYINT"/>
        <result column="need_id_health" property="needIdHealth" jdbcType="TINYINT"/>
        <result column="need_my_photo" property="needMyPhoto" jdbcType="TINYINT"/>
        <result column="sta_notice_id" property="staNoticeId" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listOrderDistance" parameterType="Object"  resultMap="StaWorkModel">
        select
        *
        <if test="staWorkModel.currentLat !=null and staWorkModel.currentLat != '' and staWorkModel.currentLng !=null and staWorkModel.currentLng != ''">
            ,(
            6371 * acos (
            cos ( radians(#{staWorkModel.currentLat}) )
            * cos( radians( address_lat ) )
            * cos( radians( address_lng ) - radians(#{staWorkModel.currentLng}) )
            + sin ( radians(#{staWorkModel.currentLat}) )
            * sin( radians( address_lat ) )
            )
            ) AS distance
        </if>
        from sta_work
        where del_flag = '0'
        <if test="staWorkModel.nameNick !=null and staWorkModel.nameNick != ''">
            and (name_nick LIKE concat(concat('%',#{staWorkModel.nameNick}),'%')
            or company_name LIKE concat(concat('%',#{staWorkModel.nameNick}),'%')
            or name_full LIKE concat(concat('%',#{staWorkModel.nameNick}),'%'))
        </if>
        <if test="staWorkModel.stateFlag !=null and staWorkModel.stateFlag != ''">
            and state_flag = #{staWorkModel.stateFlag}
        </if>
        order by
        <if test="staWorkModel.currentLat !=null and staWorkModel.currentLat != '' and staWorkModel.currentLng !=null and staWorkModel.currentLng != ''">
            ISNULL(distance),distance asc,
        </if>
        create_time desc
    </select>

    <!-- 获取灵工小程序登录用户拥有的权限 -->
    <!--    <select id="queryPageListByStateOrderDistance" parameterType="Object"  resultMap="StaWork">-->
    <!--        SELECT * FROM (-->
    <!--        SELECT p.*-->
    <!--        FROM  sys_permission p-->
    <!--        WHERE (exists(-->
    <!--        select a.id from sys_role_permission a-->
    <!--        join sys_role b on a.role_id = b.id-->
    <!--        join sys_user_role c on c.role_id = b.id-->
    <!--        join sys_user d on d.id = c.user_id-->
    <!--        where p.id = a.permission_id AND d.username = #{username,jdbcType=VARCHAR}-->
    <!--        ))-->
    <!--        and p.del_flag = 0 and (p.component like 'staffing%' or p.perms like 'staffing%')-->
    <!--        &lt;!&ndash;update begin ：加入部门权限 &ndash;&gt;-->
    <!--        UNION-->
    <!--        SELECT p.*-->
    <!--        FROM  sys_permission p-->
    <!--        WHERE exists(-->
    <!--        select a.id from sys_depart_role_permission a-->
    <!--        join sys_depart_role b on a.role_id = b.id-->
    <!--        join sys_depart_role_user c on c.drole_id = b.id-->
    <!--        join sys_user d on d.id = c.user_id-->
    <!--        where p.id = a.permission_id AND d.username = #{username,jdbcType=VARCHAR}-->
    <!--        )-->
    <!--        and p.del_flag = 0 and (p.component like 'staffing%' or p.perms like 'staffing%')-->
    <!--        &lt;!&ndash;update end ：加入部门权限 &ndash;&gt;-->
    <!--        ) h order by h.sort_no ASC-->

    <!--        select t.* from(-->
    <!--        select id,lat,lng,ST_Distance_sphere(point(lng,lat), point(20.000006, 10.000005)) 'dis' from partner_store_song1000-->
    <!--        where-->
    <!--        lng < 20.000006 + (180 / pi() / 6370986 * 10000 )/ cos(10.000005 * pi() / 180)-->
    <!--        and lng > 20.000006 - (180 / pi() / 6370986 * 10000 )/ cos(10.000005 * pi() / 180)-->
    <!--        and lat < 10.000005 + 180 / pi() / 6370986 * 10000-->
    <!--        and lat > 10.000005 - 180 / pi() / 6370986 * 10000-->
    <!--        )t-->
    <!--        where t.dis < 10000-->
    <!--        order by t.dis limit 10;-->
    <!--    </select>-->

</mapper>