package com.byun.modules.staffing.model;

import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/20 13:00
 */

@Data
public class StaWorkModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**主键*/
    private java.lang.String id;
    /**创建人*/
    private java.lang.String createBy;
    /**创建日期*/
    private java.util.Date createTime;
    /**更新人*/
    private java.lang.String updateBy;
    /**更新日期*/
    private java.util.Date updateTime;
    /**所属部门code*/
    private java.lang.String sysOrgCode;
    /**公司id（depart）*/
    private java.lang.String companyId;
    /**公司名称*/
    private java.lang.String companyName;
    /**门店id（depart）*/
    private java.lang.String firmId;
    /**门店名称*/
    private java.lang.String firmName;
    /**任务名全称*/
    private java.lang.String nameFull;
    /**任务名简称*/
    private java.lang.String nameNick;
    /**任务描述*/
    private java.lang.String content;
    /**薪资类型*/
    private java.lang.Integer salaryType;
    /**最小薪资*/
    private java.math.BigDecimal salaryMin;
    /**最大薪资*/
    private java.math.BigDecimal salaryMax;
    /**任务周期*/
    private java.lang.String needWorkCycle;
    /**报道时间*/
    private java.util.Date reportDate;
    /**任务开始时间*/
    private java.util.Date workDateStart;
    /**任务结束时间*/
    private java.util.Date workDateEnd;
    /**任务地址*/
    private java.lang.String address;
    /**任务地点坐标Lat*/
    private java.math.BigDecimal addressLat;
    /**任务地点坐标Lng*/
    private java.math.BigDecimal addressLng;
    /**所在区域编码*/
    private java.lang.String regionCode;
    /**要求最小年龄段*/
    private java.lang.Integer needAgeMin;
    /**要求最大年龄段*/
    private java.lang.Integer needAgeMax;
    /**要求性别*/
    private java.lang.Integer needSex;
    /**要求学历*/
    private java.lang.Integer needDegree;
    /**要求经验*/
    private java.lang.String needExp;
    /**其他要求*/
    private java.lang.String needOther;
    /**福利待遇*/
    private java.lang.String benefit;
    /**标签code(label1,label2)*/
    private java.lang.String labelCode;
    /**标签(label1,label2)*/
    private java.lang.String labelName;
    /**工种编码组*/
    private java.lang.String workTypeCode;
    /**工种名称组*/
    private java.lang.String workTypeName;
    /**展示开始时间*/
    private java.util.Date showDateStart;
    /**展示结束时间*/
    private java.util.Date showDateEnd;
    /**删除状态*/
    private java.lang.Integer delFlag;
    /**任务种类（1兼职2专职）*/
    private java.lang.Integer workClass;
    /**是否需要审核*/
    private java.lang.Integer examineFlag;
    /**聚合id*/
    private java.lang.String aggregationId;
    /**联系人*/
    private java.lang.String liaison;
    /**联系人电话*/
    private java.lang.String liaisonTp;
    /**集合地址*/
    private java.lang.String addressJoin;
    /**适合人群code*/
    private java.lang.String crowdCode;
    /**适合人群*/
    private java.lang.String crowd;
    /**任务时间要求*/
    private java.lang.String needDate;
    /**状态0未发布1完成2发布中3任务中4结算中*/
    private java.lang.Integer stateFlag;
    /**是否点名*/
    private java.lang.Integer rcNeedFlag;
    /**是否在列表展示*/
    private java.lang.Integer listShow;
    /**需要人数*/
    private java.lang.Integer expectNum;
    /**申请人数*/
    private java.lang.Integer applyNum;
    /**通过人数*/
    private java.lang.Integer adoptNum;
    /**所在区域*/
    private java.lang.String region;
    /**是否需要签到*/
    private java.lang.Integer lcNeedFlag;

    private java.lang.String imageUrl;
    private java.lang.String addressName;
    private java.lang.String sysUserId;
    /**真实姓名**/
    private String realNameName;
    private java.lang.Integer needIdDiploma;
    private java.lang.Integer needIdHealth;
    private java.lang.Integer needMyPhoto;
    private java.lang.Integer needHealthPhoto;
    /*入职须知*/
    private java.lang.String staNoticeId;
    /*入职须知是否阅读*/
    private java.lang.Integer noticeReadFlag;

    private java.util.Date topFlagUpdate;

    //任务图片列表
    private List<String> imgList;

    //报名人id
    private String staEnrollInfoId;

    /**任务地点坐标Lat*/
    private java.math.BigDecimal currentLat;
    /**任务地点坐标Lng*/
    private java.math.BigDecimal currentLng;

    /** 距离*/
    private java.math.BigDecimal distance;

    /** 推荐人电话（招聘官）*/
    private java.lang.String recommenderPhone;

    /** 报名人信息 */
    private StaEnrollInfo enrollInfo;
    /** 店编 */
    private String stoNe;
    /** 服务开始时间*/
    private Date serviceStartTime;
    /** 服务结束时间*/
    private Date serviceEndTime;
    /**
     * 任务开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    private Date taskStartTime;
    /**
     * 任务结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    private Date taskEndTime;
    /**
     * 任务开始日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskStartDate;
    /**
     * 任务结束日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskEndDate;
    /**
     *
     */
    private String staTaskDateId;

}