package com.byun.modules.staffing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.common.system.vo.LoginUser;
import com.byun.modules.staffing.entity.StaBlackList;
import com.byun.modules.system.entity.SysUser;

public interface IStaBlacklistService extends IService<StaBlackList> {
     void saveUser(LoginUser loginUser, SysUser sysUser, String idCard, String blackListType);
}


