package com.byun.modules.staffing.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.modules.staffing.entity.StaLocationClock;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkAgentRel;

import java.text.ParseException;
import java.util.List;

/**
 * @Description: 签到定位时间
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaLocationClockService extends IService<StaLocationClock> {

	public List<StaLocationClock> selectByMainId(String mainId);

	/**
	 * @description: 签到
	 * <AUTHOR>
	 * @date 2021/11/23 22:58
	 * @version 1.0
	 */
	StaLocationClock rollCall(StaLocationClock staLocationClock, StaOrder order) throws ParseException;

}
