package com.byun.modules.staffing.service;

import com.byun.modules.staffing.entity.StaLocationClock;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaSchedule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.staffing.entity.StaScheduleNo;

import java.text.ParseException;
import java.util.List;

/**
 * @Description: 排班表
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
public interface IStaScheduleService extends IService<StaSchedule> {

    void updateBatchScheduleForOrder(List<StaSchedule> list, StaOrder order);
//    void modifyClockInRecords(String oid, String scheduleDay, String code, List <StaLocationClock> staLocationClocks);

    List<StaSchedule> getStaScheduleClock(List<StaSchedule> staSchedules, List<StaLocationClock> staLocationClocks) throws ParseException;

    void addSchedule(String oid, String scheduleDay,List<StaSchedule> staSchedules) throws ParseException;

    /**
     * 获取司龄
     * @param oid
     * @return
     */
    int getCompanyServiceYears(String oid);
}
