package com.byun.modules.staffing.service;

import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 排班模板选项
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
public interface IStaScheduleTemplateItemService extends IService<StaScheduleTemplateItem> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<StaScheduleTemplateItem>
	 */
	public List<StaScheduleTemplateItem> selectByMainId(String mainId);
}
