package com.byun.modules.staffing.service;

import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import com.byun.modules.staffing.entity.StaScheduleTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 排班表模板
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
public interface IStaScheduleTemplateService extends IService<StaScheduleTemplate> {

	/**
	 * 添加一对多
	 *
	 * @param staScheduleTemplate
	 * @param staScheduleTemplateItemList
	 */
	public void saveMain(StaScheduleTemplate staScheduleTemplate, List<StaScheduleTemplateItem> staScheduleTemplateItemList) ;
	
	/**
	 * 修改一对多
	 *
   * @param staScheduleTemplate
   * @param staScheduleTemplateItemList
	 */
	public void updateMain(StaScheduleTemplate staScheduleTemplate, List<StaScheduleTemplateItem> staScheduleTemplateItemList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain(String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain(Collection<? extends Serializable> idList);
	
}
