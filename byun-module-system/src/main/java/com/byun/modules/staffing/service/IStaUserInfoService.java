package com.byun.modules.staffing.service;

import com.alibaba.fastjson.JSONObject;
import com.byun.modules.staffing.entity.StaUserInfoVisits;
import com.byun.modules.staffing.entity.StaUserInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 用户灵活用工信息（名片）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaUserInfoService extends IService<StaUserInfo> {

	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(StaUserInfo staUserInfo,List<StaUserInfoVisits> staUserInfoVisitsList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(StaUserInfo staUserInfo,List<StaUserInfoVisits> staUserInfoVisitsList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

    Boolean editWechatIdById(String id, String wechatId);

	void updateIdCarByUserId(String idCard, String id);

	/**
	 * 添加健康证
	 * @param data
	 * @return
	 */
    Boolean addLicense(JSONObject data);
}
