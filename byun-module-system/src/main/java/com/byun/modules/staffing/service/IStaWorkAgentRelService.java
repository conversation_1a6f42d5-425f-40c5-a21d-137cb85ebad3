package com.byun.modules.staffing.service;

import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkAgentRel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.system.entity.SysUser;

import java.util.List;

/**
 * @Description: 任务代理人关系表
 * @Author: bai
 * @Date:   2021-12-13
 * @Version: V1.0
 */
public interface IStaWorkAgentRelService extends IService<StaWorkAgentRel> {

    /**
     * @description: 批量代理绑定任务
     * @param user
     * @param staWork
     * @param userIdArr
     * @param deleteUserIdArr
     * @return: void
     * <AUTHOR>
     * @date: 2021/12/14 14:54
     */
    void batchAgentBindWork(SysUser user, StaWork staWork, String[] userIdArr, List<String> deleteUserIdArr);
}
