package com.byun.modules.staffing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkBr;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 任务浏览记录
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaWorkBrService extends IService<StaWorkBr> {

    /**
     * @description: 查询我的浏览记录
     * @param pageList
     * @param userId
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.byun.modules.staffing.entity.StaWork>
     * <AUTHOR>
     * @date: 2021/12/4 21:15
     */
    Page<StaWork> getMyWorkBrPage(Page<StaWork> pageList, String userId);
}
