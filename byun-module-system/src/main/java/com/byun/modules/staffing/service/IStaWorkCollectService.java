package com.byun.modules.staffing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkCollect;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 用户收藏任务
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaWorkCollectService extends IService<StaWorkCollect> {

    /**
     * @description:  查询我的收藏
     * @param pageList
     * @param userId
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.byun.modules.staffing.entity.StaWork>
     * <AUTHOR>
     * @date: 2021/12/4 21:32
     */
    Page<StaWork> getMyWorkCollectPage(Page<StaWork> pageList, String userId);
}
