package com.byun.modules.staffing.service;

import com.byun.modules.staffing.entity.StaWorkEnroll;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaWorkEnrollService extends IService<StaWorkEnroll> {

	public List<StaWorkEnroll> selectByMainId(String mainId);
}
