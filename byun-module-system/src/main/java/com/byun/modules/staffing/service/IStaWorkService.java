package com.byun.modules.staffing.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.modules.staffing.entity.StaWorkImage;
import com.byun.modules.staffing.entity.StaWorkVisits;
import com.byun.modules.staffing.entity.StaWorkEnroll;
import com.byun.modules.staffing.entity.StaWork;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.staffing.model.StaWorkModel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 任务列表
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaWorkService extends IService<StaWork> {

	/**
	 * @description: 查询任务列表按距离排序
	 * @param pageList
	 * @param staWorkModel
	 * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.byun.modules.staffing.model.StaWorkModel>
	 * <AUTHOR>
	 * @date: 2021/12/5 10:11
	 */
	Page<StaWorkModel> listOrderDistancePage(Page<StaWorkModel> pageList, StaWorkModel staWorkModel);

	/**
	 * @description: 更新申请人数
	 * <AUTHOR>
	 * @date 2021/12/3 18:37
	 * @version 1.0
	 */
	void updateApplyNum(String id);
	/**
	 * @description: 任务单结束
	 * <AUTHOR>
	 * @date 2021/11/23 14:15
	 * @version 1.0
	 */
	Boolean completeWork(StaWork staWork);
	/**
	 * @description: 任务设置为开始状态
	 * <AUTHOR>
	 * @date 2021/11/23 12:40
	 * @version 1.0
	 */
	Boolean startWork(StaWork staWork, String username);

	/**
	 * 保存部门数据
	 * @param staWork
	 */
	void saveWorkData(StaWork staWork,String username);
	String saveWorkData2(StaWork staWork,String username);

	/**
	 * 更新work数据
	 * @param staWork
	 * @return
	 */
	Boolean updateWorkDataById(StaWork staWork, String username);



	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(StaWork staWork,List<StaWorkImage> staWorkImageList,List<StaWorkVisits> staWorkVisitsList,List<StaWorkEnroll> staWorkEnrollList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(StaWork staWork,List<StaWorkImage> staWorkImageList,List<StaWorkVisits> staWorkVisitsList,List<StaWorkEnroll> staWorkEnrollList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	void updateApplyNum2(String staWorkId,int size);

    Result repairClock(JSONObject data);

	/**
	 * 员工异动
	 * @param data
	 */
    void storeChangeApplication(JSONObject data);

    /**
     * 优化的管理员任务列表查询
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<StaWork> getAdminTaskListOptimized(Page<StaWork> page, QueryWrapper<StaWork> queryWrapper);
}
