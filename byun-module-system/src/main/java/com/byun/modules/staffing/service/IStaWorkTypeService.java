package com.byun.modules.staffing.service;

import com.byun.modules.staffing.entity.StaWorkType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.common.exception.ByunBootException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

/**
 * @Description: 任务类型
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
public interface IStaWorkTypeService extends IService<StaWorkType> {

	/**根节点父ID的值*/
	public static final String ROOT_PID_VALUE = "0";
	
	/**树节点有子节点状态值*/
	public static final String HASCHILD = "1";
	
	/**树节点无子节点状态值*/
	public static final String NOCHILD = "0";

	/**新增节点*/
	void addStaWorkType(StaWorkType staWorkType);
	
	/**修改节点*/
	void updateStaWorkType(StaWorkType staWorkType) throws ByunBootException;
	
	/**删除节点*/
	void deleteStaWorkType(String id) throws ByunBootException;

	/**查询所有数据，无分页*/
    List<StaWorkType> queryTreeListNoPage(QueryWrapper<StaWorkType> queryWrapper);

}
