package com.byun.modules.staffing.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaBlackList;
import com.byun.modules.staffing.mapper.StaBlacklistMapper;
import com.byun.modules.staffing.service.IStaBlacklistService;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-5-24 10:16
 */
@Service
public class StaBlacklistServiceImpl  extends ServiceImpl<StaBlacklistMapper, StaBlackList> implements IStaBlacklistService {
    @Autowired
    private StaBlacklistMapper staBlacklistMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 添加黑名单用户
     * @param loginUser 登录用户
     * @param sysUser 目标用户
     * @param idCard 目标身份证
     * @param blackListType 目标黑名单类型
     */
    @Override
    public void saveUser(LoginUser loginUser, SysUser sysUser, String idCard, String blackListType) {
        StaBlackList staBlackList = new StaBlackList();
        int age = IdCardUtil.countAge(idCard);
        String sex = IdCardUtil.judgeGender(idCard);
        staBlackList.setAge(String.valueOf(age));
        staBlackList.setSex(sex.equals("男") ? 0 : 1);
        staBlackList.setIdCard(idCard);
        if (WxlConvertUtils.isNotEmpty(sysUser.getDegree())) {//学历
            staBlackList.setDegree(sysUser.getDegree());
        }
        //当前操作人
        SysUser sysUser1 = sysUserMapper.selectById(loginUser.getId());
        if (WxlConvertUtils.isNotEmpty(sysUser1.getRealname())) {
            staBlackList.setCreateByName(sysUser1.getRealNameName());
        } else {
            staBlackList.setCreateByName(sysUser1.getRealname());
        }
        staBlackList.setPhone(sysUser.getUsername());
        staBlackList.setUserName(sysUser.getRealNameName());
        staBlackList.setUserId(sysUser.getId());
        staBlackList.setBlackListType(blackListType);
        staBlackList.setCreateById(sysUser1.getId());
        staBlackList.setCreateTime(new Date());
        staBlackList.setUpdateTime(new Date());
        staBlacklistMapper.insert(staBlackList);
    }
}
