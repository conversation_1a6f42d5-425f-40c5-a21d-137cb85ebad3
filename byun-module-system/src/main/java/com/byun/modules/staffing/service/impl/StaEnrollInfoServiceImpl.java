package com.byun.modules.staffing.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.byun.modules.staffing.entity.StaUserInfo;
import com.byun.modules.staffing.mapper.StaEnrollInfoMapper;
import com.byun.modules.staffing.mapper.StaUserInfoMapper;
import com.byun.modules.staffing.service.IStaEnrollInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Description: 报名信息，用于任务报名
 * @Author: bai
 * @Date:   2021-11-21
 * @Version: V1.0
 */
@Service
public class StaEnrollInfoServiceImpl extends ServiceImpl<StaEnrollInfoMapper, StaEnrollInfo> implements IStaEnrollInfoService {

}
