package com.byun.modules.staffing.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.DateUtil;
import com.byun.common.util.DistanceUtils;
import com.byun.modules.staffing.entity.StaLocationClock;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaSchedule;
import com.byun.modules.staffing.mapper.StaLocationClockMapper;
import com.byun.modules.staffing.mapper.StaScheduleMapper;
import com.byun.modules.staffing.service.IStaLocationClockService;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.mapper.SysDepartMapper;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
/**
 * @Description: 签到定位时间
 * @Author: baiyun
 * @Date: 2021-11-14
 * @Version: V1.0
 */
@Service
public class StaLocationClockServiceImpl extends ServiceImpl<StaLocationClockMapper, StaLocationClock> implements IStaLocationClockService {
    @Autowired
    private StaLocationClockMapper staLocationClockMapper;
    @Autowired
    private StaScheduleMapper staScheduleMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Override
    public List<StaLocationClock> selectByMainId(String mainId) {
        return staLocationClockMapper.selectByMainId(mainId);
    }

    /**
     * 签到
     *
     * @param staLocationClock
     * @param startTimeDate
     * @param sId
     * @param isAddress
     * @param date
     * @param bl
     * @return
     */
    private StaLocationClock punchIn(StaLocationClock staLocationClock, Date startTimeDate, String sId, boolean isAddress, Date date, boolean bl) {
        staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_1);
        staLocationClock.setTimeExpect(startTimeDate);
        staLocationClock.setStaScheduleId(sId);
        if (bl) {
            if (isAddress) {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
            } else {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_4);
            }
        } else {
            if (isAddress) {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_2);
            } else {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_5);
            }
        }
        staLocationClock = this.standardClock(staLocationClock, date);
        staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
        String companyId = staLocationClock.getCompanyId();
        Date timeExpect = staLocationClock.getTimeExpect(); //要求时间
        int count  = staLocationClockMapper.selectCount(new LambdaQueryWrapper<StaLocationClock>()
                .eq(StaLocationClock::getStaOrderId, staLocationClock.getStaOrderId())
                .eq(StaLocationClock::getTimeExpect, timeExpect)
                .eq(StaLocationClock::getTimeType, staLocationClock.getTimeType()));
        if (count > 0) { //签到已存在
            StaLocationClock staLocationClock1 = new StaLocationClock();
            staLocationClock1.setStatus(1);
            return staLocationClock1;
        }
        SysDepart sysDepart = sysDepartMapper.selectById(companyId);
        staLocationClock.setOrgCode(sysDepart.getOrgCode()); //部门code
        staLocationClockMapper.insert(staLocationClock);
        return staLocationClock;
    }
    /**
     * 签退
     *
     * @param staLocationClock
     * @param endTimeDate
     * @param sId
     * @param isAddress
     * @param date
     * @param bl
     * @return
     */
    private synchronized StaLocationClock punchOut(StaLocationClock staLocationClock, Date endTimeDate, String sId, boolean isAddress, Date date, boolean bl) {
        staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_2);
        staLocationClock.setTimeExpect(endTimeDate);
        staLocationClock.setStaScheduleId(sId);
        if (bl) {
            if (isAddress) {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
            } else {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_4);
            }
        } else {
            if (isAddress) {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_3);
            } else {
                staLocationClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_6);
            }
        }
        staLocationClock = this.standardClock(staLocationClock, date);
        staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
        String companyId = staLocationClock.getCompanyId();
        Date timeExpect = staLocationClock.getTimeExpect(); //要求时间
        List<StaLocationClock> removeStaLocationClock = staLocationClockMapper.selectList(new LambdaQueryWrapper<StaLocationClock>()
                .eq(StaLocationClock::getStaOrderId, staLocationClock.getStaOrderId())
                .eq(StaLocationClock::getTimeExpect, timeExpect)
                .eq(StaLocationClock::getTimeType, staLocationClock.getTimeType()));
        if (WxlConvertUtils.isNotEmpty(removeStaLocationClock) && removeStaLocationClock.size() > 0) {
            List<String> collect = removeStaLocationClock.stream().map(StaLocationClock::getId).collect(Collectors.toList());
            staLocationClockMapper.deleteBatchIds(collect);
        }
        SysDepart sysDepart = sysDepartMapper.selectById(companyId);
        staLocationClock.setOrgCode(sysDepart.getOrgCode()); //部门code
        staLocationClockMapper.insert(staLocationClock);
        return staLocationClock;
    }

    @Override
    public StaLocationClock rollCall(StaLocationClock staLocationClock, StaOrder order) throws ParseException {
        if (WxlConvertUtils.isNotEmpty(staLocationClock.getStaOrderId())) {
            Date date = new Date();
            long time = date.getTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DATE, -1);//默认签到日期减去一天
            String formatStringDate = DateUtil.getFormatStringDate(calendar.getTime());//yyyy-MM-dd
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //签到范围
            BigDecimal addressLat = order.getAddressLat();
            BigDecimal addressLng = order.getAddressLng();
            BigDecimal lcAddressLat = staLocationClock.getLcAddressLat();
            BigDecimal lcAddressLng = staLocationClock.getLcAddressLng();
            boolean isAddress = false;//是否超出签到范围
            boolean acrossDay = false;//是否跨夜 默认false
            if (WxlConvertUtils.isNotEmpty(addressLat) && WxlConvertUtils.isNotEmpty(addressLng)) {//需要定位
                if (WxlConvertUtils.isNotEmpty(lcAddressLat) && WxlConvertUtils.isNotEmpty(lcAddressLng)) {//有定位
                    double v = DistanceUtils.GetDistance(addressLat.doubleValue(), addressLng.doubleValue(), lcAddressLat.doubleValue(), lcAddressLng.doubleValue());
                    if (v > 300 || v < -300) {//超过300米
                        isAddress = false;
                    } else {//在签到范围
                        isAddress = true;
                    }
                } else {//无定位，超出签到范围
                    isAddress = false;
                }
            } else {//不需定位，不显示超签到范围
                isAddress = true;
            }
            LambdaQueryWrapper<StaSchedule> scheduleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            scheduleLambdaQueryWrapper.eq(StaSchedule::getScheduleDay, formatStringDate);
            scheduleLambdaQueryWrapper.eq(StaSchedule::getStaOrderId, staLocationClock.getStaOrderId());
            scheduleLambdaQueryWrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);
            scheduleLambdaQueryWrapper.ne(StaSchedule::getCode,"///");
            //当前订单 昨天日期 的签到时间段
            List<StaSchedule> staSchedules = staScheduleMapper.selectList(scheduleLambdaQueryWrapper);
            //根据班别判断是否跨夜
            if (WxlConvertUtils.isNotEmpty(staSchedules) && !staSchedules.isEmpty()) {
                StaSchedule scheduleOne = staSchedules.get(0);
                StaSchedule scheduleTwo = null;
                if (staSchedules.size() == 2) {
                    scheduleTwo = staSchedules.get(1);
                }
                if (WxlConvertUtils.isNotEmpty(scheduleOne)) {
                    if (scheduleOne.getStartTime().after(scheduleOne.getEndTime()) ||
                            (WxlConvertUtils.isNotEmpty(scheduleTwo)
                                    && scheduleTwo.getStartTime().after(scheduleTwo.getEndTime()))){
                        //跨夜
                        Date scheduleDay = scheduleOne.getScheduleDay();//yyyy-MM-dd
                        Date scheduleStartTimeOne = scheduleOne.getStartTime(); //hh:mm:ss
                        Date staScheduleEndTimeOne = scheduleOne.getEndTime();
                        String startDateOne = dateFormat.format(scheduleDay) + " " + timeFormat.format(scheduleStartTimeOne);
                        String endDateOne = dateFormat.format(scheduleDay) + " " + timeFormat.format(staScheduleEndTimeOne);
                        //获取签到信息
                        List<StaLocationClock> staLocationClocks = staLocationClockMapper.selectList(new QueryWrapper<StaLocationClock>()
                                .lambda()
                                .likeRight(StaLocationClock::getTimeExpect, formatStringDate)
                                .eq(StaLocationClock::getStaOrderId, staLocationClock.getStaOrderId())
                                .eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0));
                        if (WxlConvertUtils.isNotEmpty(staLocationClock) && !staLocationClocks.isEmpty()) {
                            Date stOne = sb.parse(startDateOne);
                            Date edOne = sb.parse(endDateOne);
                            Boolean strFlagOne = staLocationClocks.stream().anyMatch(f -> f.getTimeExpect().equals(stOne));//班次1签到是否有效
                            Boolean endFlagOne = staLocationClocks.stream().anyMatch(f -> f.getTimeExpect().equals(edOne));//班次1签退是否有效
                            if (staSchedules.size() == 1) {//跨夜一个班次
                                if (strFlagOne && endFlagOne) { //签到签退都有效
                                    formatStringDate = DateUtil.getFormatStringDate(new Date());
                                } else {
                                    acrossDay = true;
                                }
                            } else if (staSchedules.size() == 2) {//跨夜两个班次
                                Date scheduleStartTimeTwo = scheduleOne.getStartTime(); //hh:mm:ss
                                Date staScheduleEndTimeTwo = scheduleOne.getEndTime();
                                String startDateTwo = dateFormat.format(scheduleDay) + " " + timeFormat.format(scheduleStartTimeTwo);
                                String endDateTwo = dateFormat.format(scheduleDay) + " " + timeFormat.format(staScheduleEndTimeTwo);
                                Boolean strFlagTwo = staLocationClocks.stream().anyMatch(f -> f.getTimeExpect().equals(startDateTwo));//班次2签到是否有效
                                Boolean endFlagTwo = staLocationClocks.stream().anyMatch(f -> f.getTimeExpect().equals(endDateTwo));//班次2签退是否有效
                                if (strFlagOne && endFlagOne && strFlagTwo && endFlagTwo) {
                                    formatStringDate = DateUtil.getFormatStringDate(new Date());
                                } else {
                                    //夜班下班缺卡情况
                                    formatStringDate = DateUtil.getFormatStringDate(new Date());
                                    LambdaQueryWrapper<StaSchedule> scheduleQueryWrapper = new LambdaQueryWrapper<>();
                                    scheduleQueryWrapper.eq(StaSchedule::getScheduleDay, formatStringDate);
                                    scheduleQueryWrapper.eq(StaSchedule::getStaOrderId, staLocationClock.getStaOrderId());
                                    scheduleQueryWrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);
                                    scheduleLambdaQueryWrapper.ne(StaSchedule::getCode,"///");
                                    //当前订单 当天日期 的签到时间段
                                    staSchedules = staScheduleMapper.selectList(scheduleQueryWrapper);
                                    if (WxlConvertUtils.isNotEmpty(staSchedules)) {
                                        Date endTime = staSchedules.get(0).getEndTime();
                                        String StringDate = formatStringDate + " " + timeFormat.format(endTime);
                                        if (new Date().before(sb.parse(StringDate))) acrossDay = true;
                                    }
                                }
                            }
                        } else { //无签到信息
                            formatStringDate = DateUtil.getFormatStringDate(new Date());
                        }
                    } else {
                        formatStringDate = DateUtil.getFormatStringDate(new Date()); //当前日期
                    }
                }
            } else {//昨天无排班
                formatStringDate = DateUtil.getFormatStringDate(new Date());
            }
            //不跨天查询当天排班
            if (!acrossDay) {
                formatStringDate = DateUtil.getFormatStringDate(new Date());
                LambdaQueryWrapper<StaSchedule> scheduleQueryWrapper = new LambdaQueryWrapper<>();
                scheduleQueryWrapper.eq(StaSchedule::getScheduleDay, formatStringDate);
                scheduleQueryWrapper.eq(StaSchedule::getStaOrderId, staLocationClock.getStaOrderId());
                scheduleQueryWrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);
                scheduleLambdaQueryWrapper.ne(StaSchedule::getCode,"///");
                //当前订单 当天日期 的签到时间段
                staSchedules = staScheduleMapper.selectList(scheduleQueryWrapper);
            }
            //当前订单 当前日期 的已签到时间
            LambdaQueryWrapper<StaLocationClock> locationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (acrossDay) {//跨天
                locationClockLambdaQueryWrapper.likeRight(StaLocationClock::getTimeExpect, formatStringDate);
            } else { //不跨天
                locationClockLambdaQueryWrapper.likeRight(StaLocationClock::getTimeExpect, DateUtil.getFormatStringDate(new Date()));
            }
            locationClockLambdaQueryWrapper.eq(StaLocationClock::getStaOrderId, staLocationClock.getStaOrderId());
            locationClockLambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
            List<StaLocationClock> staLocationClocks = staLocationClockMapper.selectList(locationClockLambdaQueryWrapper);
            //不跨天按签到规定时间 排序 跨天不执行排序
            if (!acrossDay) {
                for (int i = 1; i < staSchedules.size(); i++) {  //第一层for循环,用来控制冒泡的次数
                    for (int j = 0; j < staSchedules.size() - 1; j++) { //第二层for循环,用来控制冒泡一层层到最后
                        //如果前一个数比后一个数大,两者调换 ,意味着泡泡向上走了一层
                        StaSchedule j1 = staSchedules.get(j);
                        StaSchedule j2 = staSchedules.get(j + 1);
                        //空元素往后移动
                        if (WxlConvertUtils.isEmpty(j1.getStartTime()) || j1.getStartTime().getTime() > j2.getStartTime().getTime()) {
                            staSchedules.remove(j);
                            staSchedules.add(j, j2);
                            staSchedules.remove(j + 1);
                            staSchedules.add(j + 1, j1);
                        }
                    }
                }
            }
            long startLong = 0;//时间段，用于判断 当前时间属于哪个时间段
            long endLong = 0;
            boolean lastStart = false;//当前未打签到卡
            boolean lastEnd = false;//当前未打签退卡
            String lastStartTime = null;
            String lastEndTime = null;
            Date lastStartTimeDate = null;
            Date lastEndTimeDate = null;
            String lastScheduleId = null;
            //按签到签到时间 排序
            for (StaSchedule staSchedule : staSchedules) {
                String startTime = null;
                String endTime = null;
                Date startTimeDate = null;
                Date endTimeDate = null;
                if (WxlConvertUtils.isNotEmpty(staSchedule.getStartTime())) {
                    startTime = DateUtil.getFormatStringTime(staSchedule.getStartTime());//签到签到时间
                    //签到
                    startTimeDate = DateUtil.getDateByFormat(formatStringDate + " " + startTime, "yyyy-MM-dd HH:mm:ss");
                }
                if (WxlConvertUtils.isNotEmpty(staSchedule.getEndTime())) {
                    endTime = DateUtil.getFormatStringTime(staSchedule.getEndTime());
                    endTimeDate = DateUtil.getDateByFormat(formatStringDate + " " + endTime, "yyyy-MM-dd HH:mm:ss");
                }
                boolean start = false;//当前未打签到卡
                boolean end = false;//当前未打签退卡
                //判断是否已有签到
                for (StaLocationClock lc : staLocationClocks) {
                    Date timeExpect = lc.getTimeExpect();
                    if (WxlConvertUtils.isNotEmpty(timeExpect) && staSchedule.getId().compareTo(lc.getStaScheduleId()) == 0) {
                        String timeExpectString = DateUtil.getFormatStringTime(timeExpect);
                        if (timeExpectString.compareTo(startTime) == 0) {
                            start = true;
                        } else if (timeExpectString.compareTo(endTime) == 0) {
                            end = true;
                        }
                    }
                }

                if (endLong == 0 && startLong == 0) {//初始
                    if (WxlConvertUtils.isEmpty(endTimeDate)) {
                        endLong = -1;
                    } else {
                        endLong = startTimeDate.getTime();
                    }
                    //0 - s
                    //第一个时间段判断
                    if (time <= endLong) {//是否是签到之前签到
                        staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, true);
                        break;
                    }
                    if (WxlConvertUtils.isEmpty(startTimeDate)) {
                        startLong = -1;
                    } else {
                        startLong = startTimeDate.getTime();
                    }
                    if (WxlConvertUtils.isEmpty(endTimeDate)) {
                        endLong = -1;
                    } else {
                        endLong = endTimeDate.getTime();
                    }
                    // s - e
                    //第二个时间段判断
                    if (startLong == -1 && time < endLong) {//正常签到签到
                        if (start) {//有签到卡,是早退
                            staLocationClock = punchOut(staLocationClock, endTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        } else {//无签到卡，正常签到
                            staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, true);
                            break;
                        }
                    } else if (time > startLong && time < endLong) {//判断是迟到还是早退
                        if (start) {//有签到卡,是早退
                            staLocationClock = punchOut(staLocationClock, endTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        } else {//无签到卡，是迟到
                            staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        }
                    }

                } else {//不是第一个循环
                    startLong = endLong;
                    if (WxlConvertUtils.isEmpty(startTimeDate)) {
                        endLong = -1;
                    } else {
                        endLong = startTimeDate.getTime();
                    }
                    // e - s
                    //第三个时间段判断
                    if (time < endLong) {//正常签到签到
                        if (lastEnd) {//有上一个时间段有签退签到//正常签到签到
                            staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, true);
                            break;
                        } else {
                            if (lastStart) {//正常签退
                                staLocationClock = punchOut(staLocationClock, lastEndTimeDate, lastScheduleId, isAddress, date, true);
                                break;
                            } else {//正常签到
                                staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, true);
                            }
                        }
                    }
                    startLong = endLong;
                    if (WxlConvertUtils.isEmpty(endTimeDate)) {
                        endLong = -1;
                    } else {
                        endLong = endTimeDate.getTime();
                    }
                    // s - e
                    //第四个时间段判断，往后三四循环
                    if (startLong == -1 && time < endLong) {//正常签到签到
                        if (start) {//有签到卡,是早退
                            staLocationClock = punchOut(staLocationClock, endTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        } else {//无签到卡，是迟到
                            staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        }
                    } else if (time > startLong && time < endLong) {//判断是迟到还是早退
                        if (start) {//有签到卡,是早退
                            staLocationClock = punchOut(staLocationClock, endTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        } else {//无签到卡，是迟到
                            staLocationClock = punchIn(staLocationClock, startTimeDate, staSchedule.getId(), isAddress, date, false);
                            break;
                        }
                    }
                }

                lastStart = start;//保存上一个签到状态
                lastEnd = end;//保存上一个签到状态
                lastStartTime = startTime;
                lastEndTime = endTime;
                lastStartTimeDate = startTimeDate;//上一个签到时间
                lastEndTimeDate = endTimeDate;//上一个签签退时间
                lastScheduleId = staSchedule.getId();
            }
            if (staLocationClock.getStatus() != null) {
                if (staLocationClock.getStatus() == 1 ) {
                    return staLocationClock;
                }
            }
            if (WxlConvertUtils.isEmpty(staLocationClock.getStaScheduleId())
                    && WxlConvertUtils.isEmpty(staLocationClock.getTimeExpect())
                    && WxlConvertUtils.isEmpty(staLocationClock.getStaScheduleId())
                    && WxlConvertUtils.isNotEmpty(lastEndTimeDate) && WxlConvertUtils.isNotEmpty(lastScheduleId) && time > endLong) {//要不是最后，要不被覆盖
                staLocationClock = punchOut(staLocationClock, lastEndTimeDate, lastScheduleId, isAddress, date, true);
            }

            //给排班表设置签到状态
            String staScheduleId = staLocationClock.getStaScheduleId();
            if (WxlConvertUtils.isNotEmpty(staScheduleId)) {
                StaSchedule staSchedule = staScheduleMapper.selectById(staScheduleId);
                if (CommonConstant.CLOCK_STATE_FLAG_1.compareTo(staSchedule.getLcStateFlag()) != 0) {//若已正常签到则不修改
                    if (CommonConstant.CLOCK_STATE_FLAG_1.compareTo(staLocationClock.getStateFlag()) == 0) {
                        boolean bl = true;
                        for (StaSchedule ss : staSchedules) {//判断当前时间段是否有异常签到
                            boolean bl1 = false;
                            boolean bl2 = false;
                            //新增的签到
                            if (staScheduleId.compareTo(ss.getId()) == 0) {
                                //签到卡是否正常
                                if (CommonConstant.CLOCK_TYPE_1.compareTo(staLocationClock.getTimeType()) == 0) {
                                    bl1 = true;
                                }
                                //签退卡是否正常
                                if (CommonConstant.CLOCK_TYPE_2.compareTo(staLocationClock.getTimeType()) == 0) {
                                    bl2 = true;
                                }
                            }
                            //已有的签到
                            for (StaLocationClock slc : staLocationClocks) {
                                //寻找当前时间段，并且正常签到的记录
                                if (ss.getId().compareTo(slc.getStaScheduleId()) == 0 && CommonConstant.CLOCK_STATE_FLAG_1.compareTo(slc.getStateFlag()) == 0) {
                                    Integer timeType = slc.getTimeType();
                                    //签到卡是否正常
                                    if (CommonConstant.CLOCK_TYPE_1.compareTo(timeType) == 0) {
                                        bl1 = true;
                                    }
                                    //签退卡是否正常
                                    if (CommonConstant.CLOCK_TYPE_2.compareTo(timeType) == 0) {
                                        bl2 = true;
                                    }
                                }
                            }
                            if (!bl1 || !bl2) {
                                bl = false;
                            }
                        }
                        if (bl) {
                            staSchedule.setLcStateFlag(1);//0存在异常签到，1签到正常
                            staScheduleMapper.updateById(staSchedule);
                        }
                    }
                }
            }
            //无排班签到
            if (WxlConvertUtils.isEmpty(staLocationClock.getTime())) {
                //第一次签到  第二次签退  第三次 签到  第四次 签退 超过四次 改为覆盖第四次.........
                QueryWrapper<StaLocationClock> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("sta_order_id", order.getId());
                queryWrapper.likeRight("time", formatStringDate);
                queryWrapper.orderByAsc("time");
                List<StaLocationClock> staLocationClocksCount = staLocationClockMapper.selectList(queryWrapper);
                switch (staLocationClocksCount.size()) {
                    case 0:
                    case 2:
                        staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_1); //签到
                        break;
                    case 1:
                    case 3:
                        staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_2); //签退
                        break;
                    case 4:
                        //一天最多4次签到记录大于4次覆盖第四次
                        StaLocationClock deleteClock = staLocationClocksCount.get(staLocationClocksCount.size() - 1);
                        staLocationClockMapper.deleteById(deleteClock.getId());
                        staLocationClock.setTimeType(CommonConstant.CLOCK_TYPE_2); //签退
                }
                staLocationClock.setTime(date);//签到时间
                staLocationClock.setStateFlag(1);//签到成功
                staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
                String companyId = staLocationClock.getCompanyId();
                SysDepart sysDepart = sysDepartMapper.selectById(companyId);
                staLocationClock.setOrgCode(sysDepart.getOrgCode()); //部门code
                staLocationClockMapper.insert(staLocationClock);
            }
            return staLocationClock;
        }
        return null;
    }

    /**
     * 定位签到-时间处理 处理迟到、早退时长，单位分钟
     * 必须是正常签到 超出范围 迟到 早退 以实际签到时间为准
     * 8:30签到   7:00签到    签到时间 8:30
     * 17:30签退  22:30签到   签到时间 18:30
     * @param staLocationClock
     * @param date 签到时间
     * @return StaLocationClock
     */
    public StaLocationClock standardClock(StaLocationClock staLocationClock, Date date) {
        staLocationClock.setTime(date);//实际签到时间
        staLocationClock.setDelFlag(CommonConstant.DEL_FLAG_0);
        StaSchedule staSchedule = staScheduleMapper.selectById(staLocationClock.getStaScheduleId());//排班
        LocalDate currentDate = LocalDate.now();
        Date startTime = staSchedule.getStartTime();
        LocalDateTime startDateTime = LocalDateTime.ofInstant(startTime.toInstant(), ZoneId.systemDefault());
        LocalDateTime combinedDateTime = LocalDateTime.of(currentDate, startDateTime.toLocalTime());
        Date sTime = Date.from(combinedDateTime.atZone(ZoneId.systemDefault()).toInstant());//签到
        Date endTime = staSchedule.getEndTime();
        LocalDateTime endDateTime = LocalDateTime.ofInstant(endTime.toInstant(), ZoneId.systemDefault());
        LocalDateTime endCombinedDateTime = LocalDateTime.of(currentDate, endDateTime.toLocalTime());
        Date eTime = Date.from(endCombinedDateTime.atZone(ZoneId.systemDefault()).toInstant());    //签退
        //迟到时长记录(单位分钟)
        if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_1)
                && (staLocationClock.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_2)
                || staLocationClock.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_5))) {
            long diff = date.getTime() - sTime.getTime();
            int diffMinutes = (int) (diff / (60 * 1000));//分钟
            if (diffMinutes > 0) staLocationClock.setLengthOfTardiness(diffMinutes);
        }
        //早退时长记录(单位分钟)
        if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_2)
                && (staLocationClock.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_3)
                || staLocationClock.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_6))) {
            long diff = date.getTime() - eTime.getTime();
            int diffMinutes = (int) (diff / (60 * 1000));//分钟
            if (diffMinutes < 0) staLocationClock.setEarlyLeaveDuration(Math.abs(diffMinutes));
        }
        return staLocationClock;
    }
}
