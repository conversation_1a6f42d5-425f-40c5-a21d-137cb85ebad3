package com.byun.modules.staffing.service.impl;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.*;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.mapper.*;
import com.byun.modules.staffing.service.IStaLocationClockService;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaTaskDateService;
import com.byun.modules.staffing.vo.lcDayPage;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.mapper.SysDepartMapper;
import com.byun.modules.system.mapper.SysUserMapper;
import com.byun.modules.system.mapper.SysUserRelMapper;
import com.byun.modules.system.service.ISysAnnouncementService;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 任务单
 * @Author: baiyun
 * @Date: 2021-11-14
 * @Version: V1.0
 */
@Slf4j
@Service
public class StaOrderServiceImpl extends ServiceImpl<StaOrderMapper, StaOrder> implements IStaOrderService {

    @Autowired
    private StaOrderMapper staOrderMapper;
    @Autowired
    private StaWorkMapper staWorkMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private StaLocationClockMapper staLocationClockMapper;
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private StaScheduleMapper staScheduleMapper;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private IStaLocationClockService staLocationClockService;
    @Autowired
    private StaScheduleNoMapper staScheduleNoMapper;
    @Autowired
    private StaTaskDateMapper staTaskDateMapper;
    @Autowired
    private SysUserRelMapper sysUserRelMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Override
    @Transactional
    public void addRealUserIdByPhone(String phone) {
        if (WxlConvertUtils.isNotEmpty(phone)) {
            SysUser userByName = sysUserMapper.getUserByName(phone);
            LambdaUpdateWrapper<StaOrder> updateWrapper = new UpdateWrapper().lambda();
            updateWrapper.set(StaOrder::getRealUserId, userByName.getId());
            updateWrapper.eq(StaOrder::getEnrollType, CommonConstant.ENROLL_TYPE_2);
            updateWrapper.eq(StaOrder::getEnrollPhone, phone);
            updateWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
            updateWrapper.isNull(StaOrder::getRealUserId);
            StaOrder staOrder = new StaOrder();
            staOrderMapper.update(staOrder, updateWrapper);
        }
    }

    @Override
    @Transactional
    public void cancelOrder(StaOrder staOrder) {
        staOrder.setStateFlag(CommonConstant.ORDER_STATUS_0);
        staOrderMapper.updateById(staOrder);
        //更新任务申请人数
        StaWork staWork = staWorkMapper.selectById(staOrder.getStaWorkId());
        LambdaQueryWrapper<StaOrder> query = new LambdaQueryWrapper<StaOrder>();
        query.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        query.eq(StaOrder::getStaWorkId, staWork.getId());
        query.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2);
        Integer integer = staOrderMapper.selectCount(query);
        staWork.setApplyNum(integer);
        staWorkMapper.updateById(staWork);
        //发送系统通知
        SysAnnouncement sysAnnouncement = new SysAnnouncement();
        sysAnnouncement.setUserIds(staOrder.getSysUserId() + ",");//推送用户
        String msgAbstract = "用户:" + staOrder.getEnrollName() + ",成功撤销了<" + staOrder.getWorkName() + ">";
        sysAnnouncement.setMsgAbstract(msgAbstract);
        String title = "撤销申请";
        sysAnnouncement.setTitile(title);
        sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
        sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
        sysAnnouncement.setPriority(CommonConstant.PRIORITY_L);//优先级低
        sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
        sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
        sysAnnouncement.setSendTime(new Date());
        sysAnnouncement.setSender(staOrder.getUpdateBy());
        sysAnnouncement.setCreateBy(staOrder.getUpdateBy());
        sysAnnouncement.setCreateTime(new Date());
        sysAnnouncementService.saveAnnouncement(sysAnnouncement);
    }

    /**
     * @description: 批量点名
     * <AUTHOR>
     * @date 2021/11/23 10:02
     * @version 1.0
     */
    @Override
    @Transactional
    public void adminBatchRollCall(List<StaOrder> staOrderList) {
        for (StaOrder so : staOrderList) {
//			StaOrder staOrder = staOrderMapper.selectById(so.getId());
            so.setRcStateFlag(2);//已点名
            so.setRollCallFlag(1);//点名
            so.setRollCallTime(new Date());
            staOrderMapper.updateById(so);
            //发送系统通知
            SysAnnouncement sysAnnouncement = new SysAnnouncement();
            sysAnnouncement.setUserIds(so.getSysUserId() + ",");//推送用户
            String msgAbstract = "用户:" + so.getEnrollName() + ",在<" + so.getWorkName() + ">任务中完成点名";
            sysAnnouncement.setMsgAbstract(msgAbstract);
            String title = "任务点名";
            sysAnnouncement.setTitile(title);
            sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
            sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
            sysAnnouncement.setPriority(CommonConstant.PRIORITY_T);//优先级
            sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
            sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
            sysAnnouncement.setSendTime(new Date());
            sysAnnouncement.setSender(so.getUpdateBy());
            sysAnnouncement.setCreateBy(so.getUpdateBy());
            sysAnnouncement.setCreateTime(new Date());
            sysAnnouncementService.saveAnnouncement(sysAnnouncement);
        }
    }

    /**
     * @description: 批量审核拒绝
     * <AUTHOR>
     * @date 2021/11/23 10:02
     * @version 1.0
     */
    @Override
    @Transactional
    public void adminBatchReject(List<StaOrder> staOrderList) {
        for (StaOrder so : staOrderList) {
//			StaOrder staOrder = staOrderMapper.selectById(so.getId());
            so.setStateFlag(CommonConstant.ORDER_STATUS_6);//未通过审核
            staOrderMapper.updateById(so);
            //发送系统通知
            SysAnnouncement sysAnnouncement = new SysAnnouncement();
            sysAnnouncement.setUserIds(so.getSysUserId() + ",");//推送用户
            String msgAbstract = "" + so.getEnrollName() + "，抱歉您申请的任务<" + so.getWorkName() + ">被拒绝！";
            sysAnnouncement.setMsgAbstract(msgAbstract);
            String title = "未通过";
            sysAnnouncement.setTitile(title);
            sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
            sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
            sysAnnouncement.setPriority(CommonConstant.PRIORITY_F);//优先级
            sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
            sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
            sysAnnouncement.setSendTime(new Date());
            sysAnnouncement.setSender(so.getUpdateBy());
            sysAnnouncement.setCreateBy(so.getUpdateBy());
            sysAnnouncement.setCreateTime(new Date());
            sysAnnouncementService.saveAnnouncement(sysAnnouncement);
        }
    }

    /**
     * @description: 批量剔除已通过人员
     * <AUTHOR>
     * @date 2021/11/23 10:02
     * @version 1.0
     */
    @Override
    @Transactional
    public void adminBatchEliminate(List<StaOrder> staOrderList) {
        for (StaOrder so : staOrderList) {
//			StaOrder staOrder = staOrderMapper.selectById(so.getId());
            so.setStateFlag(CommonConstant.ORDER_STATUS_7);//通过审核后被剔除
            staOrderMapper.updateById(so);
            StaWork staWork = staWorkMapper.selectById(so.getStaWorkId());
            staWork.setAdoptNum(staWork.getAdoptNum() > 0 ?  staWork.getAdoptNum() - 1 : 0);
            staWorkMapper.updateById(staWork);
            //发送系统通知
            SysAnnouncement sysAnnouncement = new SysAnnouncement();
            sysAnnouncement.setUserIds(so.getSysUserId() + ",");//推送用户
            String msgAbstract = "" + so.getEnrollName() + "，抱歉将您从<" + so.getWorkName() + ">任务中被移除，如有疑问请联系任务负责人！";
            sysAnnouncement.setMsgAbstract(msgAbstract);
            String title = "被移除";
            sysAnnouncement.setTitile(title);
            sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
            sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
            sysAnnouncement.setPriority(CommonConstant.PRIORITY_F);//优先级
            sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
            sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
            sysAnnouncement.setSendTime(new Date());
            sysAnnouncement.setSender(so.getUpdateBy());
            sysAnnouncement.setCreateBy(so.getUpdateBy());
            sysAnnouncement.setCreateTime(new Date());
            sysAnnouncementService.saveAnnouncement(sysAnnouncement);
        }
    }
    @Data
    class WxMssVO {
        private String touser;//用户openid
        private String template_id;//订阅消息模版id
        private String page = "pages/index/index";//默认跳到小程序首页
        private Map<String, String> data;//推送文字
    }
    /**
     * @description: 批量审核通过
     * <AUTHOR>
     * @date 2021/11/23 10:02
     * @version 1.0
     */
    @Override
    @Transactional
    public void adminBatchAdopt(List<StaOrder> staOrderList) throws ParseException {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> oids = new ArrayList<>();
        for (StaOrder so : staOrderList) {
            oids.add(so.getId());
            so.setExpirationTime(null);
            StaWork staWork = staWorkMapper.selectById(so.getStaWorkId());
            so.setStateFlag(CommonConstant.ORDER_STATUS_3);//服务中
            so.setEntryDate(new Date());
            //结算标准
            if (WxlConvertUtils.isNotEmpty(staWork.getWorkSalary())) {
                so.setHourlySalary(staWork.getWorkSalary());
            }
            staOrderMapper.updateById(so);
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
            //添加排班
            if (WxlConvertUtils.isNotEmpty(so.getStaTaskDateId())) {
                String staTaskDateId = so.getStaTaskDateId();
                StaTaskDate staTaskDate = staTaskDateMapper.selectById(staTaskDateId);
                staTaskDate.setRemainingNum(staTaskDate.getRemainingNum() - 1);
                staTaskDateMapper.updateById(staTaskDate);
                Date taskStartDate = staTaskDate.getTaskStartDate(); //任务开始日期
                Date taskEndDate = staTaskDate.getTaskEndDate();//任务结束日期
                String shiftCode = staTaskDate.getShiftCode();
                List<StaScheduleNo> staScheduleNos = staScheduleNoMapper.selectList(new LambdaQueryWrapper<StaScheduleNo>().eq(StaScheduleNo::getCode, shiftCode));
                List<String> dateRange = DateUtil.getDateRange(taskStartDate, taskEndDate);
                for (String date : dateRange) {
                    for (StaScheduleNo staScheduleNo : staScheduleNos) {
                        StaSchedule staSchedule = new StaSchedule();
                        staSchedule.setCreateBy(user.getId()); //创建人
                        staSchedule.setCreateTime(new Date());
                        staSchedule.setStaOrderId(so.getId());
                        staSchedule.setStaWorkId(staWork.getId());
                        staSchedule.setCode(shiftCode);
                        staSchedule.setStartTime(staScheduleNo.getStartTime());
                        staSchedule.setEndTime(staScheduleNo.getEndTime());
                        staSchedule.setDelFlag(CommonConstant.DEL_FLAG_0);
                        staSchedule.setCompanyName(staWork.getCompanyName());
                        staSchedule.setOrgCode(staWork.getSysOrgCode());
                        staSchedule.setNameFull(staWork.getNameFull());
                        staSchedule.setRegionName(staWork.getStoreBusinessDivisionName()); //区域
                        staSchedule.setBusinessDivisionName(staWork.getStoreRegionName());//事业处
                        staSchedule.setRealName(so.getEnrollName());//姓名
                        staSchedule.setScheduleDay(sd.parse(date)); //排班日期
                        staSchedule.setUserIdCard(so.getEnrollIdCard());//身份证
                        staSchedule.setSysUserId(so.getSysUserId());
                        staSchedule.setStoreNo(so.getStoreNo());
                        staScheduleMapper.insert(staSchedule);
                    }
                }
            }
            staWork.setAdoptNum(staWork.getAdoptNum() + 1);
            staWorkMapper.updateById(staWork);
            SysUser sysUser = sysUserMapper.selectById(so.getSysUserId());
            sysUser.setCurrentStoresId(so.getCompanyId());
            sysUser.setCurrentStores(so.getCompanyName());
            sysUser.setCurrentWorkName(so.getWorkNameFull());
            sysUser.setStaOrderId(so.getId());
            sysUserMapper.updateById(sysUser);
            //通过当前任务后 其他申请中的任务单失效
            String sysUserId = so.getSysUserId();
            List<StaOrder> staOrders = staOrderMapper.selectList(new LambdaQueryWrapper<StaOrder>().eq(StaOrder::getSysUserId, sysUserId).eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2));
            for (StaOrder staOrder : staOrders) {
                staOrder.setStateFlag(CommonConstant.ORDER_STATUS_0);
                staOrderMapper.updateById(staOrder);
                //更新申请人数
                String staWorkId = staOrder.getStaWorkId();
                StaWork work = staWorkMapper.selectById(staWorkId);
                LambdaQueryWrapper<StaOrder> query = new LambdaQueryWrapper<StaOrder>();
                query.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
                query.eq(StaOrder::getStaWorkId, work.getId());
                query.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2);
                Integer integer = staOrderMapper.selectCount(query);
                work.setApplyNum(integer);
                staWorkMapper.updateById(work);
            }
        }
    }
    /**
     * @description: 创建订单 （订单报名表融入订单表）
     * <AUTHOR>
     * @date 2021/11/22 14:11
     * @version 1.0
     */
    @Override
    @Transactional
    public Boolean createOrder(StaOrder staOrder, Integer adoptNum) {
        String staWorkId = staOrder.getStaWorkId();
        StaWork staWork = staWorkMapper.selectById(staWorkId);
        Integer examineFlag = staWork.getExamineFlag();//是否需要审核
        SysDepart sysDepart = sysDepartMapper.selectById(staWork.getCompanyId());
        staOrder.setDelFlag(CommonConstant.DEL_FLAG_0);
        //0则不限制招聘人数
        if (staWork.getExpectNum() != 0 && 0 >= staWork.getExpectNum().compareTo(staWork.getAdoptNum())) {
            return false;
        }
        //TODO 增加 签到、点名状态
        if (0 == examineFlag || CommonConstant.ENROLL_TYPE_3.equals(staOrder.getEnrollType())) {//无需审核，补录的人也无需审核
            staOrder.setStateFlag(CommonConstant.ORDER_STATUS_5);//待就职
        } else if (1 == examineFlag) {//需审核
            staOrder.setStateFlag(CommonConstant.ORDER_STATUS_2);//待审核
        }
        //TODO 订单编码
        staOrder.setOrderCode(sysDepart.getOrgCode() + createOrderCode());
        staOrder.setOrderDate(new Date());
        //事业处  区域
        SysDepart sysDepart1 = sysDepartMapper.selectById(staOrder.getCompanyId()); //当前店铺
        SysDepart sysDepart2 = null;
        SysDepart sysDepart3 = null;
        if (sysDepart1.getParentId() != null) {
            sysDepart2 = sysDepartMapper.selectById(sysDepart1.getParentId()); //区域
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart2)) {
            staOrder.setRegionId(sysDepart2.getId());
            staOrder.setRegionName(sysDepart2.getDepartName());
            sysDepart3 = sysDepartMapper.selectById(sysDepart2.getParentId());//事业处
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart3)) {
            staOrder.setBusinessDivisionId(sysDepart3.getId());
            staOrder.setBusinessDivisionName(sysDepart3.getDepartName());
        }
        staOrderMapper.insert(staOrder);
        //更新通过人数 申请人数 报名人数
        LambdaQueryWrapper<StaOrder> queryAdop = new LambdaQueryWrapper<StaOrder>();
        queryAdop.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryAdop.eq(StaOrder::getStaWorkId, staWork.getId());
        /** 状态0撤销1完成2申请中3工作中4待结算中5待就职6未通过审核7通过审核后被剔除8任务完成 9 申请已过期 10离职申请 11 离职审核通过 12 未通过*/
        queryAdop.in(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_3, CommonConstant.ORDER_STATUS_8));
        Integer adopCount = staOrderMapper.selectCount(queryAdop);
        LambdaQueryWrapper<StaOrder> queryApply = new LambdaQueryWrapper<StaOrder>();
        queryApply.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryApply.eq(StaOrder::getStaWorkId, staWork.getId());
        queryApply.eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_2);
        Integer applyCount = staOrderMapper.selectCount(queryApply);
        staWork.setAdoptNum(adopCount);//通过人数
        staWork.setApplyNum(applyCount);//申请人数
        staWorkMapper.updateById(staWork);//更新报名人数，更新通过人数
        //发送系统通知
        SysAnnouncement sysAnnouncement = new SysAnnouncement();
        sysAnnouncement.setUserIds(staOrder.getSysUserId() + ",");//推送用户
        String msgAbstract = "";
        String title = "";
        if (0 == examineFlag) {//无需审核
            msgAbstract = "" + staOrder.getEnrollName() + "，您申请" +
                    "的<" + staOrder.getWorkName() + ">任务已通过审核，";
            if (WxlConvertUtils.isNotEmpty(staOrder.getReportDate())) {
                msgAbstract += "请您在" + DateUtils.formatTime(staOrder.getReportDate()) + "前";
            }
            msgAbstract += "，到达" + staOrder.getAddressName() + ",报道联系人:" + staOrder.getReportLiaison() + ",报道联系人电话:" + staOrder.getReportLiaisonTp() + "！";
            title = "审核通过";
        } else {
            msgAbstract = "" + staOrder.getEnrollName() + "，您申请的<" + staOrder.getWorkName() + ">任务正在审核，请稍等！";
            title = "审核中";
        }
        sysAnnouncement.setMsgAbstract(msgAbstract);
        sysAnnouncement.setTitile(title);
        sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
        sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
        sysAnnouncement.setPriority(CommonConstant.PRIORITY_T);//优先级
        sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
        sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
        sysAnnouncement.setSendTime(new Date());
        sysAnnouncement.setSender(staOrder.getCreateBy());
        sysAnnouncement.setCreateBy(staOrder.getCreateBy());
        sysAnnouncement.setCreateTime(new Date());
        sysAnnouncementService.saveAnnouncement(sysAnnouncement);
        return true;
    }
    /**
     * 生成随机文件名：当前年月日时分秒+五位随机数   *   * @return
     */
    private String createOrderCode() {
        SimpleDateFormat simpleDateFormat;
        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        String str = simpleDateFormat.format(date);
        Random random = new Random();
        int rannum = (int) (random.nextDouble() * (99999 - 10000 + 1)) + 10000;// 获取5位随机数
        return str + rannum;// 当前时间  }
    }
    @Override
    @Transactional
    public void saveMain(StaOrder staOrder, List<StaLocationClock> staLocationClockList) {
        staOrderMapper.insert(staOrder);
        if (staLocationClockList != null && staLocationClockList.size() > 0) {
            for (StaLocationClock entity : staLocationClockList) {
                //外键设置
                entity.setStaOrderId(staOrder.getId());
                staLocationClockMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateMain(StaOrder staOrder, List<StaLocationClock> staLocationClockList) {
        staOrderMapper.updateById(staOrder);

        //1.先删除子表数据
        staLocationClockMapper.deleteByMainId(staOrder.getId());

        //2.子表数据重新插入
        if (staLocationClockList != null && staLocationClockList.size() > 0) {
            for (StaLocationClock entity : staLocationClockList) {
                //外键设置
                entity.setStaOrderId(staOrder.getId());
                staLocationClockMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional
    public void delMain(String id) {
        staLocationClockMapper.deleteByMainId(id);
        staOrderMapper.deleteById(id);
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        // 查询任务单列表
        List<StaOrder> staOrderList = staOrderService.list(new QueryWrapper<StaOrder>().lambda().in(StaOrder::getId, idList));
        if (staOrderList.isEmpty()) {
            return; // 没有要删除的订单，直接返回
        }
        // 获取任务对应的工作对象
        StaWork staWork = staWorkMapper.selectById(staOrderList.get(0).getStaWorkId());
        if (staWork != null) {
            // 删除任务通过人数缓存
            String cacheKey = CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId();
            redisUtil.del(cacheKey);
            staWork.setAdoptNum(staWork.getAdoptNum() - staOrderList.size());
            staWorkMapper.updateById(staWork);
            redisUtil.set(cacheKey, staWork.getAdoptNum()); // 添加缓存
        }
        // 统计每个 StaTaskDateId 的数量
        Map<String, Long> staTaskDateIdCountMap = staOrderList.stream()
                .collect(Collectors.groupingBy(StaOrder::getStaTaskDateId, Collectors.counting()));
        // 查询相关的 StaTaskDate 对象
        List<StaTaskDate> staTaskDateList = staTaskDateMapper.selectBatchIds(staTaskDateIdCountMap.keySet());
        // 更新 StaTaskDate 的 adoptNum
        staTaskDateList.forEach(staTaskDate -> {
            Long adoptNum = staTaskDateIdCountMap.get(staTaskDate.getId());
            if (adoptNum != null) {
                staTaskDate.setAdoptNum(staTaskDate.getAdoptNum() - adoptNum.intValue());
                staTaskDateMapper.updateById(staTaskDate);
            }
        });
        // 批量删除任务单
        staOrderMapper.deleteBatchIds(idList);
        // 查询并删除相关排班
        List<StaSchedule> staSchedules = staScheduleMapper.selectList(new LambdaQueryWrapper<StaSchedule>()
                .in(StaSchedule::getStaOrderId, idList));
        if (!staSchedules.isEmpty()) {
            // 删除排班
            staScheduleMapper.deleteBatchIds(staSchedules.stream().map(StaSchedule::getId).collect(Collectors.toList()));
            // 删除签到
            staLocationClockMapper.delete(new LambdaQueryWrapper<StaLocationClock>()
                    .in(StaLocationClock::getStaScheduleId, staSchedules.stream().map(StaSchedule::getId).collect(Collectors.toList())));
        }
    }

    /**
     * 计算两个 Date 类型之间的时间差（以分钟为单位）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public long dateDifference(Date startDate, Date endDate) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(startDate);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(endDate);
        long timeDifferenceInMillis = cal2.getTimeInMillis() - cal1.getTimeInMillis();
        long minutesDifference = timeDifferenceInMillis / (60 * 1000);
        return minutesDifference;
    }

    /***
     *
     * @param wid 任务ID
     * @param realName 姓名
     * @param userIdCard 身份证
     * @param stateFlag 状态
     * @return List<StaLocationClock>
     */
    @Override
    public Page<StaLocationClock> getPunchRecord(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd, String userIdCard, String stateFlag, String deptName, String deptNo, String order) throws ParseException {
        // 开始Service层耗时统计
        long serviceStartTime = System.currentTimeMillis();
        log.info("=== getPunchRecord Service方法开始执行 ===");
        try {
            // 1. 用户获取耗时统计
            long userStartTime = System.currentTimeMillis();
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            long userEndTime = System.currentTimeMillis();
            log.info("用户获取耗时：{}ms", (userEndTime - userStartTime));

            // 使用传入的pageNo和pageSize参数创建分页对象
            Page<StaLocationClock> page = new Page<>(pageNo, pageSize);
            List<StaSchedule> staSchedules = new ArrayList<>();
            List<String> meUserIds = new ArrayList<>();

            // 2. 获取排班数据耗时统计
            long scheduleStartTime = System.currentTimeMillis();
            staSchedules = getSchedulesForUser(sysUser, pageNo, pageSize, wid, businessDivisionName, regionName, realName, dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo);
            long scheduleEndTime = System.currentTimeMillis();
            log.info("获取排班数据耗时：{}ms，排班记录数：{}", (scheduleEndTime - scheduleStartTime), staSchedules.size());

            if (staSchedules.isEmpty()) {
                log.info("未找到排班数据，直接返回空结果");
                return page;
            }

            // 3. 获取签到记录耗时统计
            long clockStartTime = System.currentTimeMillis();
            List<String> scheduleIds = staSchedules.stream().map(StaSchedule::getId).collect(Collectors.toList());
            List<StaLocationClock> staLocationClocks = getStaLocationClocks(sysUser, meUserIds, scheduleIds);
            long clockEndTime = System.currentTimeMillis();
            log.info("获取签到记录耗时：{}ms，签到记录数：{}", (clockEndTime - clockStartTime), staLocationClocks.size());

            // 4. 创建模板耗时统计
            long templateStartTime = System.currentTimeMillis();
            List<lcDayPage> ldp = createScheduleTemplate(staSchedules);
            List<lcDayPage> lcDayPages = addScheduleTemplate(ldp, staLocationClocks);
            long templateEndTime = System.currentTimeMillis();
            log.info("创建和填充模板耗时：{}ms，模板数量：{}", (templateEndTime - templateStartTime), lcDayPages.size());

            // 5. 数据处理和排序耗时统计
            long processStartTime = System.currentTimeMillis();
            List<StaLocationClock> clockInCollection = addClockAndUserInfoAndSort(lcDayPages, order);
            long processEndTime = System.currentTimeMillis();
            log.info("数据处理和排序耗时：{}ms，处理后记录数：{}", (processEndTime - processStartTime), clockInCollection.size());

            // 6. 状态筛选耗时统计
            long filterStartTime = System.currentTimeMillis();
            clockInCollection = filterByStateFlag(clockInCollection, stateFlag);
            long filterEndTime = System.currentTimeMillis();
            log.info("状态筛选耗时：{}ms，筛选后记录数：{}", (filterEndTime - filterStartTime), clockInCollection.size());

            if (clockInCollection.isEmpty()) {
                log.info("筛选后无数据，返回空结果");
                page.setTotal(0);
                page.setRecords(clockInCollection);
                return page;
            }

            // 7. 分页处理耗时统计
            long pageStartTime = System.currentTimeMillis();
            List<StaLocationClock> pageData = paginate(clockInCollection, pageNo, pageSize);
            long pageEndTime = System.currentTimeMillis();
            log.info("分页处理耗时：{}ms，分页后记录数：{}", (pageEndTime - pageStartTime), pageData.size());

            // 8. 附加数据设置耗时统计
            long additionalStartTime = System.currentTimeMillis();
            setAdditionalShiftCodeData(pageData);
            long additionalEndTime = System.currentTimeMillis();
            log.info("附加数据设置耗时：{}ms", (additionalEndTime - additionalStartTime));

            // 9. 结果构建耗时统计
            long resultStartTime = System.currentTimeMillis();
            page.setTotal(clockInCollection.size());
            page.setRecords(pageData);
            long resultEndTime = System.currentTimeMillis();
            log.info("结果构建耗时：{}ms", (resultEndTime - resultStartTime));

            // Service层总耗时统计
            long serviceTotalTime = System.currentTimeMillis() - serviceStartTime;

            // 计算各阶段耗时
            long userTime = userEndTime - userStartTime;
            long scheduleTime = scheduleEndTime - scheduleStartTime;
            long clockTime = clockEndTime - clockStartTime;
            long templateTime = templateEndTime - templateStartTime;
            long processTime = processEndTime - processStartTime;
            long filterTime = filterEndTime - filterStartTime;
            long pageTime = pageEndTime - pageStartTime;
            long additionalTime = additionalEndTime - additionalStartTime;
            long resultTime = resultEndTime - resultStartTime;

            log.info("=== getPunchRecord Service方法执行完成 ===");
            log.info("Service层总耗时：{}ms", serviceTotalTime);

            // 详细的性能统计报告
            log.info("=== 详细性能统计报告 ===");
            log.info("📊 各方法耗时统计（包含数据获取）：");
            log.info("┌─────────────────────────────────────────────────────────────┐");
            log.info("│ 方法名称                    │ 耗时(ms) │ 占比(%) │ 数据量     │");
            log.info("├─────────────────────────────────────────────────────────────┤");
            log.info("│ 1. 用户获取                 │ {}ms │ {}% │ 1条        │", String.format("%8d", userTime), String.format("%.1f", userTime * 100.0 / serviceTotalTime));
            log.info("│ 2. 排班数据查询             │ {}ms │ {}% │ {}条    │", String.format("%8d", scheduleTime), String.format("%.1f", scheduleTime * 100.0 / serviceTotalTime), staSchedules.size());
            log.info("│ 3. 签到记录查询             │ {}ms │ {}% │ {}条    │", String.format("%8d", clockTime), String.format("%.1f", clockTime * 100.0 / serviceTotalTime), staLocationClocks.size());
            log.info("│ 4. 模板创建和填充           │ {}ms │ {}% │ {}条    │", String.format("%8d", templateTime), String.format("%.1f", templateTime * 100.0 / serviceTotalTime), lcDayPages.size());
            log.info("│ 5. 数据处理和排序           │ {}ms │ {}% │ {}条    │", String.format("%8d", processTime), String.format("%.1f", processTime * 100.0 / serviceTotalTime), clockInCollection.size());
            log.info("│ 6. 状态筛选                 │ {}ms │ {}% │ {}条    │", String.format("%8d", filterTime), String.format("%.1f", filterTime * 100.0 / serviceTotalTime), clockInCollection.size());
            log.info("│ 7. 分页处理                 │ {}ms │ {}% │ {}条    │", String.format("%8d", pageTime), String.format("%.1f", pageTime * 100.0 / serviceTotalTime), pageData.size());
            log.info("│ 8. 附加数据设置             │ {}ms │ {}% │ {}条    │", String.format("%8d", additionalTime), String.format("%.1f", additionalTime * 100.0 / serviceTotalTime), pageData.size());
            log.info("│ 9. 结果构建                 │ {}ms │ {}% │ 1条        │", String.format("%8d", resultTime), String.format("%.1f", resultTime * 100.0 / serviceTotalTime));
            log.info("├─────────────────────────────────────────────────────────────┤");
            log.info("│ 总计                        │ {}ms │ {}% │ -          │", String.format("%8d", serviceTotalTime), "100.0");
            log.info("└─────────────────────────────────────────────────────────────┘");

            // 数据获取性能分析
            long dataFetchTime = scheduleTime + clockTime;
            log.info("📈 数据获取性能分析：");
            log.info("• 数据获取总耗时：{}ms（占总耗时的{}%）", dataFetchTime, String.format("%.1f", dataFetchTime * 100.0 / serviceTotalTime));
            log.info("• 排班数据获取：{}ms，平均每条{}ms", scheduleTime, String.format("%.2f", staSchedules.isEmpty() ? 0 : (scheduleTime * 1.0 / staSchedules.size())));
            log.info("• 签到数据获取：{}ms，平均每条{}ms", clockTime, String.format("%.2f", staLocationClocks.isEmpty() ? 0 : (clockTime * 1.0 / staLocationClocks.size())));

            // 数据处理性能分析
            long dataProcessTime = templateTime + processTime + filterTime;
            log.info("⚙️ 数据处理性能分析：");
            log.info("• 数据处理总耗时：{}ms（占总耗时的{}%）", dataProcessTime, String.format("%.1f", dataProcessTime * 100.0 / serviceTotalTime));
            log.info("• 模板处理效率：{}ms，平均每条{}ms", templateTime, String.format("%.2f", lcDayPages.isEmpty() ? 0 : (templateTime * 1.0 / lcDayPages.size())));
            log.info("• 数据处理效率：{}ms，平均每条{}ms", processTime, String.format("%.2f", clockInCollection.isEmpty() ? 0 : (processTime * 1.0 / clockInCollection.size())));

            // 性能瓶颈分析
            log.info("🔍 性能瓶颈分析：");
            long maxTime = Math.max(Math.max(scheduleTime, clockTime), Math.max(templateTime, processTime));
            if (maxTime == scheduleTime) {
                log.info("• 主要瓶颈：排班数据查询（{}ms），建议优化数据库查询或添加索引", scheduleTime);
            } else if (maxTime == clockTime) {
                log.info("• 主要瓶颈：签到记录查询（{}ms），建议优化查询条件或批量处理", clockTime);
            } else if (maxTime == templateTime) {
                log.info("• 主要瓶颈：模板处理（{}ms），建议优化模板创建算法", templateTime);
            } else if (maxTime == processTime) {
                log.info("• 主要瓶颈：数据处理（{}ms），建议优化排序算法或减少数据转换", processTime);
            }

            // 总体性能评级
            if (serviceTotalTime > 2000) {
                log.warn("⚠️ Service层响应时间较慢：{}ms，建议优化", serviceTotalTime);
                log.warn("🔧 优化建议：重点关注数据获取（{}ms）和数据处理（{}ms）环节", dataFetchTime, dataProcessTime);
            } else if (serviceTotalTime > 1000) {
                log.info("⚡ Service层响应时间：{}ms，性能良好", serviceTotalTime);
            } else {
                log.info("🚀 Service层响应时间：{}ms，性能优秀", serviceTotalTime);
            }

            log.info("=== 性能统计报告结束 ===");

            return page;

        } catch (Exception e) {
            long serviceTotalTime = System.currentTimeMillis() - serviceStartTime;
            log.error("❌ getPunchRecord Service方法执行异常，总耗时：{}ms", serviceTotalTime, e);
            throw e;
        }
    }
    // 获取排班数据
    private List<StaSchedule> getSchedulesForUser(LoginUser sysUser, Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd, String userIdCard, String stateFlag, String deptName, String deptNo) throws ParseException {
        long methodStartTime = System.currentTimeMillis();
        log.debug("getSchedulesForUser方法开始执行，用户组织代码：{}", sysUser.getOrgCode());

        List<StaSchedule> staSchedules = new ArrayList<>();

        if (sysUser.getOrgCode().startsWith("A03")) {
            // A03组织直接查询
            long directQueryStartTime = System.currentTimeMillis();
            staSchedules = this.getSchedules(pageNo, pageSize, wid, businessDivisionName, regionName, realName, dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, null);
            long directQueryEndTime = System.currentTimeMillis();
            log.debug("A03组织直接查询耗时：{}ms，结果数量：{}", (directQueryEndTime - directQueryStartTime), staSchedules.size());
        } else {
            // 其他组织需要先获取用户关系
            long userQueryStartTime = System.currentTimeMillis();
            List<SysUser> sysUsers = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getOrgCode, sysUser.getOrgCode())
                    .eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0));
            long userQueryEndTime = System.currentTimeMillis();
            log.debug("查询组织用户耗时：{}ms，用户数量：{}", (userQueryEndTime - userQueryStartTime), sysUsers.size());
            long userRelQueryStartTime = System.currentTimeMillis();
            List<String> uidList = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
            List<SysUserRel> userRels = sysUserRelMapper.selectList(new LambdaQueryWrapper<SysUserRel>()
                    .in(SysUserRel::getYouId, uidList));
            List<String> meUserIds = userRels.stream().map(SysUserRel::getMeId).collect(Collectors.toList());
            long userRelQueryEndTime = System.currentTimeMillis();
            log.debug("查询用户关系耗时：{}ms，关系数量：{}，被推广人数量：{}",
                    (userRelQueryEndTime - userRelQueryStartTime), userRels.size(), meUserIds.size());
            long scheduleQueryStartTime = System.currentTimeMillis();
            staSchedules = this.getSchedules(pageNo, pageSize, wid, businessDivisionName, regionName, realName, dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, meUserIds);
            long scheduleQueryEndTime = System.currentTimeMillis();
            log.debug("根据用户关系查询排班耗时：{}ms，结果数量：{}", (scheduleQueryEndTime - scheduleQueryStartTime), staSchedules.size());
        }

        long methodTotalTime = System.currentTimeMillis() - methodStartTime;
        log.debug("getSchedulesForUser方法执行完成，总耗时：{}ms", methodTotalTime);

        return staSchedules;
    }
    // 批量获取签到记录
    private List<StaLocationClock> getStaLocationClocks(LoginUser sysUser, List<String> meUserIds, List<String> scheduleIds) {
        long methodStartTime = System.currentTimeMillis();
        log.debug("getStaLocationClocks方法开始执行，scheduleIds数量：{}，meUserIds数量：{}", scheduleIds.size(), meUserIds.size());

        long queryBuildStartTime = System.currentTimeMillis();
        LambdaQueryWrapper<StaLocationClock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);

        // 🚀 注意：不限制数据量，直接查询全部签到记录
        log.info("🚀 查询全部签到记录，排班ID数量：{}", scheduleIds.size());

        // 🔧 修复：签到记录查询应该基于排班ID，不需要额外的用户限制
        queryWrapper.in(StaLocationClock::getStaScheduleId, scheduleIds);
        log.debug("使用排班ID列表查询签到记录，排班数量：{}", scheduleIds.size());

        // 🔧 如果有用户权限限制，才添加用户条件
        if (meUserIds != null && !meUserIds.isEmpty()) {
            queryWrapper.in(StaLocationClock::getSysUserId, meUserIds);
            log.debug("添加用户ID限制，用户数量：{}", meUserIds.size());
        } else {
            // 🔧 使用组织代码作为备用条件
            queryWrapper.likeRight(StaLocationClock::getOrgCode, sysUser.getOrgCode());
            log.debug("使用组织代码查询：{}", sysUser.getOrgCode());
        }
        long queryBuildEndTime = System.currentTimeMillis();
        log.debug("查询条件构建耗时：{}ms", (queryBuildEndTime - queryBuildStartTime));

        long dbQueryStartTime = System.currentTimeMillis();

        // 🚀 优化查询策略：如果排班ID过多，使用分批查询
        List<StaLocationClock> result;
        if (scheduleIds.size() > 5000) {
            log.info("🚀 排班ID数量较多（{}），使用分批查询优化", scheduleIds.size());
            result = queryClockInBatches(scheduleIds, meUserIds, sysUser);
        } else {
            result = staLocationClockService.list(queryWrapper);
        }

        long dbQueryEndTime = System.currentTimeMillis();
        long clockQueryTime = dbQueryEndTime - dbQueryStartTime;
        log.debug("数据库查询耗时：{}ms，查询结果数量：{}", clockQueryTime, result.size());

        // 🚀 如果签到查询时间过长，记录详细信息
        if (clockQueryTime > 1500) {
            log.warn("⚠️ 签到查询耗时过长：{}ms，排班ID数量：{}，结果数：{}",
                    clockQueryTime, scheduleIds.size(), result.size());
            log.warn("⚠️ 建议优化签到表索引：sta_schedule_id, del_flag, org_code");
        }

        long methodTotalTime = System.currentTimeMillis() - methodStartTime;
        log.debug("getStaLocationClocks方法执行完成，总耗时：{}ms", methodTotalTime);

        return result;
    }

    /**
     * 🚀 分批查询签到记录 - 优化大数据量查询
     */
    private List<StaLocationClock> queryClockInBatches(List<String> scheduleIds, List<String> meUserIds, LoginUser sysUser) {
        List<StaLocationClock> allResults = new ArrayList<>();
        int batchSize = 2000; // 每批查询2000个ID

        for (int i = 0; i < scheduleIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, scheduleIds.size());
            List<String> batchIds = scheduleIds.subList(i, endIndex);

            long batchStart = System.currentTimeMillis();
            LambdaQueryWrapper<StaLocationClock> batchWrapper = new LambdaQueryWrapper<>();
            batchWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
            batchWrapper.in(StaLocationClock::getStaScheduleId, batchIds);

            if (meUserIds != null && !meUserIds.isEmpty()) {
                batchWrapper.in(StaLocationClock::getSysUserId, meUserIds);
            } else {
                batchWrapper.likeRight(StaLocationClock::getOrgCode, sysUser.getOrgCode());
            }

            List<StaLocationClock> batchResult = staLocationClockService.list(batchWrapper);
            allResults.addAll(batchResult);

            long batchEnd = System.currentTimeMillis();
            log.debug("🚀 分批查询第{}批，ID数量：{}，耗时：{}ms，结果数：{}",
                    (i / batchSize + 1), batchIds.size(), (batchEnd - batchStart), batchResult.size());
        }

        log.info("🚀 分批查询完成，总批次：{}，总结果数：{}",
                (scheduleIds.size() + batchSize - 1) / batchSize, allResults.size());
        return allResults;
    }



    // 筛选签到状态
    private List<StaLocationClock> filterByStateFlag(List<StaLocationClock> clockInCollection, String stateFlag) {
        long methodStartTime = System.currentTimeMillis();
        log.debug("filterByStateFlag方法开始执行，输入记录数：{}，筛选状态：{}", clockInCollection.size(), stateFlag);

        List<StaLocationClock> result;
        if (WxlConvertUtils.isNotEmpty(stateFlag)) {
            long filterStartTime = System.currentTimeMillis();
            result = clockInCollection.stream().filter(f ->
                    (f.getClockInStatus1() != null && f.getClockInStatus1().equals(Integer.valueOf(stateFlag)))
                            || (f.getClockInStatus2() != null && f.getClockInStatus2().equals(Integer.valueOf(stateFlag)))
                            || (f.getClockInStatus3() != null && f.getClockInStatus3().equals(Integer.valueOf(stateFlag)))
                            || (f.getClockInStatus4() != null && f.getClockInStatus4().equals(Integer.valueOf(stateFlag)))
            ).collect(Collectors.toList());
            long filterEndTime = System.currentTimeMillis();
            log.debug("状态筛选处理耗时：{}ms，筛选后记录数：{}", (filterEndTime - filterStartTime), result.size());
        } else {
            result = clockInCollection;
            log.debug("无需筛选，直接返回原记录");
        }

        long methodTotalTime = System.currentTimeMillis() - methodStartTime;
        log.debug("filterByStateFlag方法执行完成，总耗时：{}ms", methodTotalTime);

        return result;
    }
    // 分页处理
    private List<StaLocationClock> paginate(List<StaLocationClock> clockInCollection, Integer pageNo, Integer pageSize) {
        long methodStartTime = System.currentTimeMillis();
        log.debug("paginate方法开始执行，总记录数：{}，页码：{}，页大小：{}", clockInCollection.size(), pageNo, pageSize);

        long calculateStartTime = System.currentTimeMillis();
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, clockInCollection.size());
        long calculateEndTime = System.currentTimeMillis();
        log.debug("分页计算耗时：{}ms，startIndex：{}，endIndex：{}", (calculateEndTime - calculateStartTime), startIndex, endIndex);

        if (startIndex >= clockInCollection.size()) { // 起始索引超出范围
            log.debug("起始索引超出范围，返回空列表");
            return new ArrayList<>();
        }

        long subListStartTime = System.currentTimeMillis();
        List<StaLocationClock> result = clockInCollection.subList(startIndex, endIndex);
        long subListEndTime = System.currentTimeMillis();
        log.debug("子列表创建耗时：{}ms，分页后记录数：{}", (subListEndTime - subListStartTime), result.size());

        long methodTotalTime = System.currentTimeMillis() - methodStartTime;
        log.debug("paginate方法执行完成，总耗时：{}ms", methodTotalTime);

        return result;
    }
    // 根据排班设置时段数据
    private void setAdditionalShiftCodeData(List<StaLocationClock> pageData) {
        Set<String> shiftCodeSet = pageData.stream().map(StaLocationClock::getShiftCode).collect(Collectors.toSet());
        List<StaScheduleNo> staScheduleNos = staScheduleNoMapper.selectList(new LambdaQueryWrapper<StaScheduleNo>()
                .in(StaScheduleNo::getCode, shiftCodeSet)
                .eq(StaScheduleNo::getDelFlag, CommonConstant.DEL_FLAG_0));

        for (StaLocationClock pageDatum : pageData) {
            long count = staScheduleNos.stream().filter(s -> s.getCode().equals(pageDatum.getShiftCode())).count();
            if (count == 1) {
                pageDatum.setClockInTime3(null);
                pageDatum.setClockInTime4(null);
                pageDatum.setClockInStatus3(null);
                pageDatum.setClockInStatus4(null);
            }
        }
    }
    @Transactional
    @Override
    public Boolean agreeVariation(String oid) {
        StaOrder staOrder = staOrderService.getById(oid);
        staOrder.setStateFlag(CommonConstant.ORDER_STATUS_14); //换店成功
        staOrder.setUpdateTime(new Date());
        staOrder.setVariationFlag(1);//换店成功
        String newShopId = staOrder.getNewShopId();//新店铺id
        String newWorkId = staOrder.getNewWorkId();//新工作id
        SysDepart sysDepart = sysDepartMapper.selectById(newShopId);
        //更新入职人数(异动前)
        Integer count = staOrderMapper.selectCount(new LambdaQueryWrapper<StaOrder>()
                .eq(StaOrder::getStaWorkId, staOrder.getStaWorkId())
                .eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_3)
        );
        StaWork staWork1 = staWorkMapper.selectById(staOrder.getStaWorkId());
        staWork1.setAdoptNum(count);
        staWorkMapper.updateById(staWork1);
        StaWork staWork = staWorkMapper.selectById(newWorkId);
        staOrderMapper.updateById(staOrder); //修改当前工作状态
        //新增工作
        StaOrder newStaOrder = new StaOrder();
        //拷贝
        BeanUtils.copyProperties(staOrder, newStaOrder, new String[]{"id", "entryDate", "newShopId", "newShopName",
                "newWorkId", "newWorkName", "variationTime", "variationFlag", "orgCode"}
        );
        newStaOrder.setCreateTime(new Date());
        newStaOrder.setUpdateTime(new Date());
        newStaOrder.setOrderDate(new Date());//下单时间
        newStaOrder.setWorkName(staWork.getNameNick()); //工作简称
        newStaOrder.setStaWorkId(staWork.getId()); //工作id
        newStaOrder.setStateFlag(CommonConstant.ORDER_STATUS_3); //工作中
        newStaOrder.setCompanyName(staWork.getCompanyName()); //门店名称
        newStaOrder.setWorkNameFull(staWork.getNameFull());//工作全称
        newStaOrder.setAddressLat(staWork.getAddressLat());
        newStaOrder.setAddressLng(staOrder.getAddressLng());
        newStaOrder.setAddressName(staWork.getAddressName());//工作地址名称
        newStaOrder.setCompanyId(sysDepart.getId()); //门店id
        newStaOrder.setSysOrgCode(sysDepart.getOrgCode());//订单编码
        newStaOrder.setFirmId(sysDepart.getParentId());
        newStaOrder.setStoNe(sysDepart.getStoreNo());//店编
        //区域   事业处
        SysDepart sysDepart2 = null;
        SysDepart sysDepart3 = null;
        if (sysDepart.getParentId() != null) {
            sysDepart2 = sysDepartMapper.selectById(sysDepart.getParentId()); //区域
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart2)) {
            newStaOrder.setRegionId(sysDepart2.getId());
            newStaOrder.setRegionName(sysDepart2.getDepartName());
            sysDepart3 = sysDepartMapper.selectById(sysDepart2.getParentId());//事业处
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart3)) {
            newStaOrder.setBusinessDivisionId(sysDepart3.getId());
            newStaOrder.setBusinessDivisionName(sysDepart3.getDepartName());
        }
        staOrderMapper.insert(newStaOrder);
        staWork.setAdoptNum(staWork.getAdoptNum() + 1);
        staWorkMapper.updateById(staWork);
        //修改用户信息
        SysUser sysUser = sysUserMapper.selectById(staOrder.getSysUserId());
        sysUser.setCurrentStoresId(newStaOrder.getCompanyId());
        sysUser.setCurrentStores(newStaOrder.getCompanyName());
        sysUser.setCurrentWorkName(newStaOrder.getWorkNameFull());
        sysUser.setStaOrderId(newStaOrder.getId());
        sysUserMapper.updateById(sysUser);
        return true;
    }

    /**
     * @param staOrder 旧订单
     * @param staWork  新工作
     * @return
     */
    @Transactional
    public Boolean agreeVariation(StaOrder staOrder, StaWork staWork) {
        staOrder.setStateFlag(CommonConstant.ORDER_STATUS_14); //换店成功
        staOrder.setUpdateTime(new Date());
        staOrder.setVariationFlag(1);//换店成功
        SysDepart sysDepart = sysDepartMapper.selectById(staWork.getFirmId());
        staOrderMapper.updateById(staOrder); //修改当前工作状态
        //更新入职人数
        Integer count = staOrderMapper.selectCount(new LambdaQueryWrapper<StaOrder>()
                .eq(StaOrder::getStaWorkId, staOrder.getStaWorkId())
                .eq(StaOrder::getStateFlag, CommonConstant.ORDER_STATUS_3)
        );
        StaWork staWork1 = staWorkMapper.selectById(staOrder.getStaWorkId());
        staWork1.setAdoptNum(count);
        staWorkMapper.updateById(staWork1);
        //新增工作
        StaOrder newStaOrder = new StaOrder();
        //拷贝
        BeanUtils.copyProperties(staOrder, newStaOrder, new String[]{"id", "entryDate", "newShopId", "newShopName",
                "newWorkId", "newWorkName", "variationTime", "variationFlag", "orgCode"}
        );
        newStaOrder.setCreateTime(new Date());
        newStaOrder.setUpdateTime(new Date());
        newStaOrder.setOrderDate(new Date());//下单时间
        newStaOrder.setWorkName(staWork.getNameNick()); //工作简称
        newStaOrder.setStaWorkId(staWork.getId()); //工作id
        newStaOrder.setStateFlag(CommonConstant.ORDER_STATUS_3); //工作中
        newStaOrder.setCompanyName(staWork.getCompanyName()); //门店名称
        newStaOrder.setWorkNameFull(staWork.getNameFull());//工作全称
        newStaOrder.setAddressLat(staWork.getAddressLat());
        newStaOrder.setAddressLng(staOrder.getAddressLng());
        newStaOrder.setAddressName(staWork.getAddressName());//工作地址名称
        newStaOrder.setCompanyId(sysDepart.getId()); //门店id
        newStaOrder.setSysOrgCode(sysDepart.getOrgCode());//订单编码
        newStaOrder.setFirmId(sysDepart.getParentId());//门店id
        newStaOrder.setStoNe(sysDepart.getStoreNo());//店编
        //区域   事业处
        SysDepart sysDepart2 = null;
        SysDepart sysDepart3 = null;
        if (sysDepart.getParentId() != null) {
            sysDepart2 = sysDepartMapper.selectById(sysDepart.getParentId()); //区域
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart2)) {
            newStaOrder.setRegionId(sysDepart2.getId());
            newStaOrder.setRegionName(sysDepart2.getDepartName());
            sysDepart3 = sysDepartMapper.selectById(sysDepart2.getParentId());//事业处
        }
        if (WxlConvertUtils.isNotEmpty(sysDepart3)) {
            newStaOrder.setBusinessDivisionId(sysDepart3.getId());
            newStaOrder.setBusinessDivisionName(sysDepart3.getDepartName());
        }
        staOrderMapper.insert(newStaOrder);
        //修改新工作中的申请工作人数
        staWork.setAdoptNum(staWork.getAdoptNum() + 1);
        staWorkMapper.updateById(staWork);
        //修改用户
        SysUser sysUser = sysUserMapper.selectById(staOrder.getSysUserId());
        sysUser.setCurrentStoresId(staOrder.getCompanyId());
        sysUser.setCurrentStores(staOrder.getCompanyName());
        sysUser.setCurrentWorkName(staOrder.getWorkNameFull());
        sysUser.setStaOrderId(staOrder.getId());
        sysUserMapper.updateById(sysUser);
        return true;
    }

    @Override
    public Boolean taskGradeScore(String sysUserId, String firmId, String staOrderId, String workId) {
        StaOrder staOrder = staOrderMapper.selectById(staOrderId); //旧订单
        StaWork staWork = staWorkMapper.selectById(workId); //新工作id
        Boolean result = false;
        if (WxlConvertUtils.isNotEmpty(staOrder) && WxlConvertUtils.isNotEmpty(staWork)) {
            result = this.agreeVariation(staOrder, staWork);
        }
        return result;
    }

    @Override
    public Map<String, Integer> fetchResignationAndMovementCount(LoginUser user) {
        String orgCode = user.getOrgCode();
        Map<String, Integer> resultCount = new HashMap<>();
        if (WxlConvertUtils.isEmpty(orgCode)) {
            return null;
        }
        LambdaQueryWrapper<StaOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0)
                .in(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_10, CommonConstant.ORDER_STATUS_13))
                .likeRight(StaOrder::getSysOrgCode, orgCode);
        List<StaOrder> staOrders = staOrderMapper.selectList(lambdaQueryWrapper);
        int resignationCount = 0; //辞职人数
        int movementCount = 0; //异动人数
        if (staOrders.isEmpty()) {
            resultCount.put("resignationCount", resignationCount);
            resultCount.put("movementCount", movementCount);
            return resultCount;
        }
        for (StaOrder staOrder : staOrders) {
            if (staOrder.getStateFlag() == CommonConstant.ORDER_STATUS_10) {
                resignationCount++;
            } else {
                movementCount++;
            }
        }
        resultCount.put("resignationCount", resignationCount);
        resultCount.put("movementCount", movementCount);
        return resultCount;
    }
    @Override
    public List<JSONObject> quickScheduling(String rosterMonth, String companyName, String enrollName,String storeNo) {
        List<JSONObject> result = new ArrayList<>();
        SysDepart sysDepart = new SysDepart();
        if (WxlConvertUtils.isNotEmpty(companyName)) {
            sysDepart = sysDepartMapper.selectOne(new LambdaQueryWrapper<SysDepart>()
                    .eq(SysDepart::getDepartName, companyName)
                    .eq(SysDepart::getStoreNo,storeNo)
            );
        }
        LambdaQueryWrapper<StaOrder> staOrderWrapper = new LambdaQueryWrapper<>();
        staOrderWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        //不包含 0撤销 1已完成 2申请中 6未通过接单审核 9申请过期
        staOrderWrapper.notIn(StaOrder::getStateFlag, Arrays.asList(
                CommonConstant.ORDER_STATUS_0,
                CommonConstant.ORDER_STATUS_1,
                CommonConstant.ORDER_STATUS_2,
                CommonConstant.ORDER_STATUS_6,
                CommonConstant.ORDER_STATUS_9
        ));
        if (WxlConvertUtils.isNotEmpty(sysDepart.getOrgCode())) {
            staOrderWrapper.likeRight(StaOrder::getOrderCode, sysDepart.getOrgCode());
        }
        if (WxlConvertUtils.isNotEmpty(enrollName)) {
            staOrderWrapper.like(StaOrder::getEnrollName, enrollName);
        }
        List<StaOrder> staOrders = staOrderMapper.selectList(staOrderWrapper);
        List<String> oids = staOrders.stream().map(StaOrder::getId).collect(Collectors.toList());
        if (oids.isEmpty()) {
            return result;
        }
        List<StaSchedule> staSchedules = staScheduleMapper.selectList(new QueryWrapper<StaSchedule>()
                .in("sta_order_id", oids)
                .eq("del_flag", CommonConstant.DEL_FLAG_0)
                .likeRight("schedule_day", rosterMonth));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        YearMonth yearMonth = YearMonth.parse(rosterMonth);
        for (StaOrder staOrder : staOrders) {
            Map<String, String> schedulingMap = new TreeMap<>();
            for (int i = 1; i <= yearMonth.lengthOfMonth(); i++) {
                int j = i;
                LocalDate date = yearMonth.atDay(i);
                String formattedDate = date.format(formatter);
                String code;
                if (staSchedules.isEmpty()) {
                    code = "///";
                } else {
                    code = staSchedules.stream()
                            .filter(s -> s.getStaOrderId().equals(staOrder.getId())
                                    && s.getScheduleDay().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getDayOfMonth() == j)
                            .map(StaSchedule::getCode)
                            .findFirst()
                            .orElse("///");
                }
                schedulingMap.put(formattedDate, code);
            }
            JSONObject json = new JSONObject();
            json.put("staOrderId", staOrder.getId());
            int index = staOrder.getCompanyName().indexOf("(");
            if (index != -1) {
                json.put("companyName", staOrder.getCompanyName().substring(0, index));
            } else {
                json.put("companyName", staOrder.getCompanyName());
            }
            json.put("entryDate",staOrder.getEntryDate());
            json.put("stateFlag",staOrder.getStateFlag());
            json.put("userName", staOrder.getEnrollName());
            json.put("workName", staOrder.getWorkName());
            json.put("schedulingMap", schedulingMap);
            result.add(json);
        }
        return result;
    }

    @Override
    public List<StaLocationClock> exportXlsAttendanceExportAll(String businessDivisionName, String regionName, String realName, String userIdCard, String dateRange, String stateFlag, String deptName, String selecteddeparts) throws ParseException {
        //获取排班
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<StaSchedule> staSchedules = new ArrayList<>();
        List<String> meUserIds = new ArrayList<>();
        //A03固定丹尼斯，剩余为其他
        if (sysUser.getOrgCode().startsWith("A03")) {
            //获取排班
            staSchedules = this.getSchedules(null, businessDivisionName, regionName, realName, dateRange,
                    userIdCard, stateFlag, selecteddeparts, deptName, null, null); //获取排班
        } else {
            //获取排班
            //获取当前机构
            SysDepart sysDepart = sysDepartMapper.selectOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getOrgCode, sysUser.getOrgCode()));
            //当前机构下用户
            List<SysUser> sysUsers = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().eq(SysUser::getOrgCode, sysDepart.getOrgCode()).eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0));
            //me_id 自己ID  对象 you_id
            List<String> uidList = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
            List<SysUserRel> userRels = sysUserRelMapper.selectList(new LambdaQueryWrapper<SysUserRel>().in(SysUserRel::getYouId, uidList));
            meUserIds = userRels.stream().map(SysUserRel::getMeId).collect(Collectors.toList()); //被推广人id
            staSchedules = this.getSchedules(null, businessDivisionName, regionName, realName, dateRange,
                    userIdCard, stateFlag, selecteddeparts, deptName,null, meUserIds); //获取排班
        }
        if (staSchedules.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<StaLocationClock> staLocationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staLocationClockLambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        staLocationClockLambdaQueryWrapper.in(StaLocationClock::getStaScheduleId, staSchedules.stream().map(staSchedule -> staSchedule.getId()).collect(Collectors.toList()));
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(staLocationClockLambdaQueryWrapper); //签到记录
        List<lcDayPage> ldp = this.createScheduleTemplate(staSchedules); //创建模板
        List<lcDayPage> lcDayPages = this.addScheduleTemplate(ldp, staLocationClocks);
        /**
         * step1:填充基本信息,签到记录（填充 缺卡，迟到时长，早退时长）数据过滤(今天以前的数据包含今天)
         * step2:排序日期降序->姓名分组(LinkedHashMap)-> 转换为原始数据
         * step3:单日时数计算填充
         */
        List<StaLocationClock> clockInCollection = this.addClockAndUserInfoAndSort(lcDayPages, null);
        //筛选签到状态 --- 正常 迟到 早退 补卡
        if (WxlConvertUtils.isNotEmpty(stateFlag)) {
            clockInCollection = clockInCollection.stream().filter(f ->
                    (f.getClockInStatus1() != null && f.getClockInStatus1().equals(Integer.valueOf(stateFlag)))
                            || (f.getClockInStatus2() != null && f.getClockInStatus2().equals(Integer.valueOf(stateFlag)))
                            || (f.getClockInStatus3() != null && f.getClockInStatus3().equals(Integer.valueOf(stateFlag)))
                            || (f.getClockInStatus4() != null && f.getClockInStatus4().equals(Integer.valueOf(stateFlag)))
            ).collect(Collectors.toList());
        }
        return clockInCollection;
    }

    @Override
    public IPage<StaOrder> listByAdmin(Integer pageNo, Integer pageSize, QueryWrapper<StaOrder> queryWrapper, HttpServletRequest request) {
        //入职日期区间查询
        String onboardingDate = request.getParameter("onboardingDate");
        if (WxlConvertUtils.isNotEmpty(onboardingDate)) {
            String[] split = onboardingDate.split(",");
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH);
            String startDate = DateUtil.getSimpleDateFormat(split[0], inputFormat);
            String entDate = DateUtil.getSimpleDateFormat(split[1], inputFormat);
            queryWrapper.between("entry_date", startDate, entDate);
        }
        Page<StaOrder> page = new Page<>(pageNo, pageSize);
        IPage<StaOrder> pageList = staOrderService.page(page, queryWrapper);
        //填充任务内容
        if (pageList.getRecords().isEmpty()) {
            return pageList;
        }
        pageList = this.populateAgeGenderBirth(pageList);
        Map<Integer, String> sMap = this.taskOrderMaps();
        if (!pageList.getRecords().isEmpty()) {
            pageList.getRecords().forEach(order -> {
                order.setPresentStatus(sMap.get(order.getStateFlag()));
            });
            return pageList;
        } else {
            return pageList;
        }
    }
    @Transactional
    @Override
    public StaOrder agreeResign(String oid, String type, String taskScore) {
        StaOrder staOrder = staOrderService.getById(oid);
        staOrder.setDimissionShop(staOrder.getFirmName());//离职店铺
        staOrder.setTimeOfResignation(new Date());//离职时间
        staOrder.setStateFlag(CommonConstant.ORDER_STATUS_4);//待结算
        if (WxlConvertUtils.isNotEmpty(taskScore)) {
            staOrder.setTaskScore(Integer.valueOf(taskScore)); //任务评分(1-5)
        }
        String staTaskDateId = staOrder.getStaTaskDateId();
        if (WxlConvertUtils.isNotEmpty(staTaskDateId)) {
            StaTaskDate staTaskDate = staTaskDateMapper.selectById(staTaskDateId);
            staTaskDate.setRemainingNum(staTaskDate.getRemainingNum() + 1);
            staTaskDateMapper.updateById(staTaskDate);
        }
        //获取当前任务单排班
        List<StaSchedule> staSchedules = staScheduleMapper.selectList(new LambdaQueryWrapper<StaSchedule>()
                .eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0)
                .eq(StaSchedule::getStaOrderId, staOrder.getId())
        );
        //过滤出今天以后的排班数据
        LocalDate today = LocalDate.now();
        List<StaSchedule> delSchedule = staSchedules.stream()
                // 过滤出今天以后的数据
                .filter(schedule -> {
                    LocalDate scheduleDate = schedule.getScheduleDay().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    return scheduleDate.isAfter(today) || scheduleDate.isEqual(today);
                })
                .collect(Collectors.toList());
        if (!delSchedule.isEmpty()) {
            //删除任务完成后续的排班
            staScheduleMapper.deleteBatchIds(delSchedule.stream().map(StaSchedule::getId).collect(Collectors.toList()));
        }
        //任务单完成计算出任务时数和金额
        //任务单打卡记录
        Map<String, Double> workingHoursMap = new HashMap<>();//任务单时数
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(new QueryWrapper<StaLocationClock>()
                .lambda()
                .eq(StaLocationClock::getStaOrderId, staOrder.getId())
                .isNotNull(StaLocationClock::getTimeExpect)
                .ne(StaLocationClock::getTimeExpect,"")
        );
        if (WxlConvertUtils.isNotEmpty(staLocationClocks)) {
            Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
            locationClocksMap.put(staOrder.getId(), staLocationClocks);
            //根据天拆分打卡(后续几天每天时数)
            for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                Double workingHours = 0.0; //当前任务单时数
                String staOrderId = entry.getKey();
                List<StaLocationClock> value = entry.getValue();
                //key签到日期，value签到数据
                Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                        .collect(Collectors.groupingBy(clock -> {
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(clock.getTime());
                            cal.set(Calendar.HOUR_OF_DAY, 0);
                            cal.set(Calendar.MINUTE, 0);
                            cal.set(Calendar.SECOND, 0);
                            cal.set(Calendar.MILLISECOND, 0);
                            return cal.getTime();
                        }));
                //升序排序
                for (List<StaLocationClock> sublist : mapByDay.values()) {
                    sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                }
                //时数计算
                for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                    List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                    List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());//打卡时间
                    List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());//要求打卡时间
                    long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                    long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                    if (totalWorkDuration > shiftDuration) {
                        totalWorkDuration = shiftDuration;
                    }
                    Long hours = totalWorkDuration / 60;//小时
                    Long remainingMinutes = totalWorkDuration % 60; //分钟
                    String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                    double workHours = Double.parseDouble(workHoursStr); //当天时数
                    if (workHours > 0) {
                        workingHours += workHours;
                    }
                }
                workingHoursMap.put(staOrderId, workingHours);
            }
        }
        if (WxlConvertUtils.isNotEmpty(workingHoursMap.get(staOrder.getId()))) {
            BigDecimal bigDecimal = new BigDecimal(workingHoursMap.get(staOrder.getId()));
            BigDecimal hourlySalary = staOrder.getHourlySalary();
            String str = String.format("%.2f", workingHoursMap.get(staOrder.getId()));
            staOrder.setTaskManHour(Double.parseDouble(str)); //任务单时数
            staOrder.setTaskAmount(hourlySalary.multiply(bigDecimal).setScale(1, RoundingMode.DOWN));//任务单金额
        }
        staOrderService.updateById(staOrder);
        //剔除任务人数sta_work和sta_task_date
        if (WxlConvertUtils.isNotEmpty(staOrder.getStaWorkId())) {
            StaWork staWork = staWorkMapper.selectById(staOrder.getStaWorkId());
            staWork.setAdoptNum(staWork.getAdoptNum() > 0 ?  staWork.getAdoptNum() - 1 : 0);
            //刷新缓存
            redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" + staWork.getId(), staWork.getAdoptNum());
            staWorkMapper.updateById(staWork);
        }
        return staOrder;
    }

    @Override
    public Double getMonthlyWorkingHours(String sysUserId) throws ParseException {
        //获取任务单
        Map<String, String> currentMonthStartAndEnd = this.getCurrentMonthStartAndEnd();
        String startDate = currentMonthStartAndEnd.get("start");
        String endDate = currentMonthStartAndEnd.get("end");
        LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staOrderLambdaQueryWrapper.eq(StaOrder::getSysUserId,sysUserId);
        staOrderLambdaQueryWrapper.notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_0,
                CommonConstant.ORDER_STATUS_2, CommonConstant.ORDER_STATUS_5, CommonConstant.ORDER_STATUS_6));
        staOrderLambdaQueryWrapper.ge(StaOrder::getEntryDate, startDate); // 大于等于开始日期
        staOrderLambdaQueryWrapper.le(StaOrder::getEntryDate, endDate); // 小于等于结束日期
        List<StaOrder> staOrders = staOrderMapper.selectList(staOrderLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(staOrders) || staOrders.isEmpty()) { //无数据
            return 0.0;
        }
        Set<String> orderIds = new TreeSet<>();
        orderIds = staOrders.stream().map(StaOrder::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<StaLocationClock> locationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        locationClockLambdaQueryWrapper.isNotNull(StaLocationClock::getTimeExpect); //没有排班的签到记录不做计算
        locationClockLambdaQueryWrapper.ne(StaLocationClock::getTimeExpect, "");
        locationClockLambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        locationClockLambdaQueryWrapper.in(StaLocationClock::getStaOrderId, orderIds);
        locationClockLambdaQueryWrapper.likeRight(StaLocationClock::getTimeExpect,startDate.substring(0,7));
        List<StaLocationClock> staLocationClocks = staLocationClockMapper.selectList(locationClockLambdaQueryWrapper);
        if (staLocationClocks.isEmpty() || WxlConvertUtils.isEmpty(staLocationClocks)) {
            return 0.0;
        }
        return processStaOrders(staOrders, staLocationClocks, false);
    }

    /**
     * 处理任务单
     * @param staOrders
     * @param locationClocks
     * @param isTotal
     */
    public Double processStaOrders(List<StaOrder> staOrders, List<StaLocationClock> locationClocks, boolean isTotal) {
        Double result = 0.0;
        if (locationClocks != null && !locationClocks.isEmpty()) {
            locationClocks = this.relatedScheduling(locationClocks);
            Map<String, Double> workingHoursMap = this.calculationOfWorkingHours(locationClocks);
            for (Map.Entry<String,Double> entry : workingHoursMap.entrySet()) {
                Double value = entry.getValue();
                result += value;
            }
        }
        return result;
    }
    /**
     * 签到 关联排班
     *
     * @return
     */
    private List<StaLocationClock> relatedScheduling(List<StaLocationClock> staLocationClocks) {
        Set<String> scheduleIdList = staLocationClocks.stream().map(StaLocationClock::getStaScheduleId).collect(Collectors.toSet());
        List<StaSchedule> staSchedules = staScheduleMapper.selectList(new LambdaQueryWrapper<StaSchedule>()
                .eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0)
                .in(StaSchedule::getId, scheduleIdList)
        );
        List<StaLocationClock> staLocationClockList = new ArrayList<>();
        for (StaSchedule staSchedule : staSchedules) {
            for (StaLocationClock staLocationClock : staLocationClocks) {
                if (staSchedule.getId().equals(staLocationClock.getStaScheduleId())) {
                    staLocationClockList.add(staLocationClock);
                }
            }
        }
        return staLocationClockList;
    }
    /**
     * 任务单工时计算
     * @param staLocationClocks
     * @param
     * @return
     */
    private Map<String, Double> calculationOfWorkingHours(List<StaLocationClock> staLocationClocks) {
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
        for (StaLocationClock staLocationClock : staLocationClocks) {
            String staOrderId = staLocationClock.getStaOrderId();
            if (!locationClocksMap.containsKey(staOrderId)) {
                locationClocksMap.put(staOrderId, new ArrayList<>());
            }
            locationClocksMap.get(staOrderId).add(staLocationClock);
        }
        Map<String, Double> workingHoursMap = new HashMap<>();//任务单时数
        //根据天拆分打卡(后续几天每天时数)
        for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
            double workingHours = 0.0; //当前任务单时数
            String orderId = entry.getKey();//任务单id
            List<StaLocationClock> value = entry.getValue();//任务单打卡数据
            //key签到日期，value签到数据
            for (StaLocationClock clock : value) {
                Date time = clock.getTime();
                Date timeExpect = clock.getTimeExpect();
                if (time != null && timeExpect != null && !isSameDay(time, timeExpect)) {
                    clock.setTime(subtractOneDay(time));
                }
            }
            Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                    .collect(Collectors.groupingBy(clock -> {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(clock.getTime());
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        cal.set(Calendar.MINUTE, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MILLISECOND, 0);
                        return cal.getTime();
                    }, TreeMap::new, Collectors.toList()));
            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntrySort : mapByDay.entrySet()) {
                List<StaLocationClock> staLocationClockList = mapByDayEntrySort.getValue();
                if (staLocationClockList.size() == 2) {
                    List<Date> dates = new ArrayList<>();
                    for (StaLocationClock staLocationClock : staLocationClockList) {// 班次开始时间
                        if (staLocationClock.getTimeType() == 1) {
                            dates.add(staLocationClock.getTimeExpect());
                            break;
                        }
                    }
                    for (StaLocationClock staLocationClock : staLocationClockList) {// 班次结束时间
                        if (staLocationClock.getTimeType() == 2) {
                            dates.add(staLocationClock.getTimeExpect());
                            break;
                        }
                    }
                    if (crossesMidnight(dates)) { //是否跨夜
                        //跨夜降序
                        for (List<StaLocationClock> sublist : mapByDay.values()) {
                            sublist.sort(Comparator.comparing(StaLocationClock::getTime).reversed());
                        }
                    } else {//升序
                        for (List<StaLocationClock> sublist : mapByDay.values()) {
                            sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                        }
                    }
                } else { //其他默认升序
                    for (List<StaLocationClock> sublist : mapByDay.values()) {
                        sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                    }
                }
            }
            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                Set<Date> mapByDayTime = new HashSet<>(); //实际签到时间
                Set<Date> mapByDayTimeExpectDate = new HashSet<>(); //要求签到时间
                if (mapByDayValue.size() == 2) { //一个班次 两次卡
                    mapByDayValue.sort(Comparator.comparingInt(StaLocationClock::getTimeType));
                    List<Date> dd = new ArrayList<>();
                    dd.add(mapByDayValue.get(0).getTimeExpect());
                    dd.add(mapByDayValue.get(1).getTimeExpect());
                    if (!crossesMidnight(dd)) { //正常班次 升序
                        mapByDayTimeExpectDate = mapByDayValue.stream()
                                .map(StaLocationClock::getTimeExpect)
                                .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                        mapByDayTime = mapByDayValue.stream()
                                .map(StaLocationClock::getTime)
                                .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序

                    } else { //跨夜班次   降序
                        mapByDayTimeExpectDate = mapByDayValue.stream()
                                .map(StaLocationClock::getTimeExpect)
                                .sorted(Comparator.reverseOrder())  // 使用 Comparator.naturalOrder() 进行降序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                        mapByDayTime = mapByDayValue.stream()
                                .map(StaLocationClock::getTime)
                                .sorted(Comparator.reverseOrder())  // 使用 Comparator.naturalOrder() 进行降序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                    }
                } else {//2个班次4次卡升序
                    mapByDayTimeExpectDate = mapByDayValue.stream()
                            .map(StaLocationClock::getTimeExpect)
                            .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                            .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                    mapByDayTime = mapByDayValue.stream()
                            .map(StaLocationClock::getTime)
                            .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                            .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                }
                List<Date> mapByDayTimeExpect = new ArrayList<>(mapByDayTimeExpectDate);
                List<Date> dayTimeList = new ArrayList<>(mapByDayTime);
                //班次1时数
                Long morning = null;
                double workHours = 0;
                //班次2时数
                Long afternoon = null;
                double workHours2 = 0;
                //没有排班不做关联计算
                String start1 = "";
                String end1 = "";
                String start2 = "";
                String end2 = "";
                switch (mapByDayTimeExpect.size()) {
                    case 1: //签到记录条数1不做计算
                        break;
                    case 2:
                    case 3:
                        Date clockInTime1 = null;
                        Date clockInTime2 = null;
                        Date clockInTime1Expect = mapByDayTimeExpect.get(0); //要求签到时间
                        Date clockInTime2Expect = mapByDayTimeExpect.get(1); //要求签退时间
                        if (mapByDayTimeExpect.size() == mapByDayTime.size()) {
                            clockInTime1 = dayTimeList.get(0);
                            clockInTime2 = dayTimeList.get(1);
                        } else {
                            clockInTime1 = findClosestTime(dayTimeList, clockInTime1Expect);//实际签到时间
                            mapByDayTime.remove(clockInTime1);
                            clockInTime2 = findClosestTime(dayTimeList, clockInTime2Expect);//实际签退时间
                            mapByDayTime.remove(clockInTime2);
                        }
                        // 判断签到时间
                        if (clockInTime1.before(clockInTime1Expect)) {
                            start1 = sb.format(clockInTime1Expect); //提前签到使用要求时间
                        } else if (clockInTime1.after(clockInTime1Expect)) { //迟到
                            start1 = sb.format(clockInTime1);
                        } else {
                            start1 = sb.format(clockInTime1Expect);
                        }//相等

                        // 判断签退时间
                        if (clockInTime2.before(clockInTime2Expect)) {//早退
                            end1 = sb.format(clockInTime2);
                        } else if (clockInTime2.after(clockInTime2Expect)) {
                            end1 = sb.format(clockInTime2Expect);
                        } else {
                            end1 = sb.format(clockInTime2Expect);
                        }//相等
                        morning = DateUtils.calculateWorkingMinutes(start1, end1);
                        workHours = morning / 60.0; //班次1时数
                        break;
                    case 4: //两个班次 4次打卡
                        Date clockTime1Expect = mapByDayTimeExpect.get(0); //要求签到时间
                        Date clockTime2Expect = mapByDayTimeExpect.get(1); //要求签退时间
                        Date clockTime1 = dayTimeList.get(0);//实际签到时间
                        Date clockTime2 = dayTimeList.get(1);//实际签退时间
                        Date clockTime3Expect = mapByDayTimeExpect.get(2); //要求签到时间
                        Date clockTime4Expect = mapByDayTimeExpect.get(3);
                        ; //要求签退时间
                        Date clockTime3 = dayTimeList.get(2);//实际签到时间
                        Date clockTime4 = dayTimeList.get(3);//实际签退时间
                        //班次1
                        // 判断签到时间
                        if (clockTime1.before(clockTime1Expect)) {
                            start1 = sb.format(clockTime1Expect); //提前签到使用要求时间
                        } else if (clockTime1.after(clockTime1Expect)) { //迟到
                            start1 = sb.format(clockTime1);
                        } else {
                            start1 = sb.format(clockTime1Expect);
                        }//相等
                        // 判断签退时间
                        if (clockTime2.before(clockTime2Expect)) {//早退
                            end1 = sb.format(clockTime2);
                        } else if (clockTime2.after(clockTime2Expect)) {
                            end1 = sb.format(clockTime2Expect);
                        } else {
                            end1 = sb.format(clockTime2Expect);
                        }//相等
                        morning = DateUtils.calculateWorkingMinutes(start1, end1);
                        workHours = morning / 60.0; //班次1时数
                        //班次二
                        // 判断签到时间
                        if (clockTime3.before(clockTime3Expect)) {
                            start2 = sb.format(clockTime3Expect); //提前签到使用要求时间
                        } else if (clockTime3.after(clockTime3Expect)) { //迟到
                            start2 = sb.format(clockTime3);
                        } else {
                            start2 = sb.format(clockTime3Expect); //提前签到使用要求时间
                        }//相等
                        // 判断签退时间
                        if (clockTime4.before(clockTime4Expect)) {//早退
                            end2 = sb.format(clockTime4);
                        } else if (clockTime4.after(clockTime4Expect)) {
                            end2 = sb.format(clockTime4Expect);
                        } else {
                            end2 = sb.format(clockTime4Expect);
                        }//相等
                        afternoon = DateUtils.calculateWorkingMinutes(start2, end2);
                        double workingHours2 = afternoon / 60.0;
                        workHours2 = workingHours2; //班次2时数
                        break;
                    default: //无签到记录
                        break;
                }
                workingHours = workingHours + (workHours + workHours2);
            }
            String str = String.format("%.1f", workingHours);
            workingHoursMap.put(orderId, Double.parseDouble(str));
        }
        return workingHoursMap;
    }
    /**
     * 寻找接近当前时间的时间
     *
     * @param dateList
     * @param targetDate
     * @return
     */
    public static Date findClosestTime(List<Date> dateList, Date targetDate) {
        if (dateList == null || dateList.isEmpty() || targetDate == null) {
            return null;
        }
        Date closestDate = null;
        long minDiff = Long.MAX_VALUE;
        for (Date date : dateList) {
            long diff = Math.abs(date.getTime() - targetDate.getTime());
            if (diff < minDiff) {
                minDiff = diff;
                closestDate = date;
            }
            // 如果找到相等的时间，直接返回
            if (diff == 0) {
                return date;
            }
        }
        return closestDate;
    }
    /**
     * 判断两个日期是否出现跨夜
     *
     * @param dateList
     * @return
     */
    public static boolean crossesMidnight(List<Date> dateList) {
        if (dateList == null || dateList.size() < 2) {
            // 如果列表为空或只有一个日期，无法判断跨夜
            return false;
        }
        // 转换日期为小时和分钟
        List<Calendar> calendars = dateList.stream()
                .map(date -> {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    return calendar;
                })
                .collect(Collectors.toList());
        // 获取第一个和最后一个时间的小时和分钟
        Calendar startCalendar = calendars.get(0);
        Calendar endCalendar = calendars.get(calendars.size() - 1);
        int startHour = startCalendar.get(Calendar.HOUR_OF_DAY);
        int startMinute = startCalendar.get(Calendar.MINUTE);
        int endHour = endCalendar.get(Calendar.HOUR_OF_DAY);
        int endMinute = endCalendar.get(Calendar.MINUTE);
        // 判断是否跨夜
        if (endHour < startHour || (endHour == startHour && endMinute < startMinute)) {
            return true;
        }
        return false;
    }
    // 将日期减去一天，时间不变
    private static Date subtractOneDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1); // 减去一天
        return cal.getTime();
    }
    /**
     * 获取本月的开始和结束时间
     * @return Map 包含 "start" 和 "end" 键，分别表示本月的开始时间和结束时间（yyyy-MM-dd 格式）
     */
    private   Map<String, String> getCurrentMonthStartAndEnd() {
        // 获取当前日期
        LocalDate now = LocalDate.now() ;
        //LocalDate now = now22.minusMonths(1); // 上个月进行测试
        // 本月开始时间
        LocalDate startOfMonth = now.withDayOfMonth(1);
        // 本月结束时间
        LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());
        // 格式化为 yyyy-MM-dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = startOfMonth.format(formatter);
        String endDate = endOfMonth.format(formatter);
        // 将结果放入 Map
        Map<String, String> result = new HashMap<>();
        result.put("start", startDate);
        result.put("end", endDate);
        return result;
    }
    /**
     * @param oids   id集合
     * @param clocks 签到集合
     * @return
     */
    public Map<String, Double> workingHours(List<String> oids, List<StaLocationClock> clocks) {
        ArrayList<Date> dates = new ArrayList<>();
        Map<String, List<Date>> workingHoursMaps = new HashMap<>();
        Map<String, Double> durationMap = new HashMap<>();
        for (String oid : oids) {
            for (StaLocationClock clock : clocks) {
                if (clock.getStaOrderId().equals(oid)) {
                    dates.add(clock.getTime());
                }
            }
            workingHoursMaps.put(oid, dates);
            dates = new ArrayList<>();
        }
        for (Map.Entry<String, List<Date>> entry : workingHoursMaps.entrySet()) {
            String userId = entry.getKey();
            List<Date> punchInTimes = entry.getValue();
            long totalWorkDuration = this.calculateWorkDuration(punchInTimes);
            Long hours = totalWorkDuration / 60;//小时
            Long remainingMinutes = totalWorkDuration % 60; //分钟
            String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
            double workHours = Double.parseDouble(workHoursStr);
            durationMap.put(userId, workHours);
        }
        return durationMap;
    }

    /**
     * 获取两个时间差
     *
     * @param punchInTimes
     * @return long
     */
    private long calculateWorkDuration(List<Date> punchInTimes) {
        if (punchInTimes.size() < 2) {
            return 0;
        }
        long totalWorkDuration = 0;
        Map<Date, Long> workDurationPerDay = new HashMap<>();
        for (int i = 0; i < punchInTimes.size() - 1; i += 2) {
            Date punchInTime = punchInTimes.get(i);
            Date punchOutTime = punchInTimes.get(i + 1);
            // TODO 判断签到时间是否在同一天 丹尼斯物流有跨日考勤需要在做处理
            if (isSameDay(punchInTime, punchOutTime)) {
                long workDuration = punchOutTime.getTime() - punchInTime.getTime();
                // 记录每天的工作时长
                Date workDate = getStartOfDay(punchInTime);
                workDurationPerDay.put(workDate, workDurationPerDay.getOrDefault(workDate, 0L) + workDuration);
            }
        }
        // 计算总的工作时长
        for (long workDuration : workDurationPerDay.values()) {
            totalWorkDuration += workDuration;
        }
        // 将工作时长从毫秒转换为分钟
        totalWorkDuration /= (1000 * 60);
        return totalWorkDuration;
    }
    // 获取一天的开始时间
    private Date getStartOfDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 是否在同一天
     *
     * @param date1
     * @param date2
     * @return
     */
    private boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 计算两个时间差(毫秒)
     * 添加跨天计算
     *
     * @param startTimeStr
     * @param endTimeStr
     * @return
     */
    private Long calculateWorkingMinutes(String startTimeStr, String endTimeStr) {
        // 定义日期时间格式，包含秒
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 使用格式化器解析开始和结束时间
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter).withSecond(0).withNano(0);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, formatter).withSecond(0).withNano(0);
        // 处理结束时间在开始时间之后的情况
        if (endTime.isBefore(startTime)) {
            endTime = endTime.plusDays(1);
        }
        // 计算两个时间之间的持续时间
        Duration duration = Duration.between(startTime, endTime);
        long minutes = duration.toMinutes();
        return minutes;
    }
    // 重载方法：用于导出功能，不需要分页
    public List<StaSchedule> getSchedules(String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd,
                                          String userIdCard, String stateFlag, String deptName, String deptNo,
                                          List<String> meUserIds)  {
        // 导出功能使用默认分页参数（获取所有数据）
        return getSchedules(1, Integer.MAX_VALUE, wid, businessDivisionName, regionName, realName, dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, meUserIds);
    }

    // 带分页参数的方法
    public List<StaSchedule> getSchedules(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd,
                                          String userIdCard, String stateFlag, String deptName, String deptNo,
                                          List<String> meUserIds)  {

        // 性能优化：使用连表查询替代多次查询
        long startTime = System.currentTimeMillis();
        log.debug("开始执行优化的连表查询");

        // 🚀 使用查询效率优化 - 不限制数据记录数量，专注于查询性能
        log.info("🚀 开始执行查询效率优化，参数：wid={}, deptName={}, deptNo={}, userIdCard={}, realName={}",
                wid, deptName, deptNo, userIdCard, realName);
        log.info("📋 注意：不限制数据记录数量，专注于查询效率优化");
        log.info("🔍 调试信息：meUserIds={}, dateRangeBegin={}, dateRangeEnd={}",
                meUserIds != null ? meUserIds.size() + "个用户" : "null", dateRangeBegin, dateRangeEnd);

        // 🚀 注意：获取全部数据，不限制日期范围
        if (StringUtils.isEmpty(dateRangeBegin) && StringUtils.isEmpty(dateRangeEnd)) {
            log.info("📋 未指定日期范围，将查询全部历史数据");
        }

        List<StaSchedule> result = getSchedulesWithQueryOptimization(pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, meUserIds);

        long endTime = System.currentTimeMillis();
        long optimizedTime = endTime - startTime;
        log.info("🚀 查询效率优化完成，耗时：{}ms，结果数量：{}", optimizedTime, result.size());

        // 性能对比分析
        analyzePerformanceImprovement(optimizedTime, result.size());

        return result;
    }

    /**
     * 🚀 查询效率优化方法 - 支持分页参数
     */
    private List<StaSchedule> getSchedulesWithQueryOptimization(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd,
                                          String userIdCard, String stateFlag, String deptName, String deptNo,
                                          List<String> meUserIds) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 🚀 策略1: 如果有具体的用户条件，使用最优查询路径
        if (StringUtils.isNotEmpty(userIdCard) || StringUtils.isNotEmpty(realName)) {
            log.info("🚀 使用用户条件优化查询路径");
            return getSchedulesByUserConditionsOptimized(pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                    dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, meUserIds, sysUser);
        }

        // 🚀 策略2: 如果有部门条件，使用部门优化查询
        if (WxlConvertUtils.isNotEmpty(deptName) && WxlConvertUtils.isNotEmpty(deptNo)) {
            log.info("🚀 使用部门条件优化查询路径");
            return getSchedulesByDeptConditionsOptimized(pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                    dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, deptName, deptNo, sysUser);
        }

        // 🚀 策略3: 默认情况，使用组织代码优化查询
        log.info("🚀 使用组织代码优化查询路径");
        return getSchedulesByOrgCodeOptimized(pageNo, pageSize, wid, businessDivisionName, regionName, realName,
                dateRangeBegin, dateRangeEnd, userIdCard, stateFlag, sysUser, meUserIds);
    }

    /**
     * 🚀 用户条件优化查询 - 支持分页参数
     */
    private List<StaSchedule> getSchedulesByUserConditionsOptimized(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd,
                                          String userIdCard, String stateFlag, String deptName, String deptNo,
                                          List<String> meUserIds, LoginUser sysUser) {

        long queryStart = System.currentTimeMillis();

        // 🚀 使用索引友好的查询条件顺序
        LambdaQueryWrapper<StaSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);

        // 🚀 优先使用最具选择性的条件
        if (StringUtils.isNotEmpty(userIdCard)) {
            wrapper.eq(StaSchedule::getUserIdCard, userIdCard);
            log.info("🚀 使用用户身份证查询：{}", userIdCard);
        }

        if (StringUtils.isNotEmpty(realName)) {
            wrapper.like(StaSchedule::getRealName, realName);
            log.info("🚀 使用用户姓名查询：{}", realName);
        }

        // 🚀 日期条件（利用索引）
        addDateRangeConditions(wrapper, dateRangeBegin, dateRangeEnd);

        if (meUserIds != null && !meUserIds.isEmpty()) {
            wrapper.in(StaSchedule::getSysUserId, meUserIds);
            log.info("🚀 使用用户ID列表查询，数量：{}", meUserIds.size());
        } else {
            wrapper.likeRight(StaSchedule::getOrgCode, sysUser.getOrgCode());
        }

        if (WxlConvertUtils.isNotEmpty(wid)) {
            wrapper.eq(StaSchedule::getStaWorkId, wid);
        }

        // 🚀 使用索引友好的排序
        wrapper.orderByDesc(StaSchedule::getScheduleDay, StaSchedule::getCreateTime);

        // 🚀 添加分页支持
        Page<StaSchedule> page = new Page<>(pageNo, pageSize);
        IPage<StaSchedule> pageResult = staScheduleMapper.selectPage(page, wrapper);
        List<StaSchedule> schedules = pageResult.getRecords();
        long queryEnd = System.currentTimeMillis();
        log.info("🚀 用户条件优化查询耗时：{}ms，结果数：{}，总记录数：{}", (queryEnd - queryStart), schedules.size(), pageResult.getTotal());

        return schedules;
    }

    /**
     * 🚀 部门条件优化查询 - 支持分页参数
     */
    private List<StaSchedule> getSchedulesByDeptConditionsOptimized(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd,
                                          String userIdCard, String stateFlag, String deptName, String deptNo, LoginUser sysUser) {

        long queryStart = System.currentTimeMillis();

        // 🚀 先查询部门相关的有效订单ID（使用索引）
        long orderQueryStart = System.currentTimeMillis();
        LambdaQueryWrapper<StaOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.select(StaOrder::getId);
        orderWrapper.eq(StaOrder::getCompanyName, deptName)
                   .eq(StaOrder::getStoreNo, deptNo)
                   .notIn(StaOrder::getStateFlag, Arrays.asList(
                           CommonConstant.ORDER_STATUS_0,
                           CommonConstant.ORDER_STATUS_1,
                           CommonConstant.ORDER_STATUS_2,
                           CommonConstant.ORDER_STATUS_6));

        List<String> orderIds = staOrderMapper.selectList(orderWrapper)
                                             .stream()
                                             .map(StaOrder::getId)
                                             .collect(Collectors.toList());

        long orderQueryEnd = System.currentTimeMillis();
        log.info("🚀 部门订单查询耗时：{}ms，订单数：{}", (orderQueryEnd - orderQueryStart), orderIds.size());

        if (orderIds.isEmpty()) {
            log.warn("⚠️ 未找到部门相关订单，返回空结果");
            return new ArrayList<>();
        }

        // 🚀 基于订单ID查询排班（使用索引）
        long scheduleQueryStart = System.currentTimeMillis();
        LambdaQueryWrapper<StaSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);
        wrapper.in(StaSchedule::getStaOrderId, orderIds);

        // 🚀 日期条件（利用索引）
        addDateRangeConditions(wrapper, dateRangeBegin, dateRangeEnd);

        if (WxlConvertUtils.isNotEmpty(wid)) {
            wrapper.eq(StaSchedule::getStaWorkId, wid);
        }

        // 🚀 使用索引友好的排序
        wrapper.orderByDesc(StaSchedule::getScheduleDay, StaSchedule::getCreateTime);

        // 🚀 添加分页支持
        Page<StaSchedule> page = new Page<>(pageNo, pageSize);
        IPage<StaSchedule> pageResult = staScheduleMapper.selectPage(page, wrapper);
        List<StaSchedule> result = pageResult.getRecords();
        long scheduleQueryEnd = System.currentTimeMillis();
        log.info("🚀 部门排班查询耗时：{}ms，排班记录数：{}，总记录数：{}", (scheduleQueryEnd - scheduleQueryStart), result.size(), pageResult.getTotal());

        long totalTime = System.currentTimeMillis() - queryStart;
        log.info("🚀 部门条件优化查询总耗时：{}ms", totalTime);

        return result;
    }

    /**
     * 🚀 组织代码优化查询 - 支持分页参数
     */
    private List<StaSchedule> getSchedulesByOrgCodeOptimized(Integer pageNo, Integer pageSize, String wid, String businessDivisionName, String regionName, String realName, String dateRangeBegin, String dateRangeEnd,
                                          String userIdCard, String stateFlag, LoginUser sysUser, List<String> meUserIds) {

        long queryStart = System.currentTimeMillis();

        // 🚀 使用索引友好的查询条件顺序
        LambdaQueryWrapper<StaSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);

        // 🚀 组织代码条件（利用索引）
        wrapper.likeRight(StaSchedule::getOrgCode, sysUser.getOrgCode());

        // 🚀 优化日期条件 - 如果没有指定日期范围，默认查询最近3个月
        if (StringUtils.isEmpty(dateRangeBegin) && StringUtils.isEmpty(dateRangeEnd)) {
            LocalDate now = LocalDate.now();
            String defaultStartDate = now.minusMonths(3).toString();
            String defaultEndDate = now.toString();
            wrapper.ge(StaSchedule::getScheduleDay, defaultStartDate);
            wrapper.le(StaSchedule::getScheduleDay, defaultEndDate);
            log.info("🚀 未指定日期范围，默认查询最近3个月：{} 到 {}", defaultStartDate, defaultEndDate);
        } else {
            addDateRangeConditions(wrapper, dateRangeBegin, dateRangeEnd);
        }

        // 🔧 修复：只有在明确需要用户限制时才添加用户条件
        if (meUserIds != null && !meUserIds.isEmpty()) {
            wrapper.in(StaSchedule::getSysUserId, meUserIds);
            log.info("🚀 添加用户ID限制，数量：{}", meUserIds.size());
        } else {
            log.info("🚀 查询所有用户的排班数据（不限制用户）");
        }

        if (WxlConvertUtils.isNotEmpty(wid)) {
            wrapper.eq(StaSchedule::getStaWorkId, wid);
        }

        // 🚀 使用索引友好的排序
        wrapper.orderByDesc(StaSchedule::getScheduleDay, StaSchedule::getCreateTime);

        // 🚀 添加分页支持
        Page<StaSchedule> page = new Page<>(pageNo, pageSize);
        IPage<StaSchedule> pageResult = staScheduleMapper.selectPage(page, wrapper);
        List<StaSchedule> schedules = pageResult.getRecords();
        long queryEnd = System.currentTimeMillis();
        log.info("🚀 组织代码查询耗时：{}ms，结果数：{}，总记录数：{}", (queryEnd - queryStart), schedules.size(), pageResult.getTotal());

        // 🚀 如果查询结果为空，提前返回避免后续处理
        if (schedules.isEmpty()) {
            log.info("🚀 排班查询结果为空，提前返回");
            return schedules;
        }

        // 🚀 如果查询时间过长，记录详细信息用于进一步优化
        if ((queryEnd - queryStart) > 2000) {
            log.warn("⚠️ 排班查询耗时过长：{}ms，结果数：{}，建议检查数据库索引", (queryEnd - queryStart), schedules.size());
            log.warn("⚠️ 查询条件：orgCode={}, dateRange={}-{}, wid={}",
                    sysUser.getOrgCode(), dateRangeBegin, dateRangeEnd, wid);
        }

        return schedules;
    }
    /**
     * 性能改进分析
     */
    private void analyzePerformanceImprovement(long optimizedTime, int resultCount) {
        // 基于历史数据估算原有查询时间
        // 原有查询大约每1000条记录需要800-1200ms
        long estimatedOriginalTime = (resultCount / 1000 + 1) * 1000;

        if (optimizedTime < estimatedOriginalTime) {
            long improvement = estimatedOriginalTime - optimizedTime;
            double improvementPercent = (improvement * 100.0) / estimatedOriginalTime;

            log.info("📈 性能优化效果分析：");
            log.info("• 预估原有查询时间：{}ms", estimatedOriginalTime);
            log.info("• 实际优化查询时间：{}ms", optimizedTime);
            log.info("• 性能提升：{}ms ({:.1f}%)", improvement, improvementPercent);
            log.info("• 查询效率：{:.2f}条/秒", resultCount * 1000.0 / optimizedTime);

            if (improvementPercent > 50) {
                log.info("🚀 性能优化效果显著！提升超过50%");
            } else if (improvementPercent > 20) {
                log.info("⚡ 性能优化效果良好！提升超过20%");
            } else {
                log.info("✅ 性能有所提升");
            }
        } else {
            log.warn("⚠️ 优化效果不明显，可能需要进一步优化");
        }
    }
    /**
     * 过滤订单状态
     */
    private List<StaSchedule> filterByOrderStatus(List<StaSchedule> schedules) {
        if (schedules.isEmpty()) {
            return schedules;
        }

        // 获取所有订单ID
        Set<String> orderIds = schedules.stream()
                                       .map(StaSchedule::getStaOrderId)
                                       .collect(Collectors.toSet());

        // 查询有效的订单ID
        LambdaQueryWrapper<StaOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.select(StaOrder::getId);
        orderWrapper.in(StaOrder::getId, orderIds)
                   .notIn(StaOrder::getStateFlag, Arrays.asList(
                           CommonConstant.ORDER_STATUS_0,
                           CommonConstant.ORDER_STATUS_1,
                           CommonConstant.ORDER_STATUS_2,
                           CommonConstant.ORDER_STATUS_6));

        Set<String> validOrderIds = staOrderMapper.selectList(orderWrapper)
                                                  .stream()
                                                  .map(StaOrder::getId)
                                                  .collect(Collectors.toSet());

        // 过滤排班数据
        return schedules.stream()
                       .filter(schedule -> validOrderIds.contains(schedule.getStaOrderId()))
                       .collect(Collectors.toList());
    }
    /**
     * 添加日期范围条件
     */
    private void addDateRangeConditions(LambdaQueryWrapper<StaSchedule> wrapper, String dateRangeBegin, String dateRangeEnd) {
        if (WxlConvertUtils.isNotEmpty(dateRangeBegin) && WxlConvertUtils.isNotEmpty(dateRangeEnd)) {
            wrapper.between(StaSchedule::getScheduleDay, dateRangeBegin, dateRangeEnd);
        } else if (WxlConvertUtils.isNotEmpty(dateRangeBegin)) {
            wrapper.ge(StaSchedule::getScheduleDay, dateRangeBegin);
        } else if (WxlConvertUtils.isNotEmpty(dateRangeEnd)) {
            wrapper.le(StaSchedule::getScheduleDay, dateRangeEnd);
        }
    }

    // 合并日期和时间
    private Date mergeDateAndTime(Date scheduleDay, Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(scheduleDay);
        Calendar timeCalendar = Calendar.getInstance();
        timeCalendar.setTime(time);
        // 设置时分秒
        calendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, timeCalendar.get(Calendar.SECOND));
        return calendar.getTime();
    }
    /**
     * 创建排班模板
     *
     * @param staSchedules
     * @return
     */
    public List<lcDayPage> createScheduleTemplate(List<StaSchedule> staSchedules) {
        List<lcDayPage> ldp = new ArrayList<>(staSchedules.size() * 2); // 预估容量，避免扩容

        for (StaSchedule staSchedule : staSchedules) {
            if (staSchedule.getStartTime() != null) {
                ldp.add(buildDayPage(staSchedule, CommonConstant.CLOCK_TYPE_1, staSchedule.getStartTime()));
            }
            if (staSchedule.getEndTime() != null) {
                ldp.add(buildDayPage(staSchedule, CommonConstant.CLOCK_TYPE_2, staSchedule.getEndTime()));
            }
        }
        return ldp;
    }
    private lcDayPage buildDayPage(StaSchedule staSchedule, int clockType, Date timeExpect) {
        lcDayPage page = new lcDayPage();
        page.setScheduleId(staSchedule.getId());
        page.setTimeType(clockType);
        page.setTimeExpect(timeExpect);
        page.setLcList(new StaLocationClock());
        page.setSysUserId(staSchedule.getSysUserId());
        page.setUserIdCard(staSchedule.getUserIdCard());
        page.setRealName(staSchedule.getRealName());
        page.setScheduleDay(staSchedule.getScheduleDay());
        page.setStaOrderId(staSchedule.getStaOrderId());
        page.setShiftCode(staSchedule.getCode());
        page.setStartTime(staSchedule.getStartTime());
        page.setEndTime(staSchedule.getEndTime());
        page.setStoreNo(staSchedule.getStoreNo());
        page.setCompanyName(staSchedule.getCompanyName());
        page.setBusinessDivisionName(staSchedule.getBusinessDivisionName());
        page.setRegionName(staSchedule.getRegionName());
        return page;
    }
    /**
     * 填充排班模板
     *
     * @param ldp               排班模板
     * @param staLocationClocks 签到集合
     * @return
     * @throws ParseException
     */
    public List<lcDayPage> addScheduleTemplate(List<lcDayPage> ldp, List<StaLocationClock> staLocationClocks) throws
            ParseException {

        long optimizeStart = System.currentTimeMillis();
        log.info("🚀 开始优化模板处理，模板数量：{}，签到记录数量：{}", ldp.size(), staLocationClocks.size());

        SimpleDateFormat sdf = new SimpleDateFormat("HH-mm");
        List<lcDayPage> lcDayPages = new ArrayList<>(ldp.size());

        // 🚀 性能优化：使用Map建立索引，避免O(n²)复杂度
        Map<String, List<StaLocationClock>> clockIndexMap = new HashMap<>();
        for (StaLocationClock clock : staLocationClocks) {
            String key = buildClockKey(clock.getTimeExpect(), clock.getTimeType(), clock.getStaOrderId());
            clockIndexMap.computeIfAbsent(key, k -> new ArrayList<>()).add(clock);
        }

        long indexTime = System.currentTimeMillis();
        log.info("🚀 建立签到记录索引耗时：{}ms，索引数量：{}", (indexTime - optimizeStart), clockIndexMap.size());

        // 🚀 优化后的模板填充逻辑
        for (lcDayPage dayPage : ldp) {
            String key = buildClockKey(dayPage.getTimeExpect(), dayPage.getTimeType(), dayPage.getStaOrderId());
            List<StaLocationClock> matchingClocks = clockIndexMap.get(key);

            if (matchingClocks != null && !matchingClocks.isEmpty()) {
                // 找到匹配的签到记录，选择最合适的一条
                StaLocationClock bestMatch = findBestMatchingClock(dayPage, matchingClocks, sdf);
                if (bestMatch != null) {
                    bestMatch.setScheduleDay(dayPage.getScheduleDay());
                    bestMatch.setShiftCode(dayPage.getShiftCode());
                    dayPage.setLcList(bestMatch);

                    // 从索引中移除已使用的记录
                    matchingClocks.remove(bestMatch);
                    if (matchingClocks.isEmpty()) {
                        clockIndexMap.remove(key);
                    }
                }
            }
            lcDayPages.add(dayPage);
        }

        long fillTime = System.currentTimeMillis();
        log.info("🚀 模板填充耗时：{}ms", (fillTime - indexTime));
        // 🚀 优化缺卡处理逻辑
        long missingStart = System.currentTimeMillis();
        List<lcDayPage> finalPages = new ArrayList<>(lcDayPages.size());

        for (lcDayPage dayPage : lcDayPages) {
            if (dayPage.getLcList() == null || dayPage.getLcList().getId() == null) {
                if (dayPage.getTimeType() == 0) {
                    // 跳过timeType为0且无签到记录的记录
                    continue;
                }
                // 创建缺卡记录
                StaLocationClock missingClock = new StaLocationClock();
                missingClock.setTimeExpect(dayPage.getTimeExpect());
                missingClock.setStaOrderId(dayPage.getStaOrderId());
                missingClock.setTimeType(dayPage.getTimeType());
                missingClock.setScheduleDay(dayPage.getScheduleDay());
                missingClock.setStaScheduleId(dayPage.getScheduleId());
                missingClock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_0);
                missingClock.setShiftCode(dayPage.getShiftCode());
                dayPage.setLcList(missingClock);
            }
            finalPages.add(dayPage);
        }

        long totalTime = System.currentTimeMillis();
        log.info("🚀 缺卡处理耗时：{}ms", (totalTime - missingStart));
        log.info("🚀 模板处理总耗时：{}ms，最终模板数量：{}", (totalTime - optimizeStart), finalPages.size());

        return finalPages;
    }

    /**
     * 🚀 构建签到记录的索引键
     */
    private String buildClockKey(Date timeExpect, Integer timeType, String staOrderId) {
        return timeExpect.toString() + "_" + timeType + "_" + staOrderId;
    }

    /**
     * 🚀 找到最匹配的签到记录
     */
    private StaLocationClock findBestMatchingClock(lcDayPage dayPage, List<StaLocationClock> matchingClocks, SimpleDateFormat sdf) throws ParseException {
        Date startTime = dayPage.getStartTime();
        Date endTime = dayPage.getEndTime();
        long shiftDuration = this.dateDifference(startTime, endTime);

        StaLocationClock bestMatch = null;
        long bestDifference = Long.MAX_VALUE;

        for (StaLocationClock clock : matchingClocks) {
            Date clockTime = sdf.parse(sdf.format(clock.getTime()));
            long timeDifference;

            if (dayPage.getTimeType().equals(CommonConstant.CLOCK_TYPE_1)) { // 签到
                timeDifference = this.dateDifference(startTime, clockTime);
            } else if (dayPage.getTimeType().equals(CommonConstant.CLOCK_TYPE_2)) { // 签退
                timeDifference = this.dateDifference(endTime, clockTime);
            } else {
                continue;
            }

            // 选择时间差最小且在合理范围内的记录
            if (shiftDuration > timeDifference && timeDifference < bestDifference) {
                bestMatch = clock;
                bestDifference = timeDifference;
            }
        }

        return bestMatch;
    }

    /**
     * step1:填充基本信息,签到记录（填充 缺卡，迟到时长，早退时长）数据过滤(今天以前的数据包含今天)
     * step2:排序日期降序->姓名分组(LinkedHashMap)-> 转换为原始数据
     * step3:单日时数计算填充
     *
     * @param lcDayPages
     * @return
     */
    public List<StaLocationClock> addClockAndUserInfoAndSort(List<lcDayPage> lcDayPages, String order) throws ParseException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<StaLocationClock> slc = new ArrayList<>();
        for (lcDayPage lcd : lcDayPages) {
            StaLocationClock lcList = lcd.getLcList();
            lcList.setRealName(lcd.getRealName()); //姓名
            lcList.setUserIdCard(lcd.getUserIdCard()); //身份证
            lcList.setSysUserId(lcd.getSysUserId()); //用户id
            lcList.setCompanyName(lcd.getCompanyName()); //门店
            lcList.setStoreNo(lcd.getStoreNo()); //店编
            lcList.setBusinessDivisionName(lcd.getBusinessDivisionName()); //事业处
            lcList.setRegionName(lcd.getRegionName()); //区域
            lcd.setLcList(lcList);
            slc.add(lcd.getLcList());
        }
        //过滤掉今天以后的数据，保留今天和今天以前的数据
        LocalDate today = LocalDate.now();
        List<StaLocationClock> anticipateList = slc.stream()
                .filter(s -> s.getScheduleDay().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().isBefore(today.plusDays(1))).collect(Collectors.toList());
        List<StaLocationClock> clockInCollection = new ArrayList<>();
        //key:排班日期+班别代码+订单id value:List<StaLocationClock>>
        Map<String, List<StaLocationClock>> extractedAnticipateClockElements = new HashMap<>();
        //遍历anticipateList检测key的条目如果不存在如果不存在建立一个新的ArrayList如果存在获取对应anticipateList中的值
        for (StaLocationClock staLocationClock : anticipateList) {
            String key = staLocationClock.getScheduleDay() + "-" + staLocationClock.getShiftCode() + "-" + staLocationClock.getStaOrderId();
            extractedAnticipateClockElements.computeIfAbsent(key, k -> new ArrayList<>()).add(staLocationClock);
        }
        //填充、缺卡、迟到时长、早退时长
        for (List<StaLocationClock> staLocationClock : extractedAnticipateClockElements.values()) {
            StaLocationClock clock = new StaLocationClock();
            for (StaLocationClock extractedElement : staLocationClock) {
                if (StringUtils.isEmpty(clock.getStaOrderId())) {
                    BeanUtils.copyProperties(extractedElement, clock);
                }
                if (StringUtils.isEmpty(clock.getClockInTime1())) { //签到1
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime1("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        LocalDateTime timeExpect = extractedElement.getTimeExpect().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime1Expect(timeExpect.format(formatter)); //要求签到时间
                        clock.setClockInTime1(time.format(formatter)); //签到时间
                        clock.setClockImg1(WxlConvertUtils.isNotEmpty(extractedElement.getClockImg()) ? extractedElement.getClockImg() : null); //签到图片
                        if (extractedElement.getLengthOfTardiness() != null) { //迟到时间
                            clock.setLateDisplay(String.valueOf(extractedElement.getLengthOfTardiness()));
                        }
                    }
                    clock.setClockInStatus1(extractedElement.getStateFlag());
                } else if (StringUtils.isEmpty(clock.getClockInTime2())) {//签到2
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime2("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        LocalDateTime timeExpect = extractedElement.getTimeExpect().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime2Expect(timeExpect.format(formatter));
                        clock.setClockInTime2(time.format(formatter));
                        //签到图片
                        clock.setClockImg2(WxlConvertUtils.isNotEmpty(extractedElement.getClockImg()) ? extractedElement.getClockImg() : null);
                        if (extractedElement.getEarlyLeaveDuration() != null) {
                            clock.setLeaveEarly(String.valueOf(extractedElement.getEarlyLeaveDuration()));
                        }
                    }
                    clock.setClockInStatus2(extractedElement.getStateFlag());
                } else if (StringUtils.isEmpty(clock.getClockInTime3())) { //签到3
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime3("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        LocalDateTime timeExpect = extractedElement.getTimeExpect().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime3Expect(timeExpect.format(formatter));
                        clock.setClockInTime3(time.format(formatter));//签到
                        clock.setClockImg3(WxlConvertUtils.isNotEmpty(extractedElement.getClockImg()) ? extractedElement.getClockImg() : null);
                        //迟到时长
                        if (extractedElement.getLengthOfTardiness() != null && extractedElement.getLengthOfTardiness() > 0) {
                            clock.setLateDisplay(clock.getLateDisplay() != null
                                    ? clock.getLateDisplay() + "+" + extractedElement.getLengthOfTardiness()
                                    : String.valueOf(extractedElement.getLengthOfTardiness()));
                        }
                    }
                    clock.setClockInStatus3(extractedElement.getStateFlag());
                } else if (StringUtils.isEmpty(clock.getClockInTime4())) { //签到4
                    if (extractedElement.getStateFlag().equals(CommonConstant.CLOCK_STATE_FLAG_0)) {
                        clock.setClockInTime4("无");
                    } else {
                        LocalDateTime time = extractedElement.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        LocalDateTime timeExpect = extractedElement.getTimeExpect().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                        clock.setClockInTime4Expect(timeExpect.format(formatter));
                        clock.setClockInTime4(time.format(formatter));//签退2
                        clock.setClockImg4(WxlConvertUtils.isNotEmpty(extractedElement.getClockImg()) ? extractedElement.getClockImg() : null);
                        //早退时长
                        if (extractedElement.getEarlyLeaveDuration() != null && extractedElement.getEarlyLeaveDuration() > 0) {
                            clock.setLeaveEarly(clock.getLeaveEarly() != null
                                    ? clock.getLeaveEarly() + "+" + extractedElement.getEarlyLeaveDuration()
                                    : String.valueOf(extractedElement.getEarlyLeaveDuration()));
                        }
                    }
                    clock.setClockInStatus4(extractedElement.getStateFlag());
                }
            }
            clockInCollection.add(clock);
        }
        // 🚀 优化排序性能 - 使用并行流排序
        long sortStart = System.currentTimeMillis();
        if (clockInCollection.size() > 10000) {
            // 大数据量使用并行流排序
            List<StaLocationClock> sortedList;
            if (WxlConvertUtils.isNotEmpty(order) && order.equals("asc")) {
                sortedList = clockInCollection.parallelStream()
                    .sorted(Comparator.comparing(StaLocationClock::getScheduleDay))
                    .collect(Collectors.toList());
            } else {
                sortedList = clockInCollection.parallelStream()
                    .sorted(Comparator.comparing(StaLocationClock::getScheduleDay).reversed())
                    .collect(Collectors.toList());
            }
            clockInCollection.clear();
            clockInCollection.addAll(sortedList);
            log.debug("🚀 使用并行流排序优化大数据量");
        } else {
            // 小数据量使用普通排序
            if (WxlConvertUtils.isNotEmpty(order) && order.equals("asc")) {
                clockInCollection.sort(Comparator.comparing(StaLocationClock::getScheduleDay));
            } else {
                clockInCollection.sort(Comparator.comparing(StaLocationClock::getScheduleDay).reversed());
            }
        }
        long sortEnd = System.currentTimeMillis();
        log.debug("🚀 排序耗时：{}ms，记录数：{}", (sortEnd - sortStart), clockInCollection.size());
        // 🚀 优化数据处理性能
        long dataProcessStart = System.currentTimeMillis();

        // 🚀 激进优化：预分配容量，避免动态扩容
        Set<String> companyIds = new HashSet<>(clockInCollection.size() / 10);

        // 🚀 优化UUID生成 - 使用并行流处理大数据量
        if (clockInCollection.size() > 5000) {
            clockInCollection.parallelStream().forEach(staLocationClock -> {
                staLocationClock.setId(UUID.randomUUID().toString());
                if (staLocationClock.getCompanyId() != null) {
                    synchronized (companyIds) {
                        companyIds.add(staLocationClock.getCompanyId());
                    }
                }
            });
            log.debug("🚀 使用并行流处理UUID生成，数据量：{}", clockInCollection.size());
        } else {
            // 小数据量使用普通循环
            for (StaLocationClock staLocationClock : clockInCollection) {
                staLocationClock.setId(UUID.randomUUID().toString());
                if (staLocationClock.getCompanyId() != null) {
                    companyIds.add(staLocationClock.getCompanyId());
                }
            }
        }

        long dataProcessEnd = System.currentTimeMillis();
        log.debug("🚀 数据处理耗时：{}ms", (dataProcessEnd - dataProcessStart));

        //班次1时数
        Long morning = null;
        double workHours = 0;
        //班次2时数
        Long afternoon = null;
        double workHours2 = 0;

        // 🚀 处理工时计算逻辑
        for (StaLocationClock staLocationClock : clockInCollection) {
            if ((WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime1())
                    && WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime2())
                    && (!staLocationClock.getClockInTime1().equals("无") && !staLocationClock.getClockInTime2().equals("无"))
                    && (WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime1Expect()) && WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime2Expect())))) {
                Date clockInTime1Expect = sb.parse(staLocationClock.getClockInTime1Expect()); //要求签到时间
                Date clockInTime2Expect = sb.parse(staLocationClock.getClockInTime2Expect()); //要求签退时间
                Date clockInTime1 = sb.parse(staLocationClock.getClockInTime1());//实际签到时间
                Date clockInTime2 = sb.parse(staLocationClock.getClockInTime2());//实际签退时间
                String startDate = "";//签到
                String endDate = "";//签退
                // 判断签到时间
                if (clockInTime1.before(clockInTime1Expect)) {
                    startDate = staLocationClock.getClockInTime1Expect(); //提前签到使用要求时间
                } else if (clockInTime1.after(clockInTime1Expect)) { //迟到
                    startDate = staLocationClock.getClockInTime1();
                } else {
                    startDate = staLocationClock.getClockInTime1Expect();
                }//相等
                // 判断签退时间
                if (clockInTime2.before(clockInTime2Expect)) {//早退
                    endDate = staLocationClock.getClockInTime2();
                } else if (clockInTime2.after(clockInTime2Expect)) {
                    endDate = staLocationClock.getClockInTime2Expect();
                } else {
                    endDate = staLocationClock.getClockInTime2();
                }//相等
                morning = this.calculateWorkingMinutes(startDate, endDate);
                double workingHours = morning / 60.0;
                workHours = workingHours; //班次1时数
            }
            if ((WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime3()) && WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime4())
                    && (!staLocationClock.getClockInTime3().equals("无") && !staLocationClock.getClockInTime4().equals("无"))
                    && (WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime3Expect()) && WxlConvertUtils.isNotEmpty(staLocationClock.getClockInTime4Expect())))) {
                Date clockInTime3Expect = sb.parse(staLocationClock.getClockInTime3Expect()); //要求签到时间
                Date clockInTime4Expect = sb.parse(staLocationClock.getClockInTime4Expect()); //要求签退时间
                Date clockInTime3 = sb.parse(staLocationClock.getClockInTime3());//实际签到时间
                Date clockInTime4 = sb.parse(staLocationClock.getClockInTime4());//实际签退时间
                String sd = "";//签到
                String ed = "";//签退
                // 判断签到时间
                if (clockInTime3.before(clockInTime3Expect)) {
                    sd = staLocationClock.getClockInTime3Expect(); //提前签到使用要求时间
                } else if (clockInTime3.after(clockInTime3Expect)) { //迟到
                    sd = staLocationClock.getClockInTime3();
                } else {
                    sd = staLocationClock.getClockInTime3Expect(); //提前签到使用要求时间
                }//相等
                // 判断签退时间
                if (clockInTime4.before(clockInTime4Expect)) {//早退
                    ed = staLocationClock.getClockInTime4();
                } else if (clockInTime4.after(clockInTime4Expect)) {
                    ed = staLocationClock.getClockInTime4Expect();
                } else {
                    ed = staLocationClock.getClockInTime4();
                }//相等
                afternoon = this.calculateWorkingMinutes(sd, ed);
                double workingHours2 = afternoon / 60.0;
                workHours2 = workingHours2; //班次2时数
            }
            double workHoursCount = workHours + workHours2;
            String str = String.format("%.1f", workHoursCount);
            staLocationClock.setDailyWorkingHours(Double.parseDouble(str));
        }
        return clockInCollection;
    }

    /**
     * 填充 年龄 性别 生日
     *
     * @param pageList
     * @return
     */
    public IPage<StaOrder> populateAgeGenderBirth(IPage<StaOrder> pageList) {
        pageList.getRecords().forEach(r -> {
                    if (IdCardUtil.isIdCardNumberValid(r.getEnrollIdCard())) {
                        if (IdCardUtil.isIdCardNumberValid(r.getEnrollIdCard())) {
                            String gender = IdCardUtil.judgeGender(r.getEnrollIdCard());
                            int age = IdCardUtil.countAge(r.getEnrollIdCard());
                            String birthFromIdCard = IdCardUtil.getBirthFromIdCard(r.getEnrollIdCard());
                            r.setAge(age);
                            r.setSex(gender);
                            r.setDateOfBirth(birthFromIdCard);
                        }
                    }
                }
        );
        return pageList;
    }

    /***
     * 任务单状态 maps
     * @return
     */
    public Map<Integer, String> taskOrderMaps() {
        return new HashMap<Integer, String>() {{
            put(0, "撤销");
            put(1, "完成");
            put(2, "申请中");
            put(3, "服务中");
            put(4, "待结算中");
            put(5, "待就职");
            put(6, "未通过审核");
            put(7, "通过审核后被剔除");
            put(8, "任务完成");
            put(9, "申请已过期");
            put(10, "任务完成申请中");
            put(11, "已完成");
            put(13, "异动申请中");
            put(14, "异动");
        }};
    }
}
