package com.byun.modules.staffing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.constant.CommonConstant;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaSchedule;
import com.byun.modules.staffing.entity.StaScheduleNo;
import com.byun.modules.staffing.mapper.StaOrderMapper;
import com.byun.modules.staffing.mapper.StaScheduleMapper;
import com.byun.modules.staffing.mapper.StaScheduleNoMapper;
import com.byun.modules.staffing.service.IStaScheduleNoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 排班编号表实现类
 * @date : 2022-9-23 8:52
 */
@Service
public class StaScheduleNoServiceImpl extends ServiceImpl<StaScheduleNoMapper, StaScheduleNo> implements IStaScheduleNoService {

    /**
     * 工时管控实现
     * @param oid
     * @return
     */
    @Autowired
    private StaScheduleMapper staScheduleMapper;
    @Autowired
    private StaOrderMapper staOrderMapper;
    @Override
    public Double timeControl(StaOrder staOrder  ,String scheduleDay) {
        //TODO 姓名
        Map<String, String> currentMonthStartAndEnd =this.getCurrentMonthStartAndEnd();
        String enrollIdCard = staOrder.getEnrollIdCard(); //用户身份证号码
        String startTime = currentMonthStartAndEnd.get("start");//本月开始时间
        String endTime = currentMonthStartAndEnd.get("end");//本月结束时间
        //获取用户任务单
        List<StaOrder> staOrders = staOrderMapper.selectList(new LambdaQueryWrapper<StaOrder>()
                .eq(StaOrder::getEnrollIdCard, enrollIdCard)
                .notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_0, CommonConstant.ORDER_STATUS_2, CommonConstant.ORDER_STATUS_5, CommonConstant.ORDER_STATUS_6))
//                .ge(StaOrder::getEntryDate, startTime) // 大于等于开始日期
//                .le(StaOrder::getEntryDate, endTime) // 小于等于结束日期
        );
        List<String> oids = new ArrayList<>(); //任务单id集合
        for (StaOrder order : staOrders) { oids.add(order.getId());   }
        List<StaSchedule> staSchedules = staScheduleMapper.selectList(
                new LambdaQueryWrapper<StaSchedule>().in(StaSchedule::getStaOrderId, oids).eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0)
                .ge(StaSchedule::getScheduleDay, startTime) // 大于等于开始日期
                .le(StaSchedule::getScheduleDay, endTime) // 小于
                .ne(StaSchedule::getScheduleDay,scheduleDay)
                .orderByDesc(StaSchedule::getScheduleDay)
        );
        return this.calculateTotalWorkTime(staSchedules);
    }
    /**
     * 计算总工时（小时）
     * @param staSchedules 工作时间列表
     * @return 总工时（小时）
     */
    private double calculateTotalWorkTime(List<StaSchedule> staSchedules) {
        // 初始化总工作时长
        final long[] totalMinutes = {0};
        staSchedules.forEach(staSchedule -> {
            Date startTime = staSchedule.getStartTime();
            Date endTime = staSchedule.getEndTime();

            if (startTime != null && endTime != null) {
                long durationMillis = endTime.getTime() - startTime.getTime();
                if (durationMillis < 0) {
                    // 跨夜情况，添加 24 小时
                    durationMillis += TimeUnit.DAYS.toMillis(1);
                }
                // 累加时长（以分钟为单位）
                totalMinutes[0] += TimeUnit.MILLISECONDS.toMinutes(durationMillis);
            }
        });
        // 返回总时长（小时），保留两位小数
        return totalMinutes[0] / 60.0;
    }
    /**
     * 获取本月的开始和结束时间
     * @return Map 包含 "start" 和 "end" 键，分别表示本月的开始时间和结束时间（yyyy-MM-dd 格式）
     */
    private   Map<String, String> getCurrentMonthStartAndEnd() {
        // 获取当前日期
        LocalDate now = LocalDate.now() ;
        //LocalDate now = now22.minusMonths(1); // 上个月进行测试
        // 本月开始时间
        LocalDate startOfMonth = now.withDayOfMonth(1);
        // 本月结束时间
        LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());
        // 格式化为 yyyy-MM-dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = startOfMonth.format(formatter);
        String endDate = endOfMonth.format(formatter);
        // 将结果放入 Map
        Map<String, String> result = new HashMap<>();
        result.put("start", startDate);
        result.put("end", endDate);
        return result;
    }
}
