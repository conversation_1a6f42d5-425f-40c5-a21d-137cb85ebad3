package com.byun.modules.staffing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.DateUtils;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.mapper.*;
import com.byun.modules.staffing.service.IStaLocationClockService;
import com.byun.modules.staffing.service.IStaScheduleService;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.mapper.SysDepartMapper;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 排班表
 * @Author: bai
 * @Date: 2022-08-17
 * @Version: V1.0
 */
@Service
public class StaScheduleServiceImpl extends ServiceImpl<StaScheduleMapper, StaSchedule> implements IStaScheduleService {
    @Autowired
    private StaScheduleMapper staScheduleMapper;
    @Autowired
    private StaOrderMapper staOrderMapper;
    @Autowired
    private StaLocationClockMapper staLocationClockMapper;
    @Autowired
    private IStaLocationClockService staLocationClockService;
    //@Autowired
    //private StaTimeSheetMapper staTimeSheetMapper;
    @Autowired
    private StaWorkMapper staWorkMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    //给订单更新排班

    @Override
    @Transactional
    public void updateBatchScheduleForOrder(List<StaSchedule> list, StaOrder order) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String orgCode;
        SysDepart sysDepart = new SysDepart();
        SysDepart sysDepart1 = new SysDepart();
        SysDepart sysDepart2 = new SysDepart();
        //如果是修改任务开始后的排班数据，则只进行逻辑上删除
        if (CommonConstant.WORK_STATUS_1.compareTo(order.getStateFlag()) == 0 || CommonConstant.WORK_STATUS_3.compareTo(order.getStateFlag()) == 0 || CommonConstant.WORK_STATUS_4.compareTo(order.getStateFlag()) == 0) {
            LambdaUpdateWrapper<StaSchedule> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(StaSchedule::getStaOrderId, order.getId());
            if (WxlConvertUtils.isNotEmpty(list.get(0).getStartDay()) && WxlConvertUtils.isNotEmpty(list.get(0).getEndDay())) {
                lambdaUpdateWrapper.between(StaSchedule::getScheduleDay, list.get(0).getStartDay(), list.get(0).getEndDay());
            } else {
                lambdaUpdateWrapper.eq(StaSchedule::getScheduleDay, list.get(0).getScheduleDay());
            }
            lambdaUpdateWrapper.set(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_1);
            lambdaUpdateWrapper.set(StaSchedule::getUpdateBy, user.getUsername());
            lambdaUpdateWrapper.set(StaSchedule::getUpdateTime, new Date());
            this.update(lambdaUpdateWrapper);
            if (list != null && !list.isEmpty()) {
                String staWorkId = list.get(0).getStaWorkId();
                StaWork staWork = staWorkMapper.selectById(staWorkId);
                String companyId = staWork.getCompanyId();
                sysDepart = sysDepartMapper.selectById(companyId); //门店
                if (WxlConvertUtils.isNotEmpty(sysDepart)) {
                    sysDepart1 = sysDepartMapper.selectById(sysDepart.getParentId()); //事业处
                    if (WxlConvertUtils.isNotEmpty(sysDepart1)) {
                        sysDepart2 = sysDepartMapper.selectById(sysDepart1.getParentId()); //区域
                    }
                }
                orgCode = sysDepart.getOrgCode();
            } else {
                orgCode = null;
            }
            for (StaSchedule staSchedule : list) {
                staSchedule.setOrgCode(orgCode != null ? orgCode : "");
                staSchedule.setStoreNo(sysDepart.getStoreNo() != null ? sysDepart.getStoreNo() : "");
                staSchedule.setCompanyName(sysDepart.getDepartName() != null ? sysDepart.getDepartName() : "");
                staSchedule.setBusinessDivisionName(sysDepart1.getDepartName() != null ? sysDepart1.getDepartName() : "");
                staSchedule.setRegionName(sysDepart2.getDepartName() != null ? sysDepart2.getDepartName() : "");
            }
            //添加排班数据
            this.saveBatch(list);
        } else {
            LambdaQueryWrapper<StaSchedule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(StaSchedule::getStaOrderId, order.getId());
            if (WxlConvertUtils.isNotEmpty(list.get(0).getStartDay()) && WxlConvertUtils.isNotEmpty(list.get(0).getEndDay())) {
                lambdaQueryWrapper.between(StaSchedule::getScheduleDay, list.get(0).getStartDay(), list.get(0).getEndDay());
            } else {
                lambdaQueryWrapper.eq(StaSchedule::getScheduleDay, list.get(0).getScheduleDay());
            }
            this.remove(lambdaQueryWrapper);
            if (list != null && list.size() > 0) {
                String staWorkId = list.get(0).getStaWorkId();
                StaWork staWork = staWorkMapper.selectById(staWorkId);
                String companyId = staWork.getCompanyId();
                sysDepart = sysDepartMapper.selectById(companyId); //门店
                if (WxlConvertUtils.isNotEmpty(sysDepart)) {
                    sysDepart1 = sysDepartMapper.selectById(sysDepart.getParentId()); //事业处
                    if (WxlConvertUtils.isNotEmpty(sysDepart1)) {
                        sysDepart2 = sysDepartMapper.selectById(sysDepart1.getParentId()); //区域
                    }
                }
                orgCode = sysDepart.getOrgCode();
            } else {
                orgCode = null;
            }
            //门店名称  店编 门店code  区域 事业处
            for (StaSchedule staSchedule : list) {
                staSchedule.setOrgCode(orgCode != null ? orgCode : "");
                staSchedule.setStoreNo(sysDepart.getStoreNo() != null ? sysDepart.getStoreNo() : "");
                staSchedule.setCompanyName(sysDepart.getDepartName() != null ? sysDepart.getDepartName() : "");
                staSchedule.setBusinessDivisionName(sysDepart1.getDepartName() != null ? sysDepart1.getDepartName() : "");
                staSchedule.setRegionName(sysDepart2.getDepartName() != null ? sysDepart2.getDepartName() : "");
            }
            //添加排班数据
            this.saveBatch(list);
        }
    }

    /**
     * 获取当天的排班和签到
     *
     * @param staSchedules
     * @param staLocationClocks·
     * @return
     */
    @Override
    public List<StaSchedule> getStaScheduleClock(List<StaSchedule> staSchedules, List<StaLocationClock> staLocationClocks) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("HH-mm");
        for (StaSchedule staSchedule : staSchedules) {
            Iterator<StaLocationClock> iterator = staLocationClocks.iterator();
            while (iterator.hasNext()) {
                StaLocationClock staLocationClock = iterator.next();
                if (staSchedules.size() == 1) {//一个班次（签到-签退）
                    if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_1)) {
                        staSchedule.setStartClock(staLocationClock.getTime());
                        staSchedule.setAddress(staLocationClock.getLcAddress());
                        iterator.remove();
                    } else if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_2)) {
                        staSchedule.setEndClock(staLocationClock.getTime());
                        staSchedule.setAddress(staLocationClock.getLcAddress());
                        iterator.remove();
                    }
                } else if (staSchedules.size() == 2) {//两个班次，上午（签到-签退）下午（签到-签退）
                    Date startTime = staSchedule.getStartTime();
                    Date endTime = staSchedule.getEndTime();
                    long shiftOneNum = this.dateDifference(startTime, endTime);//班次1 签到和签退的时间差（分钟）
                    //yyyy-MM-dd HH:mm:ss 转换为 HH-mm
                    Date time = staLocationClock.getTime();
                    String format = sdf.format(time);
                    Date clockTime = sdf.parse(format);
                    if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_1)) {//签到
                        long l = this.dateDifference(startTime, clockTime); //第一次签到时间和第一个签到时间差（分钟）
                        //班次1 签到-签退时间差 大于 班次1 签到-签到签到时间差
                        if (shiftOneNum > l) {
                            staSchedule.setStartClock(clockTime);
                            staSchedule.setAddress(staLocationClock.getLcAddress());
                            iterator.remove();
                        }
                    } else if (staLocationClock.getTimeType().equals(CommonConstant.CLOCK_TYPE_2)) {//签退
                        //班次1签退  班次2 签退  08:30-12:00  14:00-20:30
                        long l = this.dateDifference(endTime, clockTime);
                        if (shiftOneNum > l) {
                            staSchedule.setEndClock(clockTime);
                            staSchedule.setAddress(staLocationClock.getLcAddress());
                            iterator.remove();
                        }
                    }
                } else {
                    //没有三个班次
                    return null;
                }
            }
        }
        return staSchedules;
    }

    /**
     * 网页增加排班
     *
     * @param oid
     * @param scheduleDay
     * @return
     */
    @Override
    public void addSchedule(String oid, String scheduleDay, List<StaSchedule> staSchedules) {
        //查询当日未关联的签到记录
        List<StaLocationClock> clocks = staLocationClockMapper.selectList(new QueryWrapper<StaLocationClock>()
                .eq("sta_order_id", oid)
                .eq("del_flag", CommonConstant.DEL_FLAG_0)
                .likeRight("time", scheduleDay)
                .orderByAsc("time")
        );
        if (!clocks.isEmpty()) {
            List<StaLocationClock> upDateClocks = new ArrayList<>();
            for (StaSchedule staSchedule : staSchedules) {
                Date startTime = DateUtils.stringAppenDate(scheduleDay, staSchedule.getStartTime()); //签到时间
                Date endTime = DateUtils.stringAppenDate(scheduleDay, staSchedule.getEndTime());//签退时间
//                if (staSchedules.size() == 1){//单个班次
//                    if (startTime.after(endTime)) { //跨夜
//                    }else {
//                    }
//                }else { //两个班次
//                }
                //clocks = clocks.stream().sorted(Comparator.comparing(StaLocationClock::getTime)).collect(Collectors.toList()); //升序
                //clocks = clocks.stream().sorted(Comparator.comparing(StaLocationClock::getTime).reversed()).collect(Collectors.toList());//降序
                for (StaLocationClock clock : clocks) {
                    if (clock.getTimeType().equals(CommonConstant.CLOCK_TYPE_1)) {//签到类型
                        clock.setTimeExpect(startTime); //要求签到时间
                        if (startTime.getTime() < clock.getTime().getTime()) {//迟到
                            clock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_2);
                            //迟到时长(分钟)
                            long millisecondsDifference = startTime.getTime() - clock.getTime().getTime();
                            long lateArrivalTime = millisecondsDifference / (60 * 1000);
                            int lateInt = (int) lateArrivalTime;
                            clock.setLengthOfTardiness(Math.abs(lateInt));
                            clock.setStaScheduleId(staSchedule.getId());
                            upDateClocks.add(clock);
                        } else { //正常
                            clock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
                            clock.setStaScheduleId(staSchedule.getId());
                            clock.setLengthOfTardiness(0);
                            upDateClocks.add(clock);
                        }
                    } else if (clock.getTimeType().equals(CommonConstant.CLOCK_TYPE_2)) {//签退类型
                        clock.setTimeExpect(endTime); //要求签退时间
                        if (endTime.getTime() > clock.getTime().getTime()) {
                            clock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_3);//早退
                            long millisecondsDifference = endTime.getTime() - clock.getTime().getTime();
                            long lateArrivalTime = millisecondsDifference / (60 * 1000);
                            int lateInt = (int) lateArrivalTime;
                            clock.setEarlyLeaveDuration(lateInt);
                            clock.setStaScheduleId(staSchedule.getId());
                            upDateClocks.add(clock);
                        } else {
                            clock.setStateFlag(CommonConstant.CLOCK_STATE_FLAG_1);
                            clock.setStaScheduleId(staSchedule.getId());
                            clock.setEarlyLeaveDuration(0);
                            upDateClocks.add(clock);
                        }
                    }
                }
            }
            staLocationClockService.updateBatchById(upDateClocks);
            List<StaLocationClock> staLocationClocks = staLocationClockMapper.selectList(new QueryWrapper<StaLocationClock>()
                    .eq("sta_order_id", oid)
                    .eq("del_flag", CommonConstant.DEL_FLAG_0)
                    .likeRight("time", scheduleDay)
                    .isNull("time_expect")
                    .or()
                    .eq("time_expect", "")
                    .orderByAsc("time")
            );
            if (WxlConvertUtils.isNotEmpty(staLocationClocks) && !staLocationClocks.isEmpty()) {
                List<String> clocksIds = new ArrayList<>();
                clocks.forEach(c -> {
                    clocksIds.add(c.getId());
                });
                //删除多余的签到记录
                staLocationClockService.removeByIds(clocksIds);
            }
        }
    }

    @Override
    public int getCompanyServiceYears(String oid) {
        int companyServiceYears = staScheduleMapper.getCompanyServiceYears(oid);
        return companyServiceYears;
    }

    /**
     * 计算两个 Date 类型之间的时间差（以分钟为单位）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public long dateDifference(Date startDate, Date endDate) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(startDate);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(endDate);
        long timeDifferenceInMillis = cal2.getTimeInMillis() - cal1.getTimeInMillis();
        long minutesDifference = timeDifferenceInMillis / (60 * 1000);
        return minutesDifference;
    }
}
