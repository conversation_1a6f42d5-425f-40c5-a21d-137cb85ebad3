package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import com.byun.modules.staffing.mapper.StaScheduleTemplateItemMapper;
import com.byun.modules.staffing.service.IStaScheduleTemplateItemService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 排班模板选项
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Service
public class StaScheduleTemplateItemServiceImpl extends ServiceImpl<StaScheduleTemplateItemMapper, StaScheduleTemplateItem> implements IStaScheduleTemplateItemService {
	
	@Autowired
	private StaScheduleTemplateItemMapper staScheduleTemplateItemMapper;
	
	@Override
	public List<StaScheduleTemplateItem> selectByMainId(String mainId) {
		return staScheduleTemplateItemMapper.selectByMainId(mainId);
	}
}
