package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaScheduleTemplate;
import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import com.byun.modules.staffing.mapper.StaScheduleTemplateItemMapper;
import com.byun.modules.staffing.mapper.StaScheduleTemplateMapper;
import com.byun.modules.staffing.service.IStaScheduleTemplateService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 排班表模板
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Service
public class StaScheduleTemplateServiceImpl extends ServiceImpl<StaScheduleTemplateMapper, StaScheduleTemplate> implements IStaScheduleTemplateService {

	@Autowired
	private StaScheduleTemplateMapper staScheduleTemplateMapper;
	@Autowired
	private StaScheduleTemplateItemMapper staScheduleTemplateItemMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(StaScheduleTemplate staScheduleTemplate, List<StaScheduleTemplateItem> staScheduleTemplateItemList) {
		staScheduleTemplateMapper.insert(staScheduleTemplate);
		if(staScheduleTemplateItemList!=null && staScheduleTemplateItemList.size()>0) {
			for(StaScheduleTemplateItem entity:staScheduleTemplateItemList) {
				//外键设置
				entity.setStaScheduleTemplateId(staScheduleTemplate.getId());
				staScheduleTemplateItemMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(StaScheduleTemplate staScheduleTemplate,List<StaScheduleTemplateItem> staScheduleTemplateItemList) {
		staScheduleTemplateMapper.updateById(staScheduleTemplate);
		
		//1.先删除子表数据
		staScheduleTemplateItemMapper.deleteByMainId(staScheduleTemplate.getId());
		
		//2.子表数据重新插入
		if(staScheduleTemplateItemList!=null && staScheduleTemplateItemList.size()>0) {
			for(StaScheduleTemplateItem entity:staScheduleTemplateItemList) {
				//外键设置
				entity.setStaScheduleTemplateId(staScheduleTemplate.getId());
				staScheduleTemplateItemMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		staScheduleTemplateItemMapper.deleteByMainId(id);
		staScheduleTemplateMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			staScheduleTemplateItemMapper.deleteByMainId(id.toString());
			staScheduleTemplateMapper.deleteById(id);
		}
	}
	
}
