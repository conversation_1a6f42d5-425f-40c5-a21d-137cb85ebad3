package com.byun.modules.staffing.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.modules.staffing.entity.StaSendEmail;
import com.byun.modules.staffing.entity.StaTaskDeduction;
import com.byun.modules.staffing.mapper.StaSendEmailMapper;
import com.byun.modules.staffing.mapper.StaTaskDeductionMapper;
import com.byun.modules.staffing.service.IStaSendEmailService;
import com.byun.modules.staffing.service.IStaTaskDeductionService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-5-13 17:43
 */
@Service
public class StaTaskDeductionServiceImpl extends ServiceImpl<StaTaskDeductionMapper, StaTaskDeduction> implements IStaTaskDeductionService {

}
