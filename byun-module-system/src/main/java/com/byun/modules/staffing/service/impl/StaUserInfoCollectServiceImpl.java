package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaUserInfoCollect;
import com.byun.modules.staffing.mapper.StaUserInfoCollectMapper;
import com.byun.modules.staffing.service.IStaUserInfoCollectService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户名片收藏
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaUserInfoCollectServiceImpl extends ServiceImpl<StaUserInfoCollectMapper, StaUserInfoCollect> implements IStaUserInfoCollectService {

}
