package com.byun.modules.staffing.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.byun.modules.staffing.entity.StaUserInfo;
import com.byun.modules.staffing.entity.StaUserInfoVisits;
import com.byun.modules.staffing.mapper.StaUserInfoVisitsMapper;
import com.byun.modules.staffing.mapper.StaUserInfoMapper;
import com.byun.modules.staffing.service.IStaEnrollInfoService;
import com.byun.modules.staffing.service.IStaUserInfoService;
import com.byun.modules.system.entity.StaUserHealthyId;
import com.byun.modules.system.entity.SysUser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 用户灵活用工信息（名片）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaUserInfoServiceImpl extends ServiceImpl<StaUserInfoMapper, StaUserInfo> implements IStaUserInfoService {

	@Autowired
	private StaUserInfoMapper staUserInfoMapper;
	@Autowired
	private StaUserInfoVisitsMapper staUserInfoVisitsMapper;
	@Autowired
	private IStaEnrollInfoService staEnrollInfoService;
	@Override
	@Transactional
	public void saveMain(StaUserInfo staUserInfo, List<StaUserInfoVisits> staUserInfoVisitsList) {
		staUserInfoMapper.insert(staUserInfo);
		if(staUserInfoVisitsList!=null && staUserInfoVisitsList.size()>0) {
			for(StaUserInfoVisits entity:staUserInfoVisitsList) {
				//外键设置
				entity.setStaUserInfoId(staUserInfo.getId());
				staUserInfoVisitsMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(StaUserInfo staUserInfo,List<StaUserInfoVisits> staUserInfoVisitsList) {
		//同步报名信息
//		StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(new QueryWrapper<StaEnrollInfo>().eq("sys_user_id", staUserInfo.getSysUserId()));
//		BeanUtils.copyProperties(staEnrollInfo,staUserInfo,new String[] {"id","createTime","updateTime"});
		//其他信息
		//address address_lat address_lng
		staUserInfoMapper.updateById(staUserInfo);
		StaUserInfo sui = staUserInfoMapper.selectById(staUserInfo.getId());
		//1.先删除子表数据
		staUserInfoVisitsMapper.deleteByMainId(staUserInfo.getId());
		//2.子表数据重新插入
		if(staUserInfoVisitsList!=null && staUserInfoVisitsList.size()>0) {
			for(StaUserInfoVisits entity:staUserInfoVisitsList) {
				//外键设置
				entity.setStaUserInfoId(staUserInfo.getId());
				staUserInfoVisitsMapper.insert(entity);
			}
		}
		/**
		 * 名片绑定的用户 多对一
		 */
		String phone = sui.getPhone();
		String sysUserId = sui.getSysUserId();
		if (WxlConvertUtils.isNotEmpty(phone)&& WxlConvertUtils.isNotEmpty(sysUserId)){
			StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(
					new QueryWrapper<StaEnrollInfo>().eq("sys_user_id", sysUserId).eq("phone", phone));
			staEnrollInfo.setName(sui.getName());
			staEnrollInfo.setAge(sui.getAge());
			staEnrollInfo.setSex(sui.getSex());
			staEnrollInfo.setSalar(sui.getSalar());
			staEnrollInfo.setWorkClass(sui.getWorkClass());
			staEnrollInfo.setDegree(sui.getDegree());
			staEnrollInfo.setIdCard(sui.getIdCard());
			//地址处理
			staEnrollInfo.setAddressLng(sui.getAddressLng());
			staEnrollInfo.setAddressLat(sui.getAddressLat());
			staEnrollInfo.setAddress(staUserInfo.getAddressName());
			staEnrollInfo.setAddressName(staUserInfo.getAddress());
			//执行修改
			staEnrollInfoService.updateById(staEnrollInfo);
		}
	}
	@Override
	@Transactional
	public void delMain(String id) {
		staUserInfoVisitsMapper.deleteByMainId(id);
		staUserInfoMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			staUserInfoVisitsMapper.deleteByMainId(id.toString());
			staUserInfoMapper.deleteById(id);
		}
	}

	@Override
	public Boolean editWechatIdById(String id, String wechatId) {
		return staUserInfoMapper.editWechatIdById( id, wechatId);
	}

	@Override
	public void updateIdCarByUserId(String idCard, String id) {
		 staUserInfoMapper.updateIdCarByUserId(idCard,id);
	}

	@Override
	public Boolean addLicense(JSONObject data) {

		return null;
	}

}
