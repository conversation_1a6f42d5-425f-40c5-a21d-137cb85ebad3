package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaUserInfoVisits;
import com.byun.modules.staffing.mapper.StaUserInfoVisitsMapper;
import com.byun.modules.staffing.service.IStaUserInfoVisitsService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 名片访问量
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaUserInfoVisitsServiceImpl extends ServiceImpl<StaUserInfoVisitsMapper, StaUserInfoVisits> implements IStaUserInfoVisitsService {
	
	@Autowired
	private StaUserInfoVisitsMapper staUserInfoVisitsMapper;
	
	@Override
	public List<StaUserInfoVisits> selectByMainId(String mainId) {
		return staUserInfoVisitsMapper.selectByMainId(mainId);
	}
}
