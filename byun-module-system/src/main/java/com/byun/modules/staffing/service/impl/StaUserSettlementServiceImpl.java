package com.byun.modules.staffing.service.impl;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaUserSettlement;
import com.byun.modules.staffing.mapper.StaUserSettlementMapper;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaUserSettlementService;
import com.byun.modules.zh.service.IZhService;
import com.byun.modules.zh.vo.DoWithdrawVO;
import com.byun.modules.zh.vo.WithdrawListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 结算服务
 * @date : 2024-6-22 9:29
 */
@Service
public class StaUserSettlementServiceImpl extends ServiceImpl<StaUserSettlementMapper, StaUserSettlement> implements IStaUserSettlementService {
    @Autowired
    private IZhService zhService;
    @Autowired
    private IStaOrderService staOrderService;

    /**
     * 向供应商推送提现数据
     * @param staUserSettlements 结算数据
     * @return
     * @throws Exception
     */
    @Transactional
    @Override
    public Result<?> doWithdraw(List<StaUserSettlement> staUserSettlements) throws Exception {
        // 过滤出银行卡结算数据
        List<StaUserSettlement> bankS = filterSettlementsByTaskNo(staUserSettlements, CommonConstant.TASKNO1);
        // 过滤出支付宝结算数据
        List<StaUserSettlement> zfbS = filterSettlementsByTaskNo(staUserSettlements, CommonConstant.TASKNO2);
        // 获取Token
        String accessToken = zhService.getToken();
        // 处理银行卡结算
        if (!bankS.isEmpty()) {
            Result<?> bankResult = processWithdrawals(bankS, CommonConstant.TASKNO1, "物流配送搬运", accessToken, CommonConstant.ACCOUNTTYPE1);
            if (!bankResult.isSuccess()) {//不成功直接返回
                return bankResult;
            }
        }
        // 处理支付宝结算
        if (!zfbS.isEmpty()) {
            Result<?> zfbResult = processWithdrawals(zfbS, CommonConstant.TASKNO2, "物流配送搬运-支付宝", accessToken, CommonConstant.ACCOUNTTYPE2);
            if (!zfbResult.isSuccess()) {//不成功直接返回
                return zfbResult;
            }
        }
        return Result.OK("", "");
    }

    // 根据任务编号过滤结算数据
    private List<StaUserSettlement> filterSettlementsByTaskNo(List<StaUserSettlement> settlements, String taskNo) {
        return settlements.stream().filter(s -> s.getTaskNo().equals(taskNo)).collect(Collectors.toList());
    }

    //处理提现逻辑
    private Result<?> processWithdrawals(List<StaUserSettlement> settlements, String taskNo, String taskDetail, String accessToken, int accountType) throws Exception {
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateRange dateRange = getDateRange(settlements, sb); // 获取任务的最早和最晚时间
        List<WithdrawListVO> withdrawList = createWithdrawList(settlements, accountType); // 创建提现列表
        // 创建提现请求对象
        DoWithdrawVO doWithdrawVO = new DoWithdrawVO();
        doWithdrawVO.setWithdrawList(JSONArray.toJSONString(withdrawList));
        doWithdrawVO.setTaskNo(taskNo);
        //MCL20240624
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
        String date = sdf.format(new Date());
        String  randomNum = String.format("%010d", Math.abs(new Random().nextInt()));//10位随机数
        String batchBizId = "MCL"+date+randomNum;//批次号
        doWithdrawVO.setBatchBizId(batchBizId);
        doWithdrawVO.setTaskStartDate(sb.format(dateRange.earliestStartDate)); // 设置任务开始时间
        doWithdrawVO.setTaskEndDate(sb.format(dateRange.latestEndDate)); // 设置任务结束时间
        doWithdrawVO.setTaskDetail(taskDetail);
        //调用提现服务
        JSONObject jsonObject = zhService.doWithdraw(withdrawList, doWithdrawVO, accessToken);
        Boolean success = jsonObject.getBoolean("success");
        String resultMessage = jsonObject.getString("resultMessage");
        // 处理提现结果
        if (success) {
            JSONObject resultData = jsonObject.getJSONObject("resultData");
            JSONArray resDate = resultData.getJSONArray("data");
            JSONObject d = (JSONObject)resDate.get(0);
            Integer status = d.getInteger("status");
            if (status == 0) {
                updateBatchAndOrders(settlements, batchBizId); // 更新批次和订单信息
            }else {
                String message  = d.getString("message");
                return Result.error(message);    //返回请求成功错误信息
            }
        } else {
            return Result.error(resultMessage); // 返回错误信息
        }
        return Result.OK(resultMessage,"");
    }
    // 日期范围类，存储最早和最晚日期
    private static class DateRange {
        Date earliestStartDate;
        Date latestEndDate;
    }
    // 获取任务的最早和最晚时间
    private DateRange getDateRange(List<StaUserSettlement> settlements, SimpleDateFormat sb) throws Exception {
        sb = new SimpleDateFormat("yyyy-MM-dd");
        Date earliestStartDate = sb.parse(settlements.get(0).getTaskStartDate());
        Date latestEndDate = sb.parse(settlements.get(0).getTaskEndDate());
        for (StaUserSettlement settlement : settlements) {
            Date start = sb.parse(settlement.getTaskStartDate());
            Date end = sb.parse(settlement.getTaskEndDate());
            if (start.before(earliestStartDate)) {
                earliestStartDate = start;
            }
            if (end.after(latestEndDate)) {
                latestEndDate = end;
            }
        }
        DateRange dateRange = new DateRange();
        dateRange.earliestStartDate = earliestStartDate;
        dateRange.latestEndDate = latestEndDate;
        return dateRange;
    }

    // 创建提现列表
    private List<WithdrawListVO> createWithdrawList(List<StaUserSettlement> settlements, int accountType) {
        List<WithdrawListVO> withdrawList = new ArrayList<>();
        for (StaUserSettlement settlement : settlements) {
            WithdrawListVO withdrawListVO = new WithdrawListVO();
            withdrawListVO.setBizId(settlement.getBizId());
            withdrawListVO.setName(settlement.getUserName());
            withdrawListVO.setCertNo(settlement.getIdCard());
            withdrawListVO.setMobile(settlement.getMobile());
            withdrawListVO.setBank(accountType == CommonConstant.ACCOUNTTYPE1 ? settlement.getBankName() : "支付宝");
            withdrawListVO.setBankNo(settlement.getBankNo());
            withdrawListVO.setAmount(settlement.getActualAmountPaidCount().toString());
            withdrawListVO.setAccountType(accountType);
            withdrawList.add(withdrawListVO);
        }
        return withdrawList;
    }

    // 更新批次id和任务单信息
    private void updateBatchAndOrders(List<StaUserSettlement> settlements, String batchBizId) {
        settlements.forEach(b -> {
            b.setBatchBizId(batchBizId);
            b.setStatus(CommonConstant.SETTLETANACCOUNSTATUS2);//提现中
        });
        this.updateBatchById(settlements);
//        List<String> orderIds = settlements.stream()
//                .map(StaUserSettlement::getStaOrderIds)
//                .flatMap(ids -> Arrays.stream(ids.split(",")))
//                .collect(Collectors.toList());
//        if (!orderIds.isEmpty()) {
//            List<StaOrder> staOrders = staOrderService.listByIds(orderIds);
//            staOrders.forEach(s -> s.setBatchBizId(batchBizId));
//            staOrderService.updateBatchById(staOrders);
//        }
    }
}
