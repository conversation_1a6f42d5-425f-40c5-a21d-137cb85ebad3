package com.byun.modules.staffing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkAgentRel;
import com.byun.modules.staffing.mapper.StaWorkAgentRelMapper;
import com.byun.modules.staffing.service.IStaWorkAgentRelService;
import com.byun.modules.system.entity.SysUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 任务代理人关系表
 * @Author: bai
 * @Date:   2021-12-13
 * @Version: V1.0
 */
@Service
public class StaWorkAgentRelServiceImpl extends ServiceImpl<StaWorkAgentRelMapper, StaWorkAgentRel> implements IStaWorkAgentRelService {

    /**
     * @description: 批量代理绑定任务
     * @param user
     * @param staWork
     * @param userIdArr
     * @param deleteUserIdArr
     * @return: void
     * <AUTHOR>
     * @date: 2021/12/14 14:55
     */
    @Override
    @Transactional
    public void batchAgentBindWork(SysUser user, StaWork staWork, String[] userIdArr, List<String> deleteUserIdArr) {
        //删除
        for (String id :deleteUserIdArr){
            LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getStaWorkId,staWork.getId());
            staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId,id);
            baseMapper.delete(staWorkAgentRelLambdaQueryWrapper);
        }

        for (String id :userIdArr){
            LambdaQueryWrapper<StaWorkAgentRel> staWorkAgentRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getStaWorkId,staWork.getId());
            staWorkAgentRelLambdaQueryWrapper.eq(StaWorkAgentRel::getSysUserId,id);
            Integer integer = baseMapper.selectCount(staWorkAgentRelLambdaQueryWrapper);
            if(WxlConvertUtils.isEmpty(integer)||integer==0){
                StaWorkAgentRel staWorkAgentRel = new StaWorkAgentRel();
                staWorkAgentRel.setCreateBy(user.getUsername());
                staWorkAgentRel.setCreateTime(new Date());
                staWorkAgentRel.setUpdateBy(user.getUsername());
                staWorkAgentRel.setUpdateTime(new Date());
                staWorkAgentRel.setSysUserId(id);
                staWorkAgentRel.setStaWorkId(staWork.getId());
                staWorkAgentRel.setDelFlag(CommonConstant.DEL_FLAG_0);
                staWorkAgentRel.setOperatorId(user.getId());
                baseMapper.insert(staWorkAgentRel);
            }
        }
    }
}
