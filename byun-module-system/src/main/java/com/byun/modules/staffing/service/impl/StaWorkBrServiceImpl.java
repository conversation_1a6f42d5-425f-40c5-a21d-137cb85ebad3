package com.byun.modules.staffing.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkBr;
import com.byun.modules.staffing.mapper.StaWorkBrMapper;
import com.byun.modules.staffing.service.IStaWorkBrService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 任务浏览记录
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkBrServiceImpl extends ServiceImpl<StaWorkBrMapper, StaWorkBr> implements IStaWorkBrService {
    @Override
    public Page<StaWork> getMyWorkBrPage(Page<StaWork> pageList, String userId){
        return pageList.setRecords(baseMapper.getMyWorkBrList(pageList, userId));
    }
}
