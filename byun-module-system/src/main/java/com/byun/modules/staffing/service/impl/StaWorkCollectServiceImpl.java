package com.byun.modules.staffing.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.entity.StaWorkCollect;
import com.byun.modules.staffing.mapper.StaWorkCollectMapper;
import com.byun.modules.staffing.service.IStaWorkCollectService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户收藏任务
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkCollectServiceImpl extends ServiceImpl<StaWorkCollectMapper, StaWorkCollect> implements IStaWorkCollectService {
    @Override
    public Page<StaWork> getMyWorkCollectPage(Page<StaWork> pageList, String userId){
        return pageList.setRecords(baseMapper.getMyWorkCollectList(pageList, userId));
    }
}
