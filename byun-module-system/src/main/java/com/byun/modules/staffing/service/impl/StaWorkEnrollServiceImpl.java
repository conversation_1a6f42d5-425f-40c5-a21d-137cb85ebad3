package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaWorkEnroll;
import com.byun.modules.staffing.mapper.StaWorkEnrollMapper;
import com.byun.modules.staffing.service.IStaWorkEnrollService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkEnrollServiceImpl extends ServiceImpl<StaWorkEnrollMapper, StaWorkEnroll> implements IStaWorkEnrollService {
	
	@Autowired
	private StaWorkEnrollMapper staWorkEnrollMapper;
	
	@Override
	public List<StaWorkEnroll> selectByMainId(String mainId) {
		return staWorkEnrollMapper.selectByMainId(mainId);
	}
}
