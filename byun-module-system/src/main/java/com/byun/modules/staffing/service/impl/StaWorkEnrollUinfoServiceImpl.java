package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaWorkEnrollUinfo;
import com.byun.modules.staffing.mapper.StaWorkEnrollUinfoMapper;
import com.byun.modules.staffing.service.IStaWorkEnrollUinfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 任务报名人的名片，(备份报名时看到的名片，在报名人之下)报名成功后信息记录不随用户更新
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkEnrollUinfoServiceImpl extends ServiceImpl<StaWorkEnrollUinfoMapper, StaWorkEnrollUinfo> implements IStaWorkEnrollUinfoService {

}
