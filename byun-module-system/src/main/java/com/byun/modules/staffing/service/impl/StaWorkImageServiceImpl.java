package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaWorkImage;
import com.byun.modules.staffing.mapper.StaWorkImageMapper;
import com.byun.modules.staffing.service.IStaWorkImageService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 任务图片
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkImageServiceImpl extends ServiceImpl<StaWorkImageMapper, StaWorkImage> implements IStaWorkImageService {
	
	@Autowired
	private StaWorkImageMapper staWorkImageMapper;
	
	@Override
	public List<StaWorkImage> selectByMainId(String mainId) {
		return staWorkImageMapper.selectByMainId(mainId);
	}
}
