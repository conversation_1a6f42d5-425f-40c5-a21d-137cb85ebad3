package com.byun.modules.staffing.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.RedisUtil;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.mapper.*;
import com.byun.modules.staffing.model.StaWorkModel;
import com.byun.modules.staffing.service.IStaWorkService;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.mapper.SysDepartMapper;
import com.byun.modules.system.service.ISysAnnouncementService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 任务列表
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkServiceImpl extends ServiceImpl<StaWorkMapper, StaWork> implements IStaWorkService {

	@Autowired
	private StaWorkMapper staWorkMapper;
	@Autowired
	private StaWorkImageMapper staWorkImageMapper;
	@Autowired
	private StaWorkVisitsMapper staWorkVisitsMapper;
	@Autowired
	private StaWorkEnrollMapper staWorkEnrollMapper;
	@Autowired
	private SysDepartMapper sysDepartMapper;
	@Autowired
	private StaOrderMapper staOrderMapper;
	@Autowired
	private ISysAnnouncementService sysAnnouncementService;
	@Autowired
	private RedisUtil redisUtil;
	@Autowired
	private StaScheduleMapper staScheduleMapper;
	@Override
	public Page<StaWorkModel> listOrderDistancePage(Page<StaWorkModel> pageList, StaWorkModel staWorkModel){
		//List<StaWorkModel> staWorkModels = staWorkMapper.listOrderDistance(pageList, staWorkModel);
		return pageList.setRecords(staWorkMapper.listOrderDistance(pageList, staWorkModel));
	}

	@Override
	@Transactional
	public void updateApplyNum(String id){
		//更新任务申请人数
		StaWork staWork = staWorkMapper.selectById(id);
		LambdaQueryWrapper<StaOrder> query = new LambdaQueryWrapper<StaOrder>();
		query.eq(StaOrder::getDelFlag,CommonConstant.DEL_FLAG_0);
		query.eq(StaOrder::getStaWorkId, staWork.getId());
		query.eq(StaOrder::getStateFlag,CommonConstant.ORDER_STATUS_2);
		Integer integer = staOrderMapper.selectCount(query);
		staWork.setApplyNum(integer);
		staWorkMapper.updateById(staWork);
	}



	@Override
	@Transactional
	public Boolean completeWork(StaWork staWork) {
		staWork.setStateFlag(CommonConstant.WORK_STATUS_1);
		staWorkMapper.updateById(staWork);
		return true;
	}


	@Override
	@Transactional
	public Boolean startWork(StaWork staWork, String username) {
		if (WxlConvertUtils.isNotEmpty(staWork)&& WxlConvertUtils.isNotEmpty(username)) {
			LambdaQueryWrapper<StaOrder> query = new LambdaQueryWrapper<StaOrder>();
			query.eq(StaOrder::getDelFlag,0);
			query.eq(StaOrder::getStaWorkId, staWork.getId());
			query.eq(StaOrder::getStateFlag,CommonConstant.ORDER_STATUS_5);//待就职的人
			List<StaOrder> staOrders = staOrderMapper.selectList(query);
			List<StaOrder> filteredOrders = staOrders.stream()
					.filter(order -> order.getStateFlag() != CommonConstant.ORDER_STATUS_2)
					.collect(Collectors.toList());
			for (StaOrder staOrder:filteredOrders) {
				staOrder.setStateFlag(CommonConstant.ORDER_STATUS_3);
				staOrder.setMissionFlag(CommonConstant.MISSION_STATUS_0);
				staOrderMapper.updateById(staOrder);
				//发送系统通知
				SysAnnouncement sysAnnouncement = new SysAnnouncement();
				sysAnnouncement.setUserIds(staOrder.getSysUserId()+",");//推送用户
				String msgAbstract = "" + staOrder.getEnrollName() + "，您的<"+staOrder.getWorkName()+">任务开始了！";
				sysAnnouncement.setMsgAbstract(msgAbstract);
				String title = "任务开始";
				sysAnnouncement.setTitile(title);
				sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
				sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
				sysAnnouncement.setPriority(CommonConstant.PRIORITY_T);//优先级
				sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
				sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
				sysAnnouncement.setSendTime(new Date());
				sysAnnouncement.setSender(staWork.getUpdateBy());
				sysAnnouncement.setCreateBy(staWork.getUpdateBy());
				sysAnnouncement.setCreateTime(new Date());
				sysAnnouncementService.saveAnnouncement(sysAnnouncement);
			}
			staWork.setStateFlag(CommonConstant.WORK_STATUS_3);
			this.updateById(staWork);
						List<StaOrder> filteredOrders2 = staOrders.stream()
					.filter(order -> order.getStateFlag() == CommonConstant.ORDER_STATUS_2)
					.collect(Collectors.toList());
			for (StaOrder staOrder : filteredOrders2) {
				staOrderMapper.updateById(staOrder);
			}
			return true;
		}else{
			return false;
		}


	}



	/**
	 * saveWorkData 对应 add
	 */
	@Override
	@Transactional
	public void saveWorkData (StaWork staWork, String username) {
		if (WxlConvertUtils.isNotEmpty(staWork)&& WxlConvertUtils.isNotEmpty(username)) {
			//update-begin：部门编码规则生成器做成公用配置
			SysDepart sysDepart = sysDepartMapper.selectById(staWork.getCompanyId());
			staWork.setSysOrgCode(sysDepart.getOrgCode());
			staWork.setApplyNum(0);
			staWork.setAdoptNum(0);
			staWork.setListShow(CommonConstant.LIST_SHOW_STATUS_1);//展示给用户
			staWork.setStateFlag(CommonConstant.WORK_STATUS_3);//发布任务即开始工作，工作中
			staWork.setCreateTime(new Date());
			staWork.setDelFlag(CommonConstant.DEL_FLAG_0);
			this.save(staWork);
			redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" +  staWork.getId(),staWork.getAdoptNum());
		}
	}
	@Override
	public String saveWorkData2(StaWork staWork, String username) {
		if (WxlConvertUtils.isNotEmpty(staWork)&& WxlConvertUtils.isNotEmpty(username)) {
			//update-begin：部门编码规则生成器做成公用配置
			SysDepart sysDepart = sysDepartMapper.selectById(staWork.getCompanyId());
			staWork.setSysOrgCode(sysDepart.getOrgCode());
			staWork.setApplyNum(0);
			staWork.setAdoptNum(0);
			staWork.setListShow(CommonConstant.LIST_SHOW_STATUS_1);//展示给用户
			staWork.setStateFlag(CommonConstant.WORK_STATUS_3);//发布任务即开始工作，工作中
			staWork.setCreateTime(new Date());
			staWork.setDelFlag(CommonConstant.DEL_FLAG_0);
			this.save(staWork);
			String id = staWork.getId();
			redisUtil.set(CommonConstant.WORK_ADOPT_NUM_CACHE + "_staffing_" +  staWork.getId(),staWork.getAdoptNum());
			return id;
		}
		return "";
	}
	/**
	 * updateWorkDataById 对应 edit 根据任务主键来更新对应的部门数据
	 */
	@Override
	@Transactional
	public Boolean updateWorkDataById(StaWork staWork, String username) {
		if (staWork != null && username != null) {
			staWork.setUpdateTime(new Date());
			staWork.setUpdateBy(username);
			//获取名下订单
			LambdaQueryWrapper<StaOrder> query = new LambdaQueryWrapper<StaOrder>();
			query.eq(StaOrder::getStaWorkId,staWork.getId());
			query.eq(StaOrder::getDelFlag,CommonConstant.DEL_FLAG_0);
			List<StaOrder> staOrders = staOrderMapper.selectList(query);
			//同步修改用户订单
			for (StaOrder staOrder:staOrders) {
				staOrder.setDelFlag(staWork.getDelFlag());
				staOrder.setWorkClass(staWork.getWorkClass());
				staOrder.setLcStateFlag(staWork.getLcNeedFlag());
				staOrder.setRcStateFlag(staWork.getRcNeedFlag());
				staOrder.setAddressName(staWork.getAddressName());
				staOrder.setAddressLat(staWork.getAddressLat());
				staOrder.setAddressLng(staWork.getAddressLng());
				staOrder.setReportDate(staWork.getReportDate());
				staOrder.setWorkNameFull(staWork.getNameFull());
				staOrder.setWorkName(staWork.getNameNick());
				staOrder.setCompanyName(staWork.getCompanyName());
				if(WxlConvertUtils.isEmpty(staWork.getStaNoticeId())){
					staOrder.setStaNoticeId(null);
					staOrder.setNoticeReadFlag(0);
				}else {
					if(!staWork.getStaNoticeId().equals(staOrder.getStaNoticeId())){
						staOrder.setStaNoticeId(staWork.getStaNoticeId());
						staOrder.setNoticeReadFlag(1);
					}
				}
				staOrderMapper.updateById(staOrder);
				//发送系统通知
				SysAnnouncement sysAnnouncement = new SysAnnouncement();
				sysAnnouncement.setUserIds(staOrder.getSysUserId()+",");//推送用户
				String msgAbstract = "" + staOrder.getEnrollName() + "，您的<"+staOrder.getWorkName()+">任务内容改变了，请查看您的任务单！";
				sysAnnouncement.setMsgAbstract(msgAbstract);
				String title = "任务改变";
				sysAnnouncement.setTitile(title);
				sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_2);//系统公告
				sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
				sysAnnouncement.setPriority(CommonConstant.PRIORITY_H);//优先级
				sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
				sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
				sysAnnouncement.setSendTime(new Date());
				sysAnnouncement.setSender(staWork.getUpdateBy());
				sysAnnouncement.setCreateBy(staWork.getUpdateBy());
				sysAnnouncement.setCreateTime(new Date());
				sysAnnouncementService.saveAnnouncement(sysAnnouncement);
			}
			this.updateById(staWork);
			return true;
		} else {
			return false;
		}

	}
	@Override
	@Transactional
	public void saveMain(StaWork staWork, List<StaWorkImage> staWorkImageList,List<StaWorkVisits> staWorkVisitsList,List<StaWorkEnroll> staWorkEnrollList) {
		staWorkMapper.insert(staWork);
		if(staWorkImageList!=null && staWorkImageList.size()>0) {
			for(StaWorkImage entity:staWorkImageList) {
				//外键设置
				entity.setStaWorkId(staWork.getId());
				staWorkImageMapper.insert(entity);
			}
		}
		if(staWorkVisitsList!=null && staWorkVisitsList.size()>0) {
			for(StaWorkVisits entity:staWorkVisitsList) {
				//外键设置
				entity.setStaWorkId(staWork.getId());
				staWorkVisitsMapper.insert(entity);
			}
		}
		if(staWorkEnrollList!=null && staWorkEnrollList.size()>0) {
			for(StaWorkEnroll entity:staWorkEnrollList) {
				//外键设置
				entity.setStaWorkId(staWork.getId());
				staWorkEnrollMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(StaWork staWork,List<StaWorkImage> staWorkImageList,List<StaWorkVisits> staWorkVisitsList,List<StaWorkEnroll> staWorkEnrollList) {
		staWorkMapper.updateById(staWork);
		
		//1.先删除子表数据
		staWorkImageMapper.deleteByMainId(staWork.getId());
		staWorkVisitsMapper.deleteByMainId(staWork.getId());
		staWorkEnrollMapper.deleteByMainId(staWork.getId());
		
		//2.子表数据重新插入
		if(staWorkImageList!=null && staWorkImageList.size()>0) {
			for(StaWorkImage entity:staWorkImageList) {
				//外键设置
				entity.setStaWorkId(staWork.getId());
				staWorkImageMapper.insert(entity);
			}
		}
		if(staWorkVisitsList!=null && staWorkVisitsList.size()>0) {
			for(StaWorkVisits entity:staWorkVisitsList) {
				//外键设置
				entity.setStaWorkId(staWork.getId());
				staWorkVisitsMapper.insert(entity);
			}
		}
		if(staWorkEnrollList!=null && staWorkEnrollList.size()>0) {
			for(StaWorkEnroll entity:staWorkEnrollList) {
				//外键设置
				entity.setStaWorkId(staWork.getId());
				staWorkEnrollMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		staWorkImageMapper.deleteByMainId(id);
		staWorkVisitsMapper.deleteByMainId(id);
		staWorkEnrollMapper.deleteByMainId(id);
		staWorkMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			staWorkImageMapper.deleteByMainId(id.toString());
			staWorkVisitsMapper.deleteByMainId(id.toString());
			staWorkEnrollMapper.deleteByMainId(id.toString());
			staWorkMapper.deleteById(id);
		}
	}

	@Override
	public void updateApplyNum2(String staWorkId,int size) {
		//更新任务申请人数  同意撤单
		StaWork staWork = staWorkMapper.selectById(staWorkId);
		LambdaQueryWrapper<StaOrder> query = new LambdaQueryWrapper<StaOrder>();
		query.eq(StaOrder::getDelFlag,CommonConstant.DEL_FLAG_0);
		query.eq(StaOrder::getStaWorkId, staWork.getId());
		//query.eq(StaOrder::getMissionFlag, CommonConstant.MISSION_STATUS_0);
		Integer integer = staOrderMapper.selectCount(query);
		staWork.setApplyNum(integer);
		staWork.setAdoptNum(staWork.getAdoptNum() >0 ? staWork.getAdoptNum() - size : 0);
		staWorkMapper.updateById(staWork);
	}

	/**
	 * 补卡
	 * @param data
	 * @return
	 */
	@Override
	public Result repairClock(JSONObject data) {
		Result result = new Result();


		return null;
	}
	@Override
	public void storeChangeApplication(JSONObject data) {
		String staOrderId = data.getString("staOrderId");
		String sysUserId = data.getString("sysUserId");
		String newShopName = data.getString("firmName");
		String newShopId = data.getString("firmId");
		String workName = data.getString("workName");
		String workId = data.getString("workId");
		StaOrder staOrder = staOrderMapper.selectById(staOrderId);
		staOrder.setStateFlag(CommonConstant.ORDER_STATUS_13);
		staOrder.setNewShopId(newShopId); //
		staOrder.setNewShopName(newShopName);
		staOrder.setNewWorkId(workId);
		staOrder.setNewWorkName(workName);
		staOrder.setVariationTime(new Date());
		staOrder.setVariationFlag(0);
		staOrderMapper.updateById(staOrder);
	}

	@Override
	public IPage<StaWork> getAdminTaskListOptimized(Page<StaWork> page, QueryWrapper<StaWork> queryWrapper) {
		// 使用优化的查询，可以在这里添加更多的优化逻辑
		// 比如添加合适的索引提示、查询字段优化等
		return this.page(page, queryWrapper);
	}
}
