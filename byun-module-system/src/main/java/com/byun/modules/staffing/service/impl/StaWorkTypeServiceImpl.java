package com.byun.modules.staffing.service.impl;

import com.byun.common.exception.ByunBootException;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaWorkType;
import com.byun.modules.staffing.mapper.StaWorkTypeMapper;
import com.byun.modules.staffing.service.IStaWorkTypeService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 任务类型
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkTypeServiceImpl extends ServiceImpl<StaWorkTypeMapper, StaWorkType> implements IStaWorkTypeService {

	@Override
	public void addStaWorkType(StaWorkType staWorkType) {
	   //新增时设置hasChild为0
	    staWorkType.setHasChild(IStaWorkTypeService.NOCHILD);
		if(WxlConvertUtils.isEmpty(staWorkType.getPid())){
			staWorkType.setPid(IStaWorkTypeService.ROOT_PID_VALUE);
		}else{
			//如果当前节点父ID不为空 则设置父节点的hasChildren 为1
			StaWorkType parent = baseMapper.selectById(staWorkType.getPid());
			if(parent!=null && !"1".equals(parent.getHasChild())){
				parent.setHasChild("1");
				baseMapper.updateById(parent);
			}
		}
		baseMapper.insert(staWorkType);
	}
	
	@Override
	public void updateStaWorkType(StaWorkType staWorkType) {
		StaWorkType entity = this.getById(staWorkType.getId());
		if(entity==null) {
			throw new ByunBootException("未找到对应实体");
		}
		String old_pid = entity.getPid();
		String new_pid = staWorkType.getPid();
		if(!old_pid.equals(new_pid)) {
			updateOldParentNode(old_pid);
			if(WxlConvertUtils.isEmpty(new_pid)){
				staWorkType.setPid(IStaWorkTypeService.ROOT_PID_VALUE);
			}
			if(!IStaWorkTypeService.ROOT_PID_VALUE.equals(staWorkType.getPid())) {
				baseMapper.updateTreeNodeStatus(staWorkType.getPid(), IStaWorkTypeService.HASCHILD);
			}
		}
		baseMapper.updateById(staWorkType);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteStaWorkType(String id) throws ByunBootException {
		//查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if(id.indexOf(",")>0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if(idVal != null){
                    StaWorkType staWorkType = this.getById(idVal);
                    String pidVal = staWorkType.getPid();
                    //查询此节点上一级是否还有其他子节点
                    List<StaWorkType> dataList = baseMapper.selectList(new QueryWrapper<StaWorkType>().eq("pid", pidVal).notIn("id",Arrays.asList(idArr)));
                    if((dataList == null || dataList.size()==0) && !Arrays.asList(idArr).contains(pidVal)
                            && !sb.toString().contains(pidVal)){
                        //如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            //批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            //修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for(String pid : pidArr){
                this.updateOldParentNode(pid);
            }
        }else{
            StaWorkType staWorkType = this.getById(id);
            if(staWorkType==null) {
                throw new ByunBootException("未找到对应实体");
            }
            updateOldParentNode(staWorkType.getPid());
            baseMapper.deleteById(id);
        }
	}
	
	@Override
    public List<StaWorkType> queryTreeListNoPage(QueryWrapper<StaWorkType> queryWrapper) {
        List<StaWorkType> dataList = baseMapper.selectList(queryWrapper);
        List<StaWorkType> mapList = new ArrayList<>();
        for(StaWorkType data : dataList){
            String pidVal = data.getPid();
            //递归查询子节点的根节点
            if(pidVal != null && !"0".equals(pidVal)){
                StaWorkType rootVal = this.getTreeRoot(pidVal);
                if(rootVal != null && !mapList.contains(rootVal)){
                    mapList.add(rootVal);
                }
            }else{
                if(!mapList.contains(data)){
                    mapList.add(data);
                }
            }
        }
        return mapList;
    }
	
	/**
	 * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
	 * @param pid
	 */
	private void updateOldParentNode(String pid) {
		if(!IStaWorkTypeService.ROOT_PID_VALUE.equals(pid)) {
			Integer count = baseMapper.selectCount(new QueryWrapper<StaWorkType>().eq("pid", pid));
			if(count==null || count<=1) {
				baseMapper.updateTreeNodeStatus(pid, IStaWorkTypeService.NOCHILD);
			}
		}
	}

	/**
     * 递归查询节点的根节点
     * @param pidVal
     * @return
     */
    private StaWorkType getTreeRoot(String pidVal){
        StaWorkType data =  baseMapper.selectById(pidVal);
        if(data != null && !"0".equals(data.getPid())){
            return this.getTreeRoot(data.getPid());
        }else{
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        //获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if(pidVal != null){
                if(!sb.toString().contains(pidVal)){
                    if(sb.toString().length() > 0){
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal,sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal,StringBuffer sb){
        List<StaWorkType> dataList = baseMapper.selectList(new QueryWrapper<StaWorkType>().eq("pid", pidVal));
        if(dataList != null && dataList.size()>0){
            for(StaWorkType tree : dataList) {
                if(!sb.toString().contains(tree.getId())){
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(),sb);
            }
        }
        return sb;
    }

}
