package com.byun.modules.staffing.service.impl;

import com.byun.modules.staffing.entity.StaWorkVisits;
import com.byun.modules.staffing.mapper.StaWorkVisitsMapper;
import com.byun.modules.staffing.service.IStaWorkVisitsService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 访问量、收藏量
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Service
public class StaWorkVisitsServiceImpl extends ServiceImpl<StaWorkVisitsMapper, StaWorkVisits> implements IStaWorkVisitsService {
	
	@Autowired
	private StaWorkVisitsMapper staWorkVisitsMapper;
	
	@Override
	public List<StaWorkVisits> selectByMainId(String mainId) {
		return staWorkVisitsMapper.selectByMainId(mainId);
	}
}
