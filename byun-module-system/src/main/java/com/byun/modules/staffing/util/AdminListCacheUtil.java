package com.byun.modules.staffing.util;

import com.byun.common.util.RedisUtil;
import com.byun.common.util.WxlConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 管理员任务列表缓存工具类
 * 用于优化 adminList 接口性能
 * 
 * <AUTHOR> Code
 * @date 2024-01-01
 */
@Slf4j
@Component
public class AdminListCacheUtil {

    @Autowired
    private RedisUtil redisUtil;

    // 缓存键前缀
    private static final String CACHE_PREFIX = "admin_list:";
    private static final String USER_PERMISSION_PREFIX = "user_permission:";
    private static final String DEPT_HIERARCHY_PREFIX = "dept_hierarchy:";
    
    // 缓存过期时间（分钟）
    private static final int PERMISSION_CACHE_EXPIRE = 30;
    private static final int DEPT_CACHE_EXPIRE = 60;
    private static final int LIST_CACHE_EXPIRE = 5;

    /**
     * 获取用户权限缓存
     * @param username 用户名
     * @param departId 部门ID
     * @return 权限集合
     */
    @SuppressWarnings("unchecked")
    public Set<String> getUserPermissionFromCache(String username, String departId) {
        String cacheKey = USER_PERMISSION_PREFIX + username + ":" + departId;
        try {
            Object cached = redisUtil.get(cacheKey);
            if (cached != null) {
                return (Set<String>) cached;
            }
        } catch (Exception e) {
            log.warn("获取用户权限缓存失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 设置用户权限缓存
     * @param username 用户名
     * @param departId 部门ID
     * @param permissions 权限集合
     */
    public void setUserPermissionCache(String username, String departId, Set<String> permissions) {
        String cacheKey = USER_PERMISSION_PREFIX + username + ":" + departId;
        try {
            redisUtil.set(cacheKey, permissions, PERMISSION_CACHE_EXPIRE * 60);
        } catch (Exception e) {
            log.warn("设置用户权限缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 获取部门层级缓存
     * @param orgCode 组织编码
     * @return 部门ID列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getDeptHierarchyFromCache(String orgCode) {
        String cacheKey = DEPT_HIERARCHY_PREFIX + orgCode;
        try {
            Object cached = redisUtil.get(cacheKey);
            if (cached != null) {
                return (List<String>) cached;
            }
        } catch (Exception e) {
            log.warn("获取部门层级缓存失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 设置部门层级缓存
     * @param orgCode 组织编码
     * @param departIds 部门ID列表
     */
    public void setDeptHierarchyCache(String orgCode, List<String> departIds) {
        String cacheKey = DEPT_HIERARCHY_PREFIX + orgCode;
        try {
            redisUtil.set(cacheKey, departIds, DEPT_CACHE_EXPIRE * 60);
        } catch (Exception e) {
            log.warn("设置部门层级缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 生成列表查询缓存键
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @param stateFlag 状态标识
     * @param nameFull 任务名称
     * @param orgCode 组织编码
     * @return 缓存键
     */
    public String generateListCacheKey(Integer pageNo, Integer pageSize, String deptNo, 
                                     String deptName, String stateFlag, String nameFull, String orgCode) {
        StringBuilder keyBuilder = new StringBuilder(CACHE_PREFIX);
        keyBuilder.append("list:")
                  .append("page:").append(pageNo).append(":")
                  .append("size:").append(pageSize).append(":")
                  .append("org:").append(WxlConvertUtils.isEmpty(orgCode) ? "null" : orgCode).append(":");
        
        if (WxlConvertUtils.isNotEmpty(deptNo)) {
            keyBuilder.append("deptNo:").append(deptNo).append(":");
        }
        if (WxlConvertUtils.isNotEmpty(deptName)) {
            keyBuilder.append("deptName:").append(deptName).append(":");
        }
        if (WxlConvertUtils.isNotEmpty(stateFlag)) {
            keyBuilder.append("state:").append(stateFlag).append(":");
        }
        if (WxlConvertUtils.isNotEmpty(nameFull)) {
            keyBuilder.append("name:").append(nameFull).append(":");
        }
        
        return keyBuilder.toString();
    }

    /**
     * 获取列表缓存
     * @param cacheKey 缓存键
     * @return 缓存的列表数据
     */
    public Object getListCache(String cacheKey) {
        try {
            return redisUtil.get(cacheKey);
        } catch (Exception e) {
            log.warn("获取列表缓存失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 设置列表缓存
     * @param cacheKey 缓存键
     * @param data 数据
     */
    public void setListCache(String cacheKey, Object data) {
        try {
            redisUtil.set(cacheKey, data, LIST_CACHE_EXPIRE * 60);
        } catch (Exception e) {
            log.warn("设置列表缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 清除用户相关缓存
     * @param username 用户名
     */
    public void clearUserCache(String username) {
        try {
            // 使用 removeAll 方法删除指定前缀的所有键
            redisUtil.removeAll(USER_PERMISSION_PREFIX + username + ":");
        } catch (Exception e) {
            log.warn("清除用户缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 清除部门相关缓存
     * @param orgCode 组织编码
     */
    public void clearDeptCache(String orgCode) {
        try {
            String deptKey = DEPT_HIERARCHY_PREFIX + orgCode;
            redisUtil.del(deptKey);

            // 清除相关的列表缓存
            redisUtil.removeAll(CACHE_PREFIX + "list:*org:" + orgCode + ":");
        } catch (Exception e) {
            log.warn("清除部门缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 清除所有列表缓存
     */
    public void clearAllListCache() {
        try {
            redisUtil.removeAll(CACHE_PREFIX + "list:");
        } catch (Exception e) {
            log.warn("清除所有列表缓存失败: {}", e.getMessage());
        }
    }
}
