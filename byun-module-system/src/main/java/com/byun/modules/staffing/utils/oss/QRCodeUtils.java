package com.byun.modules.staffing.utils.oss;

import cn.hutool.extra.qrcode.QrCodeException;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

public class QRCodeUtils {
    /**
     * 生成二维码并转换为流
     *
     * @param qrConfig
     * @param content
     * @return
     */
    public static InputStream createQrCode(QrConfig qrConfig, String content) {
        try {

            byte[] bytes = QrCodeUtil.generatePng(content, qrConfig);
            return new ByteArrayInputStream(bytes);
        } catch (QrCodeException e) {
            //Asserts.customizeException("生成二维码失败!");
        }
        return null;
    }
    /**
     * 将网络资源图片转成BufferedImage
     *
     * @param imageUrl
     * @return
     */
    public static BufferedImage urlToBufferImage(String imageUrl) {
        BufferedImage bufferedLogo = null;
        try {
            bufferedLogo = ImageIO.read(new URL(imageUrl));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bufferedLogo;
    }

    /**
     * 图片流转换为base64图片
     * @param inputStream  输入流
     * @return base64图片
     * @throws IOException
     */
    public static String convertImageToBase64(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] imageBytes = byteArrayOutputStream.toByteArray();
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);
        return base64Image;
    }
}
