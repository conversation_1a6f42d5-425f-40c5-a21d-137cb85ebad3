package com.byun.modules.staffing.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 网页考勤execl导出类
 * @date : 2023-6-9 10:45
 */
@Data
public class AttendanceExport {
    @Excel(name = "区域", width = 15)
    private String regionName;
    @Excel(name = "事业处", width = 15)
    private String businessDivisionName;
    @Excel(name = "门店", width = 15)
    private String  companyName;
    @Excel(name = "店编", width = 15)
    private String  storeNo;
    @Excel(name = "班别代码", width = 15)
    private String shiftCode;
    @Excel(name = "姓名", width = 15)
    private String realName;
    @Excel(name = "身份证", width = 15)
    private String userIdCard;
    @Excel(name = "排班日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date scheduleDay;
    @Excel(name = "签到", width = 15)
    private String clockInTime1;
    //@Excel(name = "签到图片", width = 20,type = 2)
    private String clockImg1;
    @Excel(name = "签到状态", width = 20)
    private String clockInStatus1;
    @Excel(name = "签退", width = 15)
    private String clockInTime2;
   // @Excel(name = "签退图片", width = 20,type = 2)
    private String clockImg2;
    @Excel(name = "签退状态", width = 20)
    private String clockInStatus2;
    @Excel(name = "签到", width = 15)
    private String clockInTime3;
    //@Excel(name = "签到图片", width = 20,type = 2)
    private String clockImg3;
    @Excel(name = "签退", width = 15)
    private String clockInTime4;
    //@Excel(name = "签退图片", width = 20,type = 2)
    private String clockImg4;
    @Excel(name = "时数",width = 15)
    private Double dailyWorkingHours;
}
