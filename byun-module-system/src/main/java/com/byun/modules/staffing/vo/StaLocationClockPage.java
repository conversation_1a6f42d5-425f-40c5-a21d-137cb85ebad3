package com.byun.modules.staffing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Random;
import java.util.UUID;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 考勤导出需要
 * @date : 2022-10-28 16:11
 */
@Data
public class StaLocationClockPage {
    /**
     * 店编、用户电话、用户身份证、签到时间
     */
    @Excel(name = "店编", width = 15)
    @ApiModelProperty(value = "甲方店编")
    private String StoreNo;
    /**电话*/
    @Excel(name = "电话", width = 15)
    @ApiModelProperty(value = "电话")
    private java.lang.String phone;
    /**身份证*/
    @Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    private java.lang.String idCard;
    /**签到时间*/
    @Excel(name = "签到时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "签到时间")
    private java.util.Date time;

    public static String getRandomPwd(int len) {
        String result = null;
        while(len==32){
            result = makeRandomPwd(len);
            if (result.matches(".*[a-z]{1,}.*") && result.matches(".*[A-Z]{1,}.*") && result.matches(".*\\d{1,}.*") && result.matches(".*[~;<@#:>%^]{1,}.*")) {
                return result;
            }
            result = makeRandomPwd(len);
        }
        return "长度不得少于32位!";
    }

    /**
     * @Title: makeRandomPwd
     * @Description:随机密码生成
     * @param len
     * @return String
     * @throws
     */
    public static String makeRandomPwd(int len) {
        char charr[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".toCharArray();
        StringBuilder sb = new StringBuilder();
        Random r = new Random();
        for (int x = 0; x < len; ++x) {
            sb.append(charr[r.nextInt(charr.length)]);
        }
        return sb.toString();
    }


//    public static void main(String[] args) {
//        String uuid = UUID.randomUUID().toString();
//        System.out.println(uuid);
//        String str=uuid.replaceAll("[^0-9a-zA-Z]","");
//        uuid = str.toString();
//        System.out.println(uuid+"-----"+uuid.length());
//
//    }
}

