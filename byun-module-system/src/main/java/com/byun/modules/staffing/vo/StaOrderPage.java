package com.byun.modules.staffing.vo;

import java.util.List;

import com.byun.modules.staffing.entity.StaLocationClock;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 任务单
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@ApiModel(value="sta_orderPage对象", description="任务单")
public class StaOrderPage {

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	private java.lang.String id;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**
	 * 创建日期
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
	private java.util.Date createTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private java.lang.String updateBy;
	/**
	 * 更新日期
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
	private java.util.Date updateTime;
	/**
	 * 订单编码
	 */
	@Excel(name = "订单编码", width = 15)
	@ApiModelProperty(value = "订单编码")
	private java.lang.String orderCode;
	/**
	 * 下单时间
	 */
	@Excel(name = "下单时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "下单时间")
	private java.util.Date orderDate;
	/**
	 * 任务简称
	 */
	@ApiModelProperty(value = "任务简称")
	private java.lang.String workName;
	/**
	 * 任务id
	 */
	@ApiModelProperty(value = "任务id")
	private java.lang.String staWorkId;
	/**
	 * 任务状态
	 */
	@Excel(name = "状态", width = 15)
	@ApiModelProperty(value = "任务状态")
	private java.lang.Integer stateFlag;
	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private java.lang.String sysUserId;
	/**
	 * 报名类型(1自己2他人)
	 */
//	@Excel(name = "报名类型(1自己2他人)", width = 15)
	@ApiModelProperty(value = "报名类型")
	private java.lang.Integer enrollType;
	/**
	 * 用户灵活用工名片
	 */
	@ApiModelProperty(value = "用户灵活用工名片")
	private java.lang.String staUserInfoId;
	/**
	 * 推荐人_id
	 */
	@ApiModelProperty(value = "推荐人_id")
	private java.lang.String recommenderId;
	/**
	 * 推荐人名称
	 */
	@Excel(name = "推荐人", width = 15)
	@ApiModelProperty(value = "推荐人名称")
	private java.lang.String recommenderName;
	/**
	 * 删除状态
	 */
	@Excel(name = "删除状态", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "删除状态")
	private java.lang.Integer delFlag;

	@ApiModelProperty(value = "签到定位时间")
	private List<StaLocationClock> staLocationClockList;

	/**门店名称*/
	@Excel(name = "公司名称", width = 15)
	@ApiModelProperty(value = "门店名称")
	private java.lang.String companyName;
	/**报道时间*/
	@Excel(name = "报道时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "报道时间")
	private java.util.Date reportDate;
	@Excel(name = "地址名称", width = 15)
	@ApiModelProperty(value = "地址名称")
	private java.lang.String addressName;
	/**任务地点坐标Lat*/
	@Excel(name = "任务地点坐标Lat", width = 15)
	@ApiModelProperty(value = "任务地点坐标Lat")
	private java.math.BigDecimal addressLat;
	/**任务地点坐标Lng*/
	@Excel(name = "任务地点坐标Lng", width = 15)
	@ApiModelProperty(value = "任务地点坐标Lng")
	private java.math.BigDecimal addressLng;
	/**任务种类（1兼职2专职）*/
	@ApiModelProperty(value = "任务种类（1兼职2专职）")
	private java.lang.Integer workClass;
	/**是否点名*/
	@ApiModelProperty(value = "是否点名")
	private java.lang.Integer rcStateFlag;
	/**是否需要签到*/
	@ApiModelProperty(value = "是否需要签到")
	private java.lang.Integer lcStateFlag;
	/**任务名全称*/
	@Excel(name = "任务名", width = 15)
	@ApiModelProperty(value = "任务名全称")
	private java.lang.String workNameFull;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
	@ApiModelProperty(value = "姓名")
	private java.lang.String enrollName;
	/**身份证*/
	@Excel(name = "身份证", width = 15)
	@ApiModelProperty(value = "身份证")
	private java.lang.String enrollIdCard;
	/**身份证是否验证*/
	@ApiModelProperty(value = "身份证是否验证")
	private java.lang.Integer enrollIdCardVeri;
	/**手机*/
	@Excel(name = "手机", width = 15)
	@ApiModelProperty(value = "手机")
	private java.lang.String enrollPhone;
	/**是否短信验证*/
	@ApiModelProperty(value = "是否短信验证")
	private java.lang.Integer enrollPhoneVeri;
	/**性别*/
	@Excel(name = "性别", width = 15,dicCode="admin_sex")
	@ApiModelProperty(value = "性别")
	private java.lang.Integer enrollSex;
	/**年龄*/
	@Excel(name = "年龄", width = 15)
	@ApiModelProperty(value = "年龄")
	private java.lang.Integer enrollAge;
	/**用户报名信息id*/
	@ApiModelProperty(value = "用户报名信息id")
	private java.lang.String staEnrollInfo;
	@ApiModelProperty(value = "报名人信息")
	private String staEnrollInfoId;
	@ApiModelProperty(value = "订单批量修改操作用到")
	private List<String> orderIds;
	@ApiModelProperty(value = "订单批量修改操作用到")
	private List<Integer> missionFlags;
	@Excel(name = "毕业证编号", width = 15)
	@ApiModelProperty(value = "毕业证编号")
	private java.lang.String enrollIdDiploma;
	@Excel(name = "健康证编号", width = 15)
	@ApiModelProperty(value = "健康证编号")
	private java.lang.String enrollIdHealth;
	@Excel(name = "免冠蓝底或红底证件照", width = 15,type=2)
	@ApiModelProperty(value = "免冠蓝底或红底证件照")
	private java.lang.String enrollMyPhoto;
	/**入职须知id*/
	@ApiModelProperty(value = "入职须知id")
	private java.lang.String staNoticeId;
	/**入职须知阅读标记 0未读 1已读*/
	@ApiModelProperty(value = "入职须知阅读标记 ")
	private java.lang.Integer noticeReadFlag;

	@Excel(name = "健康码图片", width = 15,type=2)
	@ApiModelProperty(value = "健康码图片")
	private java.lang.String enrollHealthPhoto;
	/**
	 * 身份证有效期
	 */
	@Excel(name = "身份证有效期开始", width = 15, format = "yyyy-MM-dd HH:mm:ss")
//	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
//	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "身份证有效期")
	private String enrollIdCardStartDate;
	/**
	 * 身份证有效期
	 */
	@Excel(name = "身份证有效期结束", width = 15, format = "yyyy-MM-dd HH:mm:ss")
//	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
//	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "身份证有效期")
	private String enrollIdCardEndDate;
	/**
	 * 健康证有效期
	 */
	@Excel(name = "健康证有效期开始", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "健康证有效期")
	private java.util.Date enrollIdHealthStartDate;
	/**
	 * 健康证有效期
	 */
	@Excel(name = "健康证有效期结束", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "健康证有效期")
	private java.util.Date enrollIdHealthEndDate;

	@Excel(name = "身份证地址", width = 15)
	@ApiModelProperty(value = "身份证地址")
	private String enrollIdCardAddress;
	@Excel(name = "现住地址", width = 15)
	@ApiModelProperty(value = "现住地址")
	private String enrollAddress;
	/**现住址坐标*/
	@Excel(name = "现住址坐标", width = 15)
	@ApiModelProperty(value = "现住址坐标Lat")
	private java.math.BigDecimal enrollAddressLat;
	/**现住址坐标*/
	@Excel(name = "现住址坐标", width = 15)
	@ApiModelProperty(value = "现住址坐标Lng")
	private java.math.BigDecimal enrollAddressLng;
	@Excel(name = "现住地址名", width = 15)
	@ApiModelProperty(value = "现住地址名")
	private String enrollAddressName;
	@Excel(name = "文化程度", width = 15)
	@ApiModelProperty(value = "文化程度")
	private String enrollDegree;
	@Excel(name = "健康证办理单位", width = 15)
	@ApiModelProperty(value = "健康证办理单位")
	private String enrollIdHealthCompany;
	@Excel(name = "紧急联系人手机", width = 15)
	@ApiModelProperty(value = "紧急联系人手机")
	private java.lang.String enrollContactNumber;
	@Excel(name = "紧急联系人", width = 15)
	@ApiModelProperty(value = "紧急联系人")
	private String enrollContact;

	/**所属部门code*/
	@ApiModelProperty(value = "所属部门code")
	private java.lang.String sysOrgCode;
	/**门店id（depart）*/
	@ApiModelProperty(value = "门店id（depart）")
	private java.lang.String companyId;
	/**公司id（depart）*/
	@ApiModelProperty(value = "公司id（depart）")
	private java.lang.String firmId;
	/**门店名称*/
	@Excel(name = "公司名称", width = 15)
	@ApiModelProperty(value = "公司名称")
	private java.lang.String firmName;


	@Excel(name = "身份证是否在有效期内", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "身份证是否在有效期内")
	private java.lang.Integer enrollIdCardValidityDate;
	@Excel(name = "是否有毕业证", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "是否有毕业证")
	private java.lang.Integer enrollIdDiplomaValidity;
	@Excel(name = "是否有健康证", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "是否有健康证")
	private java.lang.Integer enrollIdHealthValidity;
	@Excel(name = "健康证是否在有效期内", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "健康证是否在有效期内")
	private java.lang.Integer enrollIdHealthValidityDate;
	@ApiModelProperty(value = "拒绝退单标识")//0
	private java.lang.String status;
}
