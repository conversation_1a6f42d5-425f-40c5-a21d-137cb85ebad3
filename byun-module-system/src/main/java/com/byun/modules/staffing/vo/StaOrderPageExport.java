package com.byun.modules.staffing.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import java.util.Date;
/**
 * @Description: 任务单 导出对象
 */
@Data
@ApiModel(value="sta_orderPage_export对象", description="任务单导出对象")
public class StaOrderPageExport {
	@Excel(name = "事业处", width = 20)
	private String businessDivisionName;
	@Excel(name = "区域", width = 20)
	private String regionName;
	@Excel(name = "店编", width = 15)
	private String storeNo;
	@Excel(name = "机构名称",width = 20)
	private String companyName;
	@Excel(name = "开始日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date entryDate;
	@Excel(name = "结束日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date timeOfResignation;
	@Excel(name = "状态",width = 20)
	private String presentStatus;
	@Excel(name = "任务名称")
	private String workNameFull;
	@Excel(name = "报名人姓名")
	private String enrollName;
	@Excel(name = "身份证",width = 50)
	private String enrollIdCard;
	@Excel(name = "出生日期")
	private String dateOfBirth;
	@Excel(name = "身份证地址",width = 50)
	private String enrollIdCardAddress;
	@Excel(name = "学历")
	private String enrollDegree;
	@Excel(name = "性别")
	private String sex;
	@Excel(name = "年龄")
	private Integer age;
	@Excel(name = "手机号")
	private String enrollPhone;
	@Excel(name = "结算标准")
	private BigDecimal hourlySalary;
	@Excel(name = "任务时数")
	private Double taskManHour;
	@Excel(name = "任务金额")
	private BigDecimal taskAmount;
}
