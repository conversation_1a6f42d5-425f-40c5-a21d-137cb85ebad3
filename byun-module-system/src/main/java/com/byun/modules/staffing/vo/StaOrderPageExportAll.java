package com.byun.modules.staffing.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.byun.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 任务单 导出对象
 */
@Data
@ApiModel(value="sta_orderPage_export对象", description="任务单导出对象")
public class StaOrderPageExportAll {
	@Excel(name = "事业处", width = 20)
	private String businessDivisionName;
	@Excel(name = "区域", width = 20)
	private String regionName;
	@Excel(name = "店编", width = 15)
	@ApiModelProperty(value = "甲方店编")
	private String StoreNo;
	@Excel(name = "机构名称", width = 20)
	@ApiModelProperty(value = "机构名称")
	private String companyName;
	@Excel(name = "入职日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "开始日期")
	@TableField(value = "entry_date")
	private Date entryDate;
	@Excel(name = "任务状态", width = 15)
	@ApiModelProperty(value = "任务状态")
	private String presentStatus;
	@Excel(name = "任务名全称", width = 15)
	@ApiModelProperty(value = "任务名全称")
	private String workNameFull;
	@Excel(name = "姓名", width = 15)
	@ApiModelProperty(value = "姓名")
	private String enrollName;
//	@Excel(name = "出生日期", width = 15)
//	private String dateOfBirth;
	@Dict(dicCode = "admin_degree")
	@Excel(name = "学历", width = 15)
	private String enrollDegree;
	@Excel(name = "身份证", width = 15)
	@ApiModelProperty(value = "身份证")
	private String enrollIdCard;
	@Excel(name = "身份证地址", width = 30)
	private String enrollIdCardAddress;
	@Excel(name = "手机", width = 15)
	@ApiModelProperty(value = "手机")
	private String enrollPhone;
	@Excel(name = "性别", width = 15)
	@ApiModelProperty(value = "性别")
	private String sex;
	@Excel(name = "年龄", width = 15)
	@ApiModelProperty(value = "年龄")
	private Integer enrollAge;
	@Excel(name = "离职店铺", width = 15)
	private String dimissionShop;
	@Excel(name = "离职时间", width = 15, format = "yyyy-MM-dd HH:mm")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
	private Date timeOfResignation;
	@Excel(name = "异动店铺", width = 15)
	private String newShopName;
	@Excel(name = "异动任务",width = 20)
	private String newWorkName;
	@Excel(name = "异动时间",width = 20)
	private Date variationTime;
//	@Excel(name = "时数(上半月)",width = 15)
//	private Double monthStart;
//	@Excel(name = "时数(下半月)",width = 15)
//	private Double monthEnd;
	@Excel(name = "筛选时数",width = 15)
	private Double customizeTime;
	@Excel(name = "时数(月)",width = 15)
	private Double month;
	@Excel(name = "时数(本任务)",width = 20)
	private Double workingHours;
	@Excel(name = "时数(年)")
	private Double annualWorkHours;
	private Double monthlyWorkingHours;
	@Excel(name = "司龄(天)")
	private int companyServiceDays;
	@Excel(name = "人员类别")
	private String staUserType;
}
