package com.byun.modules.staffing.vo;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 不同任务订单状态导出Excel
 * @date : 2024-4-17 13:53
 */
@Data

public class StaOrderStatusExportVo {
    @Excel(name = "事业处", width = 15)
    private String businessDivisionName;
    @Excel(name = "区域", width = 15)
    private String regionName;
    @Excel(name = "店编", width = 15)
    private String StoreNo;
    @Excel(name = "门店", width = 15)
    private java.lang.String companyName;
    @Excel(name = "任务名称", width = 15)
    private java.lang.String workNameFull;
    @Excel(name = "姓名", width = 15)
    private java.lang.String enrollName;
    @Excel(name = "身份证", width = 20)
    private java.lang.String enrollIdCard;
    @Excel(name = "性别", width = 15,dicCode = "admin_sex")
    private java.lang.String sex;
    @Excel(name = "年龄", width = 15)
    private java.lang.Integer age;
    @Excel(name = "手机", width = 15)
    private java.lang.String enrollPhone;
    @Excel(name = "学历", width = 15,dicCode = "admin_degree")
    private String enrollDegree;
}
