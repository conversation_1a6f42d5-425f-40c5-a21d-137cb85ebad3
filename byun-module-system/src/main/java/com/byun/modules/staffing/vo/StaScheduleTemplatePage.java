package com.byun.modules.staffing.vo;

import java.util.List;

import com.byun.modules.staffing.entity.StaScheduleTemplateItem;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 排班表模板
 * @Author: bai
 * @Date:   2022-08-17
 * @Version: V1.0
 */
@Data
@ApiModel(value="sta_schedule_templatePage对象", description="排班表模板")
public class StaScheduleTemplatePage {

	/**主键*/
	@ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属用户id*/
	@Excel(name = "所属用户id", width = 15)
	@ApiModelProperty(value = "所属用户id")
    private String sysUserId;
	/**模板名*/
	@Excel(name = "模板名", width = 15)
	@ApiModelProperty(value = "模板名")
    private String templateName;

	@ExcelCollection(name="排班模板选项")
	@ApiModelProperty(value = "排班模板选项")
	private List<StaScheduleTemplateItem> staScheduleTemplateItemList;

}
