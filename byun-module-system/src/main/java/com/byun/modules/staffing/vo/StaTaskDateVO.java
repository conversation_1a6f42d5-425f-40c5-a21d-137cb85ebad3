package com.byun.modules.staffing.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-6-19 16:11
 */
@Data
public class StaTaskDateVO {
    private String id;
    private String taskStartDate;//服务开始日期
    private String taskEndDate;//服务结束日期
    private String taskStartTime;//服务开始时间
    private String taskEndTime;//服务结束时间
    private String taskStartTime2;//服务开始时间
    private String taskEndTime2;//服务结束时间
    private String shiftCode;//班别代码
    private String expectNum;//需要人数
    private int remainingNum;//剩余人数
}
