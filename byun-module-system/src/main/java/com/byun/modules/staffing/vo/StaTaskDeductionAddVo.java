package com.byun.modules.staffing.vo;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-6-12 11:38
 */
@Data
public class StaTaskDeductionAddVo {
    private static final long serialVersionUID = 1L;
    /**
     * 扣款id
     */
    private java.lang.String id;
    /**
     * 扣款日期
     */
    //private Date deductionDate;
    /**
     * 门店id
     */
    private String storeId;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 扣款任务单id
     */
    private String staOrderId;
    /**
     * 迟到扣款
     */
    private BigDecimal lateDeduction;
    /***
     * 早退扣款
     */
    private BigDecimal earlyLeaveDeduction;
    /**
     * 其他扣款
     */
    private BigDecimal otherDeduction;
    /**
     * 扣款备注
     */
    private String emarks;

}
