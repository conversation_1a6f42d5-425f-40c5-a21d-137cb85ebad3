package com.byun.modules.staffing.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户数据展示量
 * @date 2021/12/4 16:37
 */
@Data
@ApiModel(value="用户数据展示量", description="用户数据展示量")
public class StaUserDataVo {
    @ApiModelProperty(value = "收藏量")
    private java.lang.Integer collectNum;
    @ApiModelProperty(value = "历史浏览量")
    private java.lang.Integer visitsNum;
    @ApiModelProperty(value = "任务完成")
    private java.lang.Integer completeWorkNum;
    @ApiModelProperty(value = "发布任务数")
    private java.lang.Integer adminPublishWorkNum;
    @ApiModelProperty(value = "任务申请人数任务数")
    private java.lang.Integer adminApplyWorkNum;
    @ApiModelProperty(value = "进行中任务数")
    private java.lang.Integer adminProgressWorkNum;
    @ApiModelProperty(value = "任务中任务数")
    private java.lang.Integer recruiterProgressWorkNum;
    @ApiModelProperty(value = "完单数")
    private java.lang.Integer recruiterCompleteWorkNum;
    @ApiModelProperty(value = "绑定人才数")
    private java.lang.Integer poolNum;

    //普通用户 任务单数
    @ApiModelProperty(value = "任务申请中")
    private java.lang.Integer applyWorkNum;
    @ApiModelProperty(value = "任务待就职")
    private java.lang.Integer waitWorkNum;
    @ApiModelProperty(value = "任务中")
    private java.lang.Integer progressWorkNum;
}
