package com.byun.modules.staffing.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.byun.modules.staffing.entity.StaUserInfoVisits;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 用户灵活用工信息（名片）
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@ApiModel(value="sta_user_infoPage对象", description="用户灵活用工信息（名片）")
public class StaUserInfoPage {

	/**主键*/
	@ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**系统用户id*/
	@Excel(name = "系统用户id", width = 15)
	@ApiModelProperty(value = "系统用户id")
    private java.lang.String sysUserId;
	/**展示级别A招聘官B代理C关联的朋友D*/
	@Excel(name = "展示级别A招聘官B代理C关联的朋友D", width = 15)
	@ApiModelProperty(value = "展示级别A招聘官B代理C关联的朋友D")
    private java.lang.String showLevelCode;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
	@ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**电话*/
	@Excel(name = "电话", width = 15)
	@ApiModelProperty(value = "电话")
    private java.lang.String phone;
	/**是否短信验证第一个电话*/
	@Excel(name = "是否短信验证第一个电话", width = 15)
	@ApiModelProperty(value = "是否短信验证第一个电话")
    private java.lang.Integer phoneVeri;
	/**电话2*/
	@Excel(name = "电话2", width = 15)
	@ApiModelProperty(value = "电话2")
    private java.lang.String phoneTwo;
	/**微信号*/
	@Excel(name = "微信号", width = 15)
	@ApiModelProperty(value = "微信号")
    private java.lang.String wechatId;
	/**身份证*/
	@Excel(name = "身份证", width = 15)
	@ApiModelProperty(value = "身份证")
    private java.lang.String idCard;
	/**性别*/
	@Excel(name = "性别", width = 15)
	@ApiModelProperty(value = "性别")
    private java.lang.Integer sex;
	/**简介*/
	@Excel(name = "简介", width = 15)
	@ApiModelProperty(value = "简介")
    private java.lang.String brief;
	/**生日*/
	@Excel(name = "生日", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "生日")
    private java.util.Date birthday;
	/**年龄*/
	@Excel(name = "年龄", width = 15)
	@ApiModelProperty(value = "年龄")
    private java.lang.Integer age;
	/**求职意向，sta_work_type工种code*/
	@Excel(name = "求职意向，sta_work_type工种code", width = 15)
	@ApiModelProperty(value = "求职意向，sta_work_type工种code")
    private java.lang.String intentionCode;
	/**所在区域code*/
	@Excel(name = "所在区域code", width = 15)
	@ApiModelProperty(value = "所在区域code")
    private java.lang.String regionCode;
	/**所在区域*/
	@Excel(name = "所在区域", width = 15)
	@ApiModelProperty(value = "所在区域")
    private java.lang.String region;
	/**
	 * 地址详情
	 */
	@TableField(exist = false)
	private String addressName;
	/**地址*/
	@Excel(name = "地址", width = 15)
	@ApiModelProperty(value = "地址")
    private java.lang.String address;
	/**任务地点坐标Lat*/
	@Excel(name = "任务地点坐标Lat", width = 15)
	@ApiModelProperty(value = "任务地点坐标Lat")
	private java.math.BigDecimal addressLat;
	/**任务地点坐标Lng*/
	@Excel(name = "任务地点坐标Lng", width = 15)
	@ApiModelProperty(value = "任务地点坐标Lng")
	private java.math.BigDecimal addressLng;
	/**身份证是否验证*/
	@Excel(name = "身份证是否验证", width = 15)
	@ApiModelProperty(value = "身份证是否验证")
    private java.lang.Integer idCardVeri;
	/**
	 * 头像
	 */
	@Excel(name = "头像", width = 15,type = 2)
	@ApiModelProperty(value = "头像")
	private String avatar;

	/**
	 * 绑定的招聘官id
	 */
	@ApiModelProperty(value = "绑定的招聘官id")
	private String bindUserId;

	/**
	 * 绑定的招聘官(未存数据库)
	 */
	@ApiModelProperty(value = "绑定的招聘官(未存数据库)")
	private  String bindUserName;

	@ExcelCollection(name="名片访问量")
	@ApiModelProperty(value = "名片访问量")
	private List<StaUserInfoVisits> staUserInfoVisitsList;
	@ApiModelProperty(value = "任务类型") // 兼职 全职
	private String workClass;
	@ApiModelProperty(value = "薪资范围")
	private String salar;

}
