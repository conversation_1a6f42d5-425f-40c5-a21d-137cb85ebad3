package com.byun.modules.staffing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2023-5-10 9:04
 */
@Data
@ApiModel(value="工作人员信息", description="工作人员信息")
public class StaWorkOrderPage {
    @ApiModelProperty(value = "店编")
    @Excel(name = "店编", width = 15)
    private String storeNo;//店编
    @Excel(name = "部门", width = 15)
    @ApiModelProperty(value = "部门")
    private String companyName;//部门
    @Excel(name = "工作名称", width = 30)
    @ApiModelProperty(value = "工作名称")
    private String workName;//工作名称
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String userName;//姓名
    @Excel(name = "身份证", width = 30)
    @ApiModelProperty(value = "身份证")
    private String idCard;//身份证
    @Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private int age;//年龄
    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private String sex;//性别
    @Excel(name = "入职日期", width = 30, format = "yyyy-MM-dd HH:mm")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入职日期")
    private Date dateOfEntry;//入职日期
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String presentStatus;//状态
    @Excel(name = "工作时长", width = 30)
    @ApiModelProperty(value = "工作时长")
    private Double workingHours;//工作时长
}
