package com.byun.modules.staffing.vo;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 任务列表
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@ApiModel(value="sta_workPage对象", description="任务列表")
public class StaWorkPage {
	/**主键*/
	@ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门code*/
	@ApiModelProperty(value = "所属部门code")
    private java.lang.String sysOrgCode;
	/**公司id（depart）*/
	@ApiModelProperty(value = "公司id（depart）")
    private java.lang.String companyId;
	/**公司名称*/
//	@Excel(name = "公司名称", width = 15)
//	@ApiModelProperty(value = "公司名称")
    private java.lang.String companyName;
	/**门店id（depart）*/
	@ApiModelProperty(value = "门店id（depart）")
    private java.lang.String firmId;
	/**门店事业处*/
	@Excel(name = "门店事业处", width = 15)
	@ApiModelProperty(value = "门店事业处")
	private String storeBusinessDivisionName;
	/**门店区域*/
	@Excel(name = "门店区域", width = 15)
	@ApiModelProperty(value = "门店区域")
	private String storeRegionName;
	/**门店区域id*/
	private String storeRegionId;
	/**门店名称*/
	@Excel(name = "店编", width = 15)
	@ApiModelProperty(value = "店编")
	private String storeNo;
	@Excel(name = "门店", width = 15)
	@ApiModelProperty(value = "门店")
    private java.lang.String firmName;
	/**任务名全称*/
	@Excel(name = "任务名称", width = 15)
	@ApiModelProperty(value = "任务名称")
    private java.lang.String nameFull;
	/**任务名简称*/
	@ApiModelProperty(value = "任务简称")
    private java.lang.String nameNick;
	/**任务描述*/
	@Excel(name = "任务描述", width = 15)
	@ApiModelProperty(value = "任务描述")
    private java.lang.String content;
	/**薪资类型*/
	@ApiModelProperty(value = "薪资类型")
	private java.lang.Integer salaryType;
	/**最小薪资*/
	@ApiModelProperty(value = "最小薪资")
    private java.math.BigDecimal salaryMin;
	/**最大薪资*/
	@ApiModelProperty(value = "最大薪资")
    private java.math.BigDecimal salaryMax;
	/**任务周期*/
	//@Excel(name = "任务周期", width = 15)
	@ApiModelProperty(value = "任务周期")
    private java.lang.String needWorkCycle;
	/**报道时间*/
	//@Excel(name = "报道时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "报道时间")
    private java.util.Date reportDate;
	/**任务开始时间*/
	//@Excel(name = "任务开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "任务开始时间")
    private java.util.Date workDateStart;
	/**任务结束时间*/
	//@Excel(name = "任务结束时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "任务结束时间")
    private java.util.Date workDateEnd;
	/**任务地址*/
	@Excel(name = "任务地址", width = 30)
	@ApiModelProperty(value = "任务地址")
    private java.lang.String address;
	/**任务地点坐标Lat*/
	@Excel(name = "任务地点坐标Lat", width = 15)
	@ApiModelProperty(value = "任务地点坐标Lat")
	private java.math.BigDecimal addressLat;
	/**任务地点坐标Lng*/
	@Excel(name = "任务地点坐标Lng", width = 15)
	@ApiModelProperty(value = "任务地点坐标Lng")
	private java.math.BigDecimal addressLng;
	/**所在区域编码*/
	@ApiModelProperty(value = "所在区域编码")
    private java.lang.String regionCode;
	@ApiModelProperty(value = "所在区域名称")
	private java.lang.String regionName;
	/**要求最小年龄段*/
	//@Excel(name = "要求最小年龄段", width = 15)
	@ApiModelProperty(value = "要求最小年龄段")
    private java.lang.Integer needAgeMin;
	/**要求最大年龄段*/
	//@Excel(name = "要求最大年龄段", width = 15)
	@ApiModelProperty(value = "要求最大年龄段")
    private java.lang.Integer needAgeMax;
	/**要求性别*/
	//@Excel(name = "要求性别", width = 15,dicCode="admin_sex")
	@ApiModelProperty(value = "要求性别")
    private java.lang.Integer needSex;
	/**要求学历*/
	//@Excel(name = "要求学历", width = 15,dicCode="admin_degree")
	@ApiModelProperty(value = "要求学历")
    private java.lang.Integer needDegree;
	/**要求经验*/
	//@Excel(name = "要求经验", width = 15)
	@ApiModelProperty(value = "要求经验")
    private java.lang.String needExp;
	/**其他要求*/
	@Excel(name = "其他要求", width = 15)
	@ApiModelProperty(value = "其他要求")
    private java.lang.String needOther;
	/**福利待遇*/
	//@Excel(name = "福利待遇", width = 15)
	@ApiModelProperty(value = "福利待遇")
    private java.lang.String benefit;
	/**标签code(label1,label2)*/
	@ApiModelProperty(value = "标签code(label1,label2)")
    private java.lang.String labelCode;
	/**标签(label1,label2)*/
	@ApiModelProperty(value = "标签(label1,label2)")
    private java.lang.String labelName;
	/**工种编码组*/
	@ApiModelProperty(value = "工种编码组")
    private java.lang.String workTypeCode;
	/**工种名称组*/
	@ApiModelProperty(value = "工种名称组")
    private java.lang.String workTypeName;
	/**展示开始时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "展示开始时间")
    private java.util.Date showDateStart;
	/**展示结束时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "展示结束时间")
    private java.util.Date showDateEnd;
	/**删除状态*/
	//@Excel(name = "删除状态", width = 15)
	@ApiModelProperty(value = "删除状态")
    private java.lang.Integer delFlag;
	/**任务种类（1兼职2专职）*/
	@ApiModelProperty(value = "任务种类（1兼职2专职）")
    private java.lang.Integer workClass;
	/**是否需要审核*/
	//@Excel(name = "是否需要审核", width = 15)
	@ApiModelProperty(value = "是否需要审核")
    private java.lang.Integer examineFlag;
	/**聚合id*/
	@ApiModelProperty(value = "聚合id")
    private java.lang.String aggregationId;
	/**联系人*/
	@Excel(name = "联系人", width = 15)
	@ApiModelProperty(value = "联系人")
    private java.lang.String liaison;
	/**联系人电话*/
	@Excel(name = "联系人电话", width = 15)
	@ApiModelProperty(value = "联系人电话")
    private java.lang.String liaisonTp;
	/**集合地址*/
	@ApiModelProperty(value = "集合地址")
    private java.lang.String addressJoin;
	/**适合人群code*/
	@ApiModelProperty(value = "适合人群code")
    private java.lang.String crowdCode;
	/**适合人群*/
	@ApiModelProperty(value = "适合人群")
    private java.lang.String crowd;
	/**任务时间要求*/
	//@Excel(name = "任务时间要求", width = 15)
	@ApiModelProperty(value = "任务时间要求")
    private java.lang.String needDate;
	/**状态0未发布1完成2发布中3任务中4结算中*/
	@Excel(name = "状态", width = 15, dicCode="sta_work_state")
	@ApiModelProperty(value = "状态0未发布1完成2发布中3任务中4结算中")
    private java.lang.Integer stateFlag;
	/**是否点名*/
	@ApiModelProperty(value = "是否点名")
    private java.lang.Integer rcNeedFlag;
	/**是否在列表展示*/
	//@Excel(name = "是否在列表展示", width = 15)
	@ApiModelProperty(value = "是否在列表展示")
    private java.lang.Integer listShow;
	/**需要人数*/
	@Excel(name = "需要人数", width = 15)
	@ApiModelProperty(value = "需要人数")
    private java.lang.Integer expectNum;
	/**申请人数*/
	@Excel(name = "申请人数", width = 15)
	@ApiModelProperty(value = "申请人数")
    private java.lang.Integer applyNum;
	/**通过人数*/
	@Excel(name = "通过人数", width = 15)
	@ApiModelProperty(value = "通过人数")
    private java.lang.Integer adoptNum;
	/**所在区域*/
	@ApiModelProperty(value = "所在区域")
    private java.lang.String region;
	/**是否需要签到*/
	@ApiModelProperty(value = "是否需要签到")
    private java.lang.Integer lcNeedFlag;
	//@Excel(name = "图片路径", width = 15,type=2)
	@ApiModelProperty(value = "图片路径")
	private java.lang.String imageUrl;
	@Excel(name = "地址名称", width = 15)
	@ApiModelProperty(value = "地址名称")
	private java.lang.String addressName;
	/**是否需要毕业证书编号*/
	//@Excel(name = "是否需要毕业证书编号", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "是否需要毕业证书编号")
	private java.lang.Integer needIdDiploma;
	/**是否需要健康证编号*/
	@Excel(name = "是否需要健康证", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "是否需要健康证")
	private java.lang.Integer needIdHealth;
	/**是否需要本人免冠蓝底或红底证件照*/
	//@Excel(name = "是否需要本人免冠蓝底或红底证件照", width = 15,dicCode="is_open_num")
	@ApiModelProperty(value = "是否需要本人免冠蓝底或红底证件照")
	private java.lang.Integer needMyPhoto;
	/**是否需要健康码*/
	//@Excel(name = "是否需要健康码", width = 15)
	@ApiModelProperty(value = "是否需要健康码")
	private java.lang.Integer needHealthPhoto;
	/**入职须知id*/
	@ApiModelProperty(value = "入职须知id")
	private java.lang.String staNoticeId;
	@ApiModelProperty(value = "入职须知内容")
	//工种id
	private String jobsId;
	//工种名称
	private String jobsName;
	/**`
	 * 任务结算标准大于工种结算标准
	 * 任务结算标准为空 使用工种结算标准 不为空使用任务结算标准
	 */
	//工种结算标准
	private BigDecimal jobsSalary;
	//任务结算标准
	private BigDecimal workSalary;
	//须知
	private java.lang.String staNoticeContext;
	/**
	 * 任务开始日期
	 */
	private Date taskStartDate;
	/**
	 * 任务结束日期
	 */
	private Date taskEndDate;
	/**
	 * 任务开始时间
	 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
	@ApiModelProperty(value = "任务开始时间")
	private Date taskStartTime;
	/**
	 * 任务结束时间
	 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
	@ApiModelProperty(value = "任务结束时间")
	private Date taskEndTime;
	@Excel(name = "置顶标记更新时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "置顶标记更新时间")
	private java.util.Date topFlagUpdate;
	/**当前用户是否收藏*/
	@ApiModelProperty(value = "当前用户是否收藏")
	private java.lang.Integer hasCollections;
	/**访问量*/
	@ApiModelProperty(value = "访问量")
	private java.lang.Integer visits;
	/**收藏量*/
	@ApiModelProperty(value = "收藏量")
	private java.lang.Integer collections;
	@ApiModelProperty(value = "薪资")
	private String salar;
	/**
	 * 任务预计总时数
	 */
	private Double taskEstimatedTotalTime;
	/**
	 * 任务预计总金额
	 */
	private BigDecimal taskEstimatedTotalAmount;

	private List<StaTaskDateVO> staTaskDateAndTimes;

//	@ExcelCollection(name="任务图片")
//	@ApiModelProperty(value = "任务图片")
//	private List<StaWorkImage> staWorkImageList;
//	@ExcelCollection(name="访问量、收藏量")
//	@ApiModelProperty(value = "访问量、收藏量")
//	private List<StaWorkVisits> staWorkVisitsList;
//	@ExcelCollection(name="任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新")
//	@ApiModelProperty(value = "任务报名人（报名时的信息，在任务之下）报名成功后信息记录不随用户更新")
//	private List<StaWorkEnroll> staWorkEnrollList;

}
