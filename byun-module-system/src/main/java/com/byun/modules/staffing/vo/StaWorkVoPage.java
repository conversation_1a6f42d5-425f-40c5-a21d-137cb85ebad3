package com.byun.modules.staffing.vo;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 任务列表｜execl导出
 * @date : 2023-8-16 17:14
 */
@Data
@ApiModel(value="StaWorkVoPage", description="网页列表展示｜execl导出")
public class StaWorkVoPage {
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    @Excel(name = "事业处", width = 15)
    @ApiModelProperty(value = "事业处")
    private String storeBusinessDivisionName;
    @Excel(name = "区域", width = 15)
    @ApiModelProperty(value = "区域")
    private String storeRegionName;
    @Excel(name = "店编", width = 15)
    @ApiModelProperty(value = "店编")
    private String storeNo;
    @Excel(name = " 部门", width = 15)
    @ApiModelProperty(value = "部门")
    private java.lang.String companyName;
    /**发布日期*/
    @Excel(name = "发布日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布日期")
    private java.util.Date createTime;
    @Excel(name = "任务类型", width = 15)
    @ApiModelProperty(value = "任务类型")
    private java.lang.String nameFull;
    @Excel(name = "任务地址", width = 40)
    @ApiModelProperty(value = "任务地址")
    private java.lang.String address;
    @Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private java.lang.String liaison;
    /**联系人电话*/
    @Excel(name = "联系人电话", width = 15)
    @ApiModelProperty(value = "联系人电话")
    private java.lang.String liaisonTp;
    @ApiModelProperty(value = "状态0未发布1完成2发布中3任务中4结算中")
    private java.lang.Integer stateFlag;
    @Excel(name = "状态", width = 15)
    private String stateFlagStr;
    @Excel(name = "服务周期", width = 15)
    @ApiModelProperty(value = "服务周期")
    private String taskStartAndEndDate;
    /**任务开始日期 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskStartDate;
    /** 任务结束日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskEndDate;
    @Excel(name = "服务时间", width = 15)
    @ApiModelProperty(value = "服务时间")
    private String taskStartAndEndTime;
    /**任务开始时间**/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "任务开始时间")
    private Date taskStartTime;
    /** 任务结束时间 **/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "任务结束时间")
    private Date taskEndTime;
    @Excel(name = "需要人数", width = 15)
    @ApiModelProperty(value = "需要人数")
    private java.lang.Integer expectNum;
    @Excel(name = "申请人数", width = 15)
    @ApiModelProperty(value = "申请人数")
    private java.lang.Integer applyNum;
    @Excel(name = "通过人数", width = 15)
    @ApiModelProperty(value = "通过人数")
    private java.lang.Integer adoptNum;
    @Excel(name = "结算标准", width = 15)
    @ApiModelProperty(value = "结算标准")
    private BigDecimal workSalary;
    @Excel(name = "时数上限", width = 15)
    @ApiModelProperty(value = "时数上限")
    private Double taskEstimatedTotalTime;
    @Excel(name = "金额上限", width = 15)
    @ApiModelProperty(value = "金额上限")
    private BigDecimal taskEstimatedTotalAmount;
    @Excel(name = "实际时数", width = 15)
    @ApiModelProperty(value = "实际时数")
    private Double totalTime;
    @Excel(name = "实际金额", width = 15)
    @ApiModelProperty(value = "实际金额")
    private BigDecimal  totalSalary;
    @Excel(name = "置顶标记更新时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "置顶标记更新时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.util.Date topFlagUpdate;
    //置顶
    private java.lang.String topFlag;
    //健康证
    private java.lang.Integer needIdHealth;
    private java.lang.String  idHealth;
    /**网页编辑回显**/
    private String selecteddeparts; //部门名称
    private String jobsName;//工种id
    private String jobsId;//工种类型
    private java.lang.String staNoticeId;// 任务描述ID
    private java.lang.String content; //任务描述内容
    List<StaTaskDateVO> staTaskDateAndTimes;

}
