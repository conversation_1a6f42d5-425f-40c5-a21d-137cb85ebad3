package com.byun.modules.staffing.vo;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description  用户结算账户信息
 * @date : 2024-7-24 9:43
 */
@Data
public class SysUserBankVo {
    @Excel(name = "事业处",width = 15)
    private String businessDivisionName;
    @Excel(name = "区域",width = 15)
    private String storeRegionName;
    @Excel(name = "店编",width = 15)
    private String storeNo;
    @Excel(name = "店别",width = 15)
    private String companyName;
    @Excel(name = "姓名",width = 15)
    private String userName;
    @Excel(name = "身份证", width = 30)
    private String idCard;
    @Excel(name = "联系方式")
    private String phone;
    @Excel(name = "开户行",width = 20)
    private String bankName;
    @Excel(name = "卡号",width =30 )
    private String cardNumber;
}
