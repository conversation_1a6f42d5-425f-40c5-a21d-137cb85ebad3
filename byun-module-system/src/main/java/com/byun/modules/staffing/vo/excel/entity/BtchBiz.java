package com.byun.modules.staffing.vo.excel.entity;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-6-25 16:40
 */
@Data
public class BtchBiz {
    @Excel(name = "批次号",width = 30)
    private String btchBizId;
    @Excel(name = "人员批次号",width = 30)
    private String bizId;
    @Excel(name = "姓名",width = 20)
    private String userName;
    @Excel(name = "身份证",width = 30)
    private String idCard;
    @Excel(name = "状态(1成功99失败)",width = 15)
    private String status;
}
