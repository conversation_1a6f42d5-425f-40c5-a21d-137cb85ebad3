package com.byun.modules.staffing.vo.excel.exportXls;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 扣款列表execl导出
 * @date : 2024-6-14 10:30
 */
@lombok.Data
public class staTaskDeductioneXportXlsVo {
    private static final long serialVersionUID = 1L;
    private java.lang.String id;
    @Excel(name = "创建时间",format = "yyyy-MM-dd")
    private Date createTime;
    @Excel(name = "事业处",width = 15)
    private String businessDivisionName;
    @Excel(name = "区域",width = 15)
    private String storeRegionName;
    @Excel(name = "店编",width = 15)
    private String storeNo;
    @Excel(name = "店别",width = 15)
    private String companyName;
    @Excel(name = "任务类型")
    private String workNameFull;
    @Excel(name = "操作人",width = 15)
    private String createBy;
    @Excel(name = "扣款人")
    private String deductionUserName;
    @Excel(name = "迟到扣款",width = 15)
    private BigDecimal lateDeduction;
    @Excel(name = "早退扣款",width = 15)
    private BigDecimal earlyLeaveDeduction;
    @Excel(name = "其他扣款",width = 15)
    private BigDecimal otherDeduction;
    //@Excel(name = "扣款日期",width = 20)
    //private Date deductionDate;
    @Excel(name = "扣款备注",width = 15)
    private String emarks;

}
