package com.byun.modules.staffing.vo.importXls.entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;


/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2024-6-20 18:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true) //链式调用
public class LocationClock {
    @Excel(name = "事业处", width = 20)
    private String businessDivisionName;
    @Excel(name = "区域", width = 20)
    private String regionName;
    @Excel(name = "店编", width = 15)
    private String storeNo;
    @Excel(name = "店别",width = 20)
    private String companyName;
    @Excel(name = "班别代码",width = 20)
    private String shiftCode;
    @Excel(name = "姓名",width = 20)
    private String userName;
    @Excel(name = "身份证",width = 20)
    private String idCard;
    @Excel(name = "排班日期",width = 20)
    private String scheduleDay;
    @Excel(name = "签到1")
    private String clockInTime1; //签到1
    @Excel(name = "签退1")
    private String clockInTime2; //签退1
    @Excel(name = "签到2")
    private String clockInTime3; //签到2
    @Excel(name = "签退2")
    private String clockInTime4; //签退2
}
