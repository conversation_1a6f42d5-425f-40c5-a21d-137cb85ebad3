package com.byun.modules.staffing.vo;

import com.byun.modules.staffing.entity.StaLocationClock;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description  网页查看签到数据转换类
 * @date : 2023-6-8 9:11
 */
@Data
public class lcDayPage {
    private String staOrderId; //订单
    private String staWorkId; //任务id
    private String scheduleId; //排班
    private Date scheduleDay; //排班日期
    private Integer timeType; //1 签到 2 签退
    private StaLocationClock lcList; //签到信息
    private Date timeExpect; //要求签到时间
    private String realName; //姓名
    private String userIdCard; //身份证
    private String sysUserId; //用户id
    private String shiftCode; //班别代码
    private String businessDivisionName; //事业处
    private String regionName; //区域
    private String  storeNo;//店编
    private String  companyName; //门店
    /**班次开始时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    private Date startTime;
    /**班次结束时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "HH:mm")
    @DateTimeFormat(pattern="HH:mm")
    private Date endTime;
}
