package com.byun.modules.system.controller;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.service.IAttendanceAnalysisService;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.vo.AgeEducationStatsDTO;
import com.byun.modules.system.vo.AttendanceStatusStatsDTO;
import com.byun.modules.system.vo.JoinsByPeriodDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * 考勤分析控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Api(tags = "考勤分析")
@RestController
@RequestMapping("/staffing/attendanceAnalysis")
public class AttendanceAnalysisController {
    @Autowired
    private IAttendanceAnalysisService attendanceAnalysisService;
    @Autowired
    private ISysDepartService sysDepartService;
    /**
     * 获取年龄和学历分布统计
     *
     * @param selectedDate  选择的日期
     * @param selectedMonth 选择的月份
     * @return 年龄和学历分布统计结果
     */
    @ApiOperation(value = "获取年龄和学历分布统计", notes = "分析员工年龄和学历分布情况")
    @GetMapping("/ageEducationStats")
    public Result<?> getAgeEducationStats(
            @RequestParam(value = "selectedDate", required = false) String selectedDate,
            @RequestParam(value = "selectedMonth", required = false) String selectedMonth,
            @RequestParam(value = "deptNo",required = false) String deptNo,
            @RequestParam(value = "deptName",required = false ) String deptName,
            HttpServletRequest request) {
        if (selectedDate == null && selectedMonth == null) {
            return Result.error("参数丢失：请提供selectedDate或selectedMonth");
        }
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }

        // 使用通用方法获取组织代码
        String orgCode = getOrgCodeByDepartment(deptNo, deptName, user.getOrgCode());
        try {
            // 获取年龄和学历分布统计
            AgeEducationStatsDTO stats = attendanceAnalysisService.getAgeEducationStats(
                    selectedDate,
                    selectedMonth,
                    orgCode,
                    Arrays.asList(
                            CommonConstant.ORDER_STATUS_0,
                            CommonConstant.ORDER_STATUS_2,
                            CommonConstant.ORDER_STATUS_7,
                            CommonConstant.ORDER_STATUS_9
                    )
            );
            return Result.OK("获取成功", stats);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取年龄和学历分布统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取每日入职统计
     * @param selectedDate 选择的日期 (获取该日期前30天到当前的数据)
     * @param selectedMonth 选择的月份 (获取本月的数据，格式: YYYY-MM)
     * @param request HTTP请求
     * @return 每日入职统计结果
     */
    @ApiOperation(value = "获取每日入职统计", notes = "根据selectedDate(前30天)或selectedMonth(本月)统计每日员工入职人数")
    @GetMapping("/countJoinsByPeriod")
    public Result<?> countJoinsByPeriod(
            @RequestParam(value = "selectedDate", required = false) String selectedDate,
            @RequestParam(value = "selectedMonth", required = false) String selectedMonth,
            @RequestParam(value = "deptNo",required = false) String deptNo,
            @RequestParam(value = "deptName",required = false ) String deptName,
            HttpServletRequest request) {
        // 参数验证
        if (selectedDate == null && selectedMonth == null) {
            return Result.error("参数丢失：请提供selectedDate或selectedMonth");
        }

        // 获取当前登录用户
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }

        // 使用通用方法获取组织代码
        String orgCode = getOrgCodeByDepartment(deptNo, deptName, user.getOrgCode());
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startDate, endDate;

            if (WxlConvertUtils.isNotEmpty(selectedDate)) {
                // selectedDate逻辑：获取该日期前30天到当前的数据
                LocalDate date = LocalDate.parse(selectedDate, formatter);
                startDate = date.minusDays(30); // 30天前
                endDate = date; // 到选择的日期
            } else {
                // selectedMonth逻辑：获取本月的数据
                LocalDate date = LocalDate.parse(selectedMonth + "-01", formatter);
                startDate = date.withDayOfMonth(1); // 月初
                endDate = date.withDayOfMonth(date.lengthOfMonth()); // 月末
            }
            // 获取每日入职统计
            JoinsByPeriodDTO stats = attendanceAnalysisService.getJoinsByPeriod(
                startDate,
                endDate,
                orgCode,
                Arrays.asList(
                    CommonConstant.ORDER_STATUS_0,
                    CommonConstant.ORDER_STATUS_2,
                    CommonConstant.ORDER_STATUS_7,
                    CommonConstant.ORDER_STATUS_9
                )
            );
            return Result.OK("获取成功", stats);
        } catch (DateTimeParseException e) {
            return Result.error("参数错误：日期格式不正确，selectedDate请使用YYYY-MM-DD格式，selectedMonth请使用YYYY-MM格式");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取入职统计失败：" + e.getMessage());
        }
    }
    /**
     * 获取考勤状况统计 - 支持多班次处理
     *
     * 业务说明：
     * 1. 支持一个人一天多个班次的情况（2个班次、4个班次，包括夜班）
     * 2. 员工当天状态按最严重情况确定：早退 > 迟到 > 缺卡 > 正常
     * 3. 只有所有班次都正常，员工当天状态才为正常
     *
     * @param selectedDate 选择的日期 (格式: YYYY-MM-DD)
     * @param selectedMonth 选择的月份 (格式: YYYY-MM)
     * @param request HTTP请求
     * @return 考勤状况统计结果
     */


    @ApiOperation(value = "获取考勤状况统计", notes = "统计指定日期或月份的员工考勤状况分布，支持多班次处理")
    @GetMapping("/attendanceStatusStats")
    public Result<?> getAttendanceStatusStats(
            @RequestParam(value = "selectedDate", required = false) String selectedDate,
            @RequestParam(value = "selectedMonth", required = false) String selectedMonth,
            @RequestParam(value = "deptNo", required = false) String deptNo,
            @RequestParam(value = "deptName", required = false) String deptName,
            HttpServletRequest request) {
        try {
            // 1. 参数验证
            if (selectedDate == null && selectedMonth == null) {
                return Result.error("参数丢失：请提供selectedDate或selectedMonth");
            }
            // 2. 用户认证
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (WxlConvertUtils.isEmpty(user)) {
                return Result.error("登录失效");
            }

            // 使用通用方法获取组织代码
            String orgCode = getOrgCodeByDepartment(deptNo, deptName, user.getOrgCode());

            // 3. 调用优化的Service层处理业务逻辑
            AttendanceStatusStatsDTO result = attendanceAnalysisService
                    .getOptimizedAttendanceStatusStats(selectedDate, selectedMonth, orgCode, deptNo);
            return Result.OK("获取成功", result);

        } catch (Exception e) {
            log.error("获取考勤状况统计失败", e);
            return Result.error("获取考勤状况统计失败：" + e.getMessage());
        }
    }

    /**
     * 通用方法：根据部门编号和部门名称获取组织代码
     *
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @param fallbackOrgCode 回退的组织代码（当查询不到部门信息时使用）
     * @return 组织代码
     */
    private String getOrgCodeByDepartment(String deptNo, String deptName, String fallbackOrgCode) {
        if (WxlConvertUtils.isNotEmpty(deptNo) && WxlConvertUtils.isNotEmpty(deptName)) {
            try {
                SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>()
                        .eq(SysDepart::getStoreNo, deptNo)
                        .eq(SysDepart::getDepartName, deptName)
                        .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));

                if (sysDepart != null && WxlConvertUtils.isNotEmpty(sysDepart.getOrgCode())) {
                    return sysDepart.getOrgCode();
                }
            } catch (Exception e) {
                log.error("查询部门信息失败：deptNo={}, deptName={}", deptNo, deptName, e);
            }
        }
        return fallbackOrgCode;
    }
}
