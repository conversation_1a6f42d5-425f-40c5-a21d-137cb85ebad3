package com.byun.modules.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.DateUtils;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.bosong.entity.BoSong;
import com.byun.modules.excel.export.PayrollCalculation.BusinessDepartmentExcel;
import com.byun.modules.excel.export.PayrollCalculation.RegionExcel;
import com.byun.modules.excel.export.PayrollCalculation.StoreExcel;
import com.byun.modules.staffing.entity.StaLocationClock;
import com.byun.modules.staffing.entity.StaOrder;
import com.byun.modules.staffing.entity.StaTaskDeduction;
import com.byun.modules.staffing.service.IStaLocationClockService;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaTaskDeductionService;
import com.byun.modules.staffing.vo.SysUserBankVo;
import com.byun.modules.system.entity.StaUserBankCards;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.model.SysDepartTreeModel;
import com.byun.modules.system.service.IStaUserBankCardsService;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.service.ISysUserService;
import com.byun.modules.zh.service.IZhService;
import com.byun.modules.zh.vo.PersonAuthVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 银行卡
 * @date : 2023-3-30 16:03
 */
@RestController
@RequestMapping("/bankCards")
public class StaUserBankCardsController {
    @Autowired
    private IStaUserBankCardsService staUserBankCardsService;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IStaLocationClockService staLocationClockService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IZhService zhService;
    @Autowired
    private IStaTaskDeductionService staTaskDeductionService;

    /**
     * 银行卡流程
     * step:1 银行卡信息跟本人是否匹配
     * step:2 根据卡号查询银行详情信息
     * step:3 绑定银行卡
     * step:4 供应商注册
     * 支付宝流程
     * step:1 绑定支付宝
     * step:2 供应商注册
     *
     * @param jsonObject
     * @return result
     */
    @PostMapping("/bindBank")
    public Result determineIfTheCardIsValid(@RequestBody JSONObject jsonObject) throws Exception {
        Result result = staUserBankCardsService.bindBankAndUpdateBank(jsonObject);
        if (result.getCode() == 200) {
            //供应商注册
            String token = zhService.getToken(); //获取Token
            if (WxlConvertUtils.isNotEmpty(token)) {
                String phone = jsonObject.getString("phone");
                String idCard = jsonObject.getString("idCard");
                String userName = jsonObject.getString("userName");
                String userId = jsonObject.getString("userId");
                String cardNumber = jsonObject.getString("cardNumber").replace(" ", "");
                SysUser sysUser = sysUserService.getById(userId);
                StaUserBankCards staUserBankCards = staUserBankCardsService.getOne(new QueryWrapper<StaUserBankCards>().lambda().eq(StaUserBankCards::getUserId, userId));
                PersonAuthVO personAuthVO = new PersonAuthVO();
                personAuthVO.setName(userName);
                personAuthVO.setCertNo(idCard);
                personAuthVO.setMobile(phone);
                personAuthVO.setBankNo(cardNumber);
                personAuthVO.setCertImgFrontHttp(sysUser.getFrontUrl());//正面
                personAuthVO.setCertImgBackHttp(sysUser.getReverseUrl());//反面
                if (staUserBankCards.getAccountType() == CommonConstant.ACCOUNTTYPE1) { //银行卡
                    personAuthVO.setBank(staUserBankCards.getBankName());
                    personAuthVO.setAccountType(CommonConstant.ACCOUNTTYPE1);//收款方式 1银行卡 2支付宝
                } else if (staUserBankCards.getAccountType() == CommonConstant.ACCOUNTTYPE2) {//支付宝
                    personAuthVO.setAccountType(CommonConstant.ACCOUNTTYPE2);//收款方式 1银行卡 2支付宝
                }
                JSONObject json = zhService.personAuth(personAuthVO, token);
                Boolean success = json.getBoolean("success");
                String resultMessage = json.getString("resultMessage");
                if (success) {
                    JSONObject resultData = json.getJSONObject("resultData");
                    int code = resultData.getInteger("code");
                    String message = resultData.getString("message");
                    if (code != 1) { //错误
                        result.setMessage(message);
                        return result;
                    }
                    result.setMessage(message);
                } else {
                    result.setSuccess(false);
                    result.setMessage(WxlConvertUtils.isNotEmpty(resultMessage) ? resultMessage : "远程API调用失败");
                    return result;
                }
            } else {
                result.setSuccess(false);
                result.setMessage("获取Token失败");
                return result;
            }
        }
        return result;
    }

    /**
     * 根据用户ID查询用户银行卡信息
     *
     * @param userId 用户ID
     * @return Result
     */
    @GetMapping("/getBankCard/{userId}")
    public Result getBankCard(@PathVariable("userId") String userId) {
        Result result = new Result();
        StaUserBankCards staUserBankCards = staUserBankCardsService.getOne(new QueryWrapper<StaUserBankCards>().eq("user_id", userId));
        StaOrder staorder = staOrderService.getById(userId);
        if (WxlConvertUtils.isNotEmpty(staUserBankCards)) {
            result.setSuccess(true);
            result.setResult("成功");
            result.setResult(staUserBankCards);
        } else if (WxlConvertUtils.isNotEmpty(staorder)) {
            StaUserBankCards s = staUserBankCardsService.getOne(new QueryWrapper<StaUserBankCards>().eq("user_id", staorder.getSysUserId()));
            if (WxlConvertUtils.isNotEmpty(s)) {
                result.setSuccess(true);
                result.setResult("成功");
                result.setResult(s);
            } else {
                result.setSuccess(false);
                result.setResult("当前用户未绑定银行卡！");
            }
        } else {
            result.setSuccess(false);
            result.setResult("当前用户未绑定银行卡！");
        }
        return result;
    }

    /**
     * 获取店铺人员结算账户
     * @param pageNo
     * @param pageSize
     * @param deptName
     * @param deptNo
     * @param userName
     * @return
     */
    @GetMapping("/list")
    public Result list(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                       @RequestParam(name = "deptName", required = false) String deptName,
                       @RequestParam(name = "deptNo",required = false) String deptNo,
                       @RequestParam(name = "userName", required = false) String userName) {
        //TODO 添加日期区间查询      获取选中店铺中的所有人员（包含已完成的人员）
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(user)) {
            return Result.error("登录失效");
        }
        if (WxlConvertUtils.isEmpty(deptName) || WxlConvertUtils.isEmpty(deptNo)) {
            return Result.OK("请选择门店");
        }
        Page<SysUserBankVo> sysUserBankVoPage = staUserBankCardsService.adminList(pageNo,pageSize,deptName,userName,deptNo);
        return Result.OK("操作成功",sysUserBankVoPage);
    }
    @RequestMapping("/exportXlsBank")
    public ModelAndView exportXlsBank(@RequestParam(name = "deptName", required = false) String deptName,
                                      @RequestParam(name = "userName", required = false) String userName,
                                      @RequestParam(name = "deptNo",required = false) String deptNo) {
        if (WxlConvertUtils.isEmpty(deptName) || WxlConvertUtils.isEmpty(deptNo)) {
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "结算账户信息");
            mv.addObject(NormalExcelConstants.CLASS, SysUserBankVo.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("结算账户", "结算账户"));
            mv.addObject(NormalExcelConstants.DATA_LIST, new ArrayList<SysUserBankVo>());
            return mv;
        }
        List<SysUserBankVo> sysUserBankVo = staUserBankCardsService.exportXlsBank(deptName,deptNo,userName);
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "结算账户信息");
        mv.addObject(NormalExcelConstants.CLASS, SysUserBankVo.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("结算账户", "结算账户"));
        mv.addObject(NormalExcelConstants.DATA_LIST, sysUserBankVo);
        return mv;
    }
    @PostMapping("/edit")
    public Result edit(@RequestBody SysUser sysUser) {
        if (WxlConvertUtils.isEmpty(sysUser.getId())) {
            return Result.error("用户丢失");
        }
        if (WxlConvertUtils.isEmpty(sysUser.getHourlySalary())) {
            return Result.error("薪资丢失");
        }
        SysUser user = sysUserService.getById(sysUser);
        user.setHourlySalary(sysUser.getHourlySalary());
        sysUserService.updateById(user);
        return Result.OK("操作成功", user);
    }

    /***
     * 薪资计算列表
     * @param
     * @param enrollName
     * @param rosterMonth
     * @return
     */
    @GetMapping("/salaryCalculatorList")
    public Result salaryCalculatorList(
            @RequestParam(name = "businessDepartmentName", required = false) String businessDepartmentName,
            @RequestParam(name = "regionId", required = false) String regionId,
            @RequestParam(name = "companyId", required = false) String companyId,
            @RequestParam(name = "enrollName", required = false) String enrollName,
            @RequestParam(name = "idCard", required = false) String idCard,
            @RequestParam(name = "rosterMonth", required = false) String rosterMonth) throws ParseException {
        if (WxlConvertUtils.isEmpty(businessDepartmentName) && WxlConvertUtils.isEmpty(regionId) && WxlConvertUtils.isEmpty(companyId)) {
            return Result.error("事业处-区域-门店必选其中一个");
        }
        if (WxlConvertUtils.isEmpty(rosterMonth)) {
            return Result.error("请选择要查询的日期");
        }
        List<JSONObject> resultJson = staUserBankCardsService.salaryCalculatorList(businessDepartmentName, regionId, companyId,enrollName,rosterMonth);
        if (resultJson == null || resultJson.isEmpty()) {
            return Result.OK("无数据！", "");
        }
        return Result.OK(resultJson);
    }

    /**
     * 薪资汇总计算
     * 事业处
     * 区域
     * 门店
     *
     * @return
     */
    @GetMapping("/salaryCollectList")
    public Result salaryCollectList(@RequestParam(name = "rosterMonth", required = true) String rosterMonth) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error("登录失效!");
        }
        String[] split = rosterMonth.split(",");
        String startTime = split[0], endTime = split[1];
        //获取所有部门 树结构
        Result<List<SysDepartTreeModel>> result = new Result<>();
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            //只查询当前登录部门
            String departId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
            if (StringUtils.isNotBlank(departId)) {
                //任务单 TODO 什么状态
                List<StaOrder> staOrders = staOrderService.list(new LambdaQueryWrapper<StaOrder>()
                        .likeRight(StaOrder::getOrderCode, sysUser.getOrgCode())
                        .ge(StaOrder::getCreateTime, startTime)
                        .le(StaOrder::getCreateTime, endTime)
                        .eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0));
                //考勤
                List<StaLocationClock> staLocationClocks = staLocationClockService.list(new LambdaQueryWrapper<StaLocationClock>()
                        .likeRight(StaLocationClock::getOrgCode, user.getOrgCode())
                        .ge(StaLocationClock::getTimeExpect, startTime)
                        .le(StaLocationClock::getTimeExpect, endTime)
                        .eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0));
                List<SysDepartTreeModel> list = sysDepartService.queryMyDeptTreeList(departId);
                //总部丹尼斯
                for (SysDepartTreeModel sysDepartTreeModel : list) {
                    double summaryWorkingHours = 0;
                    BigDecimal salarySummary = new BigDecimal(0);
                    //汇总
                    String orgCode = sysDepartTreeModel.getOrgCode();
                    List<StaOrder> staOrderS = staOrders.stream().filter(staOrder -> staOrder.getOrderCode().startsWith(orgCode)).collect(Collectors.toList());
                    List<StaLocationClock> staLocationClockStream = staLocationClocks.stream().filter(clocks -> clocks.getOrgCode().startsWith(orgCode)).collect(Collectors.toList());
                    for (StaOrder staOrder : staOrderS) {
                        staOrder.setHourlySalary(new BigDecimal(14));
                    }
                    //任务单
                    for (StaOrder staOrder : staOrderS) {
                        if (WxlConvertUtils.isNotEmpty(staOrder.getHourlySalary())) {
                            //用户打卡集合
                            List<StaLocationClock> clockCollect = staLocationClockStream.stream().filter(data -> data.getStaOrderId().equals(staOrder.getId())).collect(Collectors.toList());
                            //时数计算   金额计算
                            //拆分签到集合 一个任务单对应Map一条数据集
                            Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
                            for (StaLocationClock staLocationClock : clockCollect) {
                                String staOrderId = staLocationClock.getStaOrderId();
                                if (!locationClocksMap.containsKey(staOrderId)) {
                                    locationClocksMap.put(staOrderId, new ArrayList<>());
                                }
                                locationClocksMap.get(staOrderId).add(staLocationClock);
                            }
                            for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                                List<StaLocationClock> value = entry.getValue();
                                //key签到日期，value签到数据
                                Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                                        .collect(Collectors.groupingBy(clock -> {
                                            Calendar cal = Calendar.getInstance();
                                            cal.setTime(clock.getTime());
                                            cal.set(Calendar.HOUR_OF_DAY, 0);
                                            cal.set(Calendar.MINUTE, 0);
                                            cal.set(Calendar.SECOND, 0);
                                            cal.set(Calendar.MILLISECOND, 0);
                                            return cal.getTime();
                                        }));
                                //升序排序
                                for (List<StaLocationClock> sublist : mapByDay.values()) {
                                    sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                                }
                                //时数计算
                                for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                                    Date key = mapByDayEntry.getKey();
                                    List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                                    List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());
                                    List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());
                                    long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                                    long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                                    if (totalWorkDuration > shiftDuration) {
                                        totalWorkDuration = shiftDuration;
                                    }
                                    Long hours = totalWorkDuration / 60;//小时
                                    Long remainingMinutes = totalWorkDuration % 60; //分钟
                                    String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                                    double workHours = Double.parseDouble(workHoursStr); //当天时数
                                    summaryWorkingHours = summaryWorkingHours + workHours; //时数累加
                                    BigDecimal money = staOrder.getHourlySalary();
                                    BigDecimal bigDecimal = money.multiply(BigDecimal.valueOf(workHours)).setScale(2, RoundingMode.HALF_UP);
                                    salarySummary = salarySummary.add(bigDecimal);
                                }
                            }
                        }
                    }
                    sysDepartTreeModel.setSummaryWorkingHours(summaryWorkingHours); //汇总时数
                    sysDepartTreeModel.setSalarySummary(salarySummary); //汇总金额
                    summaryWorkingHours = 0;
                    salarySummary = new BigDecimal(0);
                    //循环事业处
                    if (!sysDepartTreeModel.getChildren().isEmpty()) {
                        double summaryWorkingHours2 = 0;
                        BigDecimal salarySummary2 = new BigDecimal(0);
                        for (SysDepartTreeModel child : sysDepartTreeModel.getChildren()) {
                            String orgCode1 = child.getOrgCode();
                            List<StaOrder> staOrders2 = staOrders.stream().filter(staOrder -> staOrder.getOrderCode().startsWith(orgCode1)).collect(Collectors.toList());
                            List<StaLocationClock> staLocationClocks2 = staLocationClocks.stream().filter(clocks -> clocks.getOrgCode().startsWith(orgCode1)).collect(Collectors.toList());
                            if (!staOrders2.isEmpty() && !staLocationClocks2.isEmpty()) {
                                for (StaOrder staOrder : staOrders2) {
                                    if (WxlConvertUtils.isNotEmpty(staOrder.getHourlySalary())) {
                                        List<StaLocationClock> clockCollect = staLocationClockStream.stream().filter(data -> data.getStaOrderId().equals(staOrder.getId())).collect(Collectors.toList());
                                        //时数计算   金额计算
                                        //拆分签到集合 一个任务单对应Map一条数据集
                                        Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
                                        for (StaLocationClock staLocationClock : clockCollect) {
                                            String staOrderId = staLocationClock.getStaOrderId();
                                            if (!locationClocksMap.containsKey(staOrderId)) {
                                                locationClocksMap.put(staOrderId, new ArrayList<>());
                                            }
                                            locationClocksMap.get(staOrderId).add(staLocationClock);
                                        }
                                        for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                                            List<StaLocationClock> value = entry.getValue();
                                            //key签到日期，value签到数据
                                            Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                                                    .collect(Collectors.groupingBy(clock -> {
                                                        Calendar cal = Calendar.getInstance();
                                                        cal.setTime(clock.getTime());
                                                        cal.set(Calendar.HOUR_OF_DAY, 0);
                                                        cal.set(Calendar.MINUTE, 0);
                                                        cal.set(Calendar.SECOND, 0);
                                                        cal.set(Calendar.MILLISECOND, 0);
                                                        return cal.getTime();
                                                    }));
                                            //升序排序
                                            for (List<StaLocationClock> sublist : mapByDay.values()) {
                                                sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                                            }
                                            //时数计算
                                            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                                                Date key = mapByDayEntry.getKey();
                                                List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                                                List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());
                                                List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());
                                                long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                                                long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                                                if (totalWorkDuration > shiftDuration) {
                                                    totalWorkDuration = shiftDuration;
                                                }
                                                Long hours = totalWorkDuration / 60;//小时
                                                Long remainingMinutes = totalWorkDuration % 60; //分钟
                                                String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                                                double workHours = Double.parseDouble(workHoursStr); //当天时数
                                                summaryWorkingHours2 = summaryWorkingHours2 + workHours; //时数累加
                                                BigDecimal money = staOrder.getHourlySalary();
                                                BigDecimal bigDecimal = money.multiply(BigDecimal.valueOf(workHours)).setScale(2, RoundingMode.HALF_UP);
                                                salarySummary2 = salarySummary2.add(bigDecimal);
                                            }
                                        }
                                    }
                                }
                            }
                            child.setSummaryWorkingHours(summaryWorkingHours2);
                            child.setSalarySummary(salarySummary2);
                            summaryWorkingHours2 = 0;
                            salarySummary2 = new BigDecimal(0);
                            //循环区域
                            if (child.getChildren() != null) {
                                double summaryWorkingHours3 = 0;
                                BigDecimal salarySummary3 = new BigDecimal(0);
                                for (SysDepartTreeModel childChild : child.getChildren()) {
                                    String orgCode2 = childChild.getOrgCode();
                                    List<StaOrder> staOrders3 = staOrders.stream().filter(staOrder -> staOrder.getOrderCode().startsWith(orgCode2)).collect(Collectors.toList());
                                    List<StaLocationClock> staLocationClocks3 = staLocationClocks.stream().filter(clocks -> clocks.getOrgCode().startsWith(orgCode2)).collect(Collectors.toList());
                                    if (!staOrders3.isEmpty() && !staLocationClocks3.isEmpty()) {
                                        for (StaOrder staOrder : staOrders3) {
                                            if (WxlConvertUtils.isNotEmpty(staOrder.getHourlySalary())) {
                                                List<StaLocationClock> clockCollect = staLocationClockStream.stream().filter(data -> data.getStaOrderId().equals(staOrder.getId())).collect(Collectors.toList());
                                                //时数计算   金额计算
                                                //拆分签到集合 一个任务单对应Map一条数据集
                                                Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
                                                for (StaLocationClock staLocationClock : clockCollect) {
                                                    String staOrderId = staLocationClock.getStaOrderId();
                                                    if (!locationClocksMap.containsKey(staOrderId)) {
                                                        locationClocksMap.put(staOrderId, new ArrayList<>());
                                                    }
                                                    locationClocksMap.get(staOrderId).add(staLocationClock);
                                                }
                                                for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                                                    List<StaLocationClock> value = entry.getValue();
                                                    //key签到日期，value签到数据
                                                    Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                                                            .collect(Collectors.groupingBy(clock -> {
                                                                Calendar cal = Calendar.getInstance();
                                                                cal.setTime(clock.getTime());
                                                                cal.set(Calendar.HOUR_OF_DAY, 0);
                                                                cal.set(Calendar.MINUTE, 0);
                                                                cal.set(Calendar.SECOND, 0);
                                                                cal.set(Calendar.MILLISECOND, 0);
                                                                return cal.getTime();
                                                            }));
                                                    //升序排序
                                                    for (List<StaLocationClock> sublist : mapByDay.values()) {
                                                        sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                                                    }
                                                    //时数计算
                                                    for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                                                        Date key = mapByDayEntry.getKey();
                                                        List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                                                        List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());
                                                        List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());
                                                        long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                                                        long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                                                        if (totalWorkDuration > shiftDuration) {
                                                            totalWorkDuration = shiftDuration;
                                                        }
                                                        Long hours = totalWorkDuration / 60;//小时
                                                        Long remainingMinutes = totalWorkDuration % 60; //分钟
                                                        String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                                                        double workHours = Double.parseDouble(workHoursStr); //当天时数
                                                        summaryWorkingHours3 = summaryWorkingHours3 + workHours; //时数累加
                                                        BigDecimal money = staOrder.getHourlySalary();
                                                        BigDecimal bigDecimal = money.multiply(BigDecimal.valueOf(workHours)).setScale(2, RoundingMode.HALF_UP);
                                                        salarySummary3 = salarySummary3.add(bigDecimal);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    childChild.setSummaryWorkingHours(summaryWorkingHours3);
                                    childChild.setSalarySummary(salarySummary3);
                                    summaryWorkingHours3 = 0;
                                    salarySummary3 = new BigDecimal(0);
                                    //循环店铺
                                    if (childChild.getChildren() != null) {
                                        double summaryWorkingHours4 = 0;
                                        BigDecimal salarySummary4 = new BigDecimal(0);
                                        for (SysDepartTreeModel childChildChild : childChild.getChildren()) {
                                            String orgCode3 = childChildChild.getOrgCode();
                                            List<StaOrder> staOrders4 = staOrders.stream().filter(staOrder -> staOrder.getOrderCode().startsWith(orgCode3)).collect(Collectors.toList());
                                            List<StaLocationClock> staLocationClocks4 = staLocationClocks.stream().filter(clocks -> clocks.getOrgCode().startsWith(orgCode3)).collect(Collectors.toList());
                                            if (!staOrders4.isEmpty() && !staLocationClocks4.isEmpty()) {
                                                for (StaOrder staOrder : staOrders4) {
                                                    if (WxlConvertUtils.isNotEmpty(staOrder.getHourlySalary())) {
                                                        List<StaLocationClock> clockCollect = staLocationClockStream.stream().filter(data -> data.getStaOrderId().equals(staOrder.getId())).collect(Collectors.toList());
                                                        //时数计算   金额计算
                                                        //拆分签到集合 一个任务单对应Map一条数据集
                                                        Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
                                                        for (StaLocationClock staLocationClock : clockCollect) {
                                                            String staOrderId = staLocationClock.getStaOrderId();
                                                            if (!locationClocksMap.containsKey(staOrderId)) {
                                                                locationClocksMap.put(staOrderId, new ArrayList<>());
                                                            }
                                                            locationClocksMap.get(staOrderId).add(staLocationClock);
                                                        }
                                                        for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
                                                            List<StaLocationClock> value = entry.getValue();
                                                            //key签到日期，value签到数据
                                                            Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                                                                    .collect(Collectors.groupingBy(clock -> {
                                                                        Calendar cal = Calendar.getInstance();
                                                                        cal.setTime(clock.getTime());
                                                                        cal.set(Calendar.HOUR_OF_DAY, 0);
                                                                        cal.set(Calendar.MINUTE, 0);
                                                                        cal.set(Calendar.SECOND, 0);
                                                                        cal.set(Calendar.MILLISECOND, 0);
                                                                        return cal.getTime();
                                                                    }));
                                                            //升序排序
                                                            for (List<StaLocationClock> sublist : mapByDay.values()) {
                                                                sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                                                            }
                                                            //时数计算
                                                            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                                                                Date key = mapByDayEntry.getKey();
                                                                List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                                                                List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());
                                                                List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());
                                                                long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                                                                long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                                                                if (totalWorkDuration > shiftDuration) {
                                                                    totalWorkDuration = shiftDuration;
                                                                }
                                                                Long hours = totalWorkDuration / 60;//小时
                                                                Long remainingMinutes = totalWorkDuration % 60; //分钟
                                                                String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                                                                double workHours = Double.parseDouble(workHoursStr); //当天时数
                                                                summaryWorkingHours4 = summaryWorkingHours4 + workHours; //时数累加
                                                                BigDecimal money = staOrder.getHourlySalary();
                                                                BigDecimal bigDecimal = money.multiply(BigDecimal.valueOf(workHours)).setScale(2, RoundingMode.HALF_UP);
                                                                salarySummary4 = salarySummary4.add(bigDecimal);
                                                            }
                                                        }
                                                    }
                                                }
                                                childChildChild.setSummaryWorkingHours(summaryWorkingHours4);
                                                childChildChild.setSalarySummary(salarySummary4);
                                                summaryWorkingHours4 = 0;
                                                salarySummary4 = new BigDecimal(0);
                                                if (childChildChild.getChildren() != null) {
                                                    //可能没有了已经到店铺了
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                result.setResult(list);
            }
            result.setSuccess(true);
        } catch (Exception e) {
            System.err.println(e.getLocalizedMessage());
        }
        return result;
    }

    /**
     * 门店导出表头
     *
     * @return
     */
    private List<ExcelExportEntity> getStoreXlsHeader() {
        List<ExcelExportEntity> entityList = new ArrayList<>(); //表头
        ExcelExportEntity e1 = new ExcelExportEntity("事业处", "businessDivisionName");
        e1.setNeedMerge(true);
        e1.setWidth(100);
        ExcelExportEntity e2 = new ExcelExportEntity("区域", "storeRegionName");
        e2.setNeedMerge(true);
        e2.setWidth(25);
        ExcelExportEntity e3 = new ExcelExportEntity("店编", "storeNo");
        e3.setNeedMerge(true);
        e3.setWidth(20);
        ExcelExportEntity e4 = new ExcelExportEntity("店别", "companyName");
        e4.setNeedMerge(true);
        e4.setWidth(30);
        ExcelExportEntity e5 = new ExcelExportEntity("任务名称", "workNameFull");
        e5.setNeedMerge(true);
        e5.setWidth(20);
        ExcelExportEntity e6 = new ExcelExportEntity("姓名", "userName");
        e6.setNeedMerge(true);
        e6.setWidth(20);
        ExcelExportEntity e7 = new ExcelExportEntity("结算通道", "cardNumber");
        e7.setNeedMerge(true);
        e7.setWidth(20);
        ExcelExportEntity e8 = new ExcelExportEntity("时薪", "hourlySalary");
        e8.setNeedMerge(true);
        e8.setWidth(20);
        //workingHours 工资总额 应发总额 扣款总额 实发总额
        ExcelExportEntity e9 = new ExcelExportEntity("时数", "workingHours");
        e9.setWidth(20);
        ExcelExportEntity e10 = new ExcelExportEntity("工资总额", "totalSalary");
        e10.setWidth(20);
        ExcelExportEntity e11 = new ExcelExportEntity("应发总额", "payableAmount");
        e11.setWidth(20);
        ExcelExportEntity e12 = new ExcelExportEntity("扣款总额", "taskDeductionMoney");
        e12.setWidth(20);
        ExcelExportEntity e13 = new ExcelExportEntity("实发总额", "actualAmountPaidCount");
        e13.setWidth(20);
        entityList.addAll(Arrays.asList(e1, e2, e3, e4, e5, e6, e7, e8, e9, e10, e11, e12, e13));
        return entityList;
    }

    /**
     * 区域导出表头
     *
     * @return
     */
    private List<ExcelExportEntity> getRegionXlsHeader() {
        List<ExcelExportEntity> entityList = new ArrayList<>(); //表头
        ExcelExportEntity e1 = new ExcelExportEntity("事业处", "businessDivisionName");
        ExcelExportEntity e2 = new ExcelExportEntity("区域", "storeRegionName");
        ExcelExportEntity e3 = new ExcelExportEntity("店编", "storeNo");
        ExcelExportEntity e4 = new ExcelExportEntity("店别", "companyName");
        ExcelExportEntity e5 = new ExcelExportEntity("时数", "workingHours");
        ExcelExportEntity e6 = new ExcelExportEntity("工资总额", "totalSalary");
        ExcelExportEntity e7 = new ExcelExportEntity("应发总额", "payableAmount");
        ExcelExportEntity e8 = new ExcelExportEntity("扣款总额", "taskDeductionMoney");
        ExcelExportEntity e9 = new ExcelExportEntity("实发总额", "actualAmountPaidCount");
        entityList.addAll(Arrays.asList(e1, e2, e3, e4, e5, e6, e7, e8, e9));
        return entityList;
    }

    /**
     * 事业处表头
     * @return
     */
    private List<ExcelExportEntity> getBusinessDepartmentXlsHeader() {
        List<ExcelExportEntity> entityList = new ArrayList<>(); //表头
        ExcelExportEntity e1 = new ExcelExportEntity("事业处", "businessDivisionName");
        ExcelExportEntity e2 = new ExcelExportEntity("区域", "storeRegionName");
        ExcelExportEntity e3 = new ExcelExportEntity("时数", "workingHours");
        ExcelExportEntity e4 = new ExcelExportEntity("工资总额", "totalSalary");
        ExcelExportEntity e5 = new ExcelExportEntity("应发总额", "payableAmount");
        ExcelExportEntity e6 = new ExcelExportEntity("扣款总额", "taskDeductionMoney");
        ExcelExportEntity e7 = new ExcelExportEntity("实发总额", "actualAmountPaidCount");
        entityList.addAll(Arrays.asList(e1, e2, e3, e4, e5, e6, e7));
        return entityList;
    }

    /**
     * 任务单添加扣款
     *
     * @return
     */
    public List<StaOrder> orderAddDeductMoney(List<StaOrder> staOrders, List<StaTaskDeduction> staTaskDeductions) {
        Map<String, List<StaTaskDeduction>> staTaskDeductiontMap = staTaskDeductions.stream().collect(Collectors.groupingBy(StaTaskDeduction::getStaOrderId));
        for (StaOrder staOrder : staOrders) {
            if (staTaskDeductiontMap.get(staOrder.getId()) != null) {
                List<StaTaskDeduction> taskDeductions = staTaskDeductiontMap.get(staOrder.getId());
                BigDecimal sb = new BigDecimal("0.0"); //任务单扣款总金额
                for (StaTaskDeduction taskDeduction : taskDeductions) {
                    if (taskDeduction.getLateDeduction() != null) {
                        sb = sb.add(taskDeduction.getLateDeduction()).setScale(1, RoundingMode.HALF_UP);
                    }
                    if (taskDeduction.getEarlyLeaveDeduction() != null) {
                        sb = sb.add(taskDeduction.getEarlyLeaveDeduction()).setScale(1, RoundingMode.HALF_UP);
                    }
                    if (taskDeduction.getOtherDeduction() != null) {
                        sb = sb.add(taskDeduction.getOtherDeduction()).setScale(1, RoundingMode.HALF_UP);
                    }
                }
                staOrder.setStaTaskDeductionMoney(sb);
            }
        }
        return staOrders;
    }

    /**
     * 门店导出数据
     *
     * @param ordersGroupedByCompany
     * @param staUserBankCards
     * @return
     */
    private Map<String, Object> exportXlsStore(Map<String, List<StaOrder>> ordersGroupedByCompany, List<StaUserBankCards> staUserBankCards) {
        List<ExcelExportEntity> entityList = this.getStoreXlsHeader(); //表头
        List<Map<String, Object>> exportData = new ArrayList<>();//表数据
        //填充导出数据
        for (Map.Entry<String, List<StaOrder>> entry : ordersGroupedByCompany.entrySet()) {
            List<StaOrder> staOrderList = entry.getValue();
            for (StaOrder staOrder : staOrderList) {
                Map<String, Object> data = new HashMap<>();
                String staOrderBusinessDivisionName = staOrder.getBusinessDivisionName(); //事业处名称
                String staOrderRegionName = staOrder.getRegionName();//区域
                String staOrderStoNe = staOrder.getStoreNo();//店编
                String staOrderCompanyName = staOrder.getCompanyName();//店别
                String staOrderWorkNameFull = staOrder.getWorkNameFull();//任务名称
                //String staOrderIdCard = staOrder.getIdCard();//身份证
                String staOrderEnrollName = staOrder.getEnrollName();//姓名
                if (WxlConvertUtils.isNotEmpty(staUserBankCards)) {
                    List<String> collect = staUserBankCards //卡号
                            .stream()
                            .filter(staUserBankCards1 -> staUserBankCards1.getUserId().equals(staOrder.getSysUserId()))
                            .map(StaUserBankCards::getIdCard)
                            .collect(Collectors.toList());
                    data.put("cardNumber", !collect.isEmpty() ? collect.get(0) : "未绑定结算通道");
                }
                BigDecimal staOrderHourlySalary = staOrder.getHourlySalary(); //时薪
                Double staOrderWorkingHours = staOrder.getWorkingHours();//时数
                BigDecimal staOrderTotalSalary = staOrder.getTotalSalary();//工资金额
                // BigDecimal staOrderTotalSalary2 = staOrder.getTotalSalary();//应发金额
                BigDecimal staOrderStaTaskDeductionMoney = staOrder.getStaTaskDeductionMoney(); //扣款金
                BigDecimal taskAmount =  new BigDecimal("0.0");
                if (staOrderTotalSalary != null) {
                    taskAmount.add(staOrderTotalSalary).setScale(1, RoundingMode.HALF_UP);
                }
                if (staOrderStaTaskDeductionMoney != null) {
                    taskAmount = taskAmount.subtract(staOrderStaTaskDeductionMoney).setScale(1, RoundingMode.HALF_UP);
                }
                data.put("businessDivisionName", staOrderBusinessDivisionName);
                data.put("storeRegionName", staOrderRegionName);
                data.put("storeNo", staOrderStoNe);
                data.put("companyName", staOrderCompanyName);
                data.put("workNameFull", staOrderWorkNameFull);
                data.put("userName", staOrderEnrollName);
                data.put("hourlySalary", WxlConvertUtils.isNotEmpty(staOrderHourlySalary) ? staOrderHourlySalary : 0);
                data.put("workingHours", WxlConvertUtils.isNotEmpty(staOrderWorkingHours) ? staOrderWorkingHours : 0);
                data.put("totalSalary", WxlConvertUtils.isNotEmpty(staOrderTotalSalary) ? staOrderTotalSalary : 0);
                data.put("payableAmount", WxlConvertUtils.isNotEmpty(staOrderTotalSalary) ? staOrderTotalSalary : 0);
                data.put("taskDeductionMoney", WxlConvertUtils.isNotEmpty(staOrderStaTaskDeductionMoney) ? staOrderStaTaskDeductionMoney : 0);
                data.put("actualAmountPaidCount", WxlConvertUtils.isNotEmpty(taskAmount) ? taskAmount : 0);
                exportData.add(data);
            }
        }
        Map<String, Object> model = new HashMap<>();
        model.put("entityList", entityList); //表头
        model.put("exportData", exportData);
        return model;
    }
    /**
     * 区域导出
     * @param ordersGroupedByCompany
     * @return
     */
    private Map<String, Object> exportXlsRegion(Map<String, List<StaOrder>> ordersGroupedByCompany,SysDepart sysDepart) {
        List<ExcelExportEntity> entityList = this.getRegionXlsHeader(); //表头
        List<Map<String, Object>> exportData = new ArrayList<>();//表数据
        List<SysDepart> sysDepartsStore = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().eq(SysDepart::getParentId, sysDepart.getId()));//区域下门店
        //key门店id value任务单集合
        HashMap<String, List<StaOrder>> storeTotalMaps = new HashMap<>();
        for (SysDepart depart : sysDepartsStore) {
            if (WxlConvertUtils.isNotEmpty(ordersGroupedByCompany.get(depart.getId()))) {
                storeTotalMaps.put(depart.getId(), ordersGroupedByCompany.get(depart.getId()));
            }
        }
        //TODO 导出空指针异常if (storeTotalMaps.isEmpty()) return null;
        for (Map.Entry<String, List<StaOrder>> entry : storeTotalMaps.entrySet()) {
            Map<String, Object> data = new HashMap<>();
            //String key = entry.getKey(); //门店ID
            List<StaOrder> staOrders = entry.getValue();//门店下任务单
            Double workingHoursCount = 0.0; //时数汇总
            BigDecimal totalSalaryCount = new BigDecimal("0.0");//工资金额合计
            BigDecimal payableAmountCount = new BigDecimal("0.0");//应发金额合计
            BigDecimal taskDeductionMoney = new BigDecimal("0.0");//任务单扣除金额
            BigDecimal actualAmountPaidCount = new BigDecimal("0.0");//实发 合计
            for (StaOrder staOrder : staOrders) {
                if (WxlConvertUtils.isNotEmpty(staOrder.getStaTaskDeductionMoney())) {//任务单扣款金额
                    taskDeductionMoney = taskDeductionMoney.add(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP);//扣款总额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours())) {
                    workingHoursCount = workingHoursCount + staOrder.getWorkingHours(); //合计时数累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())) {
                    totalSalaryCount = totalSalaryCount.add(staOrder.getTotalSalary()).setScale(1, RoundingMode.HALF_UP); //工资金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())) {
                    payableAmountCount = payableAmountCount.add(staOrder.getTotalSalary()).setScale(1, RoundingMode.HALF_UP);//应发金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())) {
                    actualAmountPaidCount = actualAmountPaidCount.add(staOrder.getTotalSalary()).setScale(1, RoundingMode.HALF_UP);//实发合计累加
                }
            }
            data.put("businessDivisionName", staOrders.get(0).getBusinessDivisionName());
            data.put("storeRegionName", staOrders.get(0).getRegionName());
            data.put("storeNo", staOrders.get(0).getStoreNo());
            data.put("companyName", staOrders.get(0).getCompanyName());
            data.put("workingHours",WxlConvertUtils.isNotEmpty(workingHoursCount) ? workingHoursCount : 0);
            data.put("totalSalary",totalSalaryCount != null ? totalSalaryCount : 0.0);
            data.put("payableAmount",payableAmountCount != null ? payableAmountCount : 0.0);
            data.put("taskDeductionMoney",taskDeductionMoney != null ? taskDeductionMoney : 0.0);
            if (taskDeductionMoney != null) {
                actualAmountPaidCount = actualAmountPaidCount.subtract(taskDeductionMoney).setScale(1,RoundingMode.HALF_UP);
            }
            data.put("actualAmountPaidCount",actualAmountPaidCount != null ? actualAmountPaidCount : 0.0);
            exportData.add(data);
        }
        Map<String, Object> model = new HashMap<>();
        model.put("entityList", entityList); //表头
        model.put("exportData", exportData);
        return model;
    }
    /**
     * 事业处表头
     * @param ordersGroupedByCompany
     * @param sysDepart
     * @return
     */
    private Map<String, Object>  exportXlsBusinessDepartment(Map<String, List<StaOrder>> ordersGroupedByCompany,SysDepart sysDepart) {
        List<ExcelExportEntity> entityList = this.getBusinessDepartmentXlsHeader(); //表头
        List<Map<String, Object>> exportData = new ArrayList<>();//表数据
        HashMap<String, List<StaOrder>> regionTotalMaps = new HashMap<>();
        List<SysDepart> sysDepartsRegion = sysDepartService.list(new QueryWrapper<SysDepart>().lambda().eq(SysDepart::getParentId, sysDepart.getId()));//事业处下区域
        List<String> sysDepartsRegionIds = sysDepartsRegion.stream().map(SysDepart::getId).collect(Collectors.toList());
        List<SysDepart> sysDepartsStore = sysDepartService.list(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getParentId, sysDepartsRegionIds));//门店
        for (SysDepart depart : sysDepartsStore) {
            if (WxlConvertUtils.isNotEmpty(ordersGroupedByCompany.get(depart.getId()))) {
                List<StaOrder> orders = ordersGroupedByCompany.get(depart.getId());
                regionTotalMaps.put(depart.getParentId(), orders);
            }
        }
       //TODO 导出空指针异常 if (regionTotalMaps.isEmpty()) return null;
        for (Map.Entry<String,List<StaOrder>> entry :ordersGroupedByCompany.entrySet()) {
            Map<String, Object> data = new HashMap<>();
            Double workingHoursCount = 0.0; //时数汇总
            BigDecimal totalSalaryCount = new BigDecimal("0.0");//工资金额合计
            BigDecimal payableAmountCount = new BigDecimal("0.0");//应发金额合计
            BigDecimal taskDeductionMoney = new BigDecimal("0.0");//任务单扣除金额
            BigDecimal actualAmountPaidCount = new BigDecimal("0.0");//实发 合计
            List<StaOrder> staOrders = entry.getValue();
            for (StaOrder staOrder : staOrders) {
                if (WxlConvertUtils.isNotEmpty(staOrder.getStaTaskDeductionMoney())) {
                    taskDeductionMoney = taskDeductionMoney.add(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP);//扣款总额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours())) {
                    workingHoursCount = workingHoursCount + staOrder.getWorkingHours(); //合计时数累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())) {
                    totalSalaryCount = totalSalaryCount.add(staOrder.getTotalSalary()).setScale(1, RoundingMode.HALF_UP); //工资金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())) {
                    payableAmountCount = payableAmountCount.add(staOrder.getTotalSalary()).setScale(1, RoundingMode.HALF_UP);//应发金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())) {
                    actualAmountPaidCount = actualAmountPaidCount.add(staOrder.getTotalSalary()).setScale(1, RoundingMode.HALF_UP);//实发合计累加
                }
            }
            data.put("businessDivisionName", staOrders.get(0).getBusinessDivisionName());
            data.put("storeRegionName", staOrders.get(0).getRegionName());
            data.put("workingHours",WxlConvertUtils.isNotEmpty(workingHoursCount) ? workingHoursCount : 0);
            data.put("totalSalary",totalSalaryCount != null ? totalSalaryCount : 0.0);
            data.put("payableAmount",payableAmountCount != null ? payableAmountCount : 0.0);
            data.put("taskDeductionMoney",taskDeductionMoney != null ? taskDeductionMoney : 0.0);
            if (taskDeductionMoney != null) {
                actualAmountPaidCount = actualAmountPaidCount.subtract(taskDeductionMoney).setScale(1,RoundingMode.HALF_UP);
            }
            data.put("actualAmountPaidCount",actualAmountPaidCount != null ? actualAmountPaidCount : 0.0);
            exportData.add(data);
        }
        Map<String, Object> model = new HashMap<>();
        model.put("entityList", entityList); //表头
        model.put("exportData", exportData); //表数据
        return model;
    }

    /**
     * 资金计算导出
     * @param businessDepartmentName 事业处
     * @param regionId  区域
     * @param companyId 门店
     * @param enrollName 姓名
     * @param idCard 身份证
     * @param rosterMonth 日期
     * @return ModelAndView
     * @throws ParseException
     */
    @RequestMapping("/exportXls")
    public ModelAndView exportXls(
            @RequestParam(name = "businessDepartmentName", required = false) String businessDepartmentName,
            @RequestParam(name = "regionId", required = false) String regionId,
            @RequestParam(name = "companyId", required = false) String companyId,
            @RequestParam(name = "enrollName", required = false) String enrollName,
            @RequestParam(name = "idCard", required = false) String idCard,
            @RequestParam(name = "rosterMonth", required = false) String rosterMonth
    ) throws ParseException {
        //LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
       // if (WxlConvertUtils.isEmpty(sysUser)) {
            //TODO 登录失效
        //}
        LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<StaOrder>();
        int flagStatus = 0;
        SysDepart sysDepart = new SysDepart();
        if (WxlConvertUtils.isNotEmpty(companyId)) {
            sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, companyId));//门店
            staOrderLambdaQueryWrapper.eq(StaOrder::getCompanyId, sysDepart.getId())
                    .notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_0, CommonConstant.ORDER_STATUS_6));
            flagStatus = 3;
        } else if (WxlConvertUtils.isNotEmpty(regionId)) {
            sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, regionId));//区域
            List<SysDepart> sysDeparts = sysDepartService.list(new LambdaQueryWrapper<SysDepart>()
                    .eq(SysDepart::getParentId, sysDepart.getId())
                    .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));
            if (sysDeparts.isEmpty()) return null; //区域下面无门店
            List<String> deptIds = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.toList());
            staOrderLambdaQueryWrapper.in(StaOrder::getCompanyId, deptIds);
            flagStatus = 2;
        } else if (WxlConvertUtils.isNotEmpty(businessDepartmentName)) {
            sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, businessDepartmentName));//事业处
            staOrderLambdaQueryWrapper.likeRight(StaOrder::getSysOrgCode, sysDepart.getOrgCode());
            flagStatus = 1;
        }
        String[] split = rosterMonth.split(",");
        String startTime = split[0], endTime = split[1];
        // 日期格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析日期字符串为LocalDate对象
        LocalDate startDate = LocalDate.parse(startTime, dateFormatter);
        LocalDate endDate = LocalDate.parse(endTime, dateFormatter);
        // 结束日期增加一天
        endDate = endDate.plusDays(1);
        Date stDate = Date.from(startDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant());
        Date enDate = Date.from(endDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant());
        //staOrderLambdaQueryWrapper.between(StaOrder::getEntryDate,stDate,enDate); //日期区间查询
        if (WxlConvertUtils.isNotEmpty(enrollName)) { //姓名查询
            staOrderLambdaQueryWrapper.like(StaOrder::getEnrollName, enrollName);
        }
        staOrderLambdaQueryWrapper.notIn(StaOrder::getStateFlag,
                Arrays.asList(
                        CommonConstant.ORDER_STATUS_0,
                        CommonConstant.ORDER_STATUS_1,
                        CommonConstant.ORDER_STATUS_2,
                        CommonConstant.ORDER_STATUS_6,
                        CommonConstant.ORDER_STATUS_9
                ));
        staOrderLambdaQueryWrapper.orderByDesc(StaOrder::getEntryDate); //任务单通过开始日期降序
        List<StaOrder> staOrders = staOrderService.list(staOrderLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(staOrders) || staOrders.isEmpty()) { //无数据
            return null;
        }
        //任务单
        Set<String> orderIds = new TreeSet<>();
        Set<String> userIds = new TreeSet<>();
        if (WxlConvertUtils.isNotEmpty(staOrders)) {
            orderIds = staOrders.stream().map(StaOrder::getId).collect(Collectors.toSet());
            userIds = staOrders.stream().map(StaOrder::getSysUserId).collect(Collectors.toSet());
        }
        //获取扣款信息
        List<StaTaskDeduction> staTaskDeductions = staTaskDeductionService.list(new QueryWrapper<StaTaskDeduction>().lambda().in(StaTaskDeduction::getStaOrderId, orderIds));
        if (WxlConvertUtils.isNotEmpty(staTaskDeductions)) {
            staOrders = this.orderAddDeductMoney(staOrders, staTaskDeductions);//任务单添加扣款信息
        }
        //用户结算账户
        List<StaUserBankCards> staUserBankCards = staUserBankCardsService.list(new QueryWrapper<StaUserBankCards>().lambda().in(StaUserBankCards::getUserId, userIds));
        //step3:获取所需部门人员打卡数据
        LambdaQueryWrapper<StaLocationClock> locationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        String[] split = rosterMonth.split(",");
//        String startTime = split[0], endTime = split[1];
        locationClockLambdaQueryWrapper.between(StaLocationClock::getTime, stDate, enDate);
        locationClockLambdaQueryWrapper.isNotNull(StaLocationClock::getTimeExpect); //没有排班的签到记录不做计算
        locationClockLambdaQueryWrapper.in(StaLocationClock::getStaOrderId, orderIds);
        List<StaLocationClock> staLocationClocks = staLocationClockService.list(locationClockLambdaQueryWrapper);
        //拆分打卡集合(key:任务单id、value:打卡数据)
        Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
        for (StaLocationClock staLocationClock : staLocationClocks) {
            String staOrderId = staLocationClock.getStaOrderId();
            if (!locationClocksMap.containsKey(staOrderId)) {
                locationClocksMap.put(staOrderId, new ArrayList<>());
            }
            locationClocksMap.get(staOrderId).add(staLocationClock);
        }
        Map<String, Double> workingHoursMap = new HashMap<>();//任务单时数
        //根据天拆分打卡(后续几天每天时数)
        for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
            Double workingHours = 0.0; //当前任务单时数
            String orderId = entry.getKey();
            List<StaLocationClock> value = entry.getValue();
            //key签到日期，value签到数据
            Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                    .collect(Collectors.groupingBy(clock -> {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(clock.getTime());
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        cal.set(Calendar.MINUTE, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MILLISECOND, 0);
                        return cal.getTime();
                    }));
            //升序排序
            for (List<StaLocationClock> sublist : mapByDay.values()) {
                sublist.sort(Comparator.comparing(StaLocationClock::getTime));
            }
            //时数计算
            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());
                List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());
                long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                if (totalWorkDuration > shiftDuration) {
                    totalWorkDuration = shiftDuration;
                }
                Long hours = totalWorkDuration / 60;//小时
                Long remainingMinutes = totalWorkDuration % 60; //分钟
                String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                double workHours = Double.parseDouble(workHoursStr); //当天时数
                if (workHours > 0) {
                    workingHours += workHours;
                }
            }
            workingHoursMap.put(orderId, workingHours);
        }
        //任务单补充 任务单时数、任务单金额
        for (StaOrder staOrder : staOrders) {
            String staOrderId = staOrder.getId();
            Double workingHours = workingHoursMap.get(staOrderId);
            staOrder.setWorkingHours(workingHours); //任务单时数
            if (WxlConvertUtils.isNotEmpty(staOrder.getHourlySalary()) && WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours())) { //薪资
                BigDecimal hourlySalaryBig = staOrder.getHourlySalary();
                BigDecimal workingHoursBig = new BigDecimal(workingHours);
                BigDecimal totalSalary = hourlySalaryBig.multiply(workingHoursBig);
                BigDecimal roundedSalary = totalSalary.setScale(1, RoundingMode.HALF_UP);
                staOrder.setSalary(roundedSalary); //任务单薪资（保留1位小数 后续四舍五入）
            }
        }
        //任务单门店分组(key : 门店id, value : 任务单)
        Map<String, List<StaOrder>> ordersGroupedByCompany = staOrders.stream()
                .collect(Collectors.groupingBy(StaOrder::getCompanyId));
        if (flagStatus == 3) {//门店导出
            Map<String, Object> model = this.exportXlsStore(ordersGroupedByCompany, staUserBankCards);
            return new ModelAndView(new StoreExcel(),model);
        } else if (flagStatus == 2) {//区域导出
            Map<String, Object> model = this.exportXlsRegion(ordersGroupedByCompany, sysDepart);
            return new ModelAndView(new RegionExcel(),model);
        }else if (flagStatus == 1) {//事业处导出
            Map<String, Object> model = this.exportXlsBusinessDepartment(ordersGroupedByCompany, sysDepart);
            return new ModelAndView(new BusinessDepartmentExcel(),model);
        }
        //TODO 请求参数错误
        return null;
    }
}
