package com.byun.modules.system.controller;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.excel.export.HealthyExcel;
import com.byun.modules.staffing.service.IStaEnrollInfoService;
import com.byun.modules.system.entity.StaUserHealthyId;
import com.byun.modules.system.service.IStaUserHealthyIdService;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 健康证
 * @date : 2023-8-25 9:27
 */
@RestController
@RequestMapping("/staffing/user/healthyId")
@SuppressWarnings("all")
public class StaUserHealthyIdController {
    @Autowired
    private IStaUserHealthyIdService staUserHealthyIdService;
    @Autowired
    private IStaEnrollInfoService staEnrollInfoService;

    /**
     * 新增健康证
     *
     * @param staUserHealthyId
     * @return Result
     */
    @PostMapping("/save")
    public Result addHealthyLicense(@RequestBody StaUserHealthyId staUserHealthyId) {
        if (!(staUserHealthyId.getType().equals(CommonConstant.CLIENT_TYPE_0)
                || staUserHealthyId.getType().equals(CommonConstant.CLIENT_TYPE_1)))
            return Result.error(500, "请求错误");
        if (WxlConvertUtils.isEmpty(staUserHealthyId.getEndDate())) return Result.error(500, "到期日期不能为空");
        if (WxlConvertUtils.isEmpty(staUserHealthyId.getAvatar())) return Result.error(500, "健康证不能为空");
        return staUserHealthyIdService.add(staUserHealthyId);
    }

    /**
     * TODO 待补充权限 获取列表
     *
     * @param pageNo
     * @param pageSize
     * @param phone
     * @param status
     * @return
     */
    @GetMapping("/list")
    public Result listHealthyLicense(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                     @RequestParam(name = "phone", required = false) String phone,
                                     @RequestParam(name = "userName", required = false) String userName,
                                     @RequestParam(name = "status", required = false) String status) {
        Page<StaUserHealthyId> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StaUserHealthyId::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.orderByDesc(StaUserHealthyId::getCreateTime);
        if (WxlConvertUtils.isNotEmpty(userName)) {
            lambdaQueryWrapper.like(StaUserHealthyId::getUserName, userName);
        }
        if (WxlConvertUtils.isNotEmpty(phone)) {
            lambdaQueryWrapper.eq(StaUserHealthyId::getPhone, phone);
        }
        if (WxlConvertUtils.isNotEmpty(status)) {
            lambdaQueryWrapper.eq(StaUserHealthyId::getStatus, status);
        }
        Page<StaUserHealthyId> pList = staUserHealthyIdService.page(page, lambdaQueryWrapper);
        return Result.OK(pList);
    }

    /**
     * 通过健康证审核
     *
     * @param staUserHealthyId
     * @return
     */
    @PutMapping(value = "/approved")
    public Result approved(@RequestBody StaUserHealthyId staUserHealthyId) {
        if (WxlConvertUtils.isEmpty(staUserHealthyId)) {
            return Result.error("参数丢失！");
        }
        staUserHealthyIdService.userIdentityApproved(staUserHealthyId);
        return Result.OK("操作成功", null);
    }

    /**
     * 健康证审批驳回
     *
     * @param data
     * @return
     */
    @PutMapping("/rejectApproval")
    public Result rejectApproval(@RequestBody JSONObject data) {
        String id = data.getString("id");
        String sysUserId = data.getString("sysUserId");
        String refuse = data.getString("refuse");
        if (id.isEmpty()) {
            return Result.error("id丢失！");
        }
        if (sysUserId.isEmpty()) {
            return Result.error("用户丢失！");
        }
        boolean flag = staUserHealthyIdService.rejectApproval(id, sysUserId, refuse);
        return flag ? Result.OK("操作成功", null) : Result.error(500, "操作失败");
    }

    /**
     * 根据用户ID获取用户健康证
     *
     * @param sysUserId
     * @return
     */
    @GetMapping("/getByUserId/{sysUserId}")
    public Result<StaUserHealthyId> getHealthyLicenseByUserId(@PathVariable("sysUserId") String sysUserId) {
        //0审核中 1 正常 2 无效 3 已过期
        LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StaUserHealthyId::getSysUserId, sysUserId);
        lambdaQueryWrapper.in(StaUserHealthyId::getDelFlag, CommonConstant.DEL_FLAG_0);
        StaUserHealthyId staUserHealthyId = staUserHealthyIdService.getOne(lambdaQueryWrapper);
        //无健康证 返回null
        return Result.OK(staUserHealthyId);
    }

    //导出Execl
    @RequestMapping("/exportXls")
    public ModelAndView exportXls(HttpServletRequest request,
                                  @RequestParam(name = "phone", required = false) String phone,
                                  @RequestParam(name = "userName", required = false) String userName,
                                  @RequestParam(name = "status", required = false) String status) {
        List<ExcelExportEntity> entityList = new ArrayList<>(); //表头
        ExcelExportEntity e1 = new ExcelExportEntity("创建日期", "createTime");
        ExcelExportEntity e2 = new ExcelExportEntity("姓名", "userName");
        ExcelExportEntity e3 = new ExcelExportEntity("身份证", "idCard");
        ExcelExportEntity e4 = new ExcelExportEntity("联系方式", "phone");
        ExcelExportEntity e5 = new ExcelExportEntity("健康证", "avatar");
        ExcelExportEntity e6 = new ExcelExportEntity("有效日期", "endDate");
        ExcelExportEntity e7 = new ExcelExportEntity("状态", "status");
        entityList.addAll(Arrays.asList(e1, e2, e3, e4, e5, e6, e7));
        List<Map<String, Object>> exportData = new ArrayList<>();//表数据
        LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StaUserHealthyId::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.orderByDesc(StaUserHealthyId::getCreateTime);
        if (WxlConvertUtils.isNotEmpty(userName)) {
            lambdaQueryWrapper.like(StaUserHealthyId::getUserName, userName);
        }
        if (WxlConvertUtils.isNotEmpty(phone)) {
            lambdaQueryWrapper.eq(StaUserHealthyId::getPhone, phone);
        }
        if (WxlConvertUtils.isNotEmpty(status)) {
            lambdaQueryWrapper.eq(StaUserHealthyId::getStatus, status);
        }
        exportData = staUserHealthyIdService.getExoortData(lambdaQueryWrapper);
        Map<String, Object> model = new HashMap<>();
        model.put("entityList", entityList);
        model.put("exportData", exportData);
        return new ModelAndView(new HealthyExcel(),model);
//        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
//        mv.addObject(NormalExcelConstants.FILE_NAME, "健康证");
//        ExportParams exportParams = new ExportParams("健康证", "导出人:" + sysUser.getRealname(), "sheet1", "opt/staffing/upload");
//        //exportParams.setStyle(CustomExcelExportStyler.class);
//        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
//        mv.addObject(NormalExcelConstants.MAP_LIST, exportData);
//        mv.addObject(NormalExcelConstants.DATA_LIST, entityList);
}
    @DeleteMapping("/del")
    public Result delById(@RequestParam("id") String id) {
        staUserHealthyIdService.removeById(id);
        return Result.OK("删除成功");
    }
}
