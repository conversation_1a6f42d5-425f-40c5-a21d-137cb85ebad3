package com.byun.modules.system.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.byun.common.system.base.controller.ByunExcelController;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.system.entity.SysApplet;
import com.byun.modules.system.service.ISysAppletService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 小程序
 * @Author: bai
 * @Date:   2021-11-28
 * @Version: V1.0
 */
@Api(tags="小程序")
@RestController
@RequestMapping("/system/sysApplet")
@Slf4j
public class SysAppletController extends ByunExcelController<SysApplet, ISysAppletService> {
	@Autowired
	private ISysAppletService sysAppletService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysApplet
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "小程序-分页列表查询")
	@ApiOperation(value="小程序-分页列表查询", notes="小程序-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SysApplet sysApplet,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysApplet> queryWrapper = QueryGenerator.initQueryWrapper(sysApplet, req.getParameterMap());
		Page<SysApplet> page = new Page<SysApplet>(pageNo, pageSize);
		IPage<SysApplet> pageList = sysAppletService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysApplet
	 * @return
	 */
	@AutoLog(value = "小程序-添加")
	@ApiOperation(value="小程序-添加", notes="小程序-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SysApplet sysApplet) {
		sysAppletService.save(sysApplet);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysApplet
	 * @return
	 */
	@AutoLog(value = "小程序-编辑")
	@ApiOperation(value="小程序-编辑", notes="小程序-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SysApplet sysApplet) {
		sysAppletService.updateById(sysApplet);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "小程序-通过id删除")
	@ApiOperation(value="小程序-通过id删除", notes="小程序-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		sysAppletService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "小程序-批量删除")
	@ApiOperation(value="小程序-批量删除", notes="小程序-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysAppletService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "小程序-通过id查询")
	@ApiOperation(value="小程序-通过id查询", notes="小程序-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SysApplet sysApplet = sysAppletService.getById(id);
		if(sysApplet==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysApplet);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysApplet
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysApplet sysApplet) {
        return super.exportXls(request, sysApplet, SysApplet.class, "小程序");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysApplet.class);
    }

}
