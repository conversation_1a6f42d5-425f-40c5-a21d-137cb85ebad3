package com.byun.modules.system.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.system.entity.SysUserAdmin;
import com.byun.modules.system.service.ISysUserAdminService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 管理员信息
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="管理员信息")
@RestController
@RequestMapping("/staffing/sysUserAdmin")
@Slf4j
public class SysUserAdminController extends ByunExcelController<SysUserAdmin, ISysUserAdminService> {
	@Autowired
	private ISysUserAdminService sysUserAdminService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysUserAdmin
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "管理员信息-分页列表查询")
	@ApiOperation(value="管理员信息-分页列表查询", notes="管理员信息-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SysUserAdmin sysUserAdmin,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysUserAdmin> queryWrapper = QueryGenerator.initQueryWrapper(sysUserAdmin, req.getParameterMap());
		Page<SysUserAdmin> page = new Page<SysUserAdmin>(pageNo, pageSize);
		IPage<SysUserAdmin> pageList = sysUserAdminService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysUserAdmin
	 * @return
	 */
	@AutoLog(value = "管理员信息-添加")
	@ApiOperation(value="管理员信息-添加", notes="管理员信息-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SysUserAdmin sysUserAdmin) {
		sysUserAdminService.save(sysUserAdmin);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysUserAdmin
	 * @return
	 */
	@AutoLog(value = "管理员信息-编辑")
	@ApiOperation(value="管理员信息-编辑", notes="管理员信息-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SysUserAdmin sysUserAdmin) {
		sysUserAdminService.updateById(sysUserAdmin);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "管理员信息-通过id删除")
	@ApiOperation(value="管理员信息-通过id删除", notes="管理员信息-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		sysUserAdminService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "管理员信息-批量删除")
	@ApiOperation(value="管理员信息-批量删除", notes="管理员信息-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysUserAdminService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "管理员信息-通过id查询")
	@ApiOperation(value="管理员信息-通过id查询", notes="管理员信息-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SysUserAdmin sysUserAdmin = sysUserAdminService.getById(id);
		if(sysUserAdmin==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysUserAdmin);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysUserAdmin
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysUserAdmin sysUserAdmin) {
        return super.exportXls(request, sysUserAdmin, SysUserAdmin.class, "管理员信息");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysUserAdmin.class);
    }

}
