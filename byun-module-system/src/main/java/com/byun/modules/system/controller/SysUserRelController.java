package com.byun.modules.system.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.byun.common.api.vo.Result;
import com.byun.common.system.query.QueryGenerator;
import com.byun.modules.system.entity.SysUserRel;
import com.byun.modules.system.service.ISysUserRelService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.byun.common.system.base.controller.ByunExcelController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.byun.common.aspect.annotation.AutoLog;

 /**
 * @Description: 用户关系表
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Api(tags="用户关系表")
@RestController
@RequestMapping("/staffing/sysUserRel")
@Slf4j
public class SysUserRelController extends ByunExcelController<SysUserRel, ISysUserRelService> {
	@Autowired
	private ISysUserRelService sysUserRelService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysUserRel
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户关系表-分页列表查询")
	@ApiOperation(value="用户关系表-分页列表查询", notes="用户关系表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SysUserRel sysUserRel,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysUserRel> queryWrapper = QueryGenerator.initQueryWrapper(sysUserRel, req.getParameterMap());
		Page<SysUserRel> page = new Page<SysUserRel>(pageNo, pageSize);
		IPage<SysUserRel> pageList = sysUserRelService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysUserRel
	 * @return
	 */
	@AutoLog(value = "用户关系表-添加")
	@ApiOperation(value="用户关系表-添加", notes="用户关系表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SysUserRel sysUserRel) {
		sysUserRelService.save(sysUserRel);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysUserRel
	 * @return
	 */
	@AutoLog(value = "用户关系表-编辑")
	@ApiOperation(value="用户关系表-编辑", notes="用户关系表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SysUserRel sysUserRel) {
		sysUserRelService.updateById(sysUserRel);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户关系表-通过id删除")
	@ApiOperation(value="用户关系表-通过id删除", notes="用户关系表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		sysUserRelService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户关系表-批量删除")
	@ApiOperation(value="用户关系表-批量删除", notes="用户关系表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysUserRelService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户关系表-通过id查询")
	@ApiOperation(value="用户关系表-通过id查询", notes="用户关系表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SysUserRel sysUserRel = sysUserRelService.getById(id);
		if(sysUserRel==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysUserRel);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysUserRel
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysUserRel sysUserRel) {
        return super.exportXls(request, sysUserRel, SysUserRel.class, "用户关系表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysUserRel.class);
    }

}
