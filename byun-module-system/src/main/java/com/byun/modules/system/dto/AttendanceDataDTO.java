package com.byun.modules.system.dto;

import com.byun.modules.staffing.entity.StaLocationClock;
import com.byun.modules.staffing.entity.StaSchedule;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤数据DTO - 封装排班和打卡数据
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDataDTO {
    
    /**
     * 排班数据列表
     */
    private List<StaSchedule> scheduleList;
    
    /**
     * 打卡数据列表
     */
    private List<StaLocationClock> clockList;
    
    /**
     * 按任务单ID分组的排班数据缓存
     */
    private Map<String, List<StaSchedule>> scheduleByOrderId;
    
    /**
     * 按任务单ID分组的打卡数据缓存
     */
    private Map<String, List<StaLocationClock>> clockByOrderId;
    
    /**
     * 构造函数，自动初始化分组缓存
     */
    public AttendanceDataDTO(List<StaSchedule> scheduleList, List<StaLocationClock> clockList) {
        this.scheduleList = scheduleList != null ? scheduleList : new ArrayList<>();
        this.clockList = clockList != null ? clockList : new ArrayList<>();
        initializeGroupedData();
    }
    
    /**
     * 初始化分组数据缓存
     */
    private void initializeGroupedData() {
        this.scheduleByOrderId = scheduleList.stream()
                .collect(Collectors.groupingBy(StaSchedule::getStaOrderId));
        
        this.clockByOrderId = clockList.stream()
                .collect(Collectors.groupingBy(StaLocationClock::getStaOrderId));
    }
    
    /**
     * 检查数据是否为空
     */
    public boolean isEmpty() {
        return scheduleList.isEmpty() || clockList.isEmpty();
    }
    
    /**
     * 获取所有任务单ID
     */
    public Set<String> getAllOrderIds() {
        return scheduleByOrderId.keySet();
    }
    
    /**
     * 根据任务单ID获取排班数据
     */
    public List<StaSchedule> getSchedulesByOrderId(String orderId) {
        return scheduleByOrderId.getOrDefault(orderId, new ArrayList<>());
    }
    
    /**
     * 根据任务单ID获取打卡数据
     */
    public List<StaLocationClock> getClocksByOrderId(String orderId) {
        return clockByOrderId.getOrDefault(orderId, new ArrayList<>());
    }
    
    /**
     * 获取数据统计信息
     */
    public String getDataSummary() {
        return String.format("排班记录: %d条, 打卡记录: %d条, 任务单数: %d个", 
                scheduleList.size(), clockList.size(), scheduleByOrderId.size());
    }
}
