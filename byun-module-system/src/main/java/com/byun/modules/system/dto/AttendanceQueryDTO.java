package com.byun.modules.system.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 考勤查询参数DTO
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceQueryDTO {
    
    /**
     * 选择的日期（格式：yyyy-MM-dd）
     */
    private String selectedDate;
    
    /**
     * 选择的月份（格式：yyyy-MM）
     */
    private String selectedMonth;
    
    /**
     * 组织代码
     */
    private String orgCode;
    
    /**
     * 是否查询日期数据
     */
    public boolean isDateQuery() {
        return selectedDate != null && !selectedDate.trim().isEmpty();
    }
    
    /**
     * 是否查询月份数据
     */
    public boolean isMonthQuery() {
        return selectedMonth != null && !selectedMonth.trim().isEmpty();
    }
    
    /**
     * 获取查询时间参数（优先返回日期，其次月份）
     */
    public String getQueryTimeParam() {
        if (isDateQuery()) {
            return selectedDate;
        }
        if (isMonthQuery()) {
            return selectedMonth;
        }
        return null;
    }
}
