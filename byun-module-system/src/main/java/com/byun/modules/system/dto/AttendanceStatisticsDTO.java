package com.byun.modules.system.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 考勤统计数据DTO
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@NoArgsConstructor
public class AttendanceStatisticsDTO {
    
    /**
     * 正常考勤次数
     */
    private int normalCount = 0;
    
    /**
     * 迟到次数
     */
    private int lateCount = 0;
    
    /**
     * 早退次数
     */
    private int earlyLeaveCount = 0;
    
    /**
     * 缺卡次数
     */
    private int missingCardCount = 0;
    
    /**
     * 考勤状态常量
     */
    public static final String STATUS_NORMAL = "正常";
    public static final String STATUS_LATE = "迟到";
    public static final String STATUS_EARLY_LEAVE = "早退";
    public static final String STATUS_MISSING_CARD = "缺卡";
    
    /**
     * 根据状态增加对应的计数
     * 
     * @param status 考勤状态
     */
    public void incrementCount(String status) {
        if (status == null) {
            missingCardCount++;
            return;
        }
        
        switch (status) {
            case STATUS_NORMAL:
                normalCount++;
                break;
            case STATUS_LATE:
                lateCount++;
                break;
            case STATUS_EARLY_LEAVE:
                earlyLeaveCount++;
                break;
            case STATUS_MISSING_CARD:
                missingCardCount++;
                break;
            default:
                // 未知状态，记录为缺卡
                missingCardCount++;
                break;
        }
    }
    
    /**
     * 获取总计数
     */
    public int getTotalCount() {
        return normalCount + lateCount + earlyLeaveCount + missingCardCount;
    }
    
    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return getTotalCount() > 0;
    }
    
    /**
     * 获取统计摘要信息
     */
    public String getSummary() {
        return String.format("总计: %d, 正常: %d, 迟到: %d, 早退: %d, 缺卡: %d", 
                getTotalCount(), normalCount, lateCount, earlyLeaveCount, missingCardCount);
    }
    
    /**
     * 重置所有计数
     */
    public void reset() {
        normalCount = 0;
        lateCount = 0;
        earlyLeaveCount = 0;
        missingCardCount = 0;
    }
}
