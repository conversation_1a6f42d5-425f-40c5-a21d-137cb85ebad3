package com.byun.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 用户银行卡
 * @date : 2023-3-30 9:26
 */
@Data
@TableName("sta_user_bank_cards")
public class StaUserBankCards {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 持卡人姓名
     */
    private String cardHolderName;
    /**
     * 账户类型1银行卡 2支付宝 3微信
     */
    private int accountType;
    /**
     * 卡类型
     */
    private String bankType;
    /**
     * 银行卡logo
     */
    private String bankLogo;
    /**
     * 卡所在地区
     */
    private String area;
    /**
     * 卡名称
     */
    private String cardName;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 开户行
     */
    private String bankName;
    /**
     * 添加时间
     */
    private Date createdAt;
    /**
     * 修改时间
     */
    private Date updatedAt;
    @TableField(exist = false)
    private String currentStores;
    @TableField(exist = false)
    private Integer hourlySalary;

}
