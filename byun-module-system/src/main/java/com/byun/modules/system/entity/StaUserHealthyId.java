package com.byun.modules.system.entity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 用户健康证
 * @date : 2023-7-25 14:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true) //链式调用
@TableName(value = "sta_user_healthy_id")
public class StaUserHealthyId {
/**
 * id	varchar	32	0	0           主键
 * sys_user_id	varchar	32			用户ID
 * start_date   dataTime            体检日期
 * end_date     dataTime            健康证到期日期
 * subject_img	varchar	255			健康证照片
 * status	tinyint	0	1			是否有效
 * del_flag	tinyint	0	1			删除状态
 * phone	varchar	11	0			用户电话
 * user_name	varchar	10			姓名
 * id_card	varchar	18	0			身份证号码
 */
private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String sysUserId;
    //体检日期
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date startDate;
    //到期日期
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endDate;
    private String avatar; //健康证
    private Integer status;
    //状态字符串
    @TableField(exist = false)
    private String  statusS;
    @TableLogic(value = "0", delval = "1")
    @TableField(value = "del_flag", fill = FieldFill.INSERT)
    private Integer delFlag;
    private String phone;
    private String userName;
    private String idCard;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    //驳回原描述
    private String description;
    @TableField(exist = false)
    private String type;
}
