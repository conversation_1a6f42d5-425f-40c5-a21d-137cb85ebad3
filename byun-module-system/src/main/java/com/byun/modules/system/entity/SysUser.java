package com.byun.modules.system.entity;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import com.byun.modules.staffing.entity.StaOrder;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.byun.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModelProperty;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
 * <p>
 * 用户表
 * </p>
 * <AUTHOR>
 * @since 2018-12-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true) //链式调用
public class SysUser implements Serializable {
    private static final long serialVersionUID = 1L;
    //@Excel(name = "店别",width = 20)

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 登录账号
     */
    @Excel(name = "登录账号", width = 15)
    private String username;
    /**
     * 实名认证真实姓名
     */
    @Excel(name = "真实姓名", width = 15)
    private String realNameName;
    /**
     * 身份证号码
     */
    @Excel(name = "身份证", width = 30)
    private String idCard;
    @Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;
    private String openId;
    /**
     * 真实姓名
     */
    private String realname;
    //@Excel(name = "微信号", width = 15)
    /**
     * 微信号
     */
    private String wechatId;
    /**
     * 密码
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String password;
    /**
     * md5密码盐
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String salt;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 电子邮件
     */
    private String email;
    /**
     * 电话
     */
    @Excel(name = "电话", width = 15)
    private String phone;
    /**
     * 状态(1：正常  2：冻结 ）
     */
    //@Excel(name = "状态", width = 15,dicCode="user_status")
    @Dict(dicCode = "user_status")
    private Integer status;
    /**
     * 删除状态（0，正常，1已删除）
     */
    //@Excel(name = "删除状态", width = 15,dicCode="del_flag")
    @TableLogic
    private Integer delFlag;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 同步任务流引擎1同步0不同步
     */
    private Integer activitiSync;
    /**
     * 身份（0 普通成员 1 上级）
     */
    //@Excel(name="（1普通成员 2上级）",width = 15)
    private Integer userIdentity;
    /**
     * 多租户id配置，编辑用户的时候设置
     */
    private String relTenantIds;
    /**设备id uniapp推送用*/
    private String clientId;
    /**绑定的招聘官*/
    private String bindUserId;
    /**绑定的推广者*/
    private String promoterId;
    /**
     * 部门code(当前选择登录部门)
     */
    private String orgCode;
    /**部门名称*/
    private transient String orgCodeTxt;
    /**
     * 负责部门
     */
    //@Excel(name="负责部门",width = 15,dictTable ="sys_depart",dicText = "depart_name",dicCode = "id")
    @Dict(dictTable ="sys_depart",dicText = "depart_name",dicCode = "id")
    private String departIds;
    /**
     * 工号，唯一键
     */
    //@Excel(name = "工号", width = 15)
    private String workNo;
    /**
     * 职务，关联职务表
     */
    //@Excel(name = "职务", width = 15)
    @Dict(dictTable ="sys_position",dicText = "name",dicCode = "code")
    private String post;
    /**
     * 座机号
     */
    //@Excel(name = "座机号", width = 15)
    private String telephone;
    /**
     * 登录首页地址
     */
    @TableField(exist = false)
    private String homePath;
    /**
     * code 临时验证码
     */
    @TableField(exist = false)
    private String code;
    /**
     * 身份证有效期始
     */
    private String startDate;
    /**
     * 身份证有效期末
     */
    private String endDate;
    /**
     * 出生日期 --
     */

    /**
     * 民族
     */
    private String nationality;
    /**
     * 性别（1：男 2：女）
     */
    @Excel(name = "性别", width = 15,dicCode="sex")
    @Dict(dicCode = "sex")
    private Integer sex;
    /**
     * 身份证正面
     */
    private String frontUrl;
    /**
     * 身份证反面
     */
    private String reverseUrl;
    /**
     * 身份证号码
     */
    //private String idCard;
    /**
     * 是否实名认证
     */
    private Integer   isRealName;
    /**
     * 实名认证时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realNameTime;

    /**
     * 身份证住址
     */
    private String idCardAddress;
    /**
     * 身份证签发机关
     */
    private String issue;
    /**
     * 是否绑定银行卡  0未绑定  1 已绑定
     */
    private String isCard;
    @TableField(exist = false)
    private int accountType;
    /**
     * 是否加入黑名单
     */
    private String blacklisted;
    /**
     * 黑名单类型 开除、劝退、内盗、劳动争议 其他
     */
    @TableField(value = "blacklist_type", fill = FieldFill.UPDATE)
    private String blacklistType;
    /**
     * 加入黑名单时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date blacklistedTime;
    /**
     * 是否签约协议 0  1
     */
    private  Integer agencyStatus;
    /**
     * 协议地址
     */
    private String agencyPdfUrl;
    /**
     * 公司Id
     */
    private String deptId;
    /**
     * 年龄
     */
    @TableField(exist = false)
    private int age;
    /**
     * 公司名称
     */
    @TableField(exist = false)
    private String firmName;
    /**
     * 健康证(0-Y有效  1-N无效  2-NN过期)
     */
    private Integer healthCardStatus;
    /**
     * 当前工作门店id
     */
    private String currentStoresId;
    /**
     * 当前工作门店
     */
    private String currentStores;
    /**
     * 当前工作名称
     */
    private String currentWorkName;
    /**
     * 当前工作id
     */
    private String staOrderId;

    @ApiModelProperty(value = "学校名称")
    @Excel(name = "学校名称", width = 15)
    private String school;
    @ApiModelProperty(value = "文化程度")
    private String degree;
    @ApiModelProperty(value = "专业")
    @Excel(name = "专业", width = 15)
    private String major;
    @ApiModelProperty(value = "入学日期")
    @JsonFormat(pattern = "yyyy-MM")
    private Date enrollmentTime;
    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty(value = "毕业日期")
    private Date graduationTime;
    /**
     * 开户行
     */
    @TableField(exist = false)
    private String bankName;
    /**
     * 卡号
     */
    @TableField(exist = false)
    private String cardNumber;
    /**
     * 小时薪资
     */
    private Integer hourlySalary;
    /**
     * 被推广人集合
     */
    @TableField(exist = false)
    private List<String> promoters;
    /**
     * 
     * 推广数量
     */
    @TableField(exist = false)
    private Integer promotersNum;
    /**
     * 任务记录
     */
    @TableField(exist = false)
    private List<StaOrder> staOrders;
    @TableField(exist = false)
    private double statisticalWorkingHours;
    /**
     * 任务总数
     */
    @TableField(exist = false)
    private int taskTotal;
    @TableField(exist = false)
    private int companyServiceDays;
    //签约编号
    private String singNo;
}
