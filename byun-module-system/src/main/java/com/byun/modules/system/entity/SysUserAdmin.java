package com.byun.modules.system.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import com.byun.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 管理员信息
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@TableName("sys_user_admin")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_user_admin对象", description="管理员信息")
public class SysUserAdmin implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**
     * 部门code(当前选择登录部门)
     */
	@Excel(name = "机构编码", width = 15)
    @ApiModelProperty(value = "机构编码")
    private java.lang.String orgCode;
    /**部门名称*/
    private transient String orgCodeTxt;
	/**状态(1-正常,2-冻结)*/
	@Excel(name = "状态(1-正常,2-冻结)", width = 15)
    @ApiModelProperty(value = "状态(1-正常,2-冻结)")
    private java.lang.Integer statusFlag;
	/**删除状态(0-正常,1-已删除)*/
	@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @ApiModelProperty(value = "删除状态(0-正常,1-已删除)")
    private java.lang.Integer delFlag;
    /**
     * 工号，唯一键（暂时先不唯一）
     */
	@Excel(name = "工号", width = 15)
    @ApiModelProperty(value = "工号")
    private java.lang.String workNo;
	/**职务，关联职务表*/
	@Excel(name = "职务，关联职务表", width = 15)
    @ApiModelProperty(value = "职务，关联职务表")
    @Dict(dictTable ="sys_position",dicText = "name",dicCode = "code")
    private java.lang.String post;
	/**负责部门*/
    @Excel(name="负责部门",width = 15,dictTable ="sys_depart",dicText = "depart_name",dicCode = "id")
    @Dict(dictTable ="sys_depart",dicText = "depart_name",dicCode = "id")
    @ApiModelProperty(value = "负责部门")
    private java.lang.String departIds;
	/**用户信息*/
	@Excel(name = "用户信息", width = 15)
    @ApiModelProperty(value = "用户信息")
    private java.lang.String sysUserId;
    /**
     * 座机号
     */
    @Excel(name = "座机号", width = 15)
    @ApiModelProperty(value = "座机号")
    private String telephone;
}
