package com.byun.modules.system.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户关系表
 * @Author: baiyun
 * @Date:   2021-11-14
 * @Version: V1.0
 */
@Data
@TableName("sys_user_rel")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_user_rel对象", description="用户关系表")
public class SysUserRel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**自己sys_user_id*/
	@Excel(name = "自己sys_user_id", width = 15)
    @ApiModelProperty(value = "自己sys_user_id")
    private java.lang.String meId;
	/**对象sys_user_id*/
	@Excel(name = "对象sys_user_id", width = 15)
    @ApiModelProperty(value = "对象sys_user_id")
    private java.lang.String youId;
	/**关系类型1、用工第一推广人2用工存有对象信息*/
	@Excel(name = "关系类型1、用工第一推广人2用工存有对象信息", width = 15)
    @ApiModelProperty(value = "关系类型1、用工第一推广人2用工存有对象信息")
    private java.lang.Integer relType;
	/**状态(1-正常,2-冻结)*/
	@Excel(name = "状态(1-正常,2-冻结)", width = 15)
    @ApiModelProperty(value = "状态(1-正常,2-冻结)")
    private java.lang.Integer delFlag;
    /**备用id*/
    @Excel(name = "备用id", width = 15)
    @ApiModelProperty(value = "备用id")
    private java.lang.String spareId;
    private String departId;
}
