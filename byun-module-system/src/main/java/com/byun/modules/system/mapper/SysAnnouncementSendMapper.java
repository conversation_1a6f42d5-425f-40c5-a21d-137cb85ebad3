package com.byun.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.byun.modules.system.entity.SysAnnouncementSend;
import com.byun.modules.system.model.AnnouncementSendModel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 用户通告阅读标记表
 * @Author: baiyun
 * @Date:  2019-02-21
 * @Version: V1.0
 */
public interface SysAnnouncementSendMapper extends BaseMapper<SysAnnouncementSend> {

	public List<String> queryByUserId(@Param("userId") String userId);

	/**
	 * @description: 获取我的消息
	 * @param page	
	 * @param announcementSendModel
	 * @return: java.util.List<com.byun.modules.system.model.AnnouncementSendModel>
	 * <AUTHOR>
	 * @date: 2021/11/8 0:59
	 */
	public List<AnnouncementSendModel> getMyAnnouncementSendList(Page<AnnouncementSendModel> page,@Param("announcementSendModel") AnnouncementSendModel announcementSendModel);

}
