package com.byun.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.byun.modules.system.entity.SysDepartRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @description: 部门角色
 * <AUTHOR>
 * @date 2021/11/4 23:46
 * @version 1.0
 */
public interface SysDepartRoleMapper extends BaseMapper<SysDepartRole> {
    /**
     * 根据用户id，部门id查询可授权所有部门角色
     * @param orgCode
     * @param userId
     * @return
     */
    public List<SysDepartRole> queryDeptRoleByDeptAndUser(@Param("orgCode") String orgCode, @Param("userId") String userId);
}
