package com.byun.modules.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byun.modules.staffing.model.StaUserRelModel;
import org.apache.ibatis.annotations.Param;
import com.byun.modules.system.entity.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.byun.modules.system.model.SysUserSysDepartModel;
import com.byun.modules.system.vo.SysUserDepVo;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-20
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    List<SysUser> getUserListByUserRelYou(Page<SysUser> page, @Param("userRelModel") StaUserRelModel userRelModel);

    List<SysUser> getUserListByUserRelMe(Page<SysUser> page, @Param("userRelModel") StaUserRelModel userRelModel);

    /**
     * 通过用户账号查询用户信息
     *
     * @param username
     * @return
     */
    public SysUser getUserByName(@Param("username") String username);

    /**
     * 根据部门Id查询用户信息
     *
     * @param page
     * @param departId
     * @return
     */
    IPage<SysUser> getUserByDepId(Page page, @Param("departId") String departId, @Param("username") String username);

    /**
     * 根据用户Ids,查询用户所属部门名称信息
     *
     * @param userIds
     * @return
     */
    List<SysUserDepVo> getDepNamesByUserIds(@Param("userIds") List<String> userIds);

    /**
     * 根据部门Ids,查询部门下用户信息
     *
     * @param page
     * @param departIds
     * @return
     */
    IPage<SysUser> getUserByDepIds(Page page, @Param("departIds") List<String> departIds, @Param("username") String username);

    /**
     * 根据角色Id查询用户信息
     *
     * @param page
     * @param
     * @return
     */
    IPage<SysUser> getUserByRoleId(Page page, @Param("roleId") String roleId, @Param("username") String username);

    /**
     * 根据用户名设置部门ID
     *
     * @param username
     * @param departId
     */
    void updateUserDepart(@Param("username") String username, @Param("orgCode") String orgCode);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone
     * @return
     */
    public SysUser getUserByPhone(@Param("phone") String phone);


    /**
     * 根据邮箱查询用户信息
     *
     * @param email
     * @return
     */
    public SysUser getUserByEmail(@Param("email") String email);

    /**
     * 根据 orgCode 查询用户，包括子部门下的用户
     *
     * @param page       分页对象, xml中可以从里面进行取值,传递参数 Page 即自动分页,必须放在第一位(你可以继承Page实现自己的分页对象)
     * @param orgCode
     * @param userParams 用户查询条件，可为空
     * @return
     */
    List<SysUserSysDepartModel> getUserByOrgCode(IPage page, @Param("orgCode") String orgCode, @Param("userParams") SysUser userParams);


    /**
     * 查询 getUserByOrgCode 的Total
     *
     * @param orgCode
     * @param userParams 用户查询条件，可为空
     * @return
     */
    Integer getUserByOrgCodeTotal(@Param("orgCode") String orgCode, @Param("userParams") SysUser userParams);

    /**
     * <AUTHOR>
     * @Date 2019/12/13 16:10
     * @Description: 批量删除角色与用户关系
     */
    void deleteBathRoleUserRelation(@Param("roleIdArray") String[] roleIdArray);

    /**
     * <AUTHOR>
     * @Date 2019/12/13 16:10
     * @Description: 批量删除角色与权限关系
     */
    void deleteBathRolePermissionRelation(@Param("roleIdArray") String[] roleIdArray);

    /**
     * 查询被逻辑删除的用户
     */
    List<SysUser> selectLogicDeleted(@Param(Constants.WRAPPER) Wrapper<SysUser> wrapper);

    /**
     * 还原被逻辑删除的用户
     */
    int revertLogicDeleted(@Param("userIds") String userIds, @Param("entity") SysUser entity);

    /**
     * 彻底删除被逻辑删除的用户
     */
    int deleteLogicDeleted(@Param("userIds") String userIds);

    /**
     * 更新空字符串为null【此写法有sql注入风险，禁止随便用】
     */
    @Deprecated
    int updateNullByEmptyString(@Param("fieldName") String fieldName);

    /**
     * 根据部门Ids,查询部门下用户信息
     *
     * @param departIds
     * @return
     */
    List<SysUser> queryByDepIds(@Param("departIds") List<String> departIds, @Param("username") String username);

    void updateEmailById(@Param("id") String id, @Param("email") String email);

    Boolean editWechatIdById(@Param("id") String id, @Param("wechatId") String wechatId);

    int getThisMonthUser();

    List<SysUser> listTaskTotalPage(IPage<SysUser> userIPage, @Param("realNameName") String realNameName,
                                    @Param("username") String username, @Param("deptNo") String deptNo,
                                    @Param("deptName") String deptName);

    List<SysUser> listTaskTotal(@Param("realNameName") String realNameName, @Param("username") String username,
                                @Param("deptName") String deptName);

    List<SysUser> selectStaOrderWithUser(@Param("selectedDate") String selectedDate,
                                         @Param("selectedMonth") String selectedMonth,
                                         @Param("orgCode") String orgCode,
                                         @Param("list") List<Integer> list);

    /**
     * 查询时间段内每日入职统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orgCode 组织代码
     * @param statusList 状态列表
     * @return 每日入职统计结果
     */
    List<java.util.Map<String, Object>> selectDailyJoinStats(@Param("startDate") String startDate,
                                                              @Param("endDate") String endDate,
                                                              @Param("orgCode") String orgCode,
                                                              @Param("statusList") List<Integer> statusList);

    /**
     * 查询指定日期或月份的考勤状态统计
     * @param selectedDate 选择的日期
     * @param selectedMonth 选择的月份
     * @param orgCode 组织代码
     * @param statusList 状态列表
     * @return 考勤状态统计结果
     */
    List<java.util.Map<String, Object>> selectAttendanceStatusStats(@Param("selectedDate") String selectedDate,
                                                                     @Param("selectedMonth") String selectedMonth,
                                                                     @Param("orgCode") String orgCode,
                                                                     @Param("statusList") List<Integer> statusList);

}
