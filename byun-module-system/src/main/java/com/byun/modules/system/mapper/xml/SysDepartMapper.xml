<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.system.mapper.SysDepartMapper">

    <resultMap id="StaDepartModel" type="com.byun.modules.staffing.model.StaDepartModel" >
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="depart_name" property="departName" jdbcType="VARCHAR"/>
        <result column="depart_name_en" property="departNameEn" jdbcType="VARCHAR"/>
        <result column="depart_name_abbr" property="departNameAbbr" jdbcType="VARCHAR"/>
        <result column="depart_order" property="departOrder" jdbcType="TINYINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="org_category" property="orgCategory" jdbcType="VARCHAR"/>
        <result column="org_type" property="orgType" jdbcType="VARCHAR"/>
        <result column="org_code" property="orgCode" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="fax" property="fax" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="address_lat" property="addressLat" jdbcType="DECIMAL"/>
        <result column="address_lng" property="addressLng" jdbcType="DECIMAL"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="VARCHAR"/>
        <result column="qywx_identifier" property="qywxIdentifier" jdbcType="VARCHAR"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="address_name" property="addressName" jdbcType="VARCHAR"/>
        <!--        距离-->
        <result column="distance" property="distance" jdbcType="DECIMAL"/>
        <result column="position_num" property="positionNum" jdbcType="TINYINT"/>
    </resultMap>

    <select id="listDepartDistance" parameterType="Object"  resultMap="StaDepartModel">
        select
        *
        ,(
          SELECT count(id) from sta_work WHERE state_flag=2 and if(d.org_type=1,firm_id=d.id,company_id=d.id)
        ) AS position_num
        <if test="staDepartModel.currentLat !=null and staDepartModel.currentLat != '' and staDepartModel.currentLng !=null and staDepartModel.currentLng != ''">
            ,(
            6371 * acos (
            cos ( radians(#{staDepartModel.currentLat}) )
            * cos( radians( address_lat ) )
            * cos( radians( address_lng ) - radians(#{staDepartModel.currentLng}) )
            + sin ( radians(#{staDepartModel.currentLat}) )
            * sin( radians( address_lat ) )
            )
            ) AS distance
        </if>
        from sys_depart d
        where del_flag = '0'
        <if test="staDepartModel.departName !=null and staDepartModel.departName != ''">
            and (depart_name LIKE concat(concat('%',#{staDepartModel.departName}),'%')
            or address LIKE concat(concat('%',#{staDepartModel.departName}),'%')
            or address_name LIKE concat(concat('%',#{staDepartModel.departName}),'%'))
        </if>
        <if test="staDepartModel.orgCategory !=null and staDepartModel.orgCategory != ''">
            and org_category = #{staDepartModel.orgCategory}
        </if>
        <if test="staDepartModel.orgType !=null and staDepartModel.orgType != ''">
            and org_type = #{staDepartModel.orgType}
        </if>
        order by
        <if test="staDepartModel.currentLat !=null and staDepartModel.currentLat != '' and staDepartModel.currentLng !=null and staDepartModel.currentLng != ''">
            ISNULL(distance),distance asc,
        </if>
        create_time desc
    </select>


    <select id="queryUserDeparts" parameterType="String" resultType="com.byun.modules.system.entity.SysDepart">
	   select * from sys_depart where id IN ( select dep_id from sys_user_depart where user_id = #{userId} )
	</select>

    <!-- 根据username查询所拥有的部门 -->
    <select id="queryDepartsByUsername" parameterType="String" resultType="com.byun.modules.system.entity.SysDepart">
        SELECT *
        FROM sys_depart
        WHERE id IN (
            SELECT dep_id
            FROM sys_user_depart
            WHERE user_id = (
                SELECT id
                FROM sys_user
                WHERE username = #{username}
            )
        )
    </select>

    <!-- 根据部门Id查询,当前和下级所有部门IDS -->
    <select id="getSubDepIdsByDepId" resultType="java.lang.String">
		select id from sys_depart where del_flag = '0' and org_code like concat((select org_code from sys_depart where id=#{departId}),'%')
	</select>

    <!--根据部门编码获取我的部门下所有部门ids -->
    <select id="getSubDepIdsByOrgCodes" resultType="java.lang.String">
		select id from sys_depart where del_flag = '0' and
        <foreach collection="orgCodes" item="item" index="index"  open="(" separator="or" close=")">
            org_code LIKE CONCAT(#{item},'%')
        </foreach>
	</select>
     <!--根据parent_id查询下级部门-->
    <select id="queryTreeListByPid" parameterType="Object"  resultType="com.byun.modules.system.entity.SysDepart">
        SELECT * FROM  sys_depart where del_flag = '0'
        <choose>
            <when test="parentId != null and parentId != ''">
                AND parent_id =  #{parentId,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND parent_id is null or parent_id=''
            </otherwise>
        </choose>
        order by depart_order
    </select>

    <!-- 根据OrgCod查询公司信息 -->
    <select id="queryCompByOrgCode" resultType="com.byun.modules.system.entity.SysDepart">
		select * from sys_depart where del_flag = '0' and org_category='1' and org_code= #{orgCode,jdbcType=VARCHAR}
	</select>
</mapper>