<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byun.modules.system.mapper.SysUserMapper">

    <select id="getUserListByUserRelMe" parameterType="Object" resultType="com.byun.modules.system.entity.SysUser">
        select
        su.*
        from sys_user su
        right join sys_user_rel sur ON sur.me_id = su.id
        <where>
            <if test="userRelModel.relType !=null and userRelModel.relType != ''">
                and sur.rel_type = #{userRelModel.relType}
            </if>
            <if test="userRelModel.delFlag !=null and userRelModel.delFlag != ''">
                and sur.del_flag = #{userRelModel.delFlag}
            </if>
            <if test="userRelModel.youId !=null and userRelModel.youId != ''">
                and sur.you_id = #{userRelModel.youId}
            </if>
            <if test="userRelModel.meId !=null and userRelModel.meId != ''">
                and sur.me_id = #{userRelModel.meId}
            </if>
            <if test="userRelModel.spareId !=null and userRelModel.spareId != ''">
                and sur.spare_id = #{userRelModel.spareId}
            </if>
        </where>
        order by sur.create_time desc
    </select>

    <select id="getUserListByUserRelYou" parameterType="Object" resultType="com.byun.modules.system.entity.SysUser">
        select
        su.*
        from sys_user su
        right join sys_user_rel sur ON sur.you_id = su.id
        <where>
            <if test="userRelModel.relType !=null and userRelModel.relType != ''">
                and sur.rel_type = #{userRelModel.relType}
            </if>
            <if test="userRelModel.delFlag !=null and userRelModel.delFlag != ''">
                and sur.del_flag = #{userRelModel.delFlag}
            </if>
            <if test="userRelModel.youId !=null and userRelModel.youId != ''">
                and sur.you_id = #{userRelModel.youId}
            </if>
            <if test="userRelModel.meId !=null and userRelModel.meId != ''">
                and sur.me_id = #{userRelModel.meId}
            </if>
            <if test="userRelModel.spareId !=null and userRelModel.spareId != ''">
                and sur.spare_id = #{userRelModel.spareId}
            </if>
        </where>
        order by sur.create_time desc
    </select>

    <!-- 根据用户名查询 -->
    <select id="getUserByName" resultType="com.byun.modules.system.entity.SysUser">
        select *
        from sys_user
        where username = #{username}
          and del_flag = 0
    </select>

    <!-- 根据部门Id查询 -->
    <select id="getUserByDepId" resultType="com.byun.modules.system.entity.SysUser">
        select * from sys_user where del_flag = 0 and id in (select user_id from sys_user_depart where
        dep_id=#{departId})
        <if test="username!=null and username!=''">
            and username = #{username}
        </if>
    </select>

    <!-- 查询用户的所属部门名称信息 -->
    <select id="getDepNamesByUserIds" resultType="com.byun.modules.system.vo.SysUserDepVo">
        select d.depart_name,ud.user_id from sys_user_depart ud,sys_depart d where d.id = ud.dep_id and ud.user_id in
        <foreach collection="userIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 通过多个部门IDS，查询部门下的用户信息 -->
    <select id="getUserByDepIds" resultType="com.byun.modules.system.entity.SysUser">
        select * from sys_user where del_flag = 0
        <if test="departIds!=null  and departIds.size()>0">
            and id in (select user_id from sys_user_depart where dep_id in
            <foreach collection="departIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="username!=null and username!=''">
            and username = #{username}
        </if>
    </select>

    <!-- 根据角色Id查询 -->
    <select id="getUserByRoleId" resultType="com.byun.modules.system.entity.SysUser">
        select * from sys_user where del_flag = 0 and id in (select user_id from sys_user_role where role_id=#{roleId})
        <if test="username!=null and username!=''">
            and username = #{username}
        </if>
    </select>

    <!--  修改用户部门code -->
    <update id="updateUserDepart">
        UPDATE sys_user
        SET org_code = #{orgCode}
        where username = #{username}
    </update>

    <!-- 根据手机号查询 -->
    <select id="getUserByPhone" resultType="com.byun.modules.system.entity.SysUser">
        select *
        from sys_user
        where phone = #{phone}
          and del_flag = 0
    </select>

    <!-- 根据邮箱查询用户信息 -->
    <select id="getUserByEmail" resultType="com.byun.modules.system.entity.SysUser">
        select *
        from sys_user
        where email = #{email}
          and del_flag = 0
    </select>

    <!-- SQL片段：getUserByOrgCode 的 FROM 和 WHERE 部分 -->
    <sql id="getUserByOrgCodeFromSql">
        FROM
        sys_depart
        INNER JOIN sys_user_depart ON sys_user_depart.dep_id = sys_depart.id
        INNER JOIN sys_user ON sys_user.id = sys_user_depart.user_id
        WHERE
        sys_user.del_flag = 0 AND sys_depart.org_code LIKE '${orgCode}%'

        <if test="userParams != null">
            <if test="userParams.realname != null and userParams.realname != ''">
                AND sys_user.realname LIKE concat(concat('%',#{userParams.realname}),'%')
            </if>
            <if test="userParams.workNo != null and userParams.workNo != ''">
                AND sys_user.work_no LIKE concat(concat('%',#{userParams.workNo}),'%')
            </if>
        </if>
    </sql>

    <!-- 根据 orgCode 查询用户，包括子部门下的用户 -->
    <select id="getUserByOrgCode" resultType="com.byun.modules.system.model.SysUserSysDepartModel">
        SELECT
        sys_user.id AS id,
        sys_user.realname AS realname,
        sys_user.avatar AS avatar,
        sys_user.work_no AS workNo,
        sys_user.post AS post,
        sys_user.telephone AS telephone,
        sys_user.email AS email,
        sys_user.phone AS phone,
        sys_depart.id AS departId,
        sys_depart.depart_name AS departName
        <include refid="getUserByOrgCodeFromSql"/>
        ORDER BY
        sys_depart.org_code ASC
    </select>

    <!-- 查询 getUserByOrgCode 的总数-->
    <select id="getUserByOrgCodeTotal" resultType="java.lang.Integer">
        SELECT COUNT(1)
        <include refid="getUserByOrgCodeFromSql"/>
    </select>

    <!-- 批量删除角色的与用户关系-->
    <update id="deleteBathRoleUserRelation">
        delete from sys_user_role
        where role_id in
        <foreach item="id" collection="roleIdArray" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!-- 批量删除角色的与权限关系-->
    <update id="deleteBathRolePermissionRelation">
        delete from sys_role_permission
        where role_id in
        <foreach item="id" collection="roleIdArray" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询被逻辑删除的用户 -->
    <select id="selectLogicDeleted" resultType="com.byun.modules.system.entity.SysUser">
        SELECT *
        FROM sys_user ${ew.customSqlSegment}
    </select>

    <!-- 更新被逻辑删除的用户 -->
    <update id="revertLogicDeleted">
        UPDATE
            sys_user
        SET del_flag    = 0,
            update_by   = #{entity.updateBy},
            update_time = #{entity.updateTime}
        WHERE del_flag = 1
          AND id IN (${userIds})
    </update>

    <!-- 彻底删除被逻辑删除的用户 -->
    <delete id="deleteLogicDeleted">
        DELETE
        FROM sys_user
        WHERE del_flag = 1
          AND id IN (${userIds})
    </delete>

    <!-- 更新空字符串为null -->
    <update id="updateNullByEmptyString">
        UPDATE sys_user
        SET ${fieldName} = NULL
        WHERE ${fieldName} = ''
    </update>
    <update id="updateEmailById">
        UPDATE sys_user
        SET email = #{email}
        WHERE id = #{id}
    </update>
    <update id="editWechatIdById">
        UPDATE sys_user
        SET wechat_id = #{wechatId}
        WHERE id = #{id}
    </update>
    <!-- 通过多个部门IDS，查询部门下的用户信息 -->
    <select id="queryByDepIds" resultType="com.byun.modules.system.entity.SysUser">
        select * from sys_user where del_flag = 0
        <if test="departIds!=null  and departIds.size()>0">
            and id in (select user_id from sys_user_depart where dep_id in
            <foreach collection="departIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="username!=null and username!=''">
            and username != #{username}
        </if>
    </select>
    <select id="getThisMonthUser" resultType="java.lang.Integer">
        SELECT COUNT(0)
        FROM sys_user
        WHERE del_flag = 0 AND MONTH (sys_user.create_time) = MONTH (NOW())
    </select>
    <!--分页人员生命周期-->
    <select id="listTaskTotalPage" resultType="com.byun.modules.system.entity.SysUser">
        -- SELECT
        -- su.username,
        -- COUNT(so.id) AS order_count,
        -- (SELECT COUNT(*) FROM sta_order) AS taskTotal
        -- FROM
        -- sys_user as su
        -- LEFT JOIN
        -- sta_order AS so ON su.id = so.sys_user_id
        -- AND so.state_flag IN(0,2,6,7,9)
        -- AND so.del_flag = 0
        -- WHERE
        -- su.del_flag = 0
        -- AND su.is_real_name = 1
        -- GROUP BY
        -- su.id
        -- ORDER BY
        -- order_count DESC;
        SELECT su.id,
        su.real_name_name,
        su.id_card,
        su.degree,
        su.username,
        su.current_stores,
        su.current_stores_id,
        su.current_work_name,
        su.school,
        su.major,
        COUNT(so.id) AS taskTotal
        FROM sys_user as su
        RIGHT JOIN
        sta_order AS so ON su.id = so.sys_user_id
        AND so.state_flag NOT IN (0,2,6,7,9)
        AND so.del_flag = 0
        WHERE su.del_flag = 0
        AND su.is_real_name = 1
        -- TODO code 权限
        <if test="deptName !=null and deptName != ''">
            AND su.current_stores = #{deptName}
        </if>
        <if test="realNameName !=null and realNameName != ''">
            AND su.real_name_name LIKE concat(concat('%',#{realNameName}),'%')
        </if>
        <if test="username !=null and username != ''">
            AND su.username = #{username}
        </if>
        GROUP BY su.id
        ORDER BY taskTotal DESC;
    </select>
<!--导出人员生命周期不分页-->
    <select id="listTaskTotal" resultType="com.byun.modules.system.entity.SysUser">
        SELECT su.id,
        su.real_name_name,
        su.id_card,
        su.degree,
        su.username,
        su.current_stores,
        su.current_stores_id,
        su.current_work_name,
        su.school,
        su.major,
        COUNT(so.id) AS taskTotal
        FROM sys_user as su
        RIGHT JOIN
        sta_order AS so ON su.id = so.sys_user_id
        AND so.state_flag NOT IN (0,2,6,7,9)
        AND so.del_flag = 0
        WHERE su.del_flag = 0
        AND su.is_real_name = 1
        -- TODO code 权限
        <if test="deptName !=null and deptName != ''">
            AND su.current_stores = #{deptName}
        </if>
        <if test="realNameName !=null and realNameName != ''">
            AND su.real_name_name LIKE concat(concat('%',#{realNameName}),'%')
        </if>
        <if test="username !=null and username != ''">
            AND su.username = #{username}
        </if>
        GROUP BY su.id
        ORDER BY taskTotal DESC;
    </select>
    <select id="selectStaOrderWithUser" resultType="com.byun.modules.system.entity.SysUser">
        SELECT o.enroll_degree as degree, u.id_card
        FROM sta_order o
        LEFT JOIN sys_user u ON o.sys_user_id = u.id
        WHERE 1=1
        <if test="orgCode != null">
            AND o.sys_org_code LIKE CONCAT(#{orgCode}, '%')
        </if>
        <if test="selectedDate != null">
            AND o.entry_date LIKE CONCAT(#{selectedDate}, '%')
        </if>
        <if test="selectedMonth != null">
            AND o.entry_date LIKE CONCAT(#{selectedMonth}, '%')
        </if>
        <if test="list != null and !list.isEmpty()">
            AND o.state_flag NOT IN
            <foreach collection="list" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
          <!--
                    AND o.entry_date IS NOT NULL
           -->
        </if>
        AND o.del_flag = 0
    </select>

    <!-- 查询时间段内每日入职统计 -->
    <select id="selectDailyJoinStats" resultType="java.util.Map">
        SELECT
            DATE(o.entry_date) as join_date,
            COUNT(*) as join_count
        FROM sta_order o
        WHERE 1=1
        <if test="orgCode != null">
            AND o.sys_org_code LIKE CONCAT(#{orgCode}, '%')
        </if>
        <if test="startDate != null">
            AND DATE(o.entry_date) &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(o.entry_date) &lt;= #{endDate}
        </if>
<!--        <if test="statusList != null and !statusList.isEmpty()">-->
<!--            AND o.state_flag NOT IN-->
<!--            <foreach collection="statusList" item="state" open="(" separator="," close=")">-->
<!--                #{state}-->
<!--            </foreach>-->
<!--        </if>-->
        AND o.entry_date IS NOT NULL
        AND o.del_flag = 0
        GROUP BY DATE(o.entry_date)
        ORDER BY join_date ASC
    </select>

    <!-- 查询指定日期或月份的考勤状态统计 -->
    <select id="selectAttendanceStatusStats" resultType="java.util.Map">
        WITH schedule_attendance AS (
            SELECT
                o.sys_user_id,
                ss.id as schedule_id,
                ss.schedule_day,
                ss.start_time,
                ss.end_time,
                ss.code as shift_code,
                CASE
                    -- 没有打卡记录则为缺卡
                    WHEN COUNT(slc.id) = 0 THEN '缺卡'
                    -- 有打卡记录，取最严重的状态（优先级：缺卡 > 早退 > 迟到 > 正常）
                    WHEN SUM(CASE WHEN slc.state_flag = 3 THEN 1 ELSE 0 END) > 0 THEN '早退'
                    WHEN SUM(CASE WHEN slc.state_flag = 2 THEN 1 ELSE 0 END) > 0 THEN '迟到'
                    WHEN SUM(CASE WHEN slc.state_flag = 1 THEN 1 ELSE 0 END) > 0 THEN '正常'
                    ELSE '缺卡'
                END as schedule_status
            FROM sta_order o
            INNER JOIN sta_schedule ss ON o.id = ss.sta_order_id
            LEFT JOIN sta_location_clock slc ON ss.id = slc.sta_schedule_id
                AND slc.del_flag = 0
                AND (
                    <if test="selectedDate != null">
                        DATE(slc.time) = #{selectedDate}
                    </if>
                    <if test="selectedMonth != null">
                        DATE_FORMAT(slc.time, '%Y-%m') = #{selectedMonth}
                    </if>
                )
            WHERE 1=1
            <if test="orgCode != null">
                AND o.sys_org_code LIKE CONCAT(#{orgCode}, '%')
            </if>
            <if test="selectedDate != null">
                AND DATE(ss.schedule_day) = #{selectedDate}
            </if>
            <if test="selectedMonth != null">
                AND DATE_FORMAT(ss.schedule_day, '%Y-%m') = #{selectedMonth}
            </if>
            AND o.del_flag = 0
            AND ss.del_flag = 0
            GROUP BY o.sys_user_id, ss.id, ss.schedule_day, ss.start_time, ss.end_time, ss.code
        ),
        user_daily_status AS (
            SELECT
                sys_user_id,
                schedule_day,
                CASE
                    -- 如果一天中有任何班次早退，则该员工当天状态为早退
                    WHEN SUM(CASE WHEN schedule_status = '早退' THEN 1 ELSE 0 END) > 0 THEN '早退'
                    -- 如果一天中有任何班次迟到，则该员工当天状态为迟到
                    WHEN SUM(CASE WHEN schedule_status = '迟到' THEN 1 ELSE 0 END) > 0 THEN '迟到'
                    -- 如果一天中有任何班次缺卡，则该员工当天状态为缺卡
                    WHEN SUM(CASE WHEN schedule_status = '缺卡' THEN 1 ELSE 0 END) > 0 THEN '缺卡'
                    -- 如果所有班次都正常，则该员工当天状态为正常
                    WHEN SUM(CASE WHEN schedule_status = '正常' THEN 1 ELSE 0 END) = COUNT(*) THEN '正常'
                    ELSE '缺卡'
                END as daily_status
            FROM schedule_attendance
            GROUP BY sys_user_id, schedule_day
        )
        SELECT
            daily_status as attendance_status,
            COUNT(*) as status_count
        FROM user_daily_status
        GROUP BY daily_status
        ORDER BY
            CASE daily_status
                WHEN '正常' THEN 1
                WHEN '迟到' THEN 2
                WHEN '早退' THEN 3
                WHEN '缺卡' THEN 4
                ELSE 5
            END
    </select>
</mapper>