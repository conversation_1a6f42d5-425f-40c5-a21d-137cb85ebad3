package com.byun.modules.system.service;

import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.vo.AgeEducationStatsDTO;
import com.byun.modules.system.vo.AttendanceStatusStatsDTO;
import com.byun.modules.system.vo.JoinsByPeriodDTO;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public interface IAttendanceAnalysisService {

    /**
     * 获取年龄和学历分布统计
     * @param selectedDate 选择的日期
     * @param selectedMonth 选择的月份
     * @param orgCode 组织代码
     * @param statusList 状态列表
     * @return 年龄和学历分布统计结果
     */
    AgeEducationStatsDTO getAgeEducationStats(String selectedDate, String selectedMonth, String orgCode, List<Integer> statusList);

    /**
     * 获取每日入职统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orgCode 组织代码
     * @param statusList 状态列表
     * @return 每日入职统计结果
     */
    JoinsByPeriodDTO getJoinsByPeriod(LocalDate startDate, LocalDate endDate, String orgCode, List<Integer> statusList);


    /**
     * 获取考勤状况统计（优化版本）
     * @param selectedDate 选择的日期
     * @param selectedMonth 选择的月份
     * @param orgCode 组织代码
     * @param deptNo 部门编号（可选，优先级高于orgCode）
     * @return 考勤状况统计结果
     */
    AttendanceStatusStatsDTO getOptimizedAttendanceStatusStats(String selectedDate, String selectedMonth, String orgCode, String deptNo);
}
