package com.byun.modules.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.common.api.vo.Result;
import com.byun.modules.staffing.vo.SysUserBankVo;
import com.byun.modules.system.entity.StaUserBankCards;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface IStaUserBankCardsService extends IService<StaUserBankCards> {
    Result bindBankAndUpdateBank(JSONObject jsonObject);
    /**
     * @param businessDepartmentName 事业处
     * @param regionName 区域
     * @param companyName 门店
     * @param rosterMonth 日期区间
     * @return
     */
    //薪资计算列表
    List<JSONObject> salaryCalculatorList(String businessDepartmentName,String regionName,String companyName,String enrollName,String rosterMonth) throws ParseException;
    //薪资计算导出
    List<Map<String, Object>> getSalaryExportData(String companyName, String enrollName, String startTime,String endTime);
    //获取用户结算账户列表
    Page<SysUserBankVo> adminList(Integer pageNo, Integer pageSize, String deptName, String userName,String deptNo);
    //导出用户结算列表
    List<SysUserBankVo> exportXlsBank(String deptName,String deptNo, String userName);
}
