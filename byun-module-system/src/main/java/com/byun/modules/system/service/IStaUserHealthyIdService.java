package com.byun.modules.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.common.api.vo.Result;
import com.byun.modules.system.entity.StaUserHealthyId;

import java.util.List;
import java.util.Map;

public interface IStaUserHealthyIdService extends IService<StaUserHealthyId> {
    /**
     * 新增健康证
     * @param staUserHealthyId
     * @return
     */
    Result add(StaUserHealthyId staUserHealthyId);

    void userIdentityApproved(StaUserHealthyId staUserHealthyId);

    boolean rejectApproval(String id, String sysUserId, String refuse);
    //execl导出
    List<Map<String, Object>> getExoortData(LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper);
}
