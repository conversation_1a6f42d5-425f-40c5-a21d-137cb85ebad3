package com.byun.modules.system.service;

import com.byun.modules.system.entity.SysApplet;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 小程序
 * @Author: bai
 * @Date:   2021-11-28
 * @Version: V1.0
 */
public interface ISysAppletService extends IService<SysApplet> {
    /**
     * @description: 重新获取微信的AccessToken，并更新数据库
     * <AUTHOR>
     * @date 2021/11/28 22:20
     * @version 1.0
     */
    String resetAccessToken();
}
