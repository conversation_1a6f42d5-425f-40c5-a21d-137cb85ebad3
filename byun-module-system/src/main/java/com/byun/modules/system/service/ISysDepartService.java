package com.byun.modules.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.modules.staffing.model.StaDepartModel;
import com.byun.modules.staffing.model.StaDepartTreeModel;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.model.DepartIdModel;
import com.byun.modules.system.model.SysDepartTreeModel;
import java.util.List;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <p>
 * 部门表 服务实现类
 * <p>
 * 
 * @Author:Steve
 * @Since：   2019-01-22
 */
public interface ISysDepartService extends IService<SysDepart>{


    Page<StaDepartModel> listDepartDistancePage(Page<StaDepartModel> pageList, StaDepartModel staDepartModel);

    /**
     * 查询我的部门信息,并分节点进行显示
     * @return
     */
    List<StaDepartTreeModel> queryStaMyDeptTreeList(String departIds);

    /**
     * 查询我的部门信息,并分节点进行显示
     * @return
     */
    List<SysDepartTreeModel> queryMyDeptTreeList(String departIds);

    /**
     * 查询我的部门信息,并分节点进行显示
     * @return
     */
    List<StaDepartTreeModel> queryStaTreeList();


    /**
     * 查询所有部门信息,并分节点进行显示
     * @return
     */
    List<SysDepartTreeModel> queryTreeList();


    /**
     * 查询所有部门信息,并分节点进行显示
     * @return
     */
    List<SysDepartTreeModel> queryTreeList(String ids);
    /**
     * 查询所有灵工部门信息,并分节点进行显示
     * @return
     */
    List<StaDepartTreeModel> queryStaTreeList(String ids);


    /**
     * 查询所有部门DepartId信息,并分节点进行显示
     * @return
     */
    public List<DepartIdModel> queryDepartIdTreeList();

    /**
     * 保存部门数据
     * @param sysDepart
     */
    void saveDepartData(SysDepart sysDepart,String username);

    /**
     * 更新depart数据
     * @param sysDepart
     * @return
     */
    Boolean updateDepartDataById(SysDepart sysDepart,String username);
    
    /**
     * 删除depart数据
     * @param id
     * @return
     */
	/* boolean removeDepartDataById(String id); */
    
    /**
     * 根据关键字搜索相关的部门数据
     * @param keyWord
     * @return
     */
    List<SysDepartTreeModel> searhBy(String keyWord,String myDeptSearch,String departIds);
    
    /**
     * 根据部门id删除并删除其可能存在的子级部门
     * @param id
     * @return
     */
    boolean delete(String id);
    
    /**
     * 查询SysDepart集合
     * @param userId
     * @return
     */
	public List<SysDepart> queryUserDeparts(String userId);

    /**
     * 根据用户名查询部门
     *
     * @param username
     * @return
     */
    List<SysDepart> queryDepartsByUsername(String username);

	 /**
     * 根据部门id批量删除并删除其可能存在的子级部门
     * @param ids
     * @return
     */
	void deleteBatchWithChildren(List<String> ids);

    /**
     * 批量初始化 店铺的 权限设置 根据前端请求的多个ID,对数据库执行店铺拥有权限，店铺初始化角色权限设置
     * @param ids
     * @return
     */
    void initializePermissionsBatchDepart(List<String> ids);


    /**
     *  根据部门Id查询,当前和下级所有部门IDS
     * @param departId
     * @return
     */
    List<String> getSubDepIdsByDepId(String departId);

    /**
     * 获取我的部门下级所有部门IDS
     * @return
     */
    List<String> getMySubDepIdsByDepId(String departIds);
    /**
     * @description: 通过orgCode获取当前和下级部门
     * <AUTHOR>
     * @date 2021/12/21 16:25
     * @version 1.0
     */
    List<String> getSubDepIdsByOrgCode(String orgCode);
    /**
     * 根据关键字获取部门信息（通讯录）
     * @return
     */
    List<SysDepartTreeModel> queryTreeByKeyWord(String keyWord);
    /**
     * 获取我的部门下级所有部门
     * @return
     */
    List<SysDepartTreeModel> queryTreeListByPid(String parentId,String ids);

    /**
     * 获取某个部门的所有父级部门的ID
     *
     * @param departId 根据departId查
     */
    JSONObject queryAllParentIdByDepartId(String departId);

    /**
     * 获取某个部门的所有父级部门的ID
     *
     * @param orgCode 根据orgCode查
     */
    JSONObject queryAllParentIdByOrgCode(String orgCode);
    /**
     * 获取公司信息
     * @return
     */
    SysDepart queryCompByOrgCode(String orgCode);
    /**
     * 获取下级部门
     * @return
     */
    List<SysDepart> queryDeptByPid(String pid);
    //本月入住企业数量
    int getThisMonthDept();

}
