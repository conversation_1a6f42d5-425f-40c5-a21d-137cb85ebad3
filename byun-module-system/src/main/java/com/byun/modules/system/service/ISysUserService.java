package com.byun.modules.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.byun.common.api.vo.Result;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.system.vo.SysUserCacheInfo;
import com.byun.modules.staffing.model.StaAgentModel;
import com.byun.modules.staffing.model.StaUserRelModel;
import com.byun.modules.system.entity.StaUserHealthyId;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysRoleIndex;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.model.SysUserSysDepartModel;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-20
 */
public interface ISysUserService extends IService<SysUser> {

	/**
	 * @description: 启用代理 绑定 招聘官
	 * <AUTHOR>
	 * @date 2021/12/14 9:33
	 * @version 1.0
	 */
	void enableAgentByUser(SysUser sysUser, StaAgentModel staAgentModel);

	/**
	 * @description: 停用代理 绑定 招聘官
	 * <AUTHOR>
	 * @date 2021/12/14 9:33
	 * @version 1.0
	 */
	void stopAgentByUser(SysUser sysUser, StaAgentModel staAgentModel);

	/**
	 * @description: 删除代理 绑定 招聘官
	 * <AUTHOR>
	 * @date 2021/12/14 9:33
	 * @version 1.0
	 */
	void deleteAgentByUser(SysUser sysUser, StaAgentModel staAgentModel);

	/**
	 * @description: 查询推广人列表
	 * @param page
	 * @param userRelModel
	 * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.byun.modules.system.entity.SysUser>
	 * <AUTHOR>
	 * @date: 2021/12/13 17:18
	 */
	Page<SysUser> getUserListByUserRelYou(Page<SysUser> page, StaUserRelModel userRelModel);
	Page<SysUser> getUserListByUserRelMe(Page<SysUser> page, StaUserRelModel userRelModel);

	void updateBindUserById(SysUser user);

	/**
	 * 重置密码
	 *
	 * @param username
	 * @param oldpassword
	 * @param newpassword
	 * @param confirmpassword
	 * @return
	 */
	public Result<?> resetPassword(String username, String oldpassword, String newpassword, String confirmpassword);

	/**
	 * 修改密码
	 *
	 * @param sysUser
	 * @return
	 */
	public Result<?> changePassword(SysUser sysUser);

	/**
	 * 删除用户
	 * @param userId
	 * @return
	 */
	public boolean deleteUser(String userId);

	/**
	 * 批量删除用户
	 * @param userIds
	 * @return
	 */
	public boolean deleteBatchUsers(String userIds);
	
	public SysUser getUserByName(String username);

	/**
	 * @description: 添加用户和角色和报名信息和名片信息表
	 * <AUTHOR>
	 * @date 2021/11/21 18:35
	 * @version 1.0
	 */
	public void addUserWithRolAndEnrollAndInfo(SysUser user, String roles);

	/**
	 * 添加用户和用户角色关系
	 * @param user
	 * @param roles
	 */
	public void addUserWithRole(SysUser user,String roles);


	/**
	 * @description: 修改部门角色
	 * <AUTHOR>
	 * @date 2021/12/27 9:59
	 * @version 1.0
	 */
	void editUserWithDepartRole(SysUser sysUser, SysDepart depart, String roleIds);
	
	/**
	 * 修改用户和用户角色关系
	 * @param user
	 * @param roles
	 */
	public void editUserWithRole(SysUser user,String roles);

	/**
	 * 获取用户的授权角色
	 * @param username
	 * @return
	 */
	public List<String> getRole(String username);
	
	/**
	  * 查询用户信息包括 部门信息
	 * @param username
	 * @return
	 */
	public SysUserCacheInfo getCacheUser(String username);

	/**
	 * 获取根据登录用户的角色获取动态首页
	 *
	 * @param username
	 * @param version 前端UI版本
	 * @return
	 */
	public SysRoleIndex getDynamicIndexByUserRole(String username, String version);

	/**
	 * 根据部门Id查询
	 * @param
	 * @return
	 */
	public IPage<SysUser> getUserByDepId(Page<SysUser> page, String departId, String username);

	/**
	 * 根据部门Ids查询
	 * @param
	 * @return
	 */
	public IPage<SysUser> getUserByDepIds(Page<SysUser> page, List<String> departIds, String username);

	/**
	 * 根据 userIds查询，查询用户所属部门的名称（多个部门名逗号隔开）
	 * @param
	 * @return
	 */
	public Map<String,String> getDepNamesByUserIds(List<String> userIds);

    /**
     * 根据部门 Id 和 QueryWrapper 查询
     *
     * @param page
     * @param departId
     * @param queryWrapper
     * @return
     */
    public IPage<SysUser> getUserByDepartIdAndQueryWrapper(Page<SysUser> page, String departId, QueryWrapper<SysUser> queryWrapper);

	/**
	 * 根据 orgCode 查询用户，包括子部门下的用户
	 *
	 * @param orgCode
	 * @param userParams 用户查询条件，可为空
	 * @param page 分页参数
	 * @return
	 */
	IPage<SysUserSysDepartModel> queryUserByOrgCode(String orgCode, SysUser userParams, IPage page);

	/**
	 * 根据角色Id查询
	 * @param
	 * @return
	 */
	public IPage<SysUser> getUserByRoleId(Page<SysUser> page,String roleId, String username);

	/**
	 * 通过用户名获取用户角色集合
	 *
	 * @param username 用户名
	 * @return 角色集合
	 */
	Set<String> getUserRolesSet(String username);

	/**
	 * 通过用户名获取用户权限集合
	 *
	 * @param username 用户名
	 * @return 权限集合
	 */
	Set<String> getUserPermissionsSet(String username);
	
	/**
	 * 根据用户名设置部门ID
	 * @param username
	 * @param orgCode
	 */
	void updateUserDepart(String username,String orgCode);
	
	/**
	 * 根据手机号获取用户名和密码
	 */
	public SysUser getUserByPhone(String phone);


	/**
	 * 根据邮箱获取用户
	 */
	public SysUser getUserByEmail(String email);


	/**
	 * 添加用户和用户部门关系
	 * @param user
	 * @param selectedParts
	 */
	void addUserWithDepart(SysUser user, String selectedParts);

	/**
	 * 编辑用户和用户部门关系
	 * @param user
	 * @param departs
	 */
	void editUserWithDepart(SysUser user, String departs);
	
	/**
	   * 校验用户是否有效
	 * @param sysUser
	 * @return
	 */
	Result checkUserIsEffective(SysUser sysUser);

	/**
	 * 查询被逻辑删除的用户
	 */
	List<SysUser> queryLogicDeleted();

	/**
	 * 查询被逻辑删除的用户（可拼装查询条件）
	 */
	List<SysUser> queryLogicDeleted(LambdaQueryWrapper<SysUser> wrapper);

	/**
	 * 还原被逻辑删除的用户
	 */
	boolean revertLogicDeleted(List<String> userIds, SysUser updateEntity);

	/**
	 * 彻底删除被逻辑删除的用户
	 */
	boolean removeLogicDeleted(List<String> userIds);

    /**
     * 更新手机号、邮箱空字符串为 null
     */
    @Transactional(rollbackFor = Exception.class)
    boolean updateNullPhoneEmail();

	/**
	 * 保存第三方用户信息
	 * @param sysUser
	 */
	void saveThirdUser(SysUser sysUser);

	/**
	 * 根据部门Ids查询
	 * @param
	 * @return
	 */
	List<SysUser> queryByDepIds(List<String> departIds, String username);

	/**
	 * 保存用户
	 * @param user 用户
	 * @param selectedRoles 选择的角色id，多个以逗号隔开
	 * @param selectedDeparts 选择的部门id，多个以逗号隔开
	 */
	void saveUser(SysUser user, String selectedRoles, String selectedDeparts);

	/**
	 * 编辑用户
	 * @param user 用户
	 * @param roles 选择的角色id，多个以逗号隔开
	 * @param departs 选择的部门id，多个以逗号隔开
	 */
	void editUser(SysUser user, String roles, String departs);

	/** userId转为username */
	List<String> userIdToUsername(Collection<String> userIdList);

	/**
	 * @description: 添加代理 绑定 到招聘官上
	 * <AUTHOR>
	 * @date 2021/12/14 9:31
	 * @version 1.0
	 */
    void addAgentByUser(SysUser sysUser, StaAgentModel staAgentModel);

	/**
	 * 修改邮箱
	 * @param id
	 * @param email
	 */
    void updateEmailById(String id, String email);

	/**
	 * 修改微信号
	 * @param id
	 * @param wechatId
	 * @return
	 */
	Boolean editWechatIdById(String id, String wechatId);

	/**
	 * 当月注册用户数量
	 * @return
	 */
    int getThisMonthUser();

	/**
	 * 修改用户信息 名片信息 申请信息
	 * @param jsonObject
	 * @param user
	 * @return
	 */
    Boolean updateUserRealName(JSONObject jsonObject, LoginUser user) throws ParseException;

	/**
	 *
	 * @param userIPage
	 * @return
	 */
	IPage<SysUser> listTaskTotalPage(IPage<SysUser> userIPage,String realNameName,String username,String deptNo,String deptName);


	List<SysUser> listTaskTotal(String realNameName, String username,String deptName);
}
