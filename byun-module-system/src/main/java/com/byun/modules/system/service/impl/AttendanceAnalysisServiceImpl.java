package com.byun.modules.system.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaLocationClock;
import com.byun.modules.staffing.entity.StaSchedule;
import com.byun.modules.staffing.mapper.StaLocationClockMapper;
import com.byun.modules.staffing.service.IStaLocationClockService;
import com.byun.modules.staffing.service.IStaScheduleService;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.mapper.SysUserMapper;
import com.byun.modules.system.service.IAttendanceAnalysisService;
import com.byun.modules.system.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Primary
@Service("attendanceAnalysisServiceImpl")
public class AttendanceAnalysisServiceImpl implements IAttendanceAnalysisService {
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private StaLocationClockMapper staLocationClockMapper;
    @Autowired
    private IStaScheduleService staScheduleService;
    @Autowired
    private IStaLocationClockService staLocationClockService;

    @Override
    public AgeEducationStatsDTO getAgeEducationStats(String selectedDate, String selectedMonth, String orgCode, List<Integer> statusList) {
        // 获取用户数据
        List<SysUser> sysUserList = sysUserMapper.selectStaOrderWithUser(selectedDate, selectedMonth, orgCode, statusList);
        if (sysUserList == null || sysUserList.isEmpty()) {
            return new AgeEducationStatsDTO(new ArrayList<>(), new ArrayList<>());
        }

        // 计算年龄分布
        List<AgeDistributionDTO> ageDistribution = calculateAgeDistribution(sysUserList);

        // 计算学历分布
        List<EducationDistributionDTO> educationDistribution = calculateEducationDistribution(sysUserList);

        return new AgeEducationStatsDTO(ageDistribution, educationDistribution);
    }
    /**
     * 计算年龄分布
     */
    private List<AgeDistributionDTO> calculateAgeDistribution(List<SysUser> sysUserList) {
        // 定义年龄范围
        Map<String, Integer> ageRangeCount = new LinkedHashMap<>();
        ageRangeCount.put("18-25岁", 0);
        ageRangeCount.put("26-35岁", 0);
        ageRangeCount.put("36-45岁", 0);
        ageRangeCount.put("46-55岁", 0);
        ageRangeCount.put("56-65岁", 0);
        ageRangeCount.put("65岁以上", 0);
        ageRangeCount.put("未知", 0);

        int totalCount = 0;

        // 统计各年龄段人数
        for (SysUser user : sysUserList) {
            if (WxlConvertUtils.isNotEmpty(user.getIdCard())) {
                try {
                    if (IdCardUtil.isIdCardNumberValid(user.getIdCard())) {
                        int age = IdCardUtil.countAge(user.getIdCard());
                        totalCount++;

                        if (age >= 18 && age <= 25) {
                            ageRangeCount.put("18-25岁", ageRangeCount.get("18-25岁") + 1);
                        } else if (age >= 26 && age <= 35) {
                            ageRangeCount.put("26-35岁", ageRangeCount.get("26-35岁") + 1);
                        } else if (age >= 36 && age <= 45) {
                            ageRangeCount.put("36-45岁", ageRangeCount.get("36-45岁") + 1);
                        } else if (age >= 46 && age <= 55) {
                            ageRangeCount.put("46-55岁", ageRangeCount.get("46-55岁") + 1);
                        } else if (age >= 56 && age <= 65) {
                            ageRangeCount.put("56-65岁", ageRangeCount.get("56-65岁") + 1);
                        } else if (age > 65) {
                            ageRangeCount.put("65岁以上", ageRangeCount.get("65岁以上") + 1);
                        } else {
                            ageRangeCount.put("未知", ageRangeCount.get("未知") + 1);
                        }
                    } else {
                        ageRangeCount.put("未知", ageRangeCount.get("未知") + 1);
                    }
                } catch (Exception e) {
                    ageRangeCount.put("未知", ageRangeCount.get("未知") + 1);
                }
            } else {
                ageRangeCount.put("未知", ageRangeCount.get("未知") + 1);
            }
        }

        // 转换为DTO并计算百分比
        List<AgeDistributionDTO> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : ageRangeCount.entrySet()) {
            if (entry.getValue() > 0) { // 只返回有数据的年龄段
                BigDecimal percentage = totalCount > 0 ?
                        new BigDecimal(entry.getValue() * 100.0 / totalCount).setScale(1, RoundingMode.HALF_UP) :
                        BigDecimal.ZERO;
                result.add(new AgeDistributionDTO(entry.getKey(), entry.getValue(), percentage));
            }
        }

        return result;
    }

    /**
     * 计算学历分布
     */
    private List<EducationDistributionDTO> calculateEducationDistribution(List<SysUser> sysUserList) {
        // 统计学历分布
        Map<String, Integer> educationCount = new HashMap<>();
        int totalCount = 0;

        for (SysUser user : sysUserList) {
            String degree = user.getDegree();
            if (WxlConvertUtils.isEmpty(degree)) {
                degree = "未知";
            }
            educationCount.put(degree, educationCount.getOrDefault(degree, 0) + 1);
            totalCount++;
        }
        // 转换为DTO并计算百分比
        List<EducationDistributionDTO> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : educationCount.entrySet()) {
            BigDecimal percentage = totalCount > 0 ?
                    new BigDecimal(entry.getValue() * 100.0 / totalCount).setScale(1, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
            result.add(new EducationDistributionDTO(entry.getKey(), entry.getValue(), percentage));
        }
        // 按人数降序排列
        result.sort((a, b) -> b.getCount().compareTo(a.getCount()));
        return result;
    }
    @Override
    public JoinsByPeriodDTO getJoinsByPeriod(LocalDate startDate, LocalDate endDate, String orgCode, List<Integer> statusList) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 查询数据库中的实际入职统计
        List<Map<String, Object>> joinStats = sysUserMapper.selectDailyJoinStats(
                startDate.format(formatter),
                endDate.format(formatter),
                orgCode,
                statusList
        );

        // 创建日期到统计数据的映射
        Map<String, Integer> joinCountMap = new HashMap<>();
        int totalJoins = 0;

        for (Map<String, Object> stat : joinStats) {
            String joinDate = stat.get("join_date").toString();
            Integer joinCount = ((Number) stat.get("join_count")).intValue();
            joinCountMap.put(joinDate, joinCount);
            totalJoins += joinCount;
        }

        // 生成完整的日期范围，包括没有入职记录的日期
        List<DailyJoinStatsDTO> dailyJoinStats = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            String dateStr = currentDate.format(formatter);
            Integer count = joinCountMap.getOrDefault(dateStr, 0);
            dailyJoinStats.add(new DailyJoinStatsDTO(dateStr, count));
            currentDate = currentDate.plusDays(1);
        }

        return new JoinsByPeriodDTO(dailyJoinStats, totalJoins);
    }

    @Override
    public AttendanceStatusStatsDTO getOptimizedAttendanceStatusStats(String selectedDate, String selectedMonth, String orgCode, String deptNo) {
        log.info("开始考勤统计，参数：selectedDate={}, selectedMonth={}, orgCode={}, deptNo={}", selectedDate, selectedMonth, orgCode, deptNo);
        try {
            // 1. 获取排班数据
            List<StaSchedule> scheduleList = loadScheduleData(selectedDate, selectedMonth, orgCode, deptNo);
            log.info("获取排班数据：{}条", scheduleList.size());
            if (scheduleList.isEmpty()) {
                log.info("未找到排班数据，返回空结果");
                return createEmptyResult(selectedDate, selectedMonth);
            }
            // 2. 获取打卡数据
            List<StaLocationClock> clockList = loadClockData(selectedDate, selectedMonth, orgCode, deptNo);
            log.info("获取打卡数据：{}条", clockList.size());
            // 3. 提取唯一的用户日期组合
            Set<String> userDateCombinations = extractUserDateCombinations(scheduleList);
            log.info("唯一用户日期组合：{}个", userDateCombinations.size());
            if (userDateCombinations.isEmpty()) {
                log.info("未找到有效的用户日期组合");
                return createEmptyResult(selectedDate, selectedMonth);
            }
            // 4. 统计各状态人数
            Map<String, Integer> statusCountMap = calculateAttendanceStatusCounts(userDateCombinations, clockList);
            // 5. 构建返回结果
            AttendanceStatusStatsDTO result = buildAttendanceStatusResult(statusCountMap, selectedDate, selectedMonth);
            log.info("考勤统计完成，总计{}人，正常{}人，迟到{}人，早退{}人，缺卡{}人",
                    result.getTotalEmployees(),
                    statusCountMap.get("正常"),
                    statusCountMap.get("迟到"),
                    statusCountMap.get("早退"),
                    statusCountMap.get("缺卡"));
            return result;
        } catch (Exception e) {
            log.error("考勤统计失败", e);
            return createEmptyResult(selectedDate, selectedMonth);
        }
    }
    /**
     * 获取排班数据 - 性能优化版本 + 部门过滤
     * 使用精确的日期范围查询替代LIKE操作，支持部门编号优先级过滤
     */
    private List<StaSchedule> loadScheduleData(String selectedDate, String selectedMonth, String orgCode, String deptNo) {
        LambdaQueryWrapper<StaSchedule> queryWrapper = new LambdaQueryWrapper<>();

        // 基础过滤条件
        queryWrapper.eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0);

        // 条件查询逻辑：deptNo优先级高于orgCode
        if (WxlConvertUtils.isNotEmpty(deptNo)) {
            // 优先使用deptNo查询store_no字段
            queryWrapper.eq(StaSchedule::getStoreNo, deptNo);
            log.info("使用部门编号过滤：deptNo={} -> store_no={}", deptNo, deptNo);
        } else if (WxlConvertUtils.isNotEmpty(orgCode)) {
            // 回退到使用orgCode查询org_code字段
            queryWrapper.likeRight(StaSchedule::getOrgCode, orgCode);
            log.info("使用组织代码过滤：orgCode={} -> org_code LIKE '{}%'", orgCode, orgCode);
        }

        // 优化日期过滤 - 使用精确匹配或日期范围
        if (WxlConvertUtils.isNotEmpty(selectedDate)) {
            // 单日查询：使用精确日期匹配
            queryWrapper.eq(StaSchedule::getScheduleDay, selectedDate);
        } else if (WxlConvertUtils.isNotEmpty(selectedMonth)) {
            // 月度查询：使用日期范围查询
            String startDate = selectedMonth + "-01";
            String endDate = selectedMonth + "-31";
            queryWrapper.between(StaSchedule::getScheduleDay, startDate, endDate);
        }

        // 只查询必要字段以减少内存使用
        queryWrapper.select(StaSchedule::getSysUserId, StaSchedule::getScheduleDay,
                           StaSchedule::getStartTime, StaSchedule::getEndTime,
                           StaSchedule::getStaOrderId, StaSchedule::getStoreNo);

        queryWrapper.orderByAsc(StaSchedule::getScheduleDay);
        return staScheduleService.list(queryWrapper);
    }

    /**
     * 获取打卡数据 - 性能优化版本 + 部门过滤
     * 使用精确的日期范围查询替代LIKE操作，支持部门编号优先级过滤
     */
    private List<StaLocationClock> loadClockData(String selectedDate, String selectedMonth, String orgCode, String deptNo) {
        LambdaQueryWrapper<StaLocationClock> queryWrapper = new LambdaQueryWrapper<>();
        // 基础过滤条件
        queryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);

        // 条件查询逻辑：deptNo优先级高于orgCode
        if (WxlConvertUtils.isNotEmpty(deptNo)) {
            // 注意：打卡表中没有store_no字段，所以当使用deptNo时，
            // 我们需要通过其他方式关联，这里暂时使用orgCode字段进行匹配
            // 实际应用中可能需要通过JOIN查询或者其他业务逻辑来关联部门信息
            queryWrapper.eq(StaLocationClock::getOrgCode, deptNo);
            log.info("使用部门编号过滤打卡数据：deptNo={} -> org_code={}", deptNo, deptNo);
        } else if (WxlConvertUtils.isNotEmpty(orgCode)) {
            // 回退到使用orgCode查询org_code字段
            queryWrapper.likeRight(StaLocationClock::getOrgCode, orgCode);
            log.info("使用组织代码过滤打卡数据：orgCode={} -> org_code LIKE '{}%'", orgCode, orgCode);
        }

        // 优化日期过滤 - 使用日期范围查询
        if (WxlConvertUtils.isNotEmpty(selectedDate)) {
            // 单日查询：使用日期范围（当天00:00:00到23:59:59）
            String startTime = selectedDate + " 00:00:00";
            String endTime = selectedDate + " 23:59:59";
            queryWrapper.between(StaLocationClock::getTime, startTime, endTime);
        } else if (WxlConvertUtils.isNotEmpty(selectedMonth)) {
            // 月度查询：使用月份范围
            String startTime = selectedMonth + "-01 00:00:00";
            String endTime = selectedMonth + "-31 23:59:59";
            queryWrapper.between(StaLocationClock::getTime, startTime, endTime);
        }

        // 只查询必要字段以减少内存使用
        queryWrapper.select(StaLocationClock::getSysUserId, StaLocationClock::getTime,
                           StaLocationClock::getStateFlag, StaLocationClock::getStaOrderId);

        queryWrapper.orderByAsc(StaLocationClock::getTime);
        return staLocationClockService.list(queryWrapper);
    }

    /**
     * 提取唯一的用户日期组合 - 性能优化版本
     * 减少日志输出，提高处理速度
     */
    private Set<String> extractUserDateCombinations(List<StaSchedule> scheduleList) {
        Set<String> userDateCombinations = new HashSet<>();

        log.info("开始提取用户日期组合，排班记录数：{}", scheduleList.size());

        for (StaSchedule schedule : scheduleList) {
            if (schedule.getSysUserId() != null && schedule.getScheduleDay() != null) {
                String userDate = schedule.getSysUserId() + "_" + formatDate(schedule.getScheduleDay());
                userDateCombinations.add(userDate);
            }
        }

        log.info("提取完成，唯一用户日期组合数：{}", userDateCombinations.size());

        // 只在DEBUG级别输出详细信息
        if (log.isDebugEnabled()) {
            for (String combination : userDateCombinations) {
                log.debug("用户日期组合：{}", combination);
            }
        }

        return userDateCombinations;
    }

    /**
     * 计算考勤状态统计 - 性能优化版本
     * 使用HashMap索引提高查找效率，从O(n²)优化到O(n)
     */
    private Map<String, Integer> calculateAttendanceStatusCounts(Set<String> userDateCombinations, List<StaLocationClock> clockList) {
        Map<String, Integer> statusCountMap = new HashMap<>();
        statusCountMap.put("正常", 0);
        statusCountMap.put("迟到", 0);
        statusCountMap.put("早退", 0);
        statusCountMap.put("缺卡", 0);

        log.info("开始优化统计，用户日期组合：{}个，打卡记录：{}条", userDateCombinations.size(), clockList.size());

        // 性能优化：预先构建打卡记录的索引 - 从O(n²)优化到O(n)
        Map<String, List<StaLocationClock>> clockIndexMap = buildClockIndexMap(clockList);
        log.info("打卡记录索引构建完成，索引键数量：{}", clockIndexMap.size());

        // 批量处理用户日期组合
        for (String userDate : userDateCombinations) {
            String[] parts = userDate.split("_");
            String userId = parts[0];
            String dateStr = parts[1];

            // 使用索引快速查找打卡记录 - O(1)复杂度
            List<StaLocationClock> userDayClocks = clockIndexMap.getOrDefault(userDate, Collections.emptyList());

            // 根据业务规则判断考勤状态
            String status = determineAttendanceStatusOptimized(userDayClocks);

            statusCountMap.put(status, statusCountMap.get(status) + 1);

            // 减少日志输出以提高性能
            if (log.isDebugEnabled()) {
                log.debug("用户{}在{}的状态：{} (打卡{}次)", userId, dateStr, status, userDayClocks.size());
            }
        }

        log.info("统计完成，结果：正常{}，迟到{}，早退{}，缺卡{}",
                statusCountMap.get("正常"), statusCountMap.get("迟到"),
                statusCountMap.get("早退"), statusCountMap.get("缺卡"));
        return statusCountMap;
    }

    /**
     * 构建打卡记录索引 - 性能优化核心
     * 将O(n²)的查找优化为O(1)
     */
    private Map<String, List<StaLocationClock>> buildClockIndexMap(List<StaLocationClock> clockList) {
        Map<String, List<StaLocationClock>> clockIndexMap = new HashMap<>();

        for (StaLocationClock clock : clockList) {
            if (clock.getSysUserId() != null && clock.getTime() != null) {
                String dateStr = formatDate(clock.getTime());
                String userDateKey = clock.getSysUserId() + "_" + dateStr;

                clockIndexMap.computeIfAbsent(userDateKey, k -> new ArrayList<>()).add(clock);
            }
        }

        return clockIndexMap;
    }

    /**
     * 优化的状态判断方法 - 减少日志输出提高性能
     */
    private String determineAttendanceStatusOptimized(List<StaLocationClock> userDayClocks) {
        // 1. 缺卡：无打卡记录
        if (userDayClocks.isEmpty()) {
            return "缺卡";
        }

        // 2. 检查打卡完整性 - 快速判断
        if (userDayClocks.size() < 2) {
            return "缺卡";
        }

        // 3. 快速状态分析 - 避免重复循环
        boolean hasEarlyLeave = false;
        boolean hasLate = false;
        int normalCount = 0;

        for (StaLocationClock clock : userDayClocks) {
            Integer stateFlag = clock.getStateFlag();
            if (stateFlag == null) continue;

            switch (stateFlag) {
                case 3: hasEarlyLeave = true; break;
                case 2: hasLate = true; break;
                case 1: case 7: normalCount++; break;
            }
        }

        // 4. 快速状态判断
        if (hasEarlyLeave) return "早退";
        if (hasLate) return "迟到";
        if (normalCount >= 2) return "正常";

        return "缺卡";
    }
    /**
     * 构建考勤状态统计结果
     */
    private AttendanceStatusStatsDTO buildAttendanceStatusResult(Map<String, Integer> statusCountMap, String selectedDate, String selectedMonth) {
        List<AttendanceStatusDTO> statusList = new ArrayList<>();
        int total = statusCountMap.values().stream().mapToInt(Integer::intValue).sum();

        // 构建状态列表
        for (Map.Entry<String, Integer> entry : statusCountMap.entrySet()) {
            String status = entry.getKey();
            Integer count = entry.getValue();
            BigDecimal percentage = total > 0 ?
                new BigDecimal(count * 100.0 / total).setScale(1, RoundingMode.HALF_UP) :
                BigDecimal.ZERO;

            // 根据状态设置颜色
            //String color = getStatusColor(status);
            statusList.add(new AttendanceStatusDTO(status, count, percentage, ""));
        }

        String dateStr = WxlConvertUtils.isNotEmpty(selectedDate) ? selectedDate : selectedMonth;
        return new AttendanceStatusStatsDTO(dateStr, total, statusList, LocalDateTime.now());
    }
    /**
     * 格式化日期为字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return new java.text.SimpleDateFormat("yyyy-MM-dd").format(date);
    }

    /**
     * 创建空结果
     */
    private AttendanceStatusStatsDTO createEmptyResult(String selectedDate, String selectedMonth) {
        String dateStr = WxlConvertUtils.isNotEmpty(selectedDate) ? selectedDate : selectedMonth;
        return new AttendanceStatusStatsDTO(dateStr, 0, new ArrayList<>(), LocalDateTime.now());
    }
}




