package com.byun.modules.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.DateUtils;
import com.byun.common.util.IdCardUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.*;
import com.byun.modules.staffing.mapper.StaLocationClockMapper;
import com.byun.modules.staffing.mapper.StaOrderMapper;
import com.byun.modules.staffing.mapper.StaScheduleMapper;
import com.byun.modules.staffing.mapper.StaTaskDeductionMapper;
import com.byun.modules.staffing.service.IStaScheduleService;
import com.byun.modules.staffing.service.IStaUserSettlementService;
import com.byun.modules.staffing.vo.SysUserBankVo;
import com.byun.modules.system.entity.StaUserBankCards;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.mapper.StaUserBankCardsMapper;
import com.byun.modules.system.mapper.SysDepartMapper;
import com.byun.modules.system.mapper.SysUserMapper;
import com.byun.modules.system.service.IStaUserBankCardsService;
import com.byun.modules.system.util.HttpUtils;
import com.byun.modules.system.vo.SysUserInfoVo;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2023-3-31 9:28
 */
@Service
public class StaUserBankCardsServiceImpl extends ServiceImpl<StaUserBankCardsMapper, StaUserBankCards> implements IStaUserBankCardsService {
    @Value("${aliyunBankCards.appcode}")
    private String appCode;
    @Value("${aliyunBankCards.url}")
    private String url;
    @Value("${aliyunCatBankInfo.host}")
    private String host;
    @Value("${aliyunCatBankInfo.path}")
    private String path;
    @Value("${aliyunCatBankInfo.appcode}")
    private String appcode2;
    @Autowired
    private StaUserBankCardsMapper staUserBankCardsMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private StaOrderMapper staOrderMapper;
    @Autowired
    private StaLocationClockMapper staLocationClockMapper;
    @Autowired
    private StaTaskDeductionMapper staTaskDeductionMapper;
    @Autowired
    private IStaUserSettlementService staUserSettlementService;
    @Autowired
    private StaScheduleMapper staScheduleMapper;

    /**
     * 银行卡
     * step:1 查询用户是否绑定银行卡 绑定修改 没绑定 绑定
     * step:2 银行卡信息跟本人是否匹配
     * step:3 根据卡号查询银行详情信息
     * step:4 绑定银行卡
     * step:5 修改结算数据中待结算结算账户
     * 支付宝
     * step:1 查询用户是否绑定支付宝 绑定修改 没绑定 绑定
     * step:2 添加或修改支付宝
     * step:3 修改结算数据中待结算结算账户
     *
     * @param jsonObject
     * @return
     */
    @Transactional
    @Override
    public Result bindBankAndUpdateBank(JSONObject jsonObject) {
        /**
         * requestData.phone = that.data.phone //电话
         * requestData.userName = that.data.userName //姓名
         * requestData.idCard = that.data.idCard //身份证
         * requestData.userId = that.data.userId //用户id
         * accountType 1银行卡 2支付宝
         * cardNumber 卡号或支付宝账户
         */
        String phone = jsonObject.getString("phone");
        String idCard = jsonObject.getString("idCard");
        String userName = jsonObject.getString("userName");
        String userId = jsonObject.getString("userId");
        String cardNumber = jsonObject.getString("cardNumber").replace(" ", "");
        Integer accountType = jsonObject.getInteger("accountType"); //1银行卡 2支付宝、
        if (WxlConvertUtils.isEmpty(phone)) {
            return Result.error("手机丢失");
        }
        if (WxlConvertUtils.isEmpty(idCard)) {
            return Result.error("身份证丢失");
        }
        if (WxlConvertUtils.isEmpty(userName)) {
            return Result.error("姓名");
        }
        if (WxlConvertUtils.isEmpty(userId)) {
            return Result.error("用户丢失");
        }
        if (WxlConvertUtils.isEmpty(cardNumber)) {
            return Result.error("支付账户丢失");
        }
        if (WxlConvertUtils.isEmpty(accountType)) {
            return Result.error("账户类型丢失");
        }
        if (accountType == CommonConstant.ACCOUNTTYPE1) {//银行卡
            return this.saveOrEditBindBank(phone, idCard, userName, userId, cardNumber);

        } else if (accountType == CommonConstant.ACCOUNTTYPE2) { //支付宝
            return this.saveOrEditBindZfb(phone, idCard, userName, userId, cardNumber);
        }
        return Result.error("账户类型错误");
    }

    /**
     * 处理绑定银行卡或修改银行卡
     *
     * @param phone
     * @param idCard
     * @param userName
     * @param userId
     * @param cardNumber
     * @return
     */
    public Result saveOrEditBindBank(String phone, String idCard, String userName, String userId, String cardNumber) {
        Result res = new Result();
        Map<String, String> params = new HashMap<>();
        params.put("idcard", idCard);
        params.put("name", userName);
        params.put("bankcard", cardNumber);
        params.put("mobile", phone);
        try {
            String result = get(appCode, url, params);
            JSONObject json = JSON.parseObject(result);
            Integer code = Integer.valueOf(json.getString("code"));
            boolean success = Boolean.valueOf(json.getString("success"));
            if (code == 200 && success == true) {
                String data = json.getString("data");
                JSONObject j = JSON.parseObject(data);
                switch (Integer.valueOf(j.getString("result"))) {
                    case 0:
                        //一致
                        //根据银行卡号查询银行卡信息
                        String method = "GET";
                        Map<String, String> headers = new HashMap<String, String>();
                        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE appcode
                        headers.put("Authorization", "APPCODE " + appcode2);
                        Map<String, String> querys = new HashMap<String, String>();
                        querys.put("cardno", cardNumber);
                        try {
                            HttpResponse response = HttpUtils.doGet(host, path, method, headers, querys);
                            String s = EntityUtils.toString(response.getEntity());
                            JSONObject jsonCareRes = JSON.parseObject(s);
                            switch (Integer.valueOf(jsonCareRes.getString("code"))) {
                                case 0:
                                    //成功
                                    JSONObject careResult = JSON.parseObject(jsonCareRes.getString("data"));
                                    StaUserBankCards userBankCards = staUserBankCardsMapper.selectOne(new QueryWrapper<StaUserBankCards>().eq("user_id", userId));
                                    if (WxlConvertUtils.isNotEmpty(userBankCards)) {
                                        //更换银行卡
                                        userBankCards.setAccountType(CommonConstant.ACCOUNTTYPE1);//银行卡
                                        userBankCards.setCardNumber(cardNumber);//卡号
                                        userBankCards.setPhone(phone);//手机号码
                                        userBankCards.setBankLogo(careResult.getString("bankLogo"));//银行logo
                                        userBankCards.setBankType(careResult.getString("cardType"));//卡类型
                                        userBankCards.setBankName(careResult.getString("bankName"));//卡名称
                                        userBankCards.setArea(careResult.getString("area"));//卡所在地区
                                        userBankCards.setUpdatedAt(new Date());
                                        staUserBankCardsMapper.updateById(userBankCards);
                                        //同步修改结算表结算账户数据
                                        List<StaUserSettlement> staUserSettlement = staUserSettlementService.list(new LambdaQueryWrapper<StaUserSettlement>()
                                                .eq(StaUserSettlement::getSysUserId, userBankCards.getUserId())
                                                .eq(StaUserSettlement::getStatus, 0));//待结算
                                        if (WxlConvertUtils.isNotEmpty(staUserSettlement)) {
                                            for (StaUserSettlement settlement : staUserSettlement) {
                                                settlement.setBankName(careResult.getString("bankName"));//卡号
                                                settlement.setBankNo(cardNumber);//银行名称
                                            }
                                            staUserSettlementService.updateBatchById(staUserSettlement);
                                        }
                                        res.setSuccess(true);
                                        res.setCode(200);
                                        res.setMessage("修改成功");
                                    } else {
                                        //插入银行卡信息
                                        StaUserBankCards staUserBankCards = new StaUserBankCards();
                                        staUserBankCards.setAccountType(CommonConstant.ACCOUNTTYPE1);//银行卡
                                        staUserBankCards.setUserId(userId);//用户ID
                                        staUserBankCards.setIdCard(idCard);//身份证号码
                                        staUserBankCards.setCardHolderName(userName);//持卡人姓名
                                        staUserBankCards.setCardNumber(cardNumber);//卡号
                                        staUserBankCards.setPhone(phone);//手机号码
                                        staUserBankCards.setBankLogo(careResult.getString("bankLogo"));//银行logo
                                        staUserBankCards.setBankType(careResult.getString("cardType"));//卡类型
                                        staUserBankCards.setBankName(careResult.getString("bankName"));//卡名称
                                        staUserBankCards.setArea(careResult.getString("area"));//卡所在地区
                                        staUserBankCardsMapper.insert(staUserBankCards);
                                        SysUser user = sysUserMapper.selectById(userId);
                                        user.setIsCard(CommonConstant.CARD_STATUS1); //已绑定银行卡
                                        sysUserMapper.updateById(user);
                                        res.setCode(200);
                                        res.setSuccess(true);
                                        res.setMessage("绑定成功");
                                    }
                                    break;
                                case 1:
                                    //无效卡号
                                    res.setSuccess(false);
                                    res.setMessage("无效卡号");
                                    break;
                                case 403:
                                    //余额不足
                                    res.setSuccess(false);
                                    res.setMessage("余额不足,请反馈管理员");
                                    break;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            res.setSuccess(false);
                            res.setMessage("系统异常,请反馈管理员");
                            return res;
                        }
                        break;
                    case 1:
                        //不一致
                        res.setResult(false);
                        res.setMessage("绑定失败,信息不一致");
                        break;
                    case 2:
                        //未认证
                        res.setResult(false);
                        res.setMessage("银行卡未认证");
                        break;
                    case 3:
                        //已注销
                        res.setResult(false);
                        res.setMessage("银行卡已注销");
                        break;
                }
            } else if (Integer.valueOf(json.getString("code")) == 400) {
                //参数错误
                res.setSuccess(false);
                res.setMessage("参数错误");
                return res;
            } else if (Integer.valueOf(json.getString("code")) == 500) {
                //系统内部错误
                res.setSuccess(false);
                res.setMessage("系统内部错误，请联系服务商");
                return res;
            }
        } catch (Exception e) {
            res.setSuccess(false);
            res.setMessage("系统异常");
            return res;
        }
        return res;
    }

    /**
     * 处理绑定支付宝或修改支付宝
     *
     * @param phone
     * @param idCard
     * @param userName
     * @param userId
     * @param cardNumber
     * @return
     */
    public Result saveOrEditBindZfb(String phone, String idCard, String userName, String userId, String cardNumber) {
        Result res = new Result();
        StaUserBankCards userBankCards = staUserBankCardsMapper.selectOne(new QueryWrapper<StaUserBankCards>().eq("user_id", userId));
        if (WxlConvertUtils.isEmpty(userBankCards)) { //新增支付宝账户
            StaUserBankCards staUserBankCards = new StaUserBankCards();
            staUserBankCards.setAccountType(CommonConstant.ACCOUNTTYPE2);//支付宝
            staUserBankCards.setBankName("支付宝");
            staUserBankCards.setUserId(userId);//用户ID
            staUserBankCards.setIdCard(idCard);//身份证号码
            staUserBankCards.setCardHolderName(userName);//姓名
            staUserBankCards.setCardNumber(cardNumber);//支付宝账户
            staUserBankCards.setPhone(phone);//手机号码
            staUserBankCardsMapper.insert(staUserBankCards);
            SysUser user = sysUserMapper.selectById(userId);
            user.setIsCard(CommonConstant.CARD_STATUS1); //已绑定银行卡
            sysUserMapper.updateById(user);
            res.setSuccess(true);
            res.setSuccess(true);
            res.setCode(200);
            res.setMessage("操作成功");
        } else {//修改支付宝账户
            userBankCards.setAccountType(CommonConstant.ACCOUNTTYPE2);//支付宝
            userBankCards.setCardNumber(cardNumber);//支付宝账户
            userBankCards.setPhone(phone);//手机号码
            userBankCards.setUpdatedAt(new Date());
            staUserBankCardsMapper.updateById(userBankCards);
            //同步修改结算表结算账户数据
            List<StaUserSettlement> staUserSettlement = staUserSettlementService.list(new LambdaQueryWrapper<StaUserSettlement>()
                    .eq(StaUserSettlement::getSysUserId, userBankCards.getUserId())
                    .eq(StaUserSettlement::getStatus, 0));//待提现
            if (WxlConvertUtils.isNotEmpty(staUserSettlement)) {
                for (StaUserSettlement settlement : staUserSettlement) {
                    settlement.setBankNo(cardNumber);//支付宝账户
                }
                staUserSettlementService.updateBatchById(staUserSettlement);
            }
            res.setSuccess(true);
            res.setCode(200);
            res.setMessage("操作成功");
        }
        return res;
    }

    /**
     * 返回薪资计算事业部数据
     *
     * @param ordersGroupedByCompany
     * @param sysDepart
     * @param resultJson
     * @return
     */
    public List<JSONObject> getBusinessDivisionList(Map<String, List<StaOrder>> ordersGroupedByCompany, SysDepart sysDepart, List<JSONObject> resultJson) {
        //key 区域ID value任务单
        Map<String, List<StaOrder>> regionTotalMaps = new HashMap<>();
        List<SysDepart> sysDepartsRegion = sysDepartMapper.selectList(new QueryWrapper<SysDepart>()
                .lambda()
                .eq(SysDepart::getParentId, sysDepart.getId()));//事业处下区域
        List<String> sysDepartsRegionIds = sysDepartsRegion
                .stream()
                .map(SysDepart::getId)
                .collect(Collectors.toList());
        List<SysDepart> sysDepartsStore = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>()
                .in(SysDepart::getParentId, sysDepartsRegionIds));//区域下面门店
        for (SysDepart depart : sysDepartsStore) { //门店信息
            if (WxlConvertUtils.isNotEmpty(ordersGroupedByCompany.get(depart.getId()))) {
                List<StaOrder> orders = ordersGroupedByCompany.get(depart.getId());
                if (WxlConvertUtils.isNotEmpty(regionTotalMaps.get(depart.getParentId()))) {
                    regionTotalMaps.get(depart.getParentId()).addAll(orders);
                } else {
                    regionTotalMaps.put(depart.getParentId(), orders);
                }
            }
        }
        if (regionTotalMaps.isEmpty()) return null;
        //数据列表
        JSONArray dataArray = new JSONArray();
        JSONObject summary = new JSONObject();
        Double summationWorkingHoursCount = 0.0; //总合计时数汇总
        BigDecimal summationTotalSalaryCount = new BigDecimal("0.0");//总合计汇总工资金额
        BigDecimal summationPayableAmountCount = new BigDecimal("0.0");//总合计应发金额合计
        BigDecimal summationTaskDeductionMoney = new BigDecimal("0.0");//总计任务单扣除金额
        BigDecimal summationActualAmountPaidCount = new BigDecimal("0.0");//总合计实发合计
        for (Map.Entry<String, List<StaOrder>> entry : regionTotalMaps.entrySet()) {
            Double workingHoursCount = 0.0; //时数汇总
            BigDecimal totalSalaryCount = new BigDecimal("0.0");//工资金额合计
            BigDecimal payableAmountCount = new BigDecimal("0.0");//应发金额合计
            BigDecimal taskDeductionMoney = new BigDecimal("0.0");//任务单扣除金额
            BigDecimal actualAmountPaidCount = new BigDecimal("0.0");//实发 合计
            List<StaOrder> staOrderList = entry.getValue();
            JSONObject jsonObject = new JSONObject();
            for (StaOrder staOrder : staOrderList) {//单个区域任务单
                if (WxlConvertUtils.isNotEmpty(staOrder.getStaTaskDeductionMoney())) {
                    taskDeductionMoney = taskDeductionMoney.add(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP);//扣款总额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours())) {
                    workingHoursCount = workingHoursCount + staOrder.getWorkingHours(); //合计时数累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    totalSalaryCount = totalSalaryCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP); //工资金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    payableAmountCount = payableAmountCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP);//应发金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) { //实发减去扣款金额
                    actualAmountPaidCount = actualAmountPaidCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP);//实发合计累加
                }
            }
            jsonObject.put("businessDivisionName", staOrderList.get(0).getBusinessDivisionName());
            jsonObject.put("storeRegionName", staOrderList.get(0).getRegionName());//区域名称
            String str = String.format("%.1f", workingHoursCount);
            jsonObject.put("workingHoursCount", Double.parseDouble(str));//区域时数汇总
            jsonObject.put("totalSalaryCount", totalSalaryCount);//区域工资金额合计
            jsonObject.put("payableAmountCount", payableAmountCount);//区域应发金额合计
            jsonObject.put("taskDeductionMoney", taskDeductionMoney);//区域扣款金额合计
            if (taskDeductionMoney != null) {//如果扣款不为空实发则减去扣款金额
                actualAmountPaidCount = actualAmountPaidCount.subtract(taskDeductionMoney).setScale(1, RoundingMode.HALF_UP);
            }
            jsonObject.put("actualAmountPaidCount", actualAmountPaidCount);//区域实发合计
            dataArray.add(jsonObject);
            //合计汇总
            if (WxlConvertUtils.isNotEmpty(workingHoursCount)) {
                summationWorkingHoursCount = summationWorkingHoursCount + workingHoursCount;
            }
            if (WxlConvertUtils.isNotEmpty(totalSalaryCount)) {
                summationTotalSalaryCount = summationTotalSalaryCount.add(totalSalaryCount).setScale(1, RoundingMode.HALF_UP);
            }
            if (WxlConvertUtils.isNotEmpty(payableAmountCount)) {
                summationPayableAmountCount = summationPayableAmountCount.add(payableAmountCount).setScale(1, RoundingMode.HALF_UP);
            }
            if (WxlConvertUtils.isNotEmpty(actualAmountPaidCount)) {
                summationActualAmountPaidCount = summationActualAmountPaidCount.add(actualAmountPaidCount).setScale(1, RoundingMode.HALF_UP);
            }
            if (WxlConvertUtils.isNotEmpty(taskDeductionMoney)) {
                summationTaskDeductionMoney = summationTaskDeductionMoney.add(taskDeductionMoney).setScale(1, RoundingMode.HALF_UP);
            }
        }
        String str = String.format("%.1f", summationWorkingHoursCount);
        summary.put("summationWorkingHoursCount", Double.parseDouble(str));
        summary.put("summationTotalSalaryCount", summationTotalSalaryCount);
        summary.put("summationPayableAmountCount", summationPayableAmountCount);
        summary.put("summationActualAmountPaidCount", summationActualAmountPaidCount);
        JSONObject salaryData = new JSONObject();
        salaryData.put("data", dataArray);
        salaryData.put("summary", summary);
        resultJson.add(salaryData);
        return resultJson;
    }

    /**
     * 返回薪资计算区域数据
     *
     * @param ordersGroupedByCompany
     * @param sysDepart
     * @param resultJson
     * @return
     */
    public List<JSONObject> getstoreRegionList(Map<String, List<StaOrder>> ordersGroupedByCompany, SysDepart sysDepart, List<JSONObject> resultJson) {
        List<SysDepart> sysDepartsStore = sysDepartMapper.selectList(new QueryWrapper<SysDepart>().lambda().eq(SysDepart::getParentId, sysDepart.getId()));//区域下门店
        //key门店id value任务单集合
        HashMap<String, List<StaOrder>> storeTotalMaps = new HashMap<>();
        for (SysDepart depart : sysDepartsStore) {
            if (WxlConvertUtils.isNotEmpty(ordersGroupedByCompany.get(depart.getId()))) {
                storeTotalMaps.put(depart.getId(), ordersGroupedByCompany.get(depart.getId()));
            }
        }
        if (storeTotalMaps.isEmpty()) return null;
        //数据列表
        JSONArray dataArray = new JSONArray();
        JSONObject summary = new JSONObject();
        Double summationWorkingHoursCount = 0.0; //总合计时数汇总
        BigDecimal summationTotalSalaryCount = new BigDecimal("0.0");//总合计汇总工资金额
        BigDecimal summationPayableAmountCount = new BigDecimal("0.0");//总合计应发金额合计
        BigDecimal summationTaskDeductionMoney = new BigDecimal("0.0");//总计任务单扣除金额
        BigDecimal summationActualAmountPaidCount = new BigDecimal("0.0");//总合计实发合计
        for (Map.Entry<String, List<StaOrder>> entry : storeTotalMaps.entrySet()) {
            String storeId = entry.getKey();
            Double workingHoursCount = 0.0; //时数汇总
            BigDecimal totalSalaryCount = new BigDecimal("0.0");//工资金额合计
            BigDecimal payableAmountCount = new BigDecimal("0.0");//应发金额合计
            BigDecimal taskDeductionMoney = new BigDecimal("0.0");//任务单扣除金额
            BigDecimal actualAmountPaidCount = new BigDecimal("0.0");//实发 合计
            List<StaOrder> staOrderList = entry.getValue();
            JSONObject jsonObject = new JSONObject();
            for (StaOrder staOrder : staOrderList) {
                if (WxlConvertUtils.isNotEmpty(staOrder.getStaTaskDeductionMoney())) {//任务单扣款金额
                    taskDeductionMoney = taskDeductionMoney.add(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP);//扣款总额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours())) {
                    workingHoursCount = workingHoursCount + staOrder.getWorkingHours(); //合计时数累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    totalSalaryCount = totalSalaryCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP); //工资金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    payableAmountCount = payableAmountCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP);//应发金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    actualAmountPaidCount = actualAmountPaidCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP);//实发合计累加
                }
            }
            jsonObject.put("businessDivisionName", staOrderList.get(0).getBusinessDivisionName());//事业处
            jsonObject.put("storeRegionName", staOrderList.get(0).getRegionName());//区域名称
            List<SysDepart> collect = sysDepartsStore.stream().filter(f -> f.getId().equals(storeId)).collect(Collectors.toList());
            jsonObject.put("storeNo", collect.get(0).getStoreNo());//店编
            jsonObject.put("companyName", collect.get(0).getDepartName());//门店名称
            String str = String.format("%.1f", workingHoursCount);
            jsonObject.put("workingHoursCount", Double.parseDouble(str));//区域时数汇总
            jsonObject.put("totalSalaryCount", totalSalaryCount);//区域工资金额合计
            jsonObject.put("payableAmountCount", payableAmountCount);//区域应发金额合计
            jsonObject.put("taskDeductionMoney", taskDeductionMoney);//区域扣款金额合计
            if (taskDeductionMoney != null) {//如果扣款不为空实发则减去扣款金额
                actualAmountPaidCount = actualAmountPaidCount.subtract(taskDeductionMoney).setScale(1, RoundingMode.HALF_UP);
            }
            jsonObject.put("actualAmountPaidCount", actualAmountPaidCount);//区域实发合计
            dataArray.add(jsonObject);
            //合计汇总
            if (WxlConvertUtils.isNotEmpty(workingHoursCount)) {
                summationWorkingHoursCount = summationWorkingHoursCount + workingHoursCount;
            }
            if (WxlConvertUtils.isNotEmpty(totalSalaryCount)) {
                summationTotalSalaryCount = summationTotalSalaryCount.add(totalSalaryCount).setScale(1, RoundingMode.HALF_UP);
            }
            if (WxlConvertUtils.isNotEmpty(payableAmountCount)) {
                summationPayableAmountCount = summationPayableAmountCount.add(payableAmountCount).setScale(1, RoundingMode.HALF_UP);
            }
            if (WxlConvertUtils.isNotEmpty(actualAmountPaidCount)) {
                summationActualAmountPaidCount = summationActualAmountPaidCount.add(actualAmountPaidCount).setScale(1, RoundingMode.HALF_UP);
            }
            if (WxlConvertUtils.isNotEmpty(taskDeductionMoney)) {
                summationTaskDeductionMoney = summationTaskDeductionMoney.add(taskDeductionMoney).setScale(1, RoundingMode.HALF_UP);
            }
        }
        String str = String.format("%.1f", summationWorkingHoursCount);
        summary.put("summationWorkingHoursCount", Double.parseDouble(str));
        summary.put("summationTotalSalaryCount", summationTotalSalaryCount);
        summary.put("summationPayableAmountCount", summationPayableAmountCount);
        summary.put("summationActualAmountPaidCount", summationActualAmountPaidCount);
        JSONObject salaryData = new JSONObject();
        salaryData.put("data", dataArray);
        salaryData.put("summary", summary);
        resultJson.add(salaryData);
        return resultJson;
    }

    /**
     * 返回薪资计算门店数据
     *
     * @param ordersGroupedByCompany
     * @param sysDepart
     * @param resultJson
     * @param staUserBankCards
     * @return
     */
    public List<JSONObject> getcompanyList(Map<String, List<StaOrder>> ordersGroupedByCompany, SysDepart sysDepart, List<JSONObject> resultJson, List<StaUserBankCards> staUserBankCards) {
        String sysDepartId = sysDepart.getId();
        List<StaOrder> staOrderList = ordersGroupedByCompany.get(sysDepartId);
        JSONArray dataArray = new JSONArray();
        JSONObject summary = new JSONObject();
        if (!staOrderList.isEmpty()) {
            //合计薪资
            Double workingHoursCount = 0.0; //时数汇总
            BigDecimal totalSalaryCount = new BigDecimal("0.0");//工资金额合计
            BigDecimal payableAmountCount = new BigDecimal("0.0");//应发金额合计
            BigDecimal taskDeductionMoney = new BigDecimal("0.0");//任务单扣除金额
            BigDecimal actualAmountPaidCount = new BigDecimal("0.0");//实发 合计
            BigDecimal taskDeductionMoneyCount = new BigDecimal("0.0");//扣款合计
            for (StaOrder staOrder : staOrderList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("businessDivisionName", staOrder.getBusinessDivisionName());//事业处
                jsonObject.put("storeRegionName", staOrder.getRegionName());//区域
                jsonObject.put("storeNo", staOrder.getStoreNo());//店编
                jsonObject.put("entryDate", staOrder.getEntryDate()); //入职日期
                jsonObject.put("stateFlag", staOrder.getStateFlag());//任务单状态
                jsonObject.put("sysUserId", staOrder.getSysUserId());//用户id
                jsonObject.put("enrollPhone", staOrder.getEnrollPhone());
                //TODO 月份
                //jsonObject.put("month","");
                jsonObject.put("id", staOrder.getId()); //任务单id
                if (staOrder.getTimeOfResignation() != null) {
                    jsonObject.put("timeOfResignation", staOrder.getTimeOfResignation()); //任务完成时间
                }
                jsonObject.put("companyName", staOrder.getCompanyName());//门店名称
                jsonObject.put("workNameFull", staOrder.getWorkNameFull());
                jsonObject.put("userName", staOrder.getEnrollName());//姓名
                jsonObject.put("userIdCard", staOrder.getEnrollIdCard());//身份证
                Optional<String> first = staUserBankCards //卡号
                        .stream()
                        .filter(staUserBankCards1 -> staUserBankCards1.getUserId().equals(staOrder.getSysUserId()))
                        .map(StaUserBankCards::getUserId)
                        .findFirst();
                jsonObject.put("cardNumber", first.isPresent() ? first.get() : "未绑定银行卡");
                jsonObject.put("workingHours", WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours()) ? staOrder.getWorkingHours() : 0);//时数
                jsonObject.put("salary", WxlConvertUtils.isNotEmpty(staOrder.getSalary()) ? staOrder.getSalary() : new BigDecimal("0.0"));//工资金额
                jsonObject.put("payableAmount", WxlConvertUtils.isNotEmpty(staOrder.getSalary()) ? staOrder.getSalary() : new BigDecimal("0.0"));//应发金额
                jsonObject.put("hourlySalary", staOrder.getHourlySalary());//结算标准
                jsonObject.put("taskDeductionMoney", staOrder.getStaTaskDeductionMoney() != null ? staOrder.getStaTaskDeductionMoney() : new BigDecimal("0.0"));//扣款金额合计
                if (staOrder.getStaTaskDeductionMoney() != null) {
                    jsonObject.put("actualAmountPaidCount", WxlConvertUtils.isNotEmpty(staOrder.getSalary())
                            ? staOrder.getSalary().subtract(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP)
                            : new BigDecimal("0.0")
                    );
                    jsonObject.put("totalActualAmountPaidCount", WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary())
                            ? staOrder.getTotalSalary().subtract(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP)
                            : new BigDecimal("0.0")
                    );
                    taskDeductionMoneyCount = taskDeductionMoneyCount.add(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP);
                } else {
                    //日期区间实发
                    jsonObject.put("actualAmountPaidCount", WxlConvertUtils.isNotEmpty(staOrder.getSalary()) ? staOrder.getSalary() : new BigDecimal("0.0"));
                    //任务单实发
                    jsonObject.put("totalActualAmountPaidCount", WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary()) ? staOrder.getTotalSalary() : new BigDecimal("0.0"));
                }
                jsonObject.put("totalWorkingHours", staOrder.getTotalWorkingHours());//任务单合计工时
                jsonObject.put("totalSalary", WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary()) ? staOrder.getTotalSalary() : new BigDecimal("0.0")); //任务单金额
                jsonObject.put("totalPayableAmount", WxlConvertUtils.isNotEmpty(staOrder.getTotalSalary()) ? staOrder.getTotalSalary() : new BigDecimal("0.0")); //任务单金额
                dataArray.add(jsonObject);
                if (WxlConvertUtils.isEmpty(taskDeductionMoney)) {
                    taskDeductionMoney = taskDeductionMoney.add(taskDeductionMoney).setScale(1, RoundingMode.HALF_UP); //任务单扣款
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getWorkingHours())) {
                    workingHoursCount = workingHoursCount + staOrder.getWorkingHours(); //合计时数累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    totalSalaryCount = totalSalaryCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP); //工资金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    payableAmountCount = payableAmountCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP);//应发金额合计累加
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getSalary())) {
                    actualAmountPaidCount = actualAmountPaidCount.add(staOrder.getSalary()).setScale(1, RoundingMode.HALF_UP);//实发合计累加
                    if (staOrder.getStaTaskDeductionMoney() != null) {
                        actualAmountPaidCount = actualAmountPaidCount.subtract(staOrder.getStaTaskDeductionMoney()).setScale(1, RoundingMode.HALF_UP);
                    }
                }
            }
            String str = String.format("%.2f", workingHoursCount);
            summary.put("summationWorkingHoursCount", Double.parseDouble(str));
            summary.put("summationTotalSalaryCount", totalSalaryCount);
            summary.put("summationPayableAmountCount", payableAmountCount);
            summary.put("summationTaskDeductionMoney", taskDeductionMoney);
            //actualAmountPaidCount = actualAmountPaidCount.subtract(taskDeductionMoneyCount).setScale(1, RoundingMode.HALF_UP);
            summary.put("summationActualAmountPaidCount", actualAmountPaidCount);
            JSONObject salaryData = new JSONObject();
            salaryData.put("data", dataArray);
            salaryData.put("summary", summary);
            resultJson.add(salaryData);
            return resultJson;
        }
        return null;
    }

    /**
     * 任务单添加扣款
     *
     * @return
     */
    public List<StaOrder> orderAddDeductMoney(List<StaOrder> staOrders, List<StaTaskDeduction> staTaskDeductions) {
        Map<String, List<StaTaskDeduction>> staTaskDeductiontMap = staTaskDeductions.stream().collect(Collectors.groupingBy(StaTaskDeduction::getStaOrderId));
        for (StaOrder staOrder : staOrders) {
            if (staTaskDeductiontMap.get(staOrder.getId()) != null) {
                List<StaTaskDeduction> taskDeductions = staTaskDeductiontMap.get(staOrder.getId());
                BigDecimal sb = new BigDecimal("0.0"); //任务单扣款总金额
                for (StaTaskDeduction taskDeduction : taskDeductions) {
                    if (taskDeduction.getLateDeduction() != null) {
                        sb = sb.add(taskDeduction.getLateDeduction()).setScale(1, RoundingMode.HALF_UP);
                    }
                    if (taskDeduction.getEarlyLeaveDeduction() != null) {
                        sb = sb.add(taskDeduction.getEarlyLeaveDeduction()).setScale(1, RoundingMode.HALF_UP);
                    }
                    if (taskDeduction.getOtherDeduction() != null) {
                        sb = sb.add(taskDeduction.getOtherDeduction()).setScale(1, RoundingMode.HALF_UP);
                    }
                }
                staOrder.setStaTaskDeductionMoney(sb);
            }
        }
        return staOrders;
    }

    @Override
    public List<JSONObject> salaryCalculatorList(String businessDepartmentName, String regionId, String companyId, String enrollName, String rosterMonth) throws ParseException {
        List<JSONObject> resultJson = new ArrayList<>();
        LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<StaOrder>();
        staOrderLambdaQueryWrapper.eq(StaOrder::getDelFlag, CommonConstant.DEL_FLAG_0);
        int flagStatus = 0;
        SysDepart sysDepart = new SysDepart();
        if (WxlConvertUtils.isNotEmpty(companyId)) {
            sysDepart = sysDepartMapper.selectOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, companyId));//门店
            staOrderLambdaQueryWrapper.eq(StaOrder::getCompanyId, sysDepart.getId())
                    .notIn(StaOrder::getStateFlag, Arrays.asList(CommonConstant.ORDER_STATUS_0, CommonConstant.ORDER_STATUS_6));
            flagStatus = 3;
        } else if (WxlConvertUtils.isNotEmpty(regionId)) {
            sysDepart = sysDepartMapper.selectOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getId, regionId));//区域
            List<SysDepart> sysDeparts = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>()
                    .eq(SysDepart::getParentId, sysDepart.getId())
                    .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));
            if (sysDeparts.isEmpty()) return null; //区域下面无门店
            List<String> deptIds = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.toList());
            staOrderLambdaQueryWrapper.in(StaOrder::getCompanyId, deptIds);
            flagStatus = 2;
        } else if (WxlConvertUtils.isNotEmpty(businessDepartmentName)) {
            sysDepart = sysDepartMapper.selectOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, businessDepartmentName));//事业处
            staOrderLambdaQueryWrapper.likeRight(StaOrder::getSysOrgCode, sysDepart.getOrgCode());
            flagStatus = 1;
        }
        String[] split = rosterMonth.split(",");
        String startTime = split[0], endTime = split[1];
        // 日期格式化器
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析日期字符串为LocalDate对象
        LocalDate startDate = LocalDate.parse(startTime, dateFormatter);
        LocalDate endDate = LocalDate.parse(endTime, dateFormatter);
        // 结束日期增加一天
        endDate = endDate.plusDays(1);
        Date stDate = Date.from(startDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant());
        Date enDate = Date.from(endDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant());
        //staOrderLambdaQueryWrapper.between(StaOrder::getEntryDate,stDate,enDate); //日期区间查询
        if (WxlConvertUtils.isNotEmpty(enrollName)) { //姓名查询
            staOrderLambdaQueryWrapper.like(StaOrder::getEnrollName, enrollName);
        }
        staOrderLambdaQueryWrapper.notIn(StaOrder::getStateFlag,
                Arrays.asList(
                        CommonConstant.ORDER_STATUS_0,
                        CommonConstant.ORDER_STATUS_1,
                        CommonConstant.ORDER_STATUS_2,
                        CommonConstant.ORDER_STATUS_6,
                        CommonConstant.ORDER_STATUS_9
                ));
        staOrderLambdaQueryWrapper.orderByDesc(StaOrder::getEntryDate); //任务单通过开始日期降序
        List<StaOrder> staOrders = staOrderMapper.selectList(staOrderLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(staOrders) || staOrders.isEmpty()) { //无数据
            return null;
        }
        //任务单
        Set<String> orderIds = new TreeSet<>();
        Set<String> userIds = new TreeSet<>();
        if (WxlConvertUtils.isNotEmpty(staOrders)) {
            orderIds = staOrders.stream().map(StaOrder::getId).collect(Collectors.toSet());
            userIds = staOrders.stream().map(StaOrder::getSysUserId).collect(Collectors.toSet());
        }
        //获取扣款信息
        List<StaTaskDeduction> staTaskDeductions = staTaskDeductionMapper.selectList(
                new QueryWrapper<StaTaskDeduction>()
                        .lambda()
                        .in(StaTaskDeduction::getStaOrderId, orderIds)
        );
        if (WxlConvertUtils.isNotEmpty(staTaskDeductions)) {
            staOrders = this.orderAddDeductMoney(staOrders, staTaskDeductions);//任务单添加扣款信息
        }
        //用户收款信息
        List<StaUserBankCards> staUserBankCards = staUserBankCardsMapper.selectList(new QueryWrapper<StaUserBankCards>()
                .lambda()
                .in(StaUserBankCards::getUserId, userIds)
        );
        LambdaQueryWrapper<StaLocationClock> locationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        locationClockLambdaQueryWrapper.isNotNull(StaLocationClock::getTimeExpect); //没有排班的签到记录不做计算
        locationClockLambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        locationClockLambdaQueryWrapper.in(StaLocationClock::getStaOrderId, orderIds);
        List<StaLocationClock> staLocationClocks = staLocationClockMapper.selectList(locationClockLambdaQueryWrapper);
        List<StaLocationClock> dateFilterLocationClock = staLocationClocks.stream()
                .filter(staLocationClock -> {
                    Date timeExpect = staLocationClock.getTimeExpect();
                    return timeExpect != null &&
                            !timeExpect.before(stDate) &&
                            !timeExpect.after(enDate);
                })
                .collect(Collectors.toList());
        if (!dateFilterLocationClock.isEmpty()) {
            processStaOrders(staOrders, dateFilterLocationClock, false); // 处理特定日期区间的薪资和时长
        }
        if (!staLocationClocks.isEmpty()) {
            processStaOrders(staOrders, staLocationClocks, true); // 处理总薪资和总时长
        }
        //任务单门店分组(key : 门店id, value : 任务单)
        Map<String, List<StaOrder>> ordersGroupedByCompany = staOrders.stream()
                .collect(Collectors.groupingBy(StaOrder::getCompanyId));
        //返回数据
        if (flagStatus == 1) { //事业处
            return this.getBusinessDivisionList(ordersGroupedByCompany, sysDepart, resultJson);
        } else if (flagStatus == 2) { //区域下面是店
            return this.getstoreRegionList(ordersGroupedByCompany, sysDepart, resultJson);
        } else if (flagStatus == 3) { //门店 人员合计 加店铺人员总合计
            return this.getcompanyList(ordersGroupedByCompany, sysDepart, resultJson, staUserBankCards);
        }
        return null;
    }

    /**
     * 处理任务单
     *
     * @param staOrders
     * @param locationClocks
     * @param isTotal
     */
    public void processStaOrders(List<StaOrder> staOrders, List<StaLocationClock> locationClocks, boolean isTotal) {
        if (locationClocks != null && !locationClocks.isEmpty()) {
            locationClocks = this.relatedScheduling(locationClocks);
            Map<String, Double> workingHoursMap = this.calculationOfWorkingHours(locationClocks);
            for (StaOrder staOrder : staOrders) {
                String staOrderId = staOrder.getId();
                Double workingHours = workingHoursMap.get(staOrderId);
                if (isTotal) {
                    staOrder.setTotalWorkingHours(workingHours); // 设置总工作时数
                } else {
                    staOrder.setWorkingHours(workingHours); // 设置当前区间的工作时数
                }
                if (WxlConvertUtils.isNotEmpty(staOrder.getHourlySalary()) && WxlConvertUtils.isNotEmpty(workingHours)) { // 薪资计算
                    BigDecimal hourlySalaryBig = staOrder.getHourlySalary();
                    BigDecimal workingHoursBig = BigDecimal.valueOf(workingHours);
                    BigDecimal totalSalary = hourlySalaryBig.multiply(workingHoursBig);
                    BigDecimal roundedSalary = totalSalary.setScale(1, RoundingMode.HALF_UP);
                    if (isTotal) {
                        staOrder.setTotalSalary(roundedSalary); // 设置总薪资
                    } else {
                        staOrder.setSalary(roundedSalary); // 设置当前区间的薪资
                    }
                }
            }
        }
    }

    /**
     * 签到 关联排班
     *
     * @return
     */
    private List<StaLocationClock> relatedScheduling(List<StaLocationClock> staLocationClocks) {
        Set<String> scheduleIdList = staLocationClocks.stream().map(StaLocationClock::getStaScheduleId).collect(Collectors.toSet());
        List<StaSchedule> staSchedules = staScheduleMapper.selectList(new LambdaQueryWrapper<StaSchedule>()
                .eq(StaSchedule::getDelFlag, CommonConstant.DEL_FLAG_0)
                .in(StaSchedule::getId, scheduleIdList)
        );
        List<StaLocationClock> staLocationClockList = new ArrayList<>();
        for (StaSchedule staSchedule : staSchedules) {
            for (StaLocationClock staLocationClock : staLocationClocks) {
                if (staSchedule.getId().equals(staLocationClock.getStaScheduleId())) {
                    staLocationClockList.add(staLocationClock);
                }
            }
        }
        return staLocationClockList;
    }

    /**
     * 任务单工时计算
     *
     * @param staLocationClocks
     * @param
     * @return
     */
    private Map<String, Double> calculationOfWorkingHours(List<StaLocationClock> staLocationClocks) {
        SimpleDateFormat sb = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
        for (StaLocationClock staLocationClock : staLocationClocks) {
            String staOrderId = staLocationClock.getStaOrderId();
            if (!locationClocksMap.containsKey(staOrderId)) {
                locationClocksMap.put(staOrderId, new ArrayList<>());
            }
            locationClocksMap.get(staOrderId).add(staLocationClock);
        }
        Map<String, Double> workingHoursMap = new HashMap<>();//任务单时数
        //根据天拆分打卡(后续几天每天时数)
        for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
            Double workingHours = 0.0; //当前任务单时数
            String orderId = entry.getKey();//任务单id
            List<StaLocationClock> value = entry.getValue();//任务单打卡数据
            //key签到日期，value签到数据
            for (StaLocationClock clock : value) {
                Date time = clock.getTime();
                Date timeExpect = clock.getTimeExpect();
                if (time != null && timeExpect != null && !isSameDay(time, timeExpect)) {
                    clock.setTime(subtractOneDay(time));
                }
            }
            Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                    .collect(Collectors.groupingBy(clock -> {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(clock.getTime());
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        cal.set(Calendar.MINUTE, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MILLISECOND, 0);
                        return cal.getTime();
                    }, TreeMap::new, Collectors.toList()));
            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntrySort : mapByDay.entrySet()) {
                List<StaLocationClock> staLocationClockList = mapByDayEntrySort.getValue();
                if (staLocationClockList.size() == 2) {
                    List<Date> dates = new ArrayList<>();
                    for (StaLocationClock staLocationClock : staLocationClockList) {// 班次开始时间
                        if (staLocationClock.getTimeType() == 1) {
                            dates.add(staLocationClock.getTimeExpect());
                            break;
                        }
                    }
                    for (StaLocationClock staLocationClock : staLocationClockList) {// 班次结束时间
                        if (staLocationClock.getTimeType() == 2) {
                            dates.add(staLocationClock.getTimeExpect());
                            break;
                        }
                    }
                    if (crossesMidnight(dates)) { //是否跨夜
                        //跨夜降序
                        for (List<StaLocationClock> sublist : mapByDay.values()) {
                            sublist.sort(Comparator.comparing(StaLocationClock::getTime).reversed());
                        }
                    } else {//升序
                        for (List<StaLocationClock> sublist : mapByDay.values()) {
                            sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                        }
                    }
                } else { //其他默认升序
                    for (List<StaLocationClock> sublist : mapByDay.values()) {
                        sublist.sort(Comparator.comparing(StaLocationClock::getTime));
                    }
                }
            }
            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                Set<Date> mapByDayTime = new HashSet<>(); //实际签到时间
                Set<Date> mapByDayTimeExpectDate = new HashSet<>(); //要求签到时间
                if (mapByDayValue.size() == 2) { //一个班次 两次卡
                    List<Date> dd = new ArrayList<>();
                    mapByDayValue.sort(Comparator.comparingInt(StaLocationClock::getTimeType));
                    dd.add(mapByDayValue.get(0).getTimeExpect());
                    dd.add(mapByDayValue.get(1).getTimeExpect());
                    if (!crossesMidnight(dd)) { //正常班次 升序
                        mapByDayTimeExpectDate = mapByDayValue.stream()
                                .map(StaLocationClock::getTimeExpect)
                                .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                        mapByDayTime = mapByDayValue.stream()
                                .map(StaLocationClock::getTime)
                                .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序

                    } else { //跨夜班次   降序
                        mapByDayTimeExpectDate = mapByDayValue.stream()
                                .map(StaLocationClock::getTimeExpect)
                                .sorted(Comparator.reverseOrder())  // 使用 Comparator.naturalOrder() 进行降序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                        mapByDayTime = mapByDayValue.stream()
                                .map(StaLocationClock::getTime)
                                .sorted(Comparator.reverseOrder())  // 使用 Comparator.naturalOrder() 进行降序排序
                                .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                    }
                } else {//2个班次4次卡升序
                    mapByDayTimeExpectDate = mapByDayValue.stream()
                            .map(StaLocationClock::getTimeExpect)
                            .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                            .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                    mapByDayTime = mapByDayValue.stream()
                            .map(StaLocationClock::getTime)
                            .sorted(Comparator.naturalOrder())  // 使用 Comparator.naturalOrder() 进行升序排序
                            .collect(Collectors.toCollection(LinkedHashSet::new));  // 使用 LinkedHashSet 来保持插入顺序
                }
                List<Date> mapByDayTimeExpect = new ArrayList<>(mapByDayTimeExpectDate);
                List<Date> dayTimeList = new ArrayList<>(mapByDayTime);
                //班次1时数
                Long morning = null;
                double workHours = 0;
                //班次2时数
                Long afternoon = null;
                double workHours2 = 0;
                //没有排班不做关联计算
                String start1 = "";
                String end1 = "";
                String start2 = "";
                String end2 = "";
                switch (mapByDayTimeExpect.size()) {
                    case 1: //签到记录条数1不做计算
                        break;
                    case 2:
                    case 3:
                        Date clockInTime1 = null;
                        Date clockInTime2 = null;
                        Date clockInTime1Expect = mapByDayTimeExpect.get(0); //要求签到时间
                        Date clockInTime2Expect = mapByDayTimeExpect.get(1); //要求签退时间
                        if (mapByDayTimeExpect.size() == mapByDayTime.size()) {
                            clockInTime1 = dayTimeList.get(0);
                            clockInTime2 = dayTimeList.get(1);
                        } else {
                            clockInTime1 = findClosestTime(dayTimeList, clockInTime1Expect);//实际签到时间
                            mapByDayTime.remove(clockInTime1);
                            clockInTime2 = findClosestTime(dayTimeList, clockInTime2Expect);//实际签退时间
                            mapByDayTime.remove(clockInTime2);
                        }
                        // 判断签到时间
                        if (clockInTime1.before(clockInTime1Expect)) {
                            start1 = sb.format(clockInTime1Expect); //提前签到使用要求时间
                        } else if (clockInTime1.after(clockInTime1Expect)) { //迟到
                            start1 = sb.format(clockInTime1);
                        } else {
                            start1 = sb.format(clockInTime1Expect);
                        }//相等

                        // 判断签退时间
                        if (clockInTime2.before(clockInTime2Expect)) {//早退
                            end1 = sb.format(clockInTime2);
                        } else if (clockInTime2.after(clockInTime2Expect)) {
                            end1 = sb.format(clockInTime2Expect);
                        } else {
                            end1 = sb.format(clockInTime2Expect);
                        }//相等
                        morning = DateUtils.calculateWorkingMinutes(start1, end1);
                        workHours = morning / 60.0; //班次1时数
                        break;
                    case 4: //两个班次 4次打卡
                        Date clockTime1Expect = mapByDayTimeExpect.get(0); //要求签到时间
                        Date clockTime2Expect = mapByDayTimeExpect.get(1); //要求签退时间
                        Date clockTime1 = dayTimeList.get(0);//实际签到时间
                        Date clockTime2 = dayTimeList.get(1);//实际签退时间
                        Date clockTime3Expect = mapByDayTimeExpect.get(2); //要求签到时间
                        Date clockTime4Expect = mapByDayTimeExpect.get(3);
                        ; //要求签退时间
                        Date clockTime3 = dayTimeList.get(2);//实际签到时间
                        Date clockTime4 = dayTimeList.get(3);//实际签退时间
                        //班次1
                        // 判断签到时间
                        if (clockTime1.before(clockTime1Expect)) {
                            start1 = sb.format(clockTime1Expect); //提前签到使用要求时间
                        } else if (clockTime1.after(clockTime1Expect)) { //迟到
                            start1 = sb.format(clockTime1);
                        } else {
                            start1 = sb.format(clockTime1Expect);
                        }//相等
                        // 判断签退时间
                        if (clockTime2.before(clockTime2Expect)) {//早退
                            end1 = sb.format(clockTime2);
                        } else if (clockTime2.after(clockTime2Expect)) {
                            end1 = sb.format(clockTime2Expect);
                        } else {
                            end1 = sb.format(clockTime2Expect);
                        }//相等
                        morning = DateUtils.calculateWorkingMinutes(start1, end1);
                        workHours = morning / 60.0; //班次1时数
                        //班次二
                        // 判断签到时间
                        if (clockTime3.before(clockTime3Expect)) {
                            start2 = sb.format(clockTime3Expect); //提前签到使用要求时间
                        } else if (clockTime3.after(clockTime3Expect)) { //迟到
                            start2 = sb.format(clockTime3);
                        } else {
                            start2 = sb.format(clockTime3Expect); //提前签到使用要求时间
                        }//相等
                        // 判断签退时间
                        if (clockTime4.before(clockTime4Expect)) {//早退
                            end2 = sb.format(clockTime4);
                        } else if (clockTime4.after(clockTime4Expect)) {
                            end2 = sb.format(clockTime4Expect);
                        } else {
                            end2 = sb.format(clockTime4Expect);
                        }//相等
                        afternoon = DateUtils.calculateWorkingMinutes(start2, end2);
                        double workingHours2 = afternoon / 60.0;
                        workHours2 = workingHours2; //班次2时数
                        break;
                    default: //无签到记录
                        break;
                }
                workingHours = workingHours + (workHours + workHours2);
            }
            String str = String.format("%.1f", workingHours);
            workingHoursMap.put(orderId, Double.parseDouble(str));
        }
        return workingHoursMap;
    }
    // 检查两个日期是否在同一天
    private static boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }
    // 将日期减去一天，时间不变
    private static Date subtractOneDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1); // 减去一天
        return cal.getTime();
    }
    /**
     * 判断两个日期是否出现跨夜
     *
     * @param dateList
     * @return
     */
    public static boolean crossesMidnight(List<Date> dateList) {
        if (dateList == null || dateList.size() < 2) {
            // 如果列表为空或只有一个日期，无法判断跨夜
            return false;
        }
        // 转换日期为小时和分钟
        List<Calendar> calendars = dateList.stream()
                .map(date -> {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    return calendar;
                })
                .collect(Collectors.toList());
        // 获取第一个和最后一个时间的小时和分钟
        Calendar startCalendar = calendars.get(0);
        Calendar endCalendar = calendars.get(calendars.size() - 1);
        int startHour = startCalendar.get(Calendar.HOUR_OF_DAY);
        int startMinute = startCalendar.get(Calendar.MINUTE);
        int endHour = endCalendar.get(Calendar.HOUR_OF_DAY);
        int endMinute = endCalendar.get(Calendar.MINUTE);
        // 判断是否跨夜
        if (endHour < startHour || (endHour == startHour && endMinute < startMinute)) {
            return true;
        }
        return false;
    }

    /**
     * 寻找接近当前时间的时间
     *
     * @param dateList
     * @param targetDate
     * @return
     */
    public static Date findClosestTime(List<Date> dateList, Date targetDate) {
        if (dateList == null || dateList.isEmpty() || targetDate == null) {
            return null;
        }
        Date closestDate = null;
        long minDiff = Long.MAX_VALUE;
        for (Date date : dateList) {
            long diff = Math.abs(date.getTime() - targetDate.getTime());
            if (diff < minDiff) {
                minDiff = diff;
                closestDate = date;
            }
            // 如果找到相等的时间，直接返回
            if (diff == 0) {
                return date;
            }
        }
        return closestDate;
    }

    @Override
    public List<Map<String, Object>> getSalaryExportData(String companyName, String enrollName, String
            startTime, String endTime) {
        DecimalFormat df = new DecimalFormat("0.0");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYY-MM-dd");
        Map<String, Double> schedulingMap = new TreeMap<>(); //key日期  value时数
        SysDepart sysDepart = new SysDepart();
        List<SysDepart> sysDeparts = new ArrayList<>();
        List<Map<String, Object>> salaryExportData = new ArrayList<>();
        LambdaQueryWrapper<StaOrder> staOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (WxlConvertUtils.isNotEmpty(companyName)) {
            sysDepart = sysDepartMapper.selectOne(new LambdaQueryWrapper<SysDepart>().eq(SysDepart::getDepartName, companyName));
            sysDeparts = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().likeRight(SysDepart::getOrgCode, sysDepart.getOrgCode()));
            staOrderLambdaQueryWrapper.likeRight(StaOrder::getSysOrgCode, sysDepart.getOrgCode());
        }
        if (WxlConvertUtils.isNotEmpty(enrollName)) {
            staOrderLambdaQueryWrapper.like(StaOrder::getEnrollName, enrollName);
        }
        List<StaOrder> staOrders = staOrderMapper.selectList(staOrderLambdaQueryWrapper);
        List<String> uids = staOrders.stream().map(StaOrder::getSysUserId).collect(Collectors.toList());
        if (uids.isEmpty()) {
            return null;
        }
        List<SysUser> sysUsers = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getId, uids)
                .isNotNull(SysUser::getHourlySalary));
        LambdaQueryWrapper<StaLocationClock> locationClockLambdaQueryWrapper = new LambdaQueryWrapper<>();
        locationClockLambdaQueryWrapper.between(StaLocationClock::getTime, startTime, endTime);
        locationClockLambdaQueryWrapper.isNotNull(StaLocationClock::getTimeExpect); //没有排班的签到记录不做计算
        locationClockLambdaQueryWrapper.eq(StaLocationClock::getDelFlag, CommonConstant.DEL_FLAG_0);
        if (!sysDeparts.isEmpty()) {
            locationClockLambdaQueryWrapper.in(StaLocationClock::getCompanyId, sysDeparts.stream().map(SysDepart::getId).collect(Collectors.toList()));
        }
        List<StaLocationClock> staLocationClocks = staLocationClockMapper.selectList(locationClockLambdaQueryWrapper);//签到
        //拆分签到集合 一个人对应Map一条数据集
        Map<String, List<StaLocationClock>> locationClocksMap = new HashMap<>();
        for (StaLocationClock staLocationClock : staLocationClocks) {
            String staOrderId = staLocationClock.getStaOrderId();
            if (!locationClocksMap.containsKey(staOrderId)) {
                locationClocksMap.put(staOrderId, new ArrayList<>());
            }
            locationClocksMap.get(staOrderId).add(staLocationClock);
        }
        for (Map.Entry<String, List<StaLocationClock>> entry : locationClocksMap.entrySet()) {
            String staOrderId = entry.getKey();
            List<StaLocationClock> value = entry.getValue();
            //key签到日期，value签到数据
            Map<Date, List<StaLocationClock>> mapByDay = value.stream()
                    .collect(Collectors.groupingBy(clock -> {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(clock.getTime());
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        cal.set(Calendar.MINUTE, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MILLISECOND, 0);
                        return cal.getTime();
                    }));
            //升序排序
            for (List<StaLocationClock> sublist : mapByDay.values()) {
                sublist.sort(Comparator.comparing(StaLocationClock::getTime));
            }
            //时数计算
            for (Map.Entry<Date, List<StaLocationClock>> mapByDayEntry : mapByDay.entrySet()) {
                Date key = mapByDayEntry.getKey();
                List<StaLocationClock> mapByDayValue = mapByDayEntry.getValue();
                List<Date> mapByDayTime = mapByDayValue.stream().map(StaLocationClock::getTime).collect(Collectors.toList());
                List<Date> mapByDayTimeExpect = mapByDayValue.stream().map(StaLocationClock::getTimeExpect).collect(Collectors.toList());
                long shiftDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTimeExpect);
                long totalWorkDuration = DateUtils.getDaysDifferenceBetweenDates(mapByDayTime);
                if (totalWorkDuration > shiftDuration) {
                    totalWorkDuration = shiftDuration;
                }
                Long hours = totalWorkDuration / 60;//小时
                Long remainingMinutes = totalWorkDuration % 60; //分钟
                String workHoursStr = remainingMinutes > 0 ? hours.toString() + '.' + remainingMinutes.toString() : hours.toString();
                double workHours = Double.parseDouble(workHoursStr); //当天时数
                if (workHours > 0) {
                    schedulingMap.put(simpleDateFormat.format(key), workHours);
                }
            }
            List<StaOrder> filteredOrders = staOrders.stream()
                    .filter(order -> order.getId().equals(staOrderId))
                    .collect(Collectors.toList());
            if (!filteredOrders.isEmpty()) {
                List<SysUser> filteredUsers = sysUsers.stream()
                        .filter(user -> user.getId().equals(filteredOrders.get(0).getSysUserId()))
                        .collect(Collectors.toList());
                //获取当前任务
                //时数
                double aboveWorkingHours = schedulingMap.entrySet().stream() //上半月时数
                        .filter(above -> {
                            String key = above.getKey();
                            int day = Integer.parseInt(key.split("-")[1]);
                            return day <= 15;
                        }).mapToDouble(Map.Entry::getValue).sum();
                double underWorkingHours = schedulingMap.entrySet().stream() //下半月时数
                        .filter(under -> {
                            String key = under.getKey();
                            int day = Integer.parseInt(key.split("-")[1]);
                            return day > 15;
                        }).mapToDouble(Map.Entry::getValue).sum();

                BigDecimal above = BigDecimal.valueOf(aboveWorkingHours);
                BigDecimal under = BigDecimal.valueOf(underWorkingHours);
                BigDecimal currentMonthWorkingHours = above.add(under).setScale(2, RoundingMode.HALF_UP);
                //工资
                BigDecimal firstHalfMonthSalary = BigDecimal.valueOf(0);//上半月工资
                BigDecimal secondHalfMonthSalary = BigDecimal.valueOf(0);//下半月工资
                BigDecimal currentMonthSalary = BigDecimal.valueOf(0); //当月工资
                BigDecimal hourlySalary = filteredOrders.get(0).getHourlySalary();
                Optional<BigDecimal> optional = Optional.ofNullable(hourlySalary);
                if (optional.isPresent()) {
                    if (aboveWorkingHours > 0) {
                        firstHalfMonthSalary = hourlySalary.multiply(BigDecimal.valueOf(aboveWorkingHours)).setScale(2, RoundingMode.HALF_UP);
                    }
                    if (underWorkingHours > 0) {
                        secondHalfMonthSalary = hourlySalary.multiply(BigDecimal.valueOf(underWorkingHours)).setScale(2, RoundingMode.HALF_UP);
                    }
                    currentMonthSalary = firstHalfMonthSalary.add(secondHalfMonthSalary);//当月工资
                } else {
                    //System.out.println("BigDecimal对象为空");
                }
                StaUserBankCards staUserBankCards = staUserBankCardsMapper.selectOne(new LambdaQueryWrapper<StaUserBankCards>()
                        .eq(StaUserBankCards::getUserId, filteredUsers.get(0).getId()));
                Map<String, Object> map = new HashMap<>();
                map.put("userName", filteredOrders.get(0).getEnrollName());//姓名
                map.put("companyName", filteredOrders.get(0).getCompanyName().replaceAll("\\(.*\\)", ""));//门店
                map.put("workName", filteredOrders.get(0).getWorkNameFull());//任务名称
                map.putAll(schedulingMap);
                map.put("hourlySalary", filteredOrders.get(0).getHourlySalary());//小时薪资
                map.put("aboveWorkingHours", df.format(aboveWorkingHours));//上半月时数
                map.put("underWorkingHours", df.format(underWorkingHours));//下半月时数
                map.put("currentMonthWorkingHours", currentMonthWorkingHours);//当月时数
                map.put("firstHalfMonthSalary", firstHalfMonthSalary);//上半月工资
                map.put("secondHalfMonthSalary", secondHalfMonthSalary);//下半月工资
                map.put("currentMonthSalary", currentMonthSalary);//当月工资
                if (WxlConvertUtils.isNotEmpty(staUserBankCards)) {
                    map.put("bankCards", staUserBankCards.getCardNumber());
                    map.put("bankName", staUserBankCards.getBankName());
                    map.put("phone", staUserBankCards.getPhone());
                }
                salaryExportData.add(map);
            }
            schedulingMap = new HashMap<>();
        }
        return salaryExportData;
    }

    /**
     * 店铺人员结算账户
     *
     * @param pageNo
     * @param pageSize
     * @param deptName
     * @param userName
     * @return
     */
    @Override
    public Page<SysUserBankVo> adminList(Integer pageNo, Integer pageSize, String deptName, String userName, String deptNo) {
        Page<SysUser> page = new Page<>(pageNo, pageSize);
        Page<SysUser> sysUserPage = staOrderMapper.getBankPage(page, deptName, deptNo, userName);
//        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        sysUserLambdaQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
//        sysUserLambdaQueryWrapper.eq(SysUser::getIsCard, CommonConstant.CARD_STATUS1);
//        sysUserLambdaQueryWrapper.eq(SysUser::getCurrentStores, deptName);
//        if (WxlConvertUtils.isNotEmpty(userName)) sysUserLambdaQueryWrapper.like(SysUser::getRealNameName, userName);
//        sysUserLambdaQueryWrapper.orderByDesc(SysUser::getCreateTime);
        //Page<SysUser> sysUserPage = sysUserMapper.selectPage(page, sysUserLambdaQueryWrapper);
        Map<String, StaUserBankCards> userBankCardsMap = new HashMap<>();
        Map<String, SysDepart> storesListMap = new HashMap<>();
        Map<String, SysDepart> regionListMap = new HashMap<>();
        Map<String, SysDepart> businessDivisionMap = new HashMap<>();
        List<SysUserBankVo> sysUserBankVos = new ArrayList<>();
        if (WxlConvertUtils.isNotEmpty(sysUserPage.getRecords()) && !sysUserPage.getRecords().isEmpty()) {
            List<String> userIdList = sysUserPage.getRecords().stream().map(SysUser::getId).collect(Collectors.toList());
            //门店id集合
            Set<String> storesIdList = sysUserPage.getRecords().stream().map(SysUser::getCurrentStoresId).collect(Collectors.toSet());
            if (!storesIdList.isEmpty()) {
                List<SysDepart> storesList = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getId, storesIdList)); //门店信息
                Set<String> regionIdList = storesList.stream().map(SysDepart::getParentId).collect(Collectors.toSet());//门店父id
                List<SysDepart> regionList = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getId, regionIdList)); //区域信息
                Set<String> businessDivisionIdList = regionList.stream().map(SysDepart::getParentId).collect(Collectors.toSet()); //区域父id
                List<SysDepart> businessDivisionList = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getId, businessDivisionIdList)); //事业处信息
                storesListMap = storesList.stream().collect(Collectors.toMap(SysDepart::getId, Function.identity())); //门店Map
                regionListMap = regionList.stream().collect(Collectors.toMap(SysDepart::getId, Function.identity()));//区域Map
                businessDivisionMap = businessDivisionList.stream().collect(Collectors.toMap(SysDepart::getId, Function.identity())); //事业处Map
            }
            //结算账户
            List<StaUserBankCards> staUserBankCards = staUserBankCardsMapper.selectList(new QueryWrapper<StaUserBankCards>().lambda().in(StaUserBankCards::getUserId, userIdList));
            userBankCardsMap = staUserBankCards.stream().collect(Collectors.toMap(StaUserBankCards::getUserId, Function.identity()));
            for (SysUser record : sysUserPage.getRecords()) {
                SysUserBankVo sysUserBankVo = new SysUserBankVo();
                //事业处  区域  店编 店别
                SysDepart storesDepart = storesListMap.get(record.getCurrentStoresId()); //门店
                if (WxlConvertUtils.isNotEmpty(storesDepart)) {
                    //添加门店信息
                    sysUserBankVo.setStoreNo(storesDepart.getStoreNo()); //店编
                    sysUserBankVo.setCompanyName(storesDepart.getDepartName());//店别
                    //区域
                    SysDepart regionDepart = regionListMap.get(storesDepart.getParentId());
                    sysUserBankVo.setStoreRegionName(regionDepart.getDepartName());
                    //事业处
                    SysDepart businessDivisionDepart = businessDivisionMap.get(regionDepart.getParentId());
                    sysUserBankVo.setBusinessDivisionName(businessDivisionDepart.getDepartName());
                }
                sysUserBankVo.setPhone(record.getUsername()); //电话
                sysUserBankVo.setUserName(record.getRealNameName());//姓名
                sysUserBankVo.setIdCard(record.getIdCard()); //身份证
                sysUserBankVo.setBankName(userBankCardsMap.get(record.getId()).getBankName()); //账户名称
                sysUserBankVo.setCardNumber(userBankCardsMap.get(record.getId()).getCardNumber());//账户
                sysUserBankVos.add(sysUserBankVo);
            }
        }
        Page<SysUserBankVo> sysUserBankVoPage = new Page<>();
        sysUserBankVoPage.setRecords(sysUserBankVos);
        sysUserBankVoPage.setSize(sysUserPage.getSize());
        sysUserBankVoPage.setPages(sysUserPage.getPages());
        sysUserBankVoPage.setCurrent(sysUserPage.getCurrent());
        sysUserBankVoPage.setTotal(sysUserPage.getTotal());
        return sysUserBankVoPage;
    }

    @Override
    public List<SysUserBankVo> exportXlsBank(String deptName, String deptNo, String userName) {
        List<SysUser> sysUserList = staOrderMapper.getBankPage(deptName, deptNo, userName);
//        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        sysUserLambdaQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
//        sysUserLambdaQueryWrapper.eq(SysUser::getIsCard, CommonConstant.CARD_STATUS1);
//        if (WxlConvertUtils.isNotEmpty(deptName)) sysUserLambdaQueryWrapper.like(SysUser::getCurrentStores, deptName);
//        if (WxlConvertUtils.isNotEmpty(userName)) sysUserLambdaQueryWrapper.like(SysUser::getRealNameName, userName);
//        sysUserLambdaQueryWrapper.orderByDesc(SysUser::getCreateTime);
        // List<SysUser> sysUserList = sysUserMapper.selectList(sysUserLambdaQueryWrapper);
        Map<String, StaUserBankCards> userBankCardsMap = new HashMap<>();
        Map<String, SysDepart> storesListMap = new HashMap<>();
        Map<String, SysDepart> regionListMap = new HashMap<>();
        Map<String, SysDepart> businessDivisionMap = new HashMap<>();
        List<SysUserBankVo> sysUserBankVos = new ArrayList<>();
        if (WxlConvertUtils.isNotEmpty(sysUserList) && !sysUserList.isEmpty()) {
            List<String> userIdList = sysUserList.stream().map(SysUser::getId).collect(Collectors.toList());
            Set<String> storesIdList = sysUserList.stream().map(SysUser::getCurrentStoresId).collect(Collectors.toSet());
            if (!storesIdList.isEmpty()) {
                List<SysDepart> storesList = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getId, storesIdList)); //门店信息
                Set<String> regionIdList = storesList.stream().map(SysDepart::getParentId).collect(Collectors.toSet());//门店父id
                List<SysDepart> regionList = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getId, regionIdList)); //区域信息
                Set<String> businessDivisionIdList = regionList.stream().map(SysDepart::getParentId).collect(Collectors.toSet()); //区域父id
                List<SysDepart> businessDivisionList = sysDepartMapper.selectList(new LambdaQueryWrapper<SysDepart>().in(SysDepart::getId, businessDivisionIdList)); //事业处信息
                storesListMap = storesList.stream().collect(Collectors.toMap(SysDepart::getId, Function.identity())); //门店Map
                regionListMap = regionList.stream().collect(Collectors.toMap(SysDepart::getId, Function.identity()));//区域Map
                businessDivisionMap = businessDivisionList.stream().collect(Collectors.toMap(SysDepart::getId, Function.identity())); //事业处Map
            }
            //结算账户
            List<StaUserBankCards> staUserBankCards = staUserBankCardsMapper.selectList(new QueryWrapper<StaUserBankCards>().lambda().in(StaUserBankCards::getUserId, userIdList));
            userBankCardsMap = staUserBankCards.stream().collect(Collectors.toMap(StaUserBankCards::getUserId, Function.identity()));
            for (SysUser sysUser : sysUserList) {
                SysUserBankVo sysUserBankVo = new SysUserBankVo();
                //事业处  区域  店编 店别
                SysDepart storesDepart = storesListMap.get(sysUser.getCurrentStoresId()); //门店
                if (WxlConvertUtils.isNotEmpty(storesDepart)) {
                    //添加门店信息
                    sysUserBankVo.setStoreNo(storesDepart.getStoreNo()); //店编
                    sysUserBankVo.setCompanyName(storesDepart.getDepartName());//店别
                    //区域
                    SysDepart regionDepart = regionListMap.get(storesDepart.getParentId());
                    sysUserBankVo.setStoreRegionName(regionDepart.getDepartName());
                    //事业处
                    SysDepart businessDivisionDepart = businessDivisionMap.get(regionDepart.getParentId());
                    sysUserBankVo.setBusinessDivisionName(businessDivisionDepart.getDepartName());
                }
                sysUserBankVo.setPhone(sysUser.getUsername()); //电话
                sysUserBankVo.setUserName(sysUser.getRealNameName());//姓名
                sysUserBankVo.setIdCard(sysUser.getIdCard()); //身份证
                sysUserBankVo.setBankName(userBankCardsMap.get(sysUser.getId()).getBankName()); //账户名称
                sysUserBankVo.setCardNumber(userBankCardsMap.get(sysUser.getId()).getCardNumber());//账户
                sysUserBankVos.add(sysUserBankVo);
            }
        }
        return sysUserBankVos;
    }

    /**
     * 创建银行卡认证接口连接
     *
     * @param appCode
     * @param url
     * @param params
     * @return
     * @throws IOException
     */
    public static String get(String appCode, String url, Map<String, String> params) throws IOException {
        url = url + buildRequestUrl(params);
        OkHttpClient client = new OkHttpClient.Builder().build();
        Request request = new Request.Builder().url(url).addHeader("Authorization", "APPCODE " + appCode).build();
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        return result;
    }

    public static String buildRequestUrl(Map<String, String> params) {
        StringBuilder url = new StringBuilder("?");
        Iterator<String> it = params.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next();
            url.append(key).append("=").append(params.get(key)).append("&");
        }
        return url.toString().substring(0, url.length() - 1);
    }

}
