package com.byun.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.DateUtil;
import com.byun.common.util.DateUtils;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.byun.modules.staffing.mapper.StaEnrollInfoMapper;
import com.byun.modules.system.entity.StaUserHealthyId;
import com.byun.modules.system.entity.SysAnnouncement;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.mapper.StaUserHealthyIdMapper;
import com.byun.modules.system.mapper.SysUserMapper;
import com.byun.modules.system.service.IStaUserHealthyIdService;
import com.byun.modules.system.service.ISysAnnouncementService;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description
 * @date : 2023-7-25 16:26
 */
@Service
//@SuppressWarnings("all")
public class StaUserHealthyIdServiceImpl extends ServiceImpl<StaUserHealthyIdMapper, StaUserHealthyId> implements IStaUserHealthyIdService {
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;
    @Autowired
    private StaEnrollInfoMapper staEnrollInfoMapper;
    @Autowired
    private StaUserHealthyIdMapper staUserHealthyIdMapper;

    @Override
    public Result add(StaUserHealthyId staUserHealthyId) {
        if (staUserHealthyId == null) {
            return Result.error(400, "参数丢失");
        }
        String clientType0 = CommonConstant.CLIENT_TYPE_0;
        String clientType1 = CommonConstant.CLIENT_TYPE_1;
        try {
            if (clientType0.equals(staUserHealthyId.getType())) {
                SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("username", staUserHealthyId.getPhone()));
//                LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//                lambdaQueryWrapper.eq(StaUserHealthyId::getSysUserId, sysUser.getId());
//                lambdaQueryWrapper.in(StaUserHealthyId::getStatus, Arrays.asList(CommonConstant.DEL_FLAG_0));
//                StaUserHealthyId suh = this.getOne(lambdaQueryWrapper);
                // if (WxlConvertUtils.isEmpty(suh)) {
                return processUserHealthyId(staUserHealthyId, sysUser);
//                } else {
//                    return Result.error(400, "当前用户已有健康证");
//                }
            } else if (clientType1.equals(staUserHealthyId.getType())) {
                SysUser sysUser = sysUserMapper.selectById(staUserHealthyId.getSysUserId());
                return processUserHealthyId(staUserHealthyId, sysUser);
            } else {
                return Result.error(400, "系统异常");
            }
        } catch (Exception e) {
            return Result.error(500, "操作失败：" + e.getMessage());
        }
    }

    /**
     * 通过健康证审核
     * TODO 后期使用微信推送消息
     *
     * @param staUserHealthyId
     */
    @Override
    @Transactional
    public void userIdentityApproved(StaUserHealthyId staUserHealthyId) {
        //修改健康证
        staUserHealthyId.setStatus(CommonConstant.HEALTHY_ID_STATUS_1); //健康证有效
        this.updateById(staUserHealthyId);
        //修改用户信息
        SysUser sysUser = sysUserMapper.selectById(staUserHealthyId.getSysUserId());
        sysUser.setHealthCardStatus(CommonConstant.GENNERASTATUS0); //有健康证
        sysUserMapper.updateById(sysUser);
        //修改申请信息
        StaEnrollInfo staEnrollInfo = staEnrollInfoMapper.selectOne(new QueryWrapper<StaEnrollInfo>().eq("sys_user_id", staUserHealthyId.getSysUserId()));
        staEnrollInfo.setIdHealthValidity(CommonConstant.GENNERASTATUS1);//有健康证
        staEnrollInfo.setIdHealthValidityDate(CommonConstant.GENNERASTATUS1);//健康证有效期内
        staEnrollInfo.setIdHealthStartDate(staUserHealthyId.getStartDate());//健康证办理日期体检日期
        staEnrollInfo.setIdHealthEndDate(staUserHealthyId.getEndDate());//健康证有效期
        staEnrollInfoMapper.updateById(staEnrollInfo);
        if (!staUserHealthyId.getSysUserId().isEmpty()) {
            SysAnnouncement sysAnnouncement = new SysAnnouncement();
            sysAnnouncement.setUserIds(staUserHealthyId.getSysUserId() + ",");//推送用户
            String msgAbstract = "审批通过";
            sysAnnouncement.setMsgAbstract(msgAbstract);
            String title = "健康证审批";
            sysAnnouncement.setTitile(title);
            sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_1);//通知公告
            sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
            sysAnnouncement.setPriority(CommonConstant.PRIORITY_T);//优先级  成功
            sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
            sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
            sysAnnouncement.setSendTime(new Date());
            sysAnnouncement.setSender(sysUser.getRealNameName());
            sysAnnouncement.setCreateBy(sysUser.getRealNameName());
            sysAnnouncement.setCreateTime(new Date());
            sysAnnouncementService.saveAnnouncement(sysAnnouncement);
        }
    }

    @Transactional
    @Override
    public boolean rejectApproval(String id, String sysUserId, String refuse) {
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            StaUserHealthyId staUserHealthyId = this.getById(id);
            staUserHealthyId.setDescription(refuse);//驳回原因
            staUserHealthyId.setStatus(CommonConstant.HEALTHY_ID_STATUS_2);//驳回状态
            this.updateById(staUserHealthyId);
            //消息推送
            if (!sysUserId.isEmpty() && WxlConvertUtils.isNotEmpty(user)) {
                SysAnnouncement sysAnnouncement = new SysAnnouncement();
                sysAnnouncement.setUserIds(sysUserId + ",");//推送用户
                String msgAbstract = refuse;
                sysAnnouncement.setMsgAbstract(msgAbstract);
                String title = "健康证审批";
                sysAnnouncement.setTitile(title);
                sysAnnouncement.setMsgCategory(CommonConstant.MSG_CATEGORY_1);//通知公告
                sysAnnouncement.setMsgType(CommonConstant.MSG_TYPE_UESR);//指定用户发送
                sysAnnouncement.setPriority(CommonConstant.PRIORITY_F);//优先级  失败
                sysAnnouncement.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
                sysAnnouncement.setSendStatus(CommonSendStatus.PUBLISHED_STATUS_1);//发布
                sysAnnouncement.setSendTime(new Date());
                sysAnnouncement.setSender(user.getRealname());
                sysAnnouncement.setCreateBy(user.getRealname());
                sysAnnouncement.setCreateTime(new Date());
                sysAnnouncementService.saveAnnouncement(sysAnnouncement);
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getExoortData(LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper) {
        List<Map<String, Object>> exportData = new ArrayList<>();
        List<StaUserHealthyId> staUserHealthyIds = staUserHealthyIdMapper.selectList(lambdaQueryWrapper);
        for (StaUserHealthyId staUserHealthyId : staUserHealthyIds) {
            Map<String, Object> mp = new HashMap<String, Object>() {{
                put("createTime", DateUtils.date2Str(staUserHealthyId.getCreateTime(), new SimpleDateFormat("yyyy-MM-dd")));
                put("userName", staUserHealthyId.getUserName());
                put("idCard", staUserHealthyId.getIdCard());
                put("phone", staUserHealthyId.getPhone());
                put("avatar", staUserHealthyId.getAvatar());
                put("endDate", DateUtils.date2Str(staUserHealthyId.getEndDate(), new SimpleDateFormat("yyyy-MM-dd")));
                put("status", staUserHealthyId.getStatus());
            }};
            exportData.add(mp);
        }
        return exportData;
    }
//                switch (staUserHealthyId.getStatus()) {
//                    case 0:
//                        put("status", "待审核");
//                        break;
//                    case 1:
//                        put("status", "正常");
//                        break;
//                    case 2:
//                        put("status", "无效");
//                        break;
//                    case 3:
//                        put("status", "已过期");
//                        break;
//                    default:
//                        put("status", "未知");
//                        break;
//                }


    private Result processUserHealthyId(StaUserHealthyId staUserHealthyId, SysUser sysUser) {
        if (WxlConvertUtils.isEmpty(sysUser)) {
            return Result.error(500, "用户不存在");
        }
        //是修改还是新增
        boolean flag = false;
        if (staUserHealthyId.getId() == null) {//新增赋值
            staUserHealthyId = prepareData(staUserHealthyId, sysUser);
//            LambdaQueryWrapper<StaUserHealthyId> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(StaUserHealthyId::getSysUserId,sysUser.getId());
//            lambdaQueryWrapper.in(StaUserHealthyId::getStatus, Arrays.asList(CommonConstant.DEL_FLAG_0));
//            StaUserHealthyId suh = this.getOne(lambdaQueryWrapper);
//            if (WxlConvertUtils.isEmpty(suh)){
            flag = save(staUserHealthyId);
            //   }
        } else {
            //修改
            staUserHealthyId.setStatus(CommonConstant.HEALTHY_ID_STATUS_0);
            staUserHealthyId.setUpdateTime(new Date());
            flag = updateById(staUserHealthyId);
        }
        return flag ? Result.OK("操作成功") : Result.error(500, "操作失败");
    }

    private StaUserHealthyId prepareData(StaUserHealthyId staUserHealthyId, SysUser sysUser) {
        staUserHealthyId.setCreateTime(new Date());
        staUserHealthyId.setUpdateTime(new Date());
        staUserHealthyId.setDelFlag(CommonConstant.DEL_FLAG_0);
        staUserHealthyId.setSysUserId(sysUser.getId());
        staUserHealthyId.setIdCard(sysUser.getIdCard());
        staUserHealthyId.setUserName(sysUser.getRealNameName());
        staUserHealthyId.setPhone(sysUser.getUsername());
        staUserHealthyId.setStatus(CommonConstant.HEALTHY_ID_STATUS_0); // 待审核
        return staUserHealthyId;
    }
}
