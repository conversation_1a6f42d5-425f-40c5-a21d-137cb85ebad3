package com.byun.modules.system.service.impl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.util.WxlConvertUtils;
import com.byun.common.util.RestUtil;
import com.byun.common.util.wechat.AppletUtil;
import com.byun.modules.system.entity.SysApplet;
import com.byun.modules.system.mapper.SysAppletMapper;
import com.byun.modules.system.service.ISysAppletService;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
/**
 * @Description: 小程序
 * @Author: bai
 * @Date:   2021-11-28
 * @Version: V1.0
 */
@Service
public class SysAppletServiceImpl extends ServiceImpl<SysAppletMapper, SysApplet> implements ISysAppletService {

    @Override
    @Transactional
    public String resetAccessToken() {
        String ret="";
        String accessTokenUrl = AppletUtil.getAccessTokenUrl();
        ResponseEntity<JSONObject> request = RestUtil.request(accessTokenUrl, HttpMethod.GET, null, null, null, JSONObject.class);
        JSONObject body = request.getBody();
        String accessToken = String.valueOf(body.get("access_token"));
        if(WxlConvertUtils.isEmpty(accessToken)){
            ret+="获取灵工小程序Accesstoken失败 ";
            return ret;
        }
        LambdaQueryWrapper<SysApplet> sysAppletLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysAppletLambdaQueryWrapper.eq(SysApplet::getAppid,AppletUtil.getStaAppid());
        SysApplet sysApplet = baseMapper.selectOne(sysAppletLambdaQueryWrapper);
        sysApplet.setAccessToken(accessToken);
        baseMapper.updateById(sysApplet);
        ret+="--成功--灵工小程序AccessToken:"+accessTokenUrl;
        return ret;
    }
}
