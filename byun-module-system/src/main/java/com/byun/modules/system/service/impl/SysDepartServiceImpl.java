package com.byun.modules.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.modules.staffing.entity.StaWork;
import com.byun.modules.staffing.mapper.StaWorkMapper;
import com.byun.modules.staffing.model.StaDepartIdModel;
import com.byun.modules.staffing.model.StaDepartModel;
import com.byun.modules.staffing.model.StaDepartTreeModel;
import com.byun.modules.system.service.ISysDepartPermissionService;
import com.byun.modules.system.service.ISysDepartRolePermissionService;
import com.byun.modules.system.service.ISysDepartRoleService;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang.StringUtils;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.constant.FillRuleConstant;
import com.byun.common.util.FillRuleUtil;
import com.byun.common.util.YouBianCodeUtil;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.mapper.*;
import com.byun.modules.system.model.DepartIdModel;
import com.byun.modules.system.model.SysDepartTreeModel;
import com.byun.modules.system.service.ISysDepartService;
import com.byun.modules.system.util.FindsDepartsChildrenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

/**
 * <p>
 * 部门表 服务实现类
 * <p>
 * 
 * <AUTHOR>
 * @Since 2019-01-22
 */
@Service
public class SysDepartServiceImpl extends ServiceImpl<SysDepartMapper, SysDepart> implements ISysDepartService {

	@Autowired
	private SysUserDepartMapper userDepartMapper;
	@Autowired
	private SysDepartRoleMapper sysDepartRoleMapper;
	@Autowired
	private SysDepartPermissionMapper departPermissionMapper;
	@Autowired
	private SysDepartRolePermissionMapper departRolePermissionMapper;
	@Autowired
	private SysDepartRoleUserMapper departRoleUserMapper;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private StaWorkMapper staWorkMapper;
	@Autowired
	private SysDepartMapper sysDepartMapper;
	@Autowired
	private ISysDepartPermissionService sysDepartPermissionService;
	@Autowired
	private ISysDepartRoleService sysDepartRoleService;
	@Autowired
	private ISysDepartRolePermissionService sysDepartRolePermissionService;

	@Override
	public Page<StaDepartModel> listDepartDistancePage(Page<StaDepartModel> pageList, StaDepartModel staDepartModel){
		return pageList.setRecords(baseMapper.listDepartDistance(pageList, staDepartModel));
	}

	@Override
	public List<StaDepartTreeModel> queryStaMyDeptTreeList(String departIds) {
		//根据部门id获取所负责部门
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		String[] codeArr = this.getMyDeptParentOrgCode(departIds);
		for(int i=0;i<codeArr.length;i++){
			query.or().likeRight(SysDepart::getOrgCode,codeArr[i]);
		}
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		query.orderByAsc(SysDepart::getDepartOrder);
		//将父节点ParentId设为null
		List<SysDepart> listDepts = this.list(query);
		for(int i=0;i<codeArr.length;i++){
			for(SysDepart dept : listDepts){
				if(dept.getOrgCode().equals(codeArr[i])){
					dept.setParentId(null);
				}
			}
		}
		// 调用wrapTreeDataToTreeList方法生成树状数据
		List<StaDepartTreeModel> listResult = wrapStaTreeDataToTreeList(listDepts);
		return listResult;
	}

	@Override
	public List<SysDepartTreeModel> queryMyDeptTreeList(String departIds) {
		//根据部门id获取所负责部门
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		String[] codeArr = this.getMyDeptParentOrgCode(departIds);
		for(int i=0;i<codeArr.length;i++){
			query.or().likeRight(SysDepart::getOrgCode,codeArr[i]);
		}
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		query.orderByAsc(SysDepart::getDepartOrder);
		//将父节点ParentId设为null
		List<SysDepart> listDepts = this.list(query);
		for(int i=0;i<codeArr.length;i++){
			for(SysDepart dept : listDepts){
				if(dept.getOrgCode().equals(codeArr[i])){
					dept.setParentId(null);
				}
			}
		}
		for (SysDepart listDept : listDepts) {
			String originalString  = listDept.getDepartName();
			// 使用正则表达式匹配括号内的内容
			java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\((.*?)\\)");
			java.util.regex.Matcher matcher = pattern.matcher(originalString);
			// 如果找到匹配项，则进行处理
			if (matcher.find()) {
				// 获取括号内的内容
				String insideParentheses = matcher.group(1);
				// 构建新的字符串，将括号内的内容移到最前面
				String modifiedString = insideParentheses + originalString.replace("(" + insideParentheses + ")", "");
				// 修改后的字符串
				listDept.setDepartName(modifiedString);
				//System.out.println();
			} else {
				// 如果未找到匹配项，则原字符串保持不变
			}
		}
		// 调用wrapTreeDataToTreeList方法生成树状数据
		List<SysDepartTreeModel> listResult = FindsDepartsChildrenUtil.wrapTreeDataToTreeList(listDepts);
		return listResult;
	}

	/**
	 * queryTreeList 对应 queryTreeList 查询所有的部门数据,以树结构形式响应给前端
	 */
	@Override
	@Cacheable(value = CacheConstant.SYS_DEPARTS_CACHE)
	public List<StaDepartTreeModel> queryStaTreeList() {
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		query.orderByAsc(SysDepart::getDepartOrder);
		List<SysDepart> list = this.list(query);
		// 调用wrapTreeDataToTreeList方法生成树状数据
		List<StaDepartTreeModel> listResult = wrapStaTreeDataToTreeList(list);
		return listResult;
	}
	/**
	 * queryTreeList 对应 queryTreeList 查询所有的部门数据,以树结构形式响应给前端
	 */
	@Override
	@Cacheable(value = CacheConstant.SYS_DEPARTS_CACHE)
	public List<SysDepartTreeModel> queryTreeList() {
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		query.orderByAsc(SysDepart::getDepartOrder);
		List<SysDepart> list = this.list(query);
		// 调用wrapTreeDataToTreeList方法生成树状数据
		List<SysDepartTreeModel> listResult = FindsDepartsChildrenUtil.wrapTreeDataToTreeList(list);
		return listResult;
	}

	/**
	 * queryTreeList 根据部门id查询,前端回显调用
	 */
	@Override
	public List<StaDepartTreeModel> queryStaTreeList(String ids) {
		List<StaDepartTreeModel> listResult=new ArrayList<>();
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		if(WxlConvertUtils.isNotEmpty(ids)){
			query.in(true,SysDepart::getId,ids.split(","));
		}
		query.orderByAsc(SysDepart::getDepartOrder);
		List<SysDepart> list= this.list(query);
		for (SysDepart depart : list) {
			listResult.add(new StaDepartTreeModel(depart));
		}
		return  listResult;

	}

	/**
	 * queryTreeList 根据部门id查询,前端回显调用
	 */
	@Override
	public List<SysDepartTreeModel> queryTreeList(String ids) {
		List<SysDepartTreeModel> listResult=new ArrayList<>();
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		if(WxlConvertUtils.isNotEmpty(ids)){
			query.in(true,SysDepart::getId,ids.split(","));
		}
		query.orderByAsc(SysDepart::getDepartOrder);
		List<SysDepart> list= this.list(query);
		for (SysDepart depart : list) {
			listResult.add(new SysDepartTreeModel(depart));
		}
		return  listResult;

	}

	@Cacheable(value = CacheConstant.SYS_DEPART_IDS_CACHE)
	@Override
	public List<DepartIdModel> queryDepartIdTreeList() {
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		query.orderByAsc(SysDepart::getDepartOrder);
		List<SysDepart> list = this.list(query);
		// 调用wrapTreeDataToTreeList方法生成树状数据
		List<DepartIdModel> listResult = FindsDepartsChildrenUtil.wrapTreeDataToDepartIdTreeList(list);
		return listResult;
	}

	/**
	 * saveDepartData 对应 add 保存用户在页面添加的新的部门对象数据
	 */
	@Override
	@Transactional
	public void saveDepartData(SysDepart sysDepart, String username) {
		if (sysDepart != null && username != null) {
			if (sysDepart.getParentId() == null) {
				sysDepart.setParentId("");
			}
			String s = UUID.randomUUID().toString().replace("-", "");
			sysDepart.setId(s);
			// 先判断该对象有无父级ID,有则意味着不是最高级,否则意味着是最高级
			// 获取父级ID
			String parentId = sysDepart.getParentId();
			//update-begin：部门编码规则生成器做成公用配置
			JSONObject formData = new JSONObject();
			formData.put("parentId",parentId);
			String[] codeArray = (String[]) FillRuleUtil.executeRule(FillRuleConstant.DEPART,formData);
			//update-end：部门编码规则生成器做成公用配置
			sysDepart.setOrgCode(codeArray[0]);
			String orgType = codeArray[1];
			sysDepart.setOrgType(String.valueOf(orgType));
			sysDepart.setCreateTime(new Date());
			sysDepart.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
			this.save(sysDepart);
		}

	}
	
	/**
	 * saveDepartData 的调用方法,生成部门编码和部门类型（作废逻辑）
	 * @deprecated
	 * @param parentId
	 * @return
	 */
	private String[] generateOrgCode(String parentId) {	
		//update-begin：组织机构添加数据代码调整
				LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
				LambdaQueryWrapper<SysDepart> query1 = new LambdaQueryWrapper<SysDepart>();
				String[] strArray = new String[2];
		        // 创建一个List集合,存储查询返回的所有SysDepart对象
		        List<SysDepart> departList = new ArrayList<>();
				// 定义新编码字符串
				String newOrgCode = "";
				// 定义旧编码字符串
				String oldOrgCode = "";
				// 定义部门类型
				String orgType = "";
				// 如果是最高级,则查询出同级的org_code, 调用工具类生成编码并返回
				if (StringUtil.isNullOrEmpty(parentId)) {
					// 线判断数据库中的表是否为空,空则直接返回初始编码
					query1.eq(SysDepart::getParentId, "").or().isNull(SysDepart::getParentId);
					query1.orderByDesc(SysDepart::getOrgCode);
					departList = this.list(query1);
					if(departList == null || departList.size() == 0) {
						strArray[0] = YouBianCodeUtil.getNextYouBianCode(null);
						strArray[1] = "1";
						return strArray;
					}else {
					SysDepart depart = departList.get(0);
					oldOrgCode = depart.getOrgCode();
					orgType = depart.getOrgType();
					newOrgCode = YouBianCodeUtil.getNextYouBianCode(oldOrgCode);
					}
				} else { // 反之则查询出所有同级的部门,获取结果后有两种情况,有同级和没有同级
					// 封装查询同级的条件
					query.eq(SysDepart::getParentId, parentId);
					// 降序排序
					query.orderByDesc(SysDepart::getOrgCode);
					// 查询出同级部门的集合
					List<SysDepart> parentList = this.list(query);
					// 查询出父级部门
					SysDepart depart = this.getById(parentId);
					// 获取父级部门的Code
					String parentCode = depart.getOrgCode();
					// 根据父级部门类型算出当前部门的类型
					orgType = String.valueOf(Integer.valueOf(depart.getOrgType()) + 1);
					// 处理同级部门为null的情况
					if (parentList == null || parentList.size() == 0) {
						// 直接生成当前的部门编码并返回
						newOrgCode = YouBianCodeUtil.getSubYouBianCode(parentCode, null);
					} else { //处理有同级部门的情况
						// 获取同级部门的编码,利用工具类
						String subCode = parentList.get(0).getOrgCode();
						// 返回生成的当前部门编码
						newOrgCode = YouBianCodeUtil.getSubYouBianCode(parentCode, subCode);
					}
				}
				// 返回最终封装了部门编码和部门类型的数组
				strArray[0] = newOrgCode;
				strArray[1] = orgType;
				return strArray;
		//update-end：组织机构添加数据代码调整
	} 

	
	/**
	 * removeDepartDataById 对应 delete方法 根据ID删除相关部门数据
	 * 
	 */
	/*
	 * @Override
	 * 
	 * @Transactional public boolean removeDepartDataById(String id) {
	 * System.out.println("要删除的ID 为=============================>>>>>"+id); boolean
	 * flag = this.removeById(id); return flag; }
	 */

	/**
	 * updateDepartDataById 对应 edit 根据部门主键来更新对应的部门数据
	 */
	@Override
	@Transactional
	public Boolean updateDepartDataById(SysDepart sysDepart, String username) {
		if (sysDepart != null && username != null) {
			sysDepart.setUpdateTime(new Date());
			sysDepart.setUpdateBy(username);
			this.updateById(sysDepart);
			return true;
		} else {
			return false;
		}

	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteBatchWithChildren(List<String> ids) {
		List<String> idList = new ArrayList<String>();
		for(String id: ids) {
			idList.add(id);
			this.checkChildrenExists(id, idList);
		}
		this.removeByIds(idList);
		//根据部门id获取部门角色id
		List<String> roleIdList = new ArrayList<>();
		LambdaQueryWrapper<SysDepartRole> query = new LambdaQueryWrapper<>();
		query.select(SysDepartRole::getId).in(SysDepartRole::getDepartId, idList);
		List<SysDepartRole> depRoleList = sysDepartRoleMapper.selectList(query);
		for(SysDepartRole deptRole : depRoleList){
			roleIdList.add(deptRole.getId());
		}
		//根据部门id删除用户与部门关系
		userDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getDepId,idList));
		//根据部门id删除部门授权
		departPermissionMapper.delete(new LambdaQueryWrapper<SysDepartPermission>().in(SysDepartPermission::getDepartId,idList));
		//根据部门id删除部门角色
		sysDepartRoleMapper.delete(new LambdaQueryWrapper<SysDepartRole>().in(SysDepartRole::getDepartId,idList));
		if(roleIdList != null && roleIdList.size()>0){
			//根据角色id删除部门角色授权
			departRolePermissionMapper.delete(new LambdaQueryWrapper<SysDepartRolePermission>().in(SysDepartRolePermission::getRoleId,roleIdList));
			//根据角色id删除部门角色用户信息
			departRoleUserMapper.delete(new LambdaQueryWrapper<SysDepartRoleUser>().in(SysDepartRoleUser::getDroleId,roleIdList));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void initializePermissionsBatchDepart(List<String> ids) {
		String permissionIds =
		"d7d6e2e4e2934f2c9385a623fd98c6f3,"
		+ "5c2f42277948043026b7a14692456828,"
		+ "1557179837016432642,"
		+ "1557179700764467201,"
		+ "1557178227318054913,"
		+ "1552852995464724481,"
		+ "1543834617538363394,"
		+ "1470303721408184322,"
		+ "1463345805689442306,"
		+ "1476365556301451265,"
		+ "1476075321508253697,"
		+ "1476075183926693890,"
		+ "1476074820825796609,"
		+ "1476074562959986690,"
		+ "1466735175910596610,"
		+ "1461286150004539394,"
		+ "1461285909385707521,"
		+ "1456296054301163521,"
		+ "1478981537662251010,"
		+ "1478981414068695042,"
		+ "1478981177296039938,"
		+ "1475340142070210561,"
		+ "1474269264184729602,"
		+ "1474269085490601986,"
		+ "1478276711164981250,"
		+ "1478276425469964289,"
		+ "1474266686235467778,"
		+ "1474266506664730626,"
		+ "1474266362850435073,"
		+ "1474265955268943873,"
		+ "1474265667501940738,"
		+ "1474265302576521218,"
		+ "1474265117460914177,"
		+ "1474264849797210113,"
		+ "1474264660109811713,"
		+ "1474264532728799234,"
		+ "1474264353694932993,"
		+ "1474264127106048002,"
		+ "1474263901515407362,"
		+ "1474263484811304961,"
		+ "1474262874078699521,"
		+ "1461504412516728833,"
		+ "1461284232612020225,"
		+ "1461284021911158786,"
		+ "1461283941548294145,"
		+ "1461283860837302273,"
		+ "1461236049961848834,"
		+ "1474272029896204290,"
		+ "1474270168178876417,"
		+ "1474262066813591554,"
		+ "1474261712852082690,"
		+ "1461241263473332225,"
		+ "1456294983365328897";

		String permissionIds1 ="1456294983365328897,1461241263473332225,1474261712852082690,1474270168178876417,1474272029896204290,1461236049961848834,1461283941548294145,1461284021911158786,1461284232612020225,1461504412516728833,1474262874078699521,1474266362850435073,1474266506664730626,1474266686235467778,1478276425469964289,1474263484811304961,1474263901515407362,1474264127106048002,1474264353694932993,1474264532728799234,1474264660109811713,1474264849797210113,1474265117460914177,1474265302576521218,1474265955268943873,1474265667501940738,1474269264184729602,1474269085490601986,1478981177296039938,1478981414068695042,1461283860837302273,1456296054301163521,1466735175910596610,1476074562959986690,1476074820825796609,1476075183926693890,1476075321508253697,1476365556301451265,1461285909385707521,1461286150004539394,1470303721408184322,1543834617538363394";

		String permissionIds2 ="1461241263473332225,1474262066813591554,1474270168178876417,1474272029896204290,1461236049961848834,1475340142070210561,1478981537662251010,1461504412516728833,1474262874078699521,1474264353694932993,1474264532728799234,1474264849797210113,1474264660109811713,1474265117460914177,1474265302576521218,1474266362850435073,1474265667501940738,1474266506664730626,1474265955268943873,1474266686235467778,1478276711164981250,1474263484811304961,1474263901515407362,1474264127106048002,1456296054301163521,1461285909385707521,1461286150004539394,1466735175910596610,1476074562959986690,1476074820825796609,1476075321508253697,1476075183926693890,1476365556301451265,1470303721408184322,1456294983365328897";

		for (String departId : ids) {
			if(WxlConvertUtils.isNotEmpty(departId)){
				SysDepart sysDepart = sysDepartMapper.selectById(departId);
				if(WxlConvertUtils.isNotEmpty(sysDepart)){
					sysDepartPermissionService.saveDepartPermission(departId, permissionIds, "");
					//店铺管理
					SysDepartRole sysDepartRole = new SysDepartRole();
					sysDepartRole.setDepartId(departId);
					sysDepartRole.setRoleCode(sysDepart.getOrgCode()+"_a01");
					sysDepartRole.setRoleName("店铺管理");
					sysDepartRoleService.save(sysDepartRole);
					sysDepartRolePermissionService.saveDeptRolePermission(sysDepartRole.getId(), permissionIds, "");

					//任务发布
					SysDepartRole sysDepartRole1 = new SysDepartRole();
					sysDepartRole1.setDepartId(departId);
					sysDepartRole1.setRoleCode(sysDepart.getOrgCode()+"_b01");
					sysDepartRole1.setRoleName("任务发布");
					sysDepartRoleService.save(sysDepartRole1);
					sysDepartRolePermissionService.saveDeptRolePermission(sysDepartRole1.getId(), permissionIds1, "");

					//招聘官
					SysDepartRole sysDepartRole2 = new SysDepartRole();
					sysDepartRole2.setDepartId(departId);
					sysDepartRole2.setRoleCode(sysDepart.getOrgCode()+"_c01");
					sysDepartRole2.setRoleName("招聘官");
					sysDepartRole2.setDescription("_c01为招聘官固定编码，请勿修改");
					sysDepartRoleService.save(sysDepartRole2);
					sysDepartRolePermissionService.saveDeptRolePermission(sysDepartRole2.getId(), permissionIds2, "");
				}
			}
		}


	}

	@Override
	public List<String> getSubDepIdsByDepId(String departId) {
		return this.baseMapper.getSubDepIdsByDepId(departId);
	}

	@Override
	public List<String> getMySubDepIdsByDepId(String departIds) {
		//根据部门id获取所负责部门
		String[] codeArr = this.getMyDeptParentOrgCode(departIds);
		return this.baseMapper.getSubDepIdsByOrgCodes(codeArr);
	}

	@Override
	public List<String> getSubDepIdsByOrgCode(String orgCode) {
		return this.baseMapper.getSubDepIdsByOrgCode(orgCode);
	}


	/**
	 * <p>
	 * 根据关键字搜索相关的部门数据
	 * </p>
	 */
	@Override
	public List<SysDepartTreeModel> searhBy(String keyWord,String myDeptSearch,String departIds) {
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		List<SysDepartTreeModel> newList = new ArrayList<>();
		//myDeptSearch不为空时为我的部门搜索，只搜索所负责部门
		if(!StringUtil.isNullOrEmpty(myDeptSearch)){
			//departIds 为空普通用户或没有管理部门
			if(StringUtil.isNullOrEmpty(departIds)){
				return newList;
			}
			//根据部门id获取所负责部门
			String[] codeArr = this.getMyDeptParentOrgCode(departIds);
			for(int i=0;i<codeArr.length;i++){
				query.or().likeRight(SysDepart::getOrgCode,codeArr[i]);
			}
			query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		}
		query.like(SysDepart::getDepartName, keyWord);
		//update-begin：[bugfree号]组织机构搜索回显优化--------------------
		SysDepartTreeModel model = new SysDepartTreeModel();
		List<SysDepart> departList = this.list(query);
		if(departList.size() > 0) {
			for(SysDepart depart : departList) {
				model = new SysDepartTreeModel(depart);
				model.setChildren(null);
	    //update-end：[bugfree号]组织机构搜索功回显优化----------------------
				newList.add(model);
			}
			return newList;
		}
		return null;
	}

	/**
	 * 根据部门id删除并且删除其可能存在的子级任何部门
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean delete(String id) {
		List<String> idList = new ArrayList<>();
		idList.add(id);
		this.checkChildrenExists(id, idList);
		//清空部门树内存
		//FindsDepartsChildrenUtil.clearDepartIdModel();
		boolean ok = this.removeByIds(idList);
		//根据部门id获取部门角色id
		List<String> roleIdList = new ArrayList<>();
		LambdaQueryWrapper<SysDepartRole> query = new LambdaQueryWrapper<>();
		query.select(SysDepartRole::getId).in(SysDepartRole::getDepartId, idList);
		List<SysDepartRole> depRoleList = sysDepartRoleMapper.selectList(query);
		for(SysDepartRole deptRole : depRoleList){
			roleIdList.add(deptRole.getId());
		}
		//根据部门id删除用户与部门关系
		userDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getDepId,idList));
		//根据部门id删除部门授权
		departPermissionMapper.delete(new LambdaQueryWrapper<SysDepartPermission>().in(SysDepartPermission::getDepartId,idList));
		//根据部门id删除部门角色
		sysDepartRoleMapper.delete(new LambdaQueryWrapper<SysDepartRole>().in(SysDepartRole::getDepartId,idList));
		if(roleIdList != null && roleIdList.size()>0){
			//根据角色id删除部门角色授权
			departRolePermissionMapper.delete(new LambdaQueryWrapper<SysDepartRolePermission>().in(SysDepartRolePermission::getRoleId,roleIdList));
			//根据角色id删除部门角色用户信息
			departRoleUserMapper.delete(new LambdaQueryWrapper<SysDepartRoleUser>().in(SysDepartRoleUser::getDroleId,roleIdList));
		}
		return ok;
	}
	
	/**
	 * delete 方法调用
	 * @param id
	 * @param idList
	 */
	private void checkChildrenExists(String id, List<String> idList) {	
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getParentId,id);
		List<SysDepart> departList = this.list(query);
		if(departList != null && departList.size() > 0) {
			for(SysDepart depart : departList) {
				idList.add(depart.getId());
				this.checkChildrenExists(depart.getId(), idList);
			}
		}
	}

	@Override
	public List<SysDepart> queryUserDeparts(String userId) {
		return baseMapper.queryUserDeparts(userId);
	}

	@Override
	public List<SysDepart> queryDepartsByUsername(String username) {
		return baseMapper.queryDepartsByUsername(username);
	}

	/**
	 * 根据用户所负责部门ids获取父级部门编码
	 * @param departIds
	 * @return
	 */
	private String[] getMyDeptParentOrgCode(String departIds){
		//根据部门id查询所负责部门
		LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
		query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
		query.in(SysDepart::getId, Arrays.asList(departIds.split(",")));
		query.orderByAsc(SysDepart::getOrgCode);
		List<SysDepart> list = this.list(query);
		//查找根部门
		if(list == null || list.size()==0){
			return null;
		}
		String orgCode = this.getMyDeptParentNode(list);
		String[] codeArr = orgCode.split(",");
		return codeArr;
	}

	/**
	 * 获取负责部门父节点
	 * @param list
	 * @return
	 */
	private String getMyDeptParentNode(List<SysDepart> list){
		Map<String,String> map = new HashMap<>();
		//1.先将同一公司归类
		for(SysDepart dept : list){
			String code = dept.getOrgCode().substring(0,3);
			if(map.containsKey(code)){
				String mapCode = map.get(code)+","+dept.getOrgCode();
				map.put(code,mapCode);
			}else{
				map.put(code,dept.getOrgCode());
			}
		}
		StringBuffer parentOrgCode = new StringBuffer();
		//2.获取同一公司的根节点
		for(String str : map.values()){
			String[] arrStr = str.split(",");
			parentOrgCode.append(",").append(this.getMinLengthNode(arrStr));
		}
		return parentOrgCode.substring(1);
	}

	/**
	 * 获取同一公司中部门编码长度最小的部门
	 * @param str
	 * @return
	 */
	private String getMinLengthNode(String[] str){
		int min =str[0].length();
		String orgCode = str[0];
		for(int i =1;i<str.length;i++){
			if(str[i].length()<=min){
				min = str[i].length();
				orgCode = orgCode+","+str[i];
			}
		}
		return orgCode;
	}
    /**
     * 获取部门树信息根据关键字
     * @param keyWord
     * @return
     */
    @Override
    public List<SysDepartTreeModel> queryTreeByKeyWord(String keyWord) {
        LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
        query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0.toString());
        query.orderByAsc(SysDepart::getDepartOrder);
        List<SysDepart> list = this.list(query);
        // 调用wrapTreeDataToTreeList方法生成树状数据
        List<SysDepartTreeModel> listResult = FindsDepartsChildrenUtil.wrapTreeDataToTreeList(list);
        List<SysDepartTreeModel> treelist =new ArrayList<>();
        if(StringUtils.isNotBlank(keyWord)){
            this.getTreeByKeyWord(keyWord,listResult,treelist);
        }else{
            return listResult;
        }
        return treelist;
    }

	/**
	 * 根据parentId查询部门树
	 * @param parentId
	 * @param ids 前端回显传递
	 * @return
	 */
	@Override
	public List<SysDepartTreeModel> queryTreeListByPid(String parentId,String ids) {
		Consumer<LambdaQueryWrapper<SysDepart>> square = i -> {
			if (WxlConvertUtils.isNotEmpty(ids)) {
				i.in(SysDepart::getId, ids.split(","));
			} else {
				if(WxlConvertUtils.isEmpty(parentId)){
					i.and(q->q.isNull(true,SysDepart::getParentId).or().eq(true,SysDepart::getParentId,""));
				}else{
					i.eq(true,SysDepart::getParentId,parentId);
				}
			}
		};
		LambdaQueryWrapper<SysDepart> lqw=new LambdaQueryWrapper();
		lqw.eq(true,SysDepart::getDelFlag,CommonConstant.DEL_FLAG_0);
		lqw.func(square);
		lqw.orderByDesc(SysDepart::getDepartOrder);
		List<SysDepart> list = list(lqw);
		List<SysDepartTreeModel> records = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			SysDepart depart = list.get(i);
            SysDepartTreeModel treeModel = new SysDepartTreeModel(depart);
            //TODO 异步树加载key拼接__+时间戳,以便于每次展开节点会刷新数据
			//treeModel.setKey(treeModel.getKey()+"__"+System.currentTimeMillis());
			treeModel.setKey(treeModel.getKey());
            Integer count=this.baseMapper.queryCountByPid(depart.getId());
            if(count>0){
                treeModel.setIsLeaf(false);
            }else{
                treeModel.setIsLeaf(true);
            }
            records.add(treeModel);
        }
		return records;
	}

	@Override
	public JSONObject queryAllParentIdByDepartId(String departId) {
		JSONObject result = new JSONObject();
		for (String id : departId.split(",")) {
			JSONObject all = this.queryAllParentId("id", id);
			result.put(id, all);
		}
		return result;
	}

	@Override
	public JSONObject queryAllParentIdByOrgCode(String orgCode) {
		JSONObject result = new JSONObject();
		for (String code : orgCode.split(",")) {
			JSONObject all = this.queryAllParentId("org_code", code);
			result.put(code, all);
		}
		return result;
	}

	/**
	 * 查询某个部门的所有父ID信息
	 *
	 * @param fieldName 字段名
	 * @param value     值
	 */
	private JSONObject queryAllParentId(String fieldName, String value) {
		JSONObject data = new JSONObject();
		// 父ID集合，有序
		data.put("parentIds", new JSONArray());
		// 父ID的部门数据，key是id，value是数据
		data.put("parentMap", new JSONObject());
		this.queryAllParentIdRecursion(fieldName, value, data);
		return data;
	}

	/**
	 * 递归调用查询父部门接口
	 */
	private void queryAllParentIdRecursion(String fieldName, String value, JSONObject data) {
		QueryWrapper<SysDepart> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq(fieldName, value);
		SysDepart depart = super.getOne(queryWrapper);
		if (depart != null) {
			data.getJSONArray("parentIds").add(0, depart.getId());
			data.getJSONObject("parentMap").put(depart.getId(), depart);
			if (WxlConvertUtils.isNotEmpty(depart.getParentId())) {
				this.queryAllParentIdRecursion("id", depart.getParentId(), data);
			}
		}
	}

	@Override
	public SysDepart queryCompByOrgCode(String orgCode) {
		int length = YouBianCodeUtil.zhanweiLength;
		String compyOrgCode = orgCode.substring(0,length);
		return this.baseMapper.queryCompByOrgCode(compyOrgCode);
	}
	/**
	 * 根据id查询下级部门
	 * @param pid
	 * @return
	 */
	@Override
	public List<SysDepart> queryDeptByPid(String pid) {
		return this.baseMapper.queryDeptByPid(pid);
	}

	@Override
	public int getThisMonthDept() {
		return baseMapper.getThisMonthDept();
	}

	/**
     * 根据关键字筛选部门信息
     * @param keyWord
     * @return
     */
    public void getTreeByKeyWord(String keyWord,List<SysDepartTreeModel> allResult,List<SysDepartTreeModel>  newResult){
        for (SysDepartTreeModel model:allResult) {
            if (model.getDepartName().contains(keyWord)){
                newResult.add(model);
                continue;
            }else if(model.getChildren()!=null){
                getTreeByKeyWord(keyWord,model.getChildren(),newResult);
            }
        }
    }


	/**
	 * queryTreeList的子方法 ====1=====
	 * 该方法是s将SysDepart类型的list集合转换成SysDepartTreeModel类型的集合
	 */
	private List<StaDepartTreeModel> wrapStaTreeDataToTreeList(List<SysDepart> recordList) {
		// 在该方法每请求一次,都要对全局list集合进行一次清理
		//idList.clear();
		List<StaDepartIdModel> idList = new ArrayList<>();
		List<StaDepartTreeModel> records = new ArrayList<>();
		for (int i = 0; i < recordList.size(); i++) {
			SysDepart depart = recordList.get(i);
			LambdaQueryWrapper<StaWork> queryStaWork = new LambdaQueryWrapper<StaWork>();
			queryStaWork.eq(StaWork::getCompanyId,depart.getId());
			int count = staWorkMapper.selectCount(queryStaWork);
			records.add(new StaDepartTreeModel(depart,count));
		}
		List<StaDepartTreeModel> tree = findStaChildren(records, idList);
		setStaEmptyChildrenAsNull(tree);
		return tree;
	}

	/**
	 * queryTreeList的子方法 ====2=====
	 * 该方法是找到并封装顶级父类的节点到TreeList集合
	 */
	private List<StaDepartTreeModel> findStaChildren(List<StaDepartTreeModel> recordList,
													 List<StaDepartIdModel> departIdList) {

		List<StaDepartTreeModel> treeList = new ArrayList<>();
		for (int i = 0; i < recordList.size(); i++) {
			StaDepartTreeModel branch = recordList.get(i);
			if (WxlConvertUtils.isEmpty(branch.getParentId())) {
				treeList.add(branch);
				StaDepartIdModel departIdModel = new StaDepartIdModel().convert(branch);
				departIdList.add(departIdModel);
			}
		}
		getStaGrandChildren(treeList,recordList,departIdList);

		//idList = departIdList;
		return treeList;
	}

	/**
	 * queryTreeList的子方法 ====4====
	 * 该方法是将子节点为空的List集合设置为Null值
	 */
	private static void setStaEmptyChildrenAsNull(List<StaDepartTreeModel> treeList) {

		for (int i = 0; i < treeList.size(); i++) {
			StaDepartTreeModel model = treeList.get(i);
			if (model.getChildren().size() == 0) {
				model.setChildren(null);
				model.setIsLeaf(true);
			}else{
				setStaEmptyChildrenAsNull(model.getChildren());
				model.setIsLeaf(false);
			}
		}
		// sysDepartTreeList = treeList;
	}

	/**
	 * queryTreeList的子方法====3====
	 *该方法是找到顶级父类下的所有子节点集合并封装到TreeList集合
	 */
	private static void getStaGrandChildren(List<StaDepartTreeModel> treeList,List<StaDepartTreeModel> recordList,List<StaDepartIdModel> idList) {

		for (int i = 0; i < treeList.size(); i++) {
			StaDepartTreeModel model = treeList.get(i);
			StaDepartIdModel idModel = idList.get(i);
			for (int i1 = 0; i1 < recordList.size(); i1++) {
				StaDepartTreeModel m = recordList.get(i1);
				if (m.getParentId()!=null && m.getParentId().equals(model.getId())) {
					model.getChildren().add(m);
					StaDepartIdModel dim = new StaDepartIdModel().convert(m);
					idModel.getChildren().add(dim);
				}
			}
			getStaGrandChildren(treeList.get(i).getChildren(), recordList, idList.get(i).getChildren());
		}

	}
}
