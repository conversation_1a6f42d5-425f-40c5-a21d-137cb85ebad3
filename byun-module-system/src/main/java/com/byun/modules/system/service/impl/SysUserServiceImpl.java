package com.byun.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.byun.common.constant.CommonSendStatus;
import com.byun.common.constant.enums.RoleIndexConfigEnum;
import com.byun.common.util.IdCardUtil;
import com.byun.modules.staffing.entity.StaEnrollInfo;
import com.byun.modules.staffing.entity.StaUserInfo;
import com.byun.modules.staffing.mapper.StaEnrollInfoMapper;
import com.byun.modules.staffing.mapper.StaUserInfoMapper;
import com.byun.modules.staffing.model.StaAgentModel;
import com.byun.modules.staffing.model.StaUserRelModel;
import com.byun.modules.staffing.service.IStaEnrollInfoService;
import com.byun.modules.staffing.service.IStaOrderService;
import com.byun.modules.staffing.service.IStaUserInfoService;
import com.byun.modules.system.service.ISysAnnouncementService;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.byun.common.api.vo.Result;
import com.byun.common.constant.CacheConstant;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.api.ISysBaseAPI;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.system.vo.SysUserCacheInfo;
import com.byun.common.util.PasswordUtil;
import com.byun.common.util.UUIDGenerator;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.base.service.BaseCommonService;
import com.byun.modules.system.entity.*;
import com.byun.modules.system.mapper.*;
import com.byun.modules.system.model.SysUserSysDepartModel;
import com.byun.modules.system.service.ISysUserService;
import com.byun.modules.system.vo.SysUserDepVo;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * @Author: scott
 * @Date: 2018-12-20
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysPermissionMapper sysPermissionMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysDepartRoleUserMapper departRoleUserMapper;
    @Autowired
    private SysDepartRoleMapper sysDepartRoleMapper;
    @Resource
    private BaseCommonService baseCommonService;
    @Autowired
    private SysThirdAccountMapper sysThirdAccountMapper;
    @Autowired
    private StaEnrollInfoMapper staEnrollInfoMapper;
    @Autowired
    private StaUserInfoMapper staUserInfoMapper;
    @Autowired
    private SysUserRelMapper sysUserRelMapper;
    @Autowired
    private IStaOrderService staOrderService;
    @Autowired
    private SysRoleIndexMapper sysRoleIndexMapper;

    @Autowired
    private IStaEnrollInfoService staEnrollInfoService;
    @Autowired
    private IStaUserInfoService staUserInfoService;
    @Autowired
    private ISysUserService sysUserService;

    /**
     * @description: 启用代理 绑定 招聘官
     * <AUTHOR>
     * @date 2021/12/14 9:33
     * @version 1.0
     */
    @Override
    @Transactional
    public void enableAgentByUser(SysUser sysUser, StaAgentModel staAgentModel) {
        //1、启用用户关系
        LambdaQueryWrapper<SysUserRel> userRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userRelLambdaQueryWrapper.eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_1);
        userRelLambdaQueryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
        //TODO 目前只能绑定的用户解绑，启用
        userRelLambdaQueryWrapper.eq(SysUserRel::getMeId, sysUser.getId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        SysUserRel sysUserRel = sysUserRelMapper.selectOne(userRelLambdaQueryWrapper);
        sysUserRel.setUpdateBy(staAgentModel.getUpdateBy());
        sysUserRel.setUpdateTime(staAgentModel.getUpdateTime());
        sysUserRel.setDelFlag(CommonConstant.DEL_FLAG_0);
        sysUserRelMapper.updateById(sysUserRel);

        //2、添加部门角色权限
        SysDepart sysDepart = sysDepartMapper.selectById(staAgentModel.getDepartId());

        //3、查询该公司下 代理角色
        LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, sysDepart.getId());
        sysDepartRoleLambdaQueryWrapper.likeLeft(SysDepartRole::getRoleCode, CommonConstant.DEPART_ROLE_CODE_RECRUITER);
        SysDepartRole sysDepartRole = sysDepartRoleMapper.selectOne(sysDepartRoleLambdaQueryWrapper);

        //4、查询是否已有代理角色
        LambdaQueryWrapper<SysDepartRoleUser> sysDepartRoleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getUserId, staAgentModel.getUserId());
        sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getDroleId, sysDepartRole.getId());
        SysDepartRoleUser sysDepartRoleUser = departRoleUserMapper.selectOne(sysDepartRoleUserLambdaQueryWrapper);
        //5、添加代理角色
        if (WxlConvertUtils.isEmpty(sysDepartRoleUser)) {
            sysDepartRoleUser = new SysDepartRoleUser();
            sysDepartRoleUser.setDroleId(sysDepartRole.getId());
            sysDepartRoleUser.setUserId(staAgentModel.getUserId());
            departRoleUserMapper.insert(sysDepartRoleUser);
        }

        //6、添加用户到部门
        LambdaQueryWrapper<SysUserDepart> userDepartLambdaQueryWrapper = new LambdaQueryWrapper<SysUserDepart>();
        userDepartLambdaQueryWrapper.eq(SysUserDepart::getDepId, staAgentModel.getDepartId());
        userDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId, staAgentModel.getUserId());
        SysUserDepart sysUserDepart = sysUserDepartMapper.selectOne(userDepartLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(sysUserDepart)) {
            sysUserDepart = new SysUserDepart(staAgentModel.getUserId(), staAgentModel.getDepartId());
            sysUserDepartMapper.insert(sysUserDepart);
        }
    }


    /**
     * @description: 停用代理 绑定 招聘官
     * <AUTHOR>
     * @date 2021/12/14 9:33
     * @version 1.0
     */
    @Override
    @Transactional
    public void stopAgentByUser(SysUser sysUser, StaAgentModel staAgentModel) {
        //1、停用用户关系
        LambdaQueryWrapper<SysUserRel> userRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userRelLambdaQueryWrapper.eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_0);
        userRelLambdaQueryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
        userRelLambdaQueryWrapper.eq(SysUserRel::getMeId, sysUser.getId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        SysUserRel sysUserRel = sysUserRelMapper.selectOne(userRelLambdaQueryWrapper);
        sysUserRel.setUpdateBy(staAgentModel.getUpdateBy());
        sysUserRel.setUpdateTime(staAgentModel.getUpdateTime());
        sysUserRel.setDelFlag(CommonConstant.DEL_FLAG_1);
        sysUserRelMapper.updateById(sysUserRel);

        //查询该用户被其他人绑定为代理没，都没有则删除在部门下的代理角色
        LambdaQueryWrapper<SysUserRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
        queryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        queryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        Integer integer = sysUserRelMapper.selectCount(queryWrapper);
        if (WxlConvertUtils.isEmpty(integer) || integer == 0) {
            //2、删除部门角色权限
            SysDepart sysDepart = sysDepartMapper.selectById(staAgentModel.getDepartId());
            //3、查询该公司下 代理角色
            LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, sysDepart.getId());
            sysDepartRoleLambdaQueryWrapper.likeLeft(SysDepartRole::getRoleCode, CommonConstant.DEPART_ROLE_CODE_RECRUITER);
            SysDepartRole sysDepartRole = sysDepartRoleMapper.selectOne(sysDepartRoleLambdaQueryWrapper);

            //4、查询是否已有代理角色
            LambdaQueryWrapper<SysDepartRoleUser> sysDepartRoleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getUserId, staAgentModel.getUserId());
            sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getDroleId, sysDepartRole.getId());
            departRoleUserMapper.delete(sysDepartRoleUserLambdaQueryWrapper);

            //5、查询用户是否有其他部门权限
            LambdaQueryWrapper<SysDepartRole> query = new LambdaQueryWrapper<SysDepartRole>();
            query.eq(SysDepartRole::getDepartId, staAgentModel.getDepartId());
            List<SysDepartRole> sysDepartRoles = sysDepartRoleMapper.selectList(query);
            List<String> sysDepartRoleIds = new ArrayList<String>();
            for (SysDepartRole itme : sysDepartRoles) {
                sysDepartRoleIds.add(itme.getId());
            }
            Integer count = departRoleUserMapper.selectCount(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, staAgentModel.getUserId()).in(SysDepartRoleUser::getDroleId, sysDepartRoleIds));

            //删除部门
            if (count <= 0) {
                LambdaQueryWrapper<SysUserDepart> userDepartLambdaQueryWrapper = new LambdaQueryWrapper<SysUserDepart>();
                userDepartLambdaQueryWrapper.eq(SysUserDepart::getDepId, staAgentModel.getDepartId());
                userDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId, staAgentModel.getUserId());
                SysUserDepart sysUserDepart = sysUserDepartMapper.selectOne(userDepartLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(sysUserDepart)) {
                    sysUserDepartMapper.deleteById(sysUserDepart);
                }
            }
        }
    }


    /**
     * @description: 删除代理 绑定 招聘官
     * <AUTHOR>
     * @date 2021/12/14 9:33
     * @version 1.0
     */
    @Override
    @Transactional
    public void deleteAgentByUser(SysUser sysUser, StaAgentModel staAgentModel) {
//		sysUser.setUserIdentity(CommonConstant.USER_IDENTITY_3);//普通用户
        baseMapper.updateById(sysUser);
        //1、停用用户关系
        LambdaQueryWrapper<SysUserRel> userRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userRelLambdaQueryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
        userRelLambdaQueryWrapper.eq(SysUserRel::getMeId, sysUser.getId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        sysUserRelMapper.delete(userRelLambdaQueryWrapper);

        //查询该用户被其他人绑定为代理没，都没有则删除在部门下的代理角色
        LambdaQueryWrapper<SysUserRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//代理
        queryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        queryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        Integer integer = sysUserRelMapper.selectCount(queryWrapper);
        if (WxlConvertUtils.isEmpty(integer) || integer == 0) {
            //2、删除部门角色权限
            SysDepart sysDepart = sysDepartMapper.selectById(staAgentModel.getDepartId());
            //3、查询该公司下 代理角色
            LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, sysDepart.getId());
            sysDepartRoleLambdaQueryWrapper.likeLeft(SysDepartRole::getRoleCode, CommonConstant.DEPART_ROLE_CODE_RECRUITER);
            SysDepartRole sysDepartRole = sysDepartRoleMapper.selectOne(sysDepartRoleLambdaQueryWrapper);

            //4、查询是否已有代理角色
            LambdaQueryWrapper<SysDepartRoleUser> sysDepartRoleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getUserId, staAgentModel.getUserId());
            sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getDroleId, sysDepartRole.getId());
            departRoleUserMapper.delete(sysDepartRoleUserLambdaQueryWrapper);

            //5、查询用户是否有其他部门权限
            LambdaQueryWrapper<SysDepartRole> query = new LambdaQueryWrapper<SysDepartRole>();
            query.eq(SysDepartRole::getDepartId, staAgentModel.getDepartId());
            List<SysDepartRole> sysDepartRoles = sysDepartRoleMapper.selectList(query);
            List<String> sysDepartRoleIds = new ArrayList<String>();
            for (SysDepartRole itme : sysDepartRoles) {
                sysDepartRoleIds.add(itme.getId());
            }
            Integer count = departRoleUserMapper.selectCount(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, staAgentModel.getUserId()).in(SysDepartRoleUser::getDroleId, sysDepartRoleIds));

            //删除部门
            if (count <= 0) {
                LambdaQueryWrapper<SysUserDepart> userDepartLambdaQueryWrapper = new LambdaQueryWrapper<SysUserDepart>();
                userDepartLambdaQueryWrapper.eq(SysUserDepart::getDepId, staAgentModel.getDepartId());
                userDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId, staAgentModel.getUserId());
                SysUserDepart sysUserDepart = sysUserDepartMapper.selectOne(userDepartLambdaQueryWrapper);
                if (WxlConvertUtils.isNotEmpty(sysUserDepart)) {
                    sysUserDepartMapper.deleteById(sysUserDepart);
                }
            }
        }
    }

    /**
     * @description: 添加代理 绑定 招聘官
     * <AUTHOR>
     * @date 2021/12/14 9:33
     * @version 1.0
     */
    @Override
    @Transactional
    public void addAgentByUser(SysUser sysUser, StaAgentModel staAgentModel) {
//		sysUser.setUserIdentity(CommonConstant.USER_IDENTITY_4);//招聘官
        baseMapper.updateById(sysUser);
        //1、添加用户关系
        LambdaQueryWrapper<SysUserRel> userRelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userRelLambdaQueryWrapper.eq(SysUserRel::getDelFlag, CommonConstant.DEL_FLAG_0);
        userRelLambdaQueryWrapper.eq(SysUserRel::getRelType, CommonConstant.SYS_USER_REL_3);//招聘官
        userRelLambdaQueryWrapper.eq(SysUserRel::getMeId, sysUser.getId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getYouId, staAgentModel.getUserId());
        userRelLambdaQueryWrapper.eq(SysUserRel::getSpareId, staAgentModel.getDepartId());
        SysUserRel sysUserRel = sysUserRelMapper.selectOne(userRelLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(sysUserRel)) {
            SysUserRel newSysUserRel = new SysUserRel();
            newSysUserRel.setDelFlag(CommonConstant.DEL_FLAG_0);
            newSysUserRel.setUpdateBy(staAgentModel.getUpdateBy());
            newSysUserRel.setCreateBy(staAgentModel.getCreateBy());
            newSysUserRel.setCreateTime(staAgentModel.getCreateTime());
            newSysUserRel.setUpdateTime(staAgentModel.getUpdateTime());
            newSysUserRel.setRelType(CommonConstant.SYS_USER_REL_3);
            newSysUserRel.setYouId(staAgentModel.getUserId());
            newSysUserRel.setSpareId(staAgentModel.getDepartId());//所属部门
            newSysUserRel.setMeId(sysUser.getId());
            sysUserRelMapper.insert(newSysUserRel);
        } else {
            sysUserRel.setUpdateBy(staAgentModel.getUpdateBy());
            sysUserRel.setUpdateTime(staAgentModel.getUpdateTime());
            sysUserRel.setDelFlag(CommonConstant.DEL_FLAG_0);
            sysUserRelMapper.updateById(sysUserRel);
        }
        //2、添加部门角色权限
        SysDepart sysDepart = sysDepartMapper.selectById(staAgentModel.getDepartId());

        //3、查询该公司下 代理角色
        LambdaQueryWrapper<SysDepartRole> sysDepartRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysDepartRoleLambdaQueryWrapper.eq(SysDepartRole::getDepartId, sysDepart.getId());
        sysDepartRoleLambdaQueryWrapper.likeLeft(SysDepartRole::getRoleCode, CommonConstant.DEPART_ROLE_CODE_RECRUITER);
        SysDepartRole sysDepartRole = sysDepartRoleMapper.selectOne(sysDepartRoleLambdaQueryWrapper);

        //4、查询是否已有代理角色
        LambdaQueryWrapper<SysDepartRoleUser> sysDepartRoleUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getUserId, staAgentModel.getUserId());
        sysDepartRoleUserLambdaQueryWrapper.eq(SysDepartRoleUser::getDroleId, sysDepartRole.getId());
        SysDepartRoleUser sysDepartRoleUser = departRoleUserMapper.selectOne(sysDepartRoleUserLambdaQueryWrapper);
        //5、添加代理角色
        if (WxlConvertUtils.isEmpty(sysDepartRoleUser)) {
            sysDepartRoleUser = new SysDepartRoleUser();
            sysDepartRoleUser.setDroleId(sysDepartRole.getId());
            sysDepartRoleUser.setUserId(staAgentModel.getUserId());
            departRoleUserMapper.insert(sysDepartRoleUser);
        }
        //6、添加用户到部门
        LambdaQueryWrapper<SysUserDepart> userDepartLambdaQueryWrapper = new LambdaQueryWrapper<SysUserDepart>();
        userDepartLambdaQueryWrapper.eq(SysUserDepart::getDepId, staAgentModel.getDepartId());
        userDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId, staAgentModel.getUserId());
        SysUserDepart sysUserDepart = sysUserDepartMapper.selectOne(userDepartLambdaQueryWrapper);
        if (WxlConvertUtils.isEmpty(sysUserDepart)) {
            sysUserDepart = new SysUserDepart(staAgentModel.getUserId(), staAgentModel.getDepartId());
            sysUserDepartMapper.insert(sysUserDepart);
        }
    }

    @Override
    public void updateEmailById(String id, String email) {
        userMapper.updateEmailById(id, email);
    }

    @Override
    public Boolean editWechatIdById(String id, String wechatId) {
        return userMapper.editWechatIdById(id, wechatId);
    }

    @Override
    public int getThisMonthUser() {
        return userMapper.getThisMonthUser();
    }

    @Override
    public IPage<SysUser> listTaskTotalPage(IPage<SysUser> userIPage, String realNameName, String username,String deptNo,String deptName) {
        return userIPage.setRecords(userMapper.listTaskTotalPage(userIPage, realNameName, username,deptNo,deptName));
    }
    public List<SysUser> listTaskTotal(String realNameName, String username,String deptName) {
        return userMapper.listTaskTotal(realNameName,username,deptName);
        //return  userIPage.setRecords(userMapper.listTaskTotal(userIPage, realNameName, username));
    }
    @Override
    @Transactional
    public Boolean updateUserRealName(JSONObject jsonObject, LoginUser user) throws ParseException {
        boolean enrollerFlag = true;
        Boolean sysUserFlag = true;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        /**
         * 身份证正面、身份证反面、姓名、有效期始、有效期末、出生日期、性别、民族、身份证号码、住址、签发机关
         * 是否有健康证、是否有体检表、身高、体重、现住址名称、现住址全称、现住址精度、现住址维度、
         */
        String frontUrl = jsonObject.getString("frontUrl");
        String reverseUrl = jsonObject.getString("reverseUrl");
        String name = jsonObject.getString("name");
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        String birthday = jsonObject.getString("birthday");
        Integer sex = jsonObject.getInteger("sex");
        String nationality = jsonObject.getString("nationality");
        String idCard = jsonObject.getString("idCard");
        String address = jsonObject.getString("address");//身份证地址
        String issue = jsonObject.getString("issue");//签发机关
        Integer idHealthValidity = jsonObject.getInteger("idHealthValidity");//健康证
        Integer physicalExaminationValidity = jsonObject.getInteger("physicalExaminationValidity");//体检表
        String height = jsonObject.getString("height");//身高
        String weight = jsonObject.getString("weight");//体重
        String addressName = jsonObject.getString("addressName");//现住址名称
        String addressNameFull = jsonObject.getString("addressNameFull");//完整住址
        BigDecimal addressLat = jsonObject.getBigDecimal("addressLat");//现住址维度
        BigDecimal addressLng = jsonObject.getBigDecimal("addressLng");//现住址精度
        if (WxlConvertUtils.isNotEmpty(idCard) && IdCardUtil.isIdCardNumberValid(idCard)) {
            //修改用户信息
            String sysUserId = user.getId();
            SysUser sysUser = this.getById(sysUserId);
            sysUser.setFrontUrl(frontUrl); //人像面
            sysUser.setReverseUrl(reverseUrl);//国徽面
            sysUser.setRealNameName(name); //真实姓名
            sysUser.setRealNameTime(new Date());//认证时间
            sysUser.setStartDate(startDate); //有效期始
            sysUser.setEndDate(endDate);//有效期末
            sysUser.setBirthday(sdf.parse(birthday));//出生日期
            sysUser.setNationality(nationality);//民族
            sysUser.setIdCard(idCard);//身份证号
            sysUser.setSex(sex);//性别
            sysUser.setIdCardAddress(address);//身份证地址
            sysUser.setIssue(issue);
            sysUser.setIsRealName(CommonConstant.AUTHENTICATION_STATUS1);//已实名认证
            sysUser.setUpdateTime(new Date());
            sysUserFlag = this.updateById(sysUser);
            //修改申请信息
            StaEnrollInfo staEnrollInfo = staEnrollInfoService.getOne(new LambdaQueryWrapper<StaEnrollInfo>().eq(StaEnrollInfo::getSysUserId, sysUserId));
            if (WxlConvertUtils.isNotEmpty(staEnrollInfo)) {
                staEnrollInfo.setName(name);//姓名
                staEnrollInfo.setIdCard(idCard);//身份证号码
                staEnrollInfo.setSex(sex);//性别
                LocalDate birthDate = LocalDate.parse(idCard.substring(6, 14),
                        java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
                int age = Period.between(birthDate, LocalDate.now()).getYears();
                staEnrollInfo.setAge(age);//年龄
                staEnrollInfo.setBirthday(sdf.parse(birthday));//生日
                staEnrollInfo.setIdCardStartDate(startDate);//有效期始
                staEnrollInfo.setIdCardEndDate(endDate);//有效期末
                staEnrollInfo.setIdCardAddress(address);//身份证地址
                staEnrollInfo.setUpdateTime(new Date());
                staEnrollInfo.setIdHealthValidity(idHealthValidity);
                staEnrollInfo.setPhysicalExaminationValidity(physicalExaminationValidity);
                staEnrollInfo.setHeight(height);
                staEnrollInfo.setWeight(weight);
                staEnrollInfo.setAddressName(addressName);
                staEnrollInfo.setAddressLng(addressLng);
                staEnrollInfo.setAddressLat(addressLat);
                staEnrollInfo.setAddress(addressNameFull);
                enrollerFlag = staEnrollInfoService.updateById(staEnrollInfo);
            } else {
                StaEnrollInfo enrollInfo = new StaEnrollInfo(sysUser);
                enrollInfo.setPhoneVeri(CommonConstant.VERIFICATION_1);//手机验证码注册，则自动验证过手机
                enrollInfo.setUserRel(CommonConstant.USER_REL_0);
                enrollInfo.setName(name);//姓名
                enrollInfo.setIdCard(idCard);//身份证号码
                enrollInfo.setSex(sex);//性别
                LocalDate birthDate = LocalDate.parse(idCard.substring(6, 14),
                        java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
                int age = Period.between(birthDate, LocalDate.now()).getYears();
                enrollInfo.setAge(age);//年龄
                enrollInfo.setBirthday(sdf.parse(birthday));//生日
                enrollInfo.setIdCardStartDate(startDate);//有效期始
                enrollInfo.setIdCardEndDate(endDate);//有效期末
                enrollInfo.setIdCardAddress(address);//身份证地址
                enrollInfo.setUpdateTime(new Date());
                enrollInfo.setIdHealthValidity(idHealthValidity);
                enrollInfo.setPhysicalExaminationValidity(physicalExaminationValidity);
                enrollInfo.setHeight(height);
                enrollInfo.setWeight(weight);
                enrollInfo.setAddressName(addressName);
                enrollInfo.setAddressLng(addressLng);
                enrollInfo.setAddressLat(addressLat);
                enrollInfo.setAddress(addressNameFull);
                staEnrollInfoService.save(enrollInfo);
            }
        }
        if (sysUserFlag && enrollerFlag) {
            return true;
        } else {
            return false;
        }
    }
    // 通过用户关系查询用户
    @Override
    public Page<SysUser> getUserListByUserRelYou(Page<SysUser> page, StaUserRelModel userRelModel) {
        return page.setRecords(userMapper.getUserListByUserRelYou(page, userRelModel));
    }

    // 通过用户关系查询用户
    @Override
    public Page<SysUser> getUserListByUserRelMe(Page<SysUser> page, StaUserRelModel userRelModel) {
        return page.setRecords(userMapper.getUserListByUserRelMe(page, userRelModel));
    }

    @Override
    @Transactional
    public void updateBindUserById(SysUser user) {
        this.update(user,
                Wrappers.<SysUser>lambdaUpdate()
                        .set(SysUser::getBindUserId, user.getBindUserId())
                        .eq(SysUser::getId, user.getId())
        );
        LambdaQueryWrapper<StaUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaUserInfo::getSysUserId, user.getId());
        StaUserInfo staUserInfo = staUserInfoMapper.selectOne(queryWrapper);
        //绑定招聘官id
        LambdaQueryWrapper<SysUserRel> staUserInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staUserInfoLambdaQueryWrapper.eq(SysUserRel::getMeId, user.getId());
        List<SysUserRel> sysUserRels = sysUserRelMapper.selectList(staUserInfoLambdaQueryWrapper);
        SysUserRel sysUserRel = null;
        for (SysUserRel sur : sysUserRels) {
            if (CommonConstant.SYS_USER_REL_2 == sur.getRelType()) {
                //判断是否之前绑定过该招聘官
                if (WxlConvertUtils.isNotEmpty(user.getBindUserId()) && 0 == sur.getYouId().compareTo(user.getBindUserId())) {
                    //启用招聘官
                    sur.setUpdateTime(new Date());
                    sur.setUpdateBy(user.getUpdateBy());
                    sur.setDelFlag(CommonConstant.DEL_FLAG_0);
                    sysUserRelMapper.updateById(sur);
                    sysUserRel = sur;
                } else {
                    //去除其他招聘官
                    sur.setUpdateTime(new Date());
                    sur.setUpdateBy(user.getUpdateBy());
                    sur.setDelFlag(CommonConstant.DEL_FLAG_1);
                    sysUserRelMapper.updateById(sur);
                }
            }
        }
        if (WxlConvertUtils.isNotEmpty(user.getBindUserId()) && WxlConvertUtils.isEmpty(sysUserRel)) {
            //绑定新招聘官
            sysUserRel = new SysUserRel();
            sysUserRel.setCreateTime(new Date());
            sysUserRel.setCreateBy(user.getCreateBy());
            sysUserRel.setUpdateTime(new Date());
            sysUserRel.setUpdateBy(user.getUpdateBy());
            sysUserRel.setMeId(user.getId());
            sysUserRel.setYouId(user.getBindUserId());
            sysUserRel.setRelType(CommonConstant.SYS_USER_REL_2);//绑定招聘官
            sysUserRel.setDelFlag(CommonConstant.DEL_FLAG_0);
            sysUserRelMapper.insert(sysUserRel);
        }

        staUserInfo.setBindUserId(user.getBindUserId());
        staUserInfoMapper.update(staUserInfo,
                Wrappers.<StaUserInfo>lambdaUpdate()
                        .set(StaUserInfo::getBindUserId, staUserInfo.getBindUserId())
                        .eq(StaUserInfo::getId, staUserInfo.getId())
        );
    }


    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> resetPassword(String username, String oldpassword, String newpassword, String confirmpassword) {
        SysUser user = userMapper.getUserByName(username);
        String passwordEncode = PasswordUtil.encrypt(username, oldpassword, user.getSalt());
        if (!user.getPassword().equals(passwordEncode)) {
            return Result.error("旧密码输入错误!");
        }
        if (WxlConvertUtils.isEmpty(newpassword)) {
            return Result.error("新密码不允许为空!");
        }
        if (!newpassword.equals(confirmpassword)) {
            return Result.error("两次输入密码不一致!");
        }
        String password = PasswordUtil.encrypt(username, newpassword, user.getSalt());
        this.userMapper.update(new SysUser().setPassword(password), new LambdaQueryWrapper<SysUser>().eq(SysUser::getId, user.getId()));
        return Result.ok("密码重置成功!");
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> changePassword(SysUser sysUser) {
        String salt = WxlConvertUtils.randomGen(8);
        sysUser.setSalt(salt);
        String password = sysUser.getPassword();
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
        sysUser.setPassword(passwordEncode);
        this.userMapper.updateById(sysUser);
        return Result.ok("密码修改成功!");
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(String userId) {
        //1.删除用户
        this.removeById(userId);
        return false;
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchUsers(String userIds) {
        //1.删除用户
        this.removeByIds(Arrays.asList(userIds.split(",")));
        return false;
    }

    @Override
    public SysUser getUserByName(String username) {
        return userMapper.getUserByName(username);
    }


    @Override
    @Transactional
    public void addUserWithRolAndEnrollAndInfo(SysUser user, String roles) {
        //LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        this.save(user);
        //添加用户报名信息
        StaEnrollInfo staEnrollInfo = new StaEnrollInfo(user);
        staEnrollInfo.setPhoneVeri(CommonConstant.VERIFICATION_1);//手机验证码注册，则自动验证过手机
        staEnrollInfo.setUserRel(CommonConstant.USER_REL_0);
        staEnrollInfoMapper.insert(staEnrollInfo);
        //添加用户名片
        StaUserInfo staUserInfo = new StaUserInfo(user);
        staUserInfo.setPhoneVeri(CommonConstant.VERIFICATION_1);//手机验证码注册，则自动验证过手机
        //绑定第一推广人
        if (WxlConvertUtils.isNotEmpty(user.getPromoterId())) {
            SysUserRel sysUserRel = new SysUserRel();
            sysUserRel.setCreateTime(new Date());
            sysUserRel.setCreateBy(user.getCreateBy());
            sysUserRel.setUpdateTime(new Date());
            sysUserRel.setUpdateBy(user.getUpdateBy());
            sysUserRel.setMeId(user.getId());
            sysUserRel.setYouId(user.getPromoterId());
            sysUserRel.setRelType(CommonConstant.SYS_USER_REL_1);//第一推广人
            sysUserRel.setDelFlag(CommonConstant.DEL_FLAG_0);
            SysUser sysUser = sysUserService.getById(user.getPromoterId());
            String orgCode = sysUser.getOrgCode();
            if (WxlConvertUtils.isNotEmpty(orgCode)) {
                SysDepart sysDepart = sysDepartMapper.selectOne(new QueryWrapper<SysDepart>().lambda().eq(SysDepart::getOrgCode, orgCode));
                sysUserRel.setDepartId(sysDepart.getId());
            }
            sysUserRelMapper.insert(sysUserRel);
        }
        //绑定招聘官id
        if (WxlConvertUtils.isNotEmpty(user.getBindUserId())) {
            //招聘官
            SysUserRel sur = new SysUserRel();
            sur.setCreateTime(new Date());
            sur.setCreateBy(user.getCreateBy());
            sur.setUpdateTime(new Date());
            sur.setUpdateBy(user.getUpdateBy());
            sur.setMeId(user.getId());
            sur.setYouId(user.getBindUserId());
            sur.setRelType(CommonConstant.SYS_USER_REL_2);//招聘官
            sur.setDelFlag(CommonConstant.DEL_FLAG_0);

            sysUserRelMapper.insert(sur);
            staUserInfo.setBindUserId(user.getBindUserId());
        }
        staUserInfoMapper.insert(staUserInfo);

        if (WxlConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
        //根据电话号码关联，其他人帮忙报名的任务单
        if (WxlConvertUtils.isNotEmpty(user.getUsername())) {
            staOrderService.addRealUserIdByPhone(user.getUsername());
        }
    }

    @Override
    @Transactional
    public void addUserWithRole(SysUser user, String roles) {
        this.save(user);
        if (WxlConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional
    public void editUserWithDepartRole(SysUser user, SysDepart depart, String roles) {
        //TODO 修改用户表中 部门id
//		if(ByunConvertUtils.isNotEmpty(user.getDepartIds())){
//
//		}else{
//			user.setDepartIds(depart.getId());
//		}
//		this.updateById(user);
        //TODO 没有改变招聘官的用户关系
        LambdaQueryWrapper<SysUserDepart> userDepartLambdaQueryWrapper = new LambdaQueryWrapper<SysUserDepart>();
        userDepartLambdaQueryWrapper.eq(SysUserDepart::getDepId, depart.getId());
        userDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId, user.getId());
        SysUserDepart sysUserDepart = sysUserDepartMapper.selectOne(userDepartLambdaQueryWrapper);

        if (WxlConvertUtils.isEmpty(sysUserDepart)) {
            sysUserDepart = new SysUserDepart(user.getId(), depart.getId());
            sysUserDepartMapper.insert(sysUserDepart);
        } else {
            if (WxlConvertUtils.isEmpty(roles)) {
                sysUserDepartMapper.deleteById(sysUserDepart);//没有权限则删除部门
            }
        }

        //所有部门角色
        LambdaQueryWrapper<SysDepartRole> query = new LambdaQueryWrapper<SysDepartRole>();
        query.eq(SysDepartRole::getDepartId, depart.getId());
        List<SysDepartRole> sysDepartRoles = sysDepartRoleMapper.selectList(query);
        List<String> sysDepartRoleIds = new ArrayList<String>();
        for (SysDepartRole sysDepartRole : sysDepartRoles) {
            sysDepartRoleIds.add(sysDepartRole.getId());
        }
        //先删后加
        departRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId()).in(SysDepartRoleUser::getDroleId, sysDepartRoleIds));

        if (WxlConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysDepartRoleUser departRoleUser = new SysDepartRoleUser(user.getId(), roleId);
                departRoleUserMapper.insert(departRoleUser);
            }
        }
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional
    public void editUserWithRole(SysUser user, String roles) {
        this.updateById(user);
        //先删后加
        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
        if (WxlConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
    }


    @Override
    public List<String> getRole(String username) {
        return sysUserRoleMapper.getRoleByUserName(username);
    }

    /**
     * 获取动态首页路由配置
     *
     * @param username
     * @param version
     * @return
     */
    @Override
    public SysRoleIndex getDynamicIndexByUserRole(String username, String version) {
        List<String> roles = sysUserRoleMapper.getRoleByUserName(username);
        String componentUrl = RoleIndexConfigEnum.getIndexByRoles(roles);
        SysRoleIndex roleIndex = new SysRoleIndex(componentUrl);
        //只有 X-Version=v3 的时候，才读取sys_role_index表获取角色首页配置
        if (WxlConvertUtils.isNotEmpty(version) && roles != null && roles.size() > 0) {
            LambdaQueryWrapper<SysRoleIndex> routeIndexQuery = new LambdaQueryWrapper();
            //用户所有角色
            routeIndexQuery.in(SysRoleIndex::getRoleCode, roles);
            //角色首页状态0：未开启  1：开启
            routeIndexQuery.eq(SysRoleIndex::getStatus, CommonConstant.STATUS_1);
            //优先级正序排序
            routeIndexQuery.orderByAsc(SysRoleIndex::getPriority);
            List<SysRoleIndex> list = sysRoleIndexMapper.selectList(routeIndexQuery);
            if (null != list && list.size() > 0) {
                roleIndex = list.get(0);
            }
        }

        //如果componentUrl为空，则返回空
        if (WxlConvertUtils.isEmpty(roleIndex.getComponent())) {
            return null;
        }
        return roleIndex;
    }


    /**
     * 通过用户名获取用户角色集合
     *
     * @param username 用户名
     * @return 角色集合
     */
    @Override
    public Set<String> getUserRolesSet(String username) {
        // 查询用户拥有的角色集合
        List<String> roles = sysUserRoleMapper.getRoleByUserName(username);
        log.info("-------通过数据库读取用户拥有的角色Rules------username： " + username + ",Roles size: " + (roles == null ? 0 : roles.size()));
        return new HashSet<>(roles);
    }

    /**
     * 通过用户名获取用户权限集合
     *
     * @param username 用户名
     * @return 权限集合
     */
    @Override
    public Set<String> getUserPermissionsSet(String username) {
        Set<String> permissionSet = new HashSet<>();
        List<SysPermission> permissionList = sysPermissionMapper.queryByUser(username);
        for (SysPermission po : permissionList) {
//			// TODO URL规则有问题？
//			if (ByunConvertUtils.isNotEmpty(po.getUrl())) {
//				permissionSet.add(po.getUrl());
//			}
            if (WxlConvertUtils.isNotEmpty(po.getPerms())) {
                permissionSet.add(po.getPerms());
            }
        }
        log.info("-------通过数据库读取用户拥有的权限Perms------username： " + username + ",Perms size: " + (permissionSet == null ? 0 : permissionSet.size()));
        return permissionSet;
    }

    @Override
    public SysUserCacheInfo getCacheUser(String username) {
        SysUserCacheInfo info = new SysUserCacheInfo();
        info.setOneDepart(true);
//		SysUser user = userMapper.getUserByName(username);
//		info.setSysUserCode(user.getUsername());
//		info.setSysUserName(user.getRealname());


        LoginUser user = sysBaseAPI.getUserByName(username);
        if (user != null) {
            info.setSysUserCode(user.getUsername());
            info.setSysUserName(user.getRealname());
            info.setSysOrgCode(user.getOrgCode());
        }

        //多部门支持in查询
        List<SysDepart> list = sysDepartMapper.queryUserDeparts(user.getId());
        List<String> sysMultiOrgCode = new ArrayList<String>();
        if (list == null || list.size() == 0) {
            //当前用户无部门
            //sysMultiOrgCode.add("0");
        } else if (list.size() == 1) {
            sysMultiOrgCode.add(list.get(0).getOrgCode());
        } else {
            info.setOneDepart(false);
            for (SysDepart dpt : list) {
                sysMultiOrgCode.add(dpt.getOrgCode());
            }
        }
        info.setSysMultiOrgCode(sysMultiOrgCode);

        return info;
    }

    // 根据部门Id查询
    @Override
    public IPage<SysUser> getUserByDepId(Page<SysUser> page, String departId, String username) {
        return userMapper.getUserByDepId(page, departId, username);
    }

    @Override
    public IPage<SysUser> getUserByDepIds(Page<SysUser> page, List<String> departIds, String username) {
        return userMapper.getUserByDepIds(page, departIds, username);
    }

    @Override
    public Map<String, String> getDepNamesByUserIds(List<String> userIds) {
        List<SysUserDepVo> list = this.baseMapper.getDepNamesByUserIds(userIds);

        Map<String, String> res = new HashMap<String, String>();
        list.forEach(item -> {
                    if (res.get(item.getUserId()) == null) {
                        res.put(item.getUserId(), item.getDepartName());
                    } else {
                        res.put(item.getUserId(), res.get(item.getUserId()) + "," + item.getDepartName());
                    }
                }
        );
        return res;
    }

    @Override
    public IPage<SysUser> getUserByDepartIdAndQueryWrapper(Page<SysUser> page, String departId, QueryWrapper<SysUser> queryWrapper) {
        LambdaQueryWrapper<SysUser> lambdaQueryWrapper = queryWrapper.lambda();

        lambdaQueryWrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.inSql(SysUser::getId, "SELECT user_id FROM sys_user_depart WHERE dep_id = '" + departId + "'");

        return userMapper.selectPage(page, lambdaQueryWrapper);
    }

    @Override
    public IPage<SysUserSysDepartModel> queryUserByOrgCode(String orgCode, SysUser userParams, IPage page) {
        List<SysUserSysDepartModel> list = baseMapper.getUserByOrgCode(page, orgCode, userParams);
        Integer total = baseMapper.getUserByOrgCodeTotal(orgCode, userParams);

        IPage<SysUserSysDepartModel> result = new Page<>(page.getCurrent(), page.getSize(), total);
        result.setRecords(list);

        return result;
    }

    // 根据角色Id查询
    @Override
    public IPage<SysUser> getUserByRoleId(Page<SysUser> page, String roleId, String username) {
        return userMapper.getUserByRoleId(page, roleId, username);
    }


    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, key = "#username")
    public void updateUserDepart(String username, String orgCode) {
        baseMapper.updateUserDepart(username, orgCode);
    }


    @Override
    public SysUser getUserByPhone(String phone) {
        return userMapper.getUserByPhone(phone);
    }


    @Override
    public SysUser getUserByEmail(String email) {
        return userMapper.getUserByEmail(email);
    }

    @Override
    @Transactional
    public void addUserWithDepart(SysUser user, String selectedParts) {
//		this.save(user);  //保存角色的时候已经添加过一次了
        if (WxlConvertUtils.isNotEmpty(selectedParts)) {
            String[] arr = selectedParts.split(",");
            for (String deaprtId : arr) {
                SysUserDepart userDeaprt = new SysUserDepart(user.getId(), deaprtId);
                sysUserDepartMapper.insert(userDeaprt);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public void editUserWithDepart(SysUser user, String departs) {
        this.updateById(user);  //更新角色的时候已经更新了一次了，可以再跟新一次
        String[] arr = {};
        if (WxlConvertUtils.isNotEmpty(departs)) {
            arr = departs.split(",");
        }
        //查询已关联部门
        List<SysUserDepart> userDepartList = sysUserDepartMapper.selectList(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        if (userDepartList != null && userDepartList.size() > 0) {
            for (SysUserDepart depart : userDepartList) {
                //修改已关联部门删除部门用户角色关系
                if (!Arrays.asList(arr).contains(depart.getDepId())) {
                    List<SysDepartRole> sysDepartRoleList = sysDepartRoleMapper.selectList(
                            new QueryWrapper<SysDepartRole>().lambda().eq(SysDepartRole::getDepartId, depart.getDepId()));
                    List<String> roleIds = sysDepartRoleList.stream().map(SysDepartRole::getId).collect(Collectors.toList());
                    if (roleIds != null && roleIds.size() > 0) {
                        departRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId())
                                .in(SysDepartRoleUser::getDroleId, roleIds));
                    }
                }
            }
        }
        //先删后加
        sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        if (WxlConvertUtils.isNotEmpty(departs)) {
            for (String departId : arr) {
                SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
                sysUserDepartMapper.insert(userDepart);
            }
        }
    }


    /**
     * 校验用户是否有效
     *
     * @param sysUser
     * @return
     */
    @Override
    public Result<?> checkUserIsEffective(SysUser sysUser) {
        Result<?> result = new Result<Object>();
        //情况1：根据用户信息查询，该用户不存在
        if (sysUser == null) {
            result.error500("该用户不存在，请注册");
            baseCommonService.addLog("用户登录失败，用户不存在！", CommonConstant.LOG_TYPE_1, null);
            return result;
        }
        //情况2：根据用户信息查询，该用户已注销
        //update-begin-：if条件永远为falsebug------------
        if (CommonConstant.DEL_FLAG_1.equals(sysUser.getDelFlag())) {
            //update-end-：if条件永远为falsebug------------
            baseCommonService.addLog("用户登录失败，用户名:" + sysUser.getUsername() + "已注销！", CommonConstant.LOG_TYPE_1, null);
            result.error500("该用户已注销");
            return result;
        }
        //情况3：根据用户信息查询，该用户已冻结
        if (CommonConstant.USER_FREEZE.equals(sysUser.getStatus())) {
            baseCommonService.addLog("用户登录失败，用户名:" + sysUser.getUsername() + "已冻结！", CommonConstant.LOG_TYPE_1, null);
            result.error500("该用户已冻结");
            return result;
        }
        return result;
    }

    @Override
    public List<SysUser> queryLogicDeleted() {
        return this.queryLogicDeleted(null);
    }

    @Override
    public List<SysUser> queryLogicDeleted(LambdaQueryWrapper<SysUser> wrapper) {
        if (wrapper == null) {
            wrapper = new LambdaQueryWrapper<>();
        }
        wrapper.eq(SysUser::getDelFlag, CommonConstant.DEL_FLAG_1);
        return userMapper.selectLogicDeleted(wrapper);
    }

    @Override
    public boolean revertLogicDeleted(List<String> userIds, SysUser updateEntity) {
        String ids = String.format("'%s'", String.join("','", userIds));
        return userMapper.revertLogicDeleted(ids, updateEntity) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLogicDeleted(List<String> userIds) {
        String ids = String.format("'%s'", String.join("','", userIds));
        // 1. 删除用户
        int line = userMapper.deleteLogicDeleted(ids);
        // 2. 删除用户部门关系
        line += sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getUserId, userIds));
        //3. 删除用户角色关系
        line += sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, userIds));
        //TODO 4.同步删除第三方App的用户
//		try {
//			dingtalkService.removeThirdAppUser(userIds);
//			wechatEnterpriseService.removeThirdAppUser(userIds);
//		} catch (Exception e) {
//			log.error("同步删除第三方App的用户失败：", e);
//		}
        //5. 删除第三方用户表（因为第4步需要用到第三方用户表，所以在他之后删）
        line += sysThirdAccountMapper.delete(new LambdaQueryWrapper<SysThirdAccount>().in(SysThirdAccount::getSysUserId, userIds));

        return line != 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNullPhoneEmail() {
        userMapper.updateNullByEmptyString("email");
        userMapper.updateNullByEmptyString("phone");
        return true;
    }

    @Override
    public void saveThirdUser(SysUser sysUser) {
        //保存用户
        String userid = UUIDGenerator.generate();
        sysUser.setId(userid);
        baseMapper.insert(sysUser);
        //获取第三方角色
        SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, "third_role"));
        //保存用户角色
        SysUserRole userRole = new SysUserRole();
        userRole.setRoleId(sysRole.getId());
        userRole.setUserId(userid);
        sysUserRoleMapper.insert(userRole);
    }

    @Override
    public List<SysUser> queryByDepIds(List<String> departIds, String username) {
        return userMapper.queryByDepIds(departIds, username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUser(SysUser user, String selectedRoles, String selectedDeparts) {
        //step.1 保存用户
        this.save(user);
        //step.2 保存角色
        if (WxlConvertUtils.isNotEmpty(selectedRoles)) {
            String[] arr = selectedRoles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
        //step.3 保存所属部门
        if (WxlConvertUtils.isNotEmpty(selectedDeparts)) {
            String[] arr = selectedDeparts.split(",");
            for (String deaprtId : arr) {
                SysUserDepart userDeaprt = new SysUserDepart(user.getId(), deaprtId);
                sysUserDepartMapper.insert(userDeaprt);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public void editUser(SysUser user, String roles, String departs) {
        //step.1 修改用户基础信息
        this.updateById(user);
        //step.2 修改角色
        //处理用户角色 先删后加
        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
        if (WxlConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
        //step.3 修改部门
        String[] arr = {};
        if (WxlConvertUtils.isNotEmpty(departs)) {
            arr = departs.split(",");
        }
        //查询已关联部门
        List<SysUserDepart> userDepartList = sysUserDepartMapper.selectList(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        if (userDepartList != null && userDepartList.size() > 0) {
            for (SysUserDepart depart : userDepartList) {
                //修改已关联部门删除部门用户角色关系
                if (!Arrays.asList(arr).contains(depart.getDepId())) {
                    List<SysDepartRole> sysDepartRoleList = sysDepartRoleMapper.selectList(
                            new QueryWrapper<SysDepartRole>().lambda().eq(SysDepartRole::getDepartId, depart.getDepId()));
                    List<String> roleIds = sysDepartRoleList.stream().map(SysDepartRole::getId).collect(Collectors.toList());
                    if (roleIds != null && roleIds.size() > 0) {
                        departRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId())
                                .in(SysDepartRoleUser::getDroleId, roleIds));
                    }
                }
            }
        }
        //先删后加
        sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        if (WxlConvertUtils.isNotEmpty(departs)) {
            for (String departId : arr) {
                SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
                sysUserDepartMapper.insert(userDepart);
            }
        }
        //step.4 修改手机号和邮箱
        // 更新手机号、邮箱空字符串为 null
        userMapper.updateNullByEmptyString("email");
        userMapper.updateNullByEmptyString("phone");

    }

    @Override
    public List<String> userIdToUsername(Collection<String> userIdList) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getId, userIdList);
        List<SysUser> userList = super.list(queryWrapper);
        return userList.stream().map(SysUser::getUsername).collect(Collectors.toList());
    }

}
