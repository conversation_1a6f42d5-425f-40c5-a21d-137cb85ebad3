//package com.byun.modules.system.style;
//
//import org.apache.poi.hssf.util.HSSFColor;
//import org.apache.poi.ss.usermodel.*;
//import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
//import org.jeecgframework.poi.excel.export.styler.ExcelExportStylerDefaultImpl;
///**
// * <AUTHOR> wxl
// * @version : V1.0
// * @description
// * @date : 2024-3-4 15:57
// */
//public class CustomExcelExportStyler extends ExcelExportStylerDefaultImpl  {
//    public CustomExcelExportStyler(Workbook workbook) {
//        super(workbook);
//    }
//
//    @Override
//    public CellStyle getHeaderStyle(short headerColor) {
//
//        return null;
//    }
//
//    @Override
//    public CellStyle getTitleStyle(short color) {
//
//        return null;
//    }
//
//    @Override
//    public CellStyle getStyles(boolean noneStyler, ExcelExportEntity entity) {
//        System.out.println(entity);
//        CellStyle style = workbook.createCellStyle();
//        Font font = workbook.createFont();
//        font.setColor(IndexedColors.BLACK.getIndex());
//        System.out.println(entity.getName());
//        entity.getObj();
//        return super.getStyles(noneStyler, entity);
//    }
//    //    public CustomExcelExportStyler(Workbook workbook) {
////        super(workbook);
////    }
////
////    @Override
////    public CellStyle getStyles(boolean noneStyler, ExcelExportEntity entity) {
////        // 设置单元格样式
////        if (entity != null && entity instanceof YourEntity) {
////            YourEntity yourEntity = (YourEntity) entity;
////            // 根据需要判断条件，设置个别单元格样式
////            if ("特定条件".equals(yourEntity.getSpecialCondition())) {
////                // 设置特定样式
////                // 例如：设置字体颜色为红色
////                CellStyle style = workbook.createCellStyle();
////                Font font = workbook.createFont();
////                font.setColor(IndexedColors.RED.getIndex());
////                style.setFont(font);
////                return style;
////            }
////        }
////        return null;
////    }
////
////
////    /**
////     * descroption: 设置循环行有样式的一行（来实现换行变色效果）
////     * @author: Ye
////     * @date @time 2022/10/27 15:48
////     * @param workbook
////     * @param isWarp
////     * @return
////     */
////    @Override
////    public CellStyle stringSeptailStyle(Workbook workbook, boolean isWarp) {
////        CellStyle style = workbook.createCellStyle();
////        style.setBorderLeft(BorderStyle.THIN);
////        style.setBorderRight(BorderStyle.THIN);
////        style.setBorderBottom(BorderStyle.THIN);
////        style.setBorderTop(BorderStyle.THIN);
////        //设置填充前景色   LIGHT_TURQUOISE  浅绿松石
////        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_TURQUOISE.getIndex());
////        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
////        style.setAlignment(HorizontalAlignment.CENTER);
////        style.setVerticalAlignment(VerticalAlignment.CENTER);
////        style.setDataFormat(STRING_FORMAT);
////        if (isWarp) {
////            style.setWrapText(true);
////        }
////        return style;
////    }
////
////    /**
////     * descroption: 这里设置循环行，没有样式的一行
////     * @author: Ye
////     * @date @time 2022/10/27 15:46
////     * @param workbook
////     * @param isWarp
////     * @return
////     */
////    @Override
////    public CellStyle stringNoneStyle(Workbook workbook, boolean isWarp) {
////        CellStyle style = workbook.createCellStyle();
////        //四个边的边框
////        style.setBorderLeft(BorderStyle.THIN);
////        style.setBorderRight(BorderStyle.THIN);
////        style.setBorderBottom(BorderStyle.THIN);
////        style.setBorderTop(BorderStyle.THIN);
////        style.setAlignment(HorizontalAlignment.CENTER);
////        style.setVerticalAlignment(VerticalAlignment.CENTER);
////        style.setDataFormat(STRING_FORMAT);
////        if (isWarp) {
////            style.setWrapText(true);
////        }
////        return style;
////    }
//}
