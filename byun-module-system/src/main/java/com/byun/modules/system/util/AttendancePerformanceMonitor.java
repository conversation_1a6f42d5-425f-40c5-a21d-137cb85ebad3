package com.byun.modules.system.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 考勤分析性能监控工具
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Component
public class AttendancePerformanceMonitor {

    private final ConcurrentHashMap<String, AtomicLong> executionCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> totalExecutionTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> maxExecutionTimes = new ConcurrentHashMap<>();

    /**
     * 记录方法执行时间
     * 
     * @param methodName 方法名
     * @param executionTime 执行时间（毫秒）
     */
    public void recordExecution(String methodName, long executionTime) {
        executionCounts.computeIfAbsent(methodName, k -> new AtomicLong(0)).incrementAndGet();
        totalExecutionTimes.computeIfAbsent(methodName, k -> new AtomicLong(0)).addAndGet(executionTime);
        maxExecutionTimes.computeIfAbsent(methodName, k -> new AtomicLong(0))
                .updateAndGet(current -> Math.max(current, executionTime));
        
        // 记录慢查询（超过1秒）
        if (executionTime > 1000) {
            log.warn("慢查询检测 - 方法: {}, 执行时间: {}ms", methodName, executionTime);
        }
    }

    /**
     * 获取方法性能统计
     * 
     * @param methodName 方法名
     * @return 性能统计信息
     */
    public String getPerformanceStats(String methodName) {
        long count = executionCounts.getOrDefault(methodName, new AtomicLong(0)).get();
        long totalTime = totalExecutionTimes.getOrDefault(methodName, new AtomicLong(0)).get();
        long maxTime = maxExecutionTimes.getOrDefault(methodName, new AtomicLong(0)).get();
        
        if (count == 0) {
            return String.format("方法 %s 未执行过", methodName);
        }
        
        long avgTime = totalTime / count;
        return String.format("方法: %s, 执行次数: %d, 平均耗时: %dms, 最大耗时: %dms, 总耗时: %dms", 
                methodName, count, avgTime, maxTime, totalTime);
    }

    /**
     * 获取所有方法的性能统计
     */
    public void logAllPerformanceStats() {
        log.info("=== 考勤分析性能统计报告 ===");
        executionCounts.keySet().forEach(methodName -> {
            log.info(getPerformanceStats(methodName));
        });
        log.info("=== 性能统计报告结束 ===");
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        executionCounts.clear();
        totalExecutionTimes.clear();
        maxExecutionTimes.clear();
        log.info("性能监控统计数据已重置");
    }

    /**
     * 性能监控装饰器
     */
    public <T> T monitor(String methodName, MonitoredOperation<T> operation) throws Exception {
        long startTime = System.currentTimeMillis();
        try {
            T result = operation.execute();
            long executionTime = System.currentTimeMillis() - startTime;
            recordExecution(methodName, executionTime);
            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            recordExecution(methodName + "_ERROR", executionTime);
            throw e;
        }
    }

    /**
     * 监控操作接口
     */
    @FunctionalInterface
    public interface MonitoredOperation<T> {
        T execute() throws Exception;
    }
}
