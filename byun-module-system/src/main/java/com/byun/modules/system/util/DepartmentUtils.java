package com.byun.modules.system.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.system.vo.LoginUser;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.system.entity.SysDepart;
import com.byun.modules.system.service.ISysDepartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 部门相关工具类
 * 提供部门信息查询和组织代码获取的通用方法
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
@Component
public class DepartmentUtils {

    @Autowired
    private ISysDepartService sysDepartService;

    /**
     * 根据部门编号和部门名称获取组织代码
     * 
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @param fallbackOrgCode 回退的组织代码（当查询不到部门信息时使用）
     * @return 组织代码
     */
    public String getOrgCodeByDepartment(String deptNo, String deptName, String fallbackOrgCode) {
        // 如果部门编号和部门名称都为空，直接返回回退的组织代码
        if (WxlConvertUtils.isEmpty(deptNo) || WxlConvertUtils.isEmpty(deptName)) {
            log.debug("部门编号或部门名称为空，使用回退组织代码：{}", fallbackOrgCode);
            return fallbackOrgCode;
        }

        try {
            log.debug("查询部门信息：deptNo={}, deptName={}", deptNo, deptName);
            
            // 根据部门编号和部门名称查询部门信息
            SysDepart sysDepart = sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>()
                    .eq(SysDepart::getStoreNo, deptNo)
                    .eq(SysDepart::getDepartName, deptName)
                    .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));

            if (sysDepart != null && WxlConvertUtils.isNotEmpty(sysDepart.getOrgCode())) {
                log.debug("找到部门信息，使用部门的组织代码：{}", sysDepart.getOrgCode());
                return sysDepart.getOrgCode();
            } else {
                log.warn("未找到匹配的部门信息，使用回退组织代码：{}", fallbackOrgCode);
                return fallbackOrgCode;
            }
        } catch (Exception e) {
            log.error("查询部门信息失败，使用回退组织代码：{}", fallbackOrgCode, e);
            return fallbackOrgCode;
        }
    }

    /**
     * 根据部门编号和部门名称获取组织代码（使用用户的组织代码作为回退）
     * 
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @param user 当前登录用户（用于获取回退的组织代码）
     * @return 组织代码
     */
    public String getOrgCodeByDepartment(String deptNo, String deptName, LoginUser user) {
        String fallbackOrgCode = (user != null) ? user.getOrgCode() : null;
        return getOrgCodeByDepartment(deptNo, deptName, fallbackOrgCode);
    }

    /**
     * 根据部门编号和部门名称获取组织代码（无回退）
     * 
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @return 组织代码，如果查询不到则返回null
     */
    public String getOrgCodeByDepartment(String deptNo, String deptName) {
        return getOrgCodeByDepartment(deptNo, deptName, (String) null);
    }

    /**
     * 根据部门编号获取部门信息
     * 
     * @param deptNo 部门编号
     * @return 部门信息，如果查询不到则返回null
     */
    public SysDepart getDepartmentByNo(String deptNo) {
        if (WxlConvertUtils.isEmpty(deptNo)) {
            return null;
        }

        try {
            return sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>()
                    .eq(SysDepart::getStoreNo, deptNo)
                    .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));
        } catch (Exception e) {
            log.error("根据部门编号查询部门信息失败：deptNo={}", deptNo, e);
            return null;
        }
    }

    /**
     * 根据部门编号和部门名称获取部门信息
     * 
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @return 部门信息，如果查询不到则返回null
     */
    public SysDepart getDepartmentByNoAndName(String deptNo, String deptName) {
        if (WxlConvertUtils.isEmpty(deptNo) || WxlConvertUtils.isEmpty(deptName)) {
            return null;
        }

        try {
            return sysDepartService.getOne(new LambdaQueryWrapper<SysDepart>()
                    .eq(SysDepart::getStoreNo, deptNo)
                    .eq(SysDepart::getDepartName, deptName)
                    .eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0));
        } catch (Exception e) {
            log.error("根据部门编号和名称查询部门信息失败：deptNo={}, deptName={}", deptNo, deptName, e);
            return null;
        }
    }

    /**
     * 验证部门编号和部门名称是否匹配
     * 
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @return true表示匹配，false表示不匹配
     */
    public boolean validateDepartment(String deptNo, String deptName) {
        SysDepart department = getDepartmentByNoAndName(deptNo, deptName);
        return department != null;
    }

    /**
     * 获取部门的完整信息（包括组织代码）
     * 
     * @param deptNo 部门编号
     * @param deptName 部门名称
     * @return 部门信息结果对象
     */
    public DepartmentResult getDepartmentResult(String deptNo, String deptName) {
        SysDepart department = getDepartmentByNoAndName(deptNo, deptName);
        
        if (department != null) {
            return new DepartmentResult(true, department.getOrgCode(), department, "查询成功");
        } else {
            return new DepartmentResult(false, null, null, "未找到匹配的部门信息");
        }
    }

    /**
     * 部门查询结果封装类
     */
    public static class DepartmentResult {
        private boolean success;
        private String orgCode;
        private SysDepart department;
        private String message;

        public DepartmentResult(boolean success, String orgCode, SysDepart department, String message) {
            this.success = success;
            this.orgCode = orgCode;
            this.department = department;
            this.message = message;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getOrgCode() { return orgCode; }
        public SysDepart getDepartment() { return department; }
        public String getMessage() { return message; }

        // Setters
        public void setSuccess(boolean success) { this.success = success; }
        public void setOrgCode(String orgCode) { this.orgCode = orgCode; }
        public void setDepartment(SysDepart department) { this.department = department; }
        public void setMessage(String message) { this.message = message; }
    }
}
