package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 年龄分布统计DTO
 */
@Data
@ApiModel(value = "年龄分布统计", description = "年龄分布统计")
public class AgeDistributionDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 年龄范围
     */
    @ApiModelProperty(value = "年龄范围")
    private String ageRange;
    
    /**
     * 人数
     */
    @ApiModelProperty(value = "人数")
    private Integer count;
    
    /**
     * 百分比
     */
    @ApiModelProperty(value = "百分比")
    private BigDecimal percentage;
    
    public AgeDistributionDTO() {
    }
    
    public AgeDistributionDTO(String ageRange, Integer count, BigDecimal percentage) {
        this.ageRange = ageRange;
        this.count = count;
        this.percentage = percentage;
    }
}
