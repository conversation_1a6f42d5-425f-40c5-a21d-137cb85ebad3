package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 年龄和学历分布统计DTO
 */
@Data
@ApiModel(value = "年龄和学历分布统计", description = "年龄和学历分布统计")
public class AgeEducationStatsDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 年龄分布
     */
    @ApiModelProperty(value = "年龄分布")
    private List<AgeDistributionDTO> ageDistribution;
    
    /**
     * 学历分布
     */
    @ApiModelProperty(value = "学历分布")
    private List<EducationDistributionDTO> educationDistribution;
    
    public AgeEducationStatsDTO() {
    }
    
    public AgeEducationStatsDTO(List<AgeDistributionDTO> ageDistribution, List<EducationDistributionDTO> educationDistribution) {
        this.ageDistribution = ageDistribution;
        this.educationDistribution = educationDistribution;
    }
}
