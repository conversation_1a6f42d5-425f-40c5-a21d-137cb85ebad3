package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 考勤状态统计DTO
 */
@Data
@ApiModel(value = "考勤状态统计", description = "考勤状态统计")
public class AttendanceStatusDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String status;
    
    /**
     * 人数
     */
    @ApiModelProperty(value = "人数")
    private Integer count;
    
    /**
     * 百分比
     */
    @ApiModelProperty(value = "百分比")
    private BigDecimal percentage;
    
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String color;
    
    public AttendanceStatusDTO() {
    }
    
    public AttendanceStatusDTO(String status, Integer count, BigDecimal percentage, String color) {
        this.status = status;
        this.count = count;
        this.percentage = percentage;
        this.color = color;
    }
}
