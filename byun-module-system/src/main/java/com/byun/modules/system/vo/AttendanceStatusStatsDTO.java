package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 考勤状况统计DTO
 */
@Data
@ApiModel(value = "考勤状况统计", description = "考勤状况统计")
public class AttendanceStatusStatsDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 统计日期
     */
    @ApiModelProperty(value = "统计日期")
    private String date;
    
    /**
     * 总员工数
     */
    @ApiModelProperty(value = "总员工数")
    private Integer totalEmployees;
    
    /**
     * 考勤状态统计列表
     */
    @ApiModelProperty(value = "考勤状态统计列表")
    private List<AttendanceStatusDTO> attendanceStats;
    
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
    public AttendanceStatusStatsDTO() {
    }
    
    public AttendanceStatusStatsDTO(String date, Integer totalEmployees, List<AttendanceStatusDTO> attendanceStats, LocalDateTime updateTime) {
        this.date = date;
        this.totalEmployees = totalEmployees;
        this.attendanceStats = attendanceStats;
        this.updateTime = updateTime;
    }
}
