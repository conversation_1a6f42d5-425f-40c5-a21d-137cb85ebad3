package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 每日入职统计DTO
 */
@Data
@ApiModel(value = "每日入职统计", description = "每日入职统计")
public class DailyJoinStatsDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String date;
    
    /**
     * 入职人数
     */
    @ApiModelProperty(value = "入职人数")
    private Integer count;
    
    public DailyJoinStatsDTO() {
    }
    
    public DailyJoinStatsDTO(String date, Integer count) {
        this.date = date;
        this.count = count;
    }
}
