package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 学历分布统计DTO
 */
@Data
@ApiModel(value = "学历分布统计", description = "学历分布统计")
public class EducationDistributionDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String education;
    
    /**
     * 人数
     */
    @ApiModelProperty(value = "人数")
    private Integer count;
    
    /**
     * 百分比
     */
    @ApiModelProperty(value = "百分比")
    private BigDecimal percentage;
    
    public EducationDistributionDTO() {
    }
    
    public EducationDistributionDTO(String education, Integer count, BigDecimal percentage) {
        this.education = education;
        this.count = count;
        this.percentage = percentage;
    }
}
