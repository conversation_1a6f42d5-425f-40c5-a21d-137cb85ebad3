package com.byun.modules.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 时间段入职统计DTO
 */
@Data
@ApiModel(value = "时间段入职统计", description = "时间段入职统计")
public class JoinsByPeriodDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 每日入职统计列表
     */
    @ApiModelProperty(value = "每日入职统计列表")
    private List<DailyJoinStatsDTO> dailyJoinStats;
    
    /**
     * 总入职人数
     */
    @ApiModelProperty(value = "总入职人数")
    private Integer totalJoins;
    
    public JoinsByPeriodDTO() {
    }
    
    public JoinsByPeriodDTO(List<DailyJoinStatsDTO> dailyJoinStats, Integer totalJoins) {
        this.dailyJoinStats = dailyJoinStats;
        this.totalJoins = totalJoins;
    }
}
