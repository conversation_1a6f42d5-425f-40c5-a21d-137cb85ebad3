package com.byun.modules.system.vo;
import com.byun.common.aspect.annotation.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
/**
 * <AUTHOR> wxl
 * @version : V1.0
 * @description 人员生命周期明细
 * @date : 2024-7-9 15:35
 */
@Data
public class SysUserInfoVo {
    private String id; //用户id
    @Excel(name = "事业处",width = 20)
    private String businessDivisionName; //事业处
    @Excel(name = "区域",width = 20)
    private String regionName; //区域
    private String currentStoresId; //门店id
    @Excel(name = "店编",width = 20)
    private String storeNo; //店编
    @Excel(name = "店别",width = 20)
    private String currentStores; //门店名称
    @Excel(name = "任务名称",width = 20)
    private String currentWorkName;//任务名称
    @Excel(name = "姓名",width = 20)
    private String realNameName; //姓名
    @Excel(name = "身份证",width = 30)
    private String idCard; //身份证号码
    //@Excel(name = "生日",width = 30)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday; //生日
    @Excel(name = "性别",width = 15)
    @Dict(dicCode = "sex")
    private String sex; //性别
    @Excel(name = "年龄",width = 15)
    private Integer age;//年龄
    @Excel(name = "电话",width = 20)
    private String username;//电话
    @Excel(name = "学历",width = 20)
    private String degree;//学历
    @Excel(name = "学校",width = 25)
    private String school; //学校名称
    @Excel(name = "专业",width = 20)
    private String major; //专业
    @Excel(name = "总时数",width = 15)
    private double statisticalWorkingHours;//总时数
    @Excel(name = "司龄",width = 15)
    private int companyServiceDays; //司龄
    @Excel(name = "任务数",width = 15)
    private int taskTotal;//任务数
}
