package com.byun.modules.zh.controller;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.byun.modules.system.service.ISysUserService;
import com.byun.modules.zh.service.IZhService;
import com.byun.modules.zh.utils.RSAUtils;
import com.byun.modules.zh.vo.RequestVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Slf4j
@RestController
@RequestMapping("test")
public class Test {
    //公钥
    private final String publicKey = "";
    @Autowired
    private IZhService demoService;
    @Autowired
    private ISysUserService sysUserService;
    /**
     * 通用回调
     */
    @PostMapping("callBack")
    public void callBack(@RequestBody RequestVO requestVO) {
        try {
            log.info("回调参数：" + JSON.toJSONString(requestVO));
            //验签
            String data = JSON.toJSONString(requestVO.getData());
            Map<String, String> map = JSONObject.parseObject(data, Map.class);
            //拼接参数
            StringBuilder content = new StringBuilder();
            List<String> keys = new ArrayList(map.keySet());
            for (int i = 0; i < keys.size(); ++i) {
                String key = keys.get(i);
                String value = map.get(key);
                content.append((i == 0 ? "" : "&") + key + "=" + value);
            }
            content.append("&signDate=" + requestVO.getSign().getSignDate());
            log.info("签名原文：" + content);
            String signStr = content.toString();
            boolean verify = RSAUtils.verifyByPublicKey(signStr.getBytes(), publicKey, requestVO.getSign().getSignature());
            if (verify) {
                log.info("验签成功");
                return;
            }
            log.info("验签失败");
        } catch (Exception e) {
            log.error("异常", e);
        }
    }
    /**
     * 获取token
     */
    @GetMapping("/aa")
    public void aa() throws Exception {
        //SysUser sysUser = sysUserService.getById("");
        //获取Token
        //demoService.getToken();
        //注册
        //demoService.personAuth();
        //获取签约任务
        //demoService.getSingTask();
        /**
         * {
         * 	"success": true,
         * 	"resultCode": 0,
         * 	"resultMessage": "请求成功",
         * 	"resultData": {
         * 		"code": 1,
         * 		"message": "请求成功",
         * 		"data": [{
         * 			"taskNo": "QY20240603629900001",
         * 			"taskName": "测试-技术服务",
         * 			"status": 1,
         * 			"taxCompany": "河南省鸿运信息技术有限公司 "
         *                }]* 	}
         * }
         */
        //获取签约接口
        //demoService.singPerson();
        /**
         * {
         *     "success": true,
         *     "resultCode": 0,
         *     "resultMessage": "请求成功",
         *     "resultData": {
         *         "code": 1,
         *         "message": "请求成功",
         *         "data": {
         *             "singNo": "1ddbfe85eb754d7295b17dbee7343573",
         *             "singUrl": "https://jlm.h5.esign.cn/mesign/guide?context=SNefreH&flowId=1ddbfe85eb754d7295b17dbee7343573&organ=false&appId=5111678444&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_WUKONG&tsign_source_detail=1vGqUyZBm8k4aIosrZzTBwX2N5c5zALDf%2FY9Jpgt72fPDbhsBANy932c5NKzMr419x3BS5dQb3h%2FwnoMSoAAMBGOsfUoxpVOXLIgKKcKL60EEsHkHCwnvaIYdhrKo5Eegq63gEWpOF516IQ0Y5x9gtOWtcO8UI%2FL0LX%2FGti8WaKrQZZtnKPeJJMCCVVbf5ve1HV6Bg8X0ftYH4akHWA0Q8ST7YpkL%2Fat5aQC1xJNlQV0WGKhWWm%2BLayiPiCsKWIHu",
         *             "singContractUrl": null,
         *             "singStatus": 0
         *         }
         *     }
         * }
         */

    }
}