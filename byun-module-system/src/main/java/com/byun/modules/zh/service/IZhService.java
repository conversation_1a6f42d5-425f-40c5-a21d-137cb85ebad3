package com.byun.modules.zh.service;
import com.alibaba.fastjson.JSONObject;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.zh.vo.DoWithdrawVO;
import com.byun.modules.zh.vo.PersonAuthVO;
import com.byun.modules.zh.vo.WithdrawListVO;

import java.util.List;

public interface IZhService {
    /**
     * 获取Token
     * @throws Exception
     */
    String getToken() throws Exception;

    /**
     * 注册接口
     * @throws Exception
     */
    JSONObject personAuth(PersonAuthVO personAuthVO,String accessToken) throws Exception;
    /**
     * 获取签约任务信息接口
     * @throws Exception
     */
    void getSingTask() throws Exception;
    /**
     * 签约
     * @throws Exception
     */
    JSONObject singPerson(SysUser user, String accessToken) throws Exception;
    /**
     * 提现
     * @throws Exception
     */
    JSONObject doWithdraw(List<WithdrawListVO> list,DoWithdrawVO doWithdrawVO,String accessToken) throws Exception;
    /**
     * MOCK结算完成
     * @throws Exception
     */
    void mockSettle() throws Exception;
    /**
     * 提现查询
     * @throws Exception
     */
    void queryWithdraw() throws Exception;
    /**
     * 查询电子回单
     * @throws Exception
     */
    void getBankReceipt() throws Exception;
}
