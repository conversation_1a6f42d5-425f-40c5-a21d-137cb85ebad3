package com.byun.modules.zh.service.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byun.common.constant.CommonConstant;
import com.byun.common.util.WxlConvertUtils;
import com.byun.modules.system.entity.StaUserBankCards;
import com.byun.modules.system.entity.SysUser;
import com.byun.modules.system.service.IStaUserBankCardsService;
import com.byun.modules.zh.constants.VerifFilterKeyConstants;
import com.byun.modules.zh.service.IZhService;
import com.byun.modules.zh.utils.HttpUtils;
import com.byun.modules.zh.utils.RSAUtils;
import com.byun.modules.zh.vo.*;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.SimpleDateFormat;
import java.util.*;
@Service
public class ZhServiceImpl implements IZhService {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
    //私钥
   // @Value("${zh.privateKey}")
    private  String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCiYm5r55ehhSUbpe8yZ/CpsXwx7oxcQCBAgTdVhDwqoTw5c4C8mCvB+Jki2gEW6PCPbI0T16cHIVMAfd0xXpyTKsAGOo+y8KngPnM//qbSamPnGSXNMTMmiHTQTNKgSwzRm+fgkvmli/OssuxW4VSmq+liLayigxJPXNOqkQyDaj3c5tfa8vOmr+Idu1bPg4q22psv4zLOwlZH5xNt0kaqDlpvmIMAJBs9HhDvUa3id4iBZ+x8zjzSgkyF5FSs1DyEWe5aLfdeYMSOK4R1JTe5BiUgjmeStRs17eddjrq+TAVsnqZ1ERrQHQUlMb9pjZQoCGSXtNEf1n0gf2AaB1e5AgMBAAECggEAPiq033agZGPW2wjh1EhaqHyccXlEHyUls8xEZOblKeTka5I+nPUc+3uzFNalSCIRxrbmI8yR03b8kdmdzwLLqsPZFAljbjwYrRL7iGpzb2bvrE/CWxYU4RGotv5SPK++zpiDY0ShcrFrrpgpRvGpOM3j4YRe7TCE2AFPv6McSUrNBSM8ZuH0GdfSbLZiWVKvX4LXORAOmBs1oA+ZssWwCqla3rJkH5SQJxWDyqN7ced2tPttra7JIaGCXAU9Ct+Als3r81pvTBOR8vTyYE8WgPy0eWlmqTlkaj16M2qXHoYO2iEc4Yf1cC5S/4+AFZy0SAKeKop9oGzV7maNbS9aAQKBgQDSvOcvxpQUp0CDmL3HtJPVjyr45F78RfHrzPhEmOKef+Sgnmj0ezlXpAAY1dFal3mCn/VlBF09Jf+f33B/IV+ovZy++OBArnSnq6qlnOfI2/iqdSdo1/0vsSERD04IRCrB8BYPvZV5D+pQhkOT+V9Ju6HzOR7vTnViMMK39Wu72QKBgQDFQuQnO0MGyujOR9gfYeZVfLfKAdC9tKY9BZposXnSPUpCNsGzMKbFruTshw0MSNHl1nzZIEfKJK2RrXS92gPWLRKUJ6vsiOqkr1XHWA00QVFrBDu2R6AB2OWGSFOmPgARjLyYPZtFwbXoVQh/a4+XFWClRZnmhb97WEEY1KBu4QKBgQDJSmStDsnM/ICz8kZ8JfD7kRfjywcrgof0ysDtjPcRnGm+PfFUbQs1ulHZZwrktED9U1rrVYLV8KC9jYh/9lnP7OV2yHQzdC+7JE8Ih9oh0nMwJl0xWPXfAxrpl6vaW3pX6sK34EeQ7nHK3qRYCF9LJ20mzI3O2StoUdj8K/Du6QKBgGVGXXqi21uua24Uxqn+ClKTkIx++Budlse3i7n6fE/rNaffEv2bmdPeYhvpjlJjRJha0YhsIU9wG0iypEAgoV2hkGtOHt92v/lDJ6gL1eOhMVrfNoT0Kvsnz1ds5L1yEXjxJ5aF8qUSugEJPy6kG3l2+UKBBG2s900tV2aXK7MhAoGAa/4ZloP53/uX5AwIYmHiZrPMJAJensqomTfQbbsUrVKZBCqjafdlMyra4msLXJ4Ge9J514c0ZL9ck11pVzcQQkosV8qh7jdzKDJZtjrhs1wFsBowDWBfO5Gy7fLPTF/2TXMIHyTGEwv0NbDwzoOJ9hxCwyuDzf1FcLoTEl9+OR8=";
    //客户端编码
   // @Value("${zh.clientId}")
    private  String clientId = "ec19b7a394803fb6e72f7ee0156ccc2b";
    //客户端密码
   // @Value("${zh.secret}")
    private  String secret = "88109333782a9d109d1efb1d12064ff1";
    //请求路径
   // @Value("${zh.getTokenUrl}")
    private  String getTokenUrl = "https://jlmapinew.zanhua.com.cn/api/open/authorize";
    //@Value("${zh.commonUrl}")
    private  String commonUrl = "https://jlmapinew.zanhua.com.cn/api/v1";
    //获取token body固定值
   // @Value("${zh.getTokenParam}")
    private  String getTokenParam = "grant_type=client_credentials";
   // @Value("${zh.taskNo}")
    //银行卡签约编号
    private String taskNo ="QY20240611574000006";
    //支付宝签约编号
    private String alipayTaskNo = "QY20240617859200001";
    //token
    private final String accessToken = "";
    @Autowired
    private IStaUserBankCardsService staUserBankCardsService;
    @Override
    public String getToken() throws Exception {
        //获取签名
        String signDate = sdf.format(new Date());
        String signStr = getTokenParam + "&signDate=" + signDate;
        String signature = RSAUtils.signByPrivateKey(signStr.getBytes(), privateKey);
        SignVO signVO = new SignVO();
        signVO.setSignature(signature);
        signVO.setSignDate(signDate);
        //组装body参数
        RequestVO requestVO = new RequestVO();
        requestVO.setData(getTokenParam);
        requestVO.setSign(signVO);
        //组装header参数
        String authorization = new String(Base64.encode((clientId + ":" + secret).getBytes()));
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("Authorization", authorization);
        //发送请求
        String s = this.sendPost(getTokenUrl, requestVO, headerParam);
        JSONObject jsonObject = JSONObject.parseObject(s);
        Boolean success = jsonObject.getBoolean("success");
        if (success) {
            JSONObject resultData = jsonObject.getJSONObject("resultData");
            JSONObject data = resultData.getJSONObject("data");
            String token = data.getString("accessToken");
            return token;
        }
        return null;
    }
    public static String processUrl(String url) {
        if (url == null || url.isEmpty()) {
            return url; // 或者抛出一个异常，取决于你的需求
        }
        // 检查是否以'['开头
        if (url.startsWith("[")) {
            url = url.substring(1); // 删除开头的'['
        }
        // 检查是否以']'结尾
        if (url.endsWith("]")) {
            url = url.substring(0, url.length() - 1); // 删除结尾的']'
        }
        return url;
    }
    /**
     * 人员注册
     * @throws Exception
     */
    @Override
    public JSONObject personAuth(PersonAuthVO personAuthVO,String accessToken ) throws Exception {
        //获取签名
        SignVO sign = this.getSign(personAuthVO);
        //certImgFrontHttp  certImgBackHttp
        personAuthVO.setCertImgFrontHttp(processUrl(personAuthVO.getCertImgFrontHttp()));
        personAuthVO.setCertImgBackHttp(processUrl(personAuthVO.getCertImgBackHttp()));
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("personAuth");
        requestVO.setData(personAuthVO);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //发送请求
        String result = this.sendPost(commonUrl, requestVO, headerParam);
        JSONObject jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }

    /**
     * 获取签约任务信息接口
     * @throws Exception
     */
    @Override
    public void getSingTask() throws Exception {
        GetSingTaskVO getSingTaskVO = new GetSingTaskVO();
//        getSingTaskVO.setTaskNo("1231231231");
//        getSingTaskVO.setTaskName("测试");
        SignVO sign = this.getSign(getSingTaskVO);
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("getSingTask");
        requestVO.setData(getSingTaskVO);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //headerParam.put("accessToken", "7c7cd15418504675bd99847a83594bf8");
        //发送请求
        this.sendPost(commonUrl, requestVO, headerParam);
    }

    /**
     * 签约
     * @throws Exception
     */
    @Override
    public JSONObject singPerson(SysUser user,String accessToken) throws Exception {
        SingPersonVO singPersonVO = new SingPersonVO();
        singPersonVO.setName(user.getRealNameName()); //姓名
        singPersonVO.setCertNo(user.getIdCard());//身份证
        StaUserBankCards staUserBankCards = staUserBankCardsService.getOne(new QueryWrapper<StaUserBankCards>().lambda().eq(StaUserBankCards::getUserId, user.getId()));
        int accountType = 1 ;
        if (WxlConvertUtils.isNotEmpty(staUserBankCards)) {
            accountType = staUserBankCards.getAccountType();
        }
        singPersonVO.setTaskNo(accountType == CommonConstant.ACCOUNTTYPE1 ? taskNo : alipayTaskNo); //任务编号 1银行卡 2支付宝
        SignVO sign = this.getSign(singPersonVO);
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("singPerson");
        requestVO.setData(singPersonVO);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //发送请求
        String result = this.sendPost(commonUrl, requestVO, headerParam);
        JSONObject jsonObjectResult = JSONObject.parseObject(result);
        return jsonObjectResult;
    }

    /**
     * 提现
     * @throws Exception
     */
    @Override
    public JSONObject doWithdraw(List<WithdrawListVO> list,DoWithdrawVO doWithdrawVO,String accessToken) throws Exception {
        //List<WithdrawListVO> list = new ArrayList<>();
//        WithdrawListVO withdrawListVO = new WithdrawListVO();
//        withdrawListVO.setBizId(UUID.randomUUID().toString());
//        withdrawListVO.setName("张三");
//        withdrawListVO.setCertNo("111111198901011111");
//        withdrawListVO.setMobile("***********");
//        withdrawListVO.setBank("招商银行");
//        withdrawListVO.setBankNo("****************");
//        withdrawListVO.setAmount("5000");
//        withdrawListVO.setAccountType(1);//收款方式 1银行卡 2支付宝
//        list.add(withdrawListVO);
        //DoWithdrawVO doWithdrawVO = new DoWithdrawVO();
//
//        doWithdrawVO.setTaskNo("******************");
//        doWithdrawVO.setBatchBizId(UUID.randomUUID().toString());
//        doWithdrawVO.setTaskStartDate("2022-08-30 00:00:00");
//        doWithdrawVO.setTaskEndDate("2022-08-30 23:59:59");
//        doWithdrawVO.setTaskDetail("idea测试");
        doWithdrawVO.setWithdrawList(JSONArray.toJSONString(list));
        SignVO sign = this.getSign(doWithdrawVO);
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("doWithdraw");
        requestVO.setData(doWithdrawVO);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //发送请求
        String result  = this.sendPost(commonUrl, requestVO, headerParam);
        JSONObject jsonObjectResult = JSONObject.parseObject(result);
        return jsonObjectResult;
    }

    /**
     * 结算完成
     * @throws Exception
     */
    @Override
    public void mockSettle() throws Exception {
        Map<String, String> param = new HashMap<>(2);
        param.put("batchBizId", "");
        param.put("bizId", "c98804f2-e815-42a5-9fa6-4f5d7c17389c");
        SignVO sign = this.getSign(param);
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("mockSettle");
        requestVO.setData(param);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //发送请求
        this.sendPost(commonUrl, requestVO, headerParam);
    }

    /**
     * 提现查询接口
     * @throws Exception
     */
    @Override
    public void queryWithdraw() throws Exception {
        QueryWithdrawVO queryWithdrawVO = new QueryWithdrawVO();
//        queryWithdrawVO.setBizId("d5968521-5f78-4bbd-afff-e39aa0f014d4");
        queryWithdrawVO.setBatchBizId("ae13c9cc-2cdb-42b1-972a-b1e5a86e62db");
        SignVO sign = this.getSign(queryWithdrawVO);
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("queryWithdraw");
        requestVO.setData(queryWithdrawVO);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //发送请求
        this.sendPost(commonUrl, requestVO, headerParam);
    }
    /**
     * 查询电子回单接口
     * @throws Exception
     */
    @Override
    public void getBankReceipt() throws Exception {
        Map<String, String> param = new HashMap<>();
        param.put("batchBizId", "**********");
//        param.put("bizId", "789645");
        SignVO sign = this.getSign(param);
        //拼接请求参数
        RequestVO requestVO = new RequestVO();
        requestVO.setType("getBankReceipt");
        requestVO.setData(param);
        requestVO.setSign(sign);
        Map<String, String> headerParam = new HashMap<>();
        headerParam.put("accessToken", accessToken);
        //发送请求
        this.sendPost(commonUrl, requestVO, headerParam);
    }

    /**
     * 获取签名
     */
    private SignVO getSign(Object obj) throws Exception {
        //获取签名
        Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(obj), Map.class);
        List<String> keys = new ArrayList(map.keySet());
        Collections.sort(keys);
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < keys.size(); ++i) {
            String key = keys.get(i);
            if (VerifFilterKeyConstants.CERT_IMG_BACK.equals(key)
                    || VerifFilterKeyConstants.CERT_IMG_FRONT.equals(key)
                    || VerifFilterKeyConstants.CERT_IMG_FRONT_HTTP.equals(key)
                    || VerifFilterKeyConstants.CERT_IMG_BACK_HTTP.equals(key)
                    || VerifFilterKeyConstants.ACCOUNT_TYPE.equals(key)) {
                continue;
            }
            String value = map.get(key).toString();
            content.append((i == 0 ? "" : "&") + key + "=" + value);
        }
        String signDate = sdf.format(new Date());
        content.append("&signDate=" + signDate);
        //System.out.println("签名原文：" + content.toString());
        String signature = RSAUtils.signByPrivateKey(content.toString().getBytes(), privateKey);
        SignVO signVO = new SignVO();
        signVO.setSignature(signature);
        signVO.setSignDate(signDate);
        return signVO;
    }

    /**
     * 发送请求
     */
    private String sendPost(String url, RequestVO requestVO, Map<String, String> headerParam) throws Exception {
        //发送请求
        System.out.println("请求头：" + JSON.toJSONString(headerParam));
        System.out.println("请求参数：" + JSON.toJSONString(requestVO));
        String response = HttpUtils.sendJsonPostUTF8(url, JSON.toJSONString(requestVO), headerParam);
        return response;
        //System.out.println("响应参数：" + response);
    }
}
