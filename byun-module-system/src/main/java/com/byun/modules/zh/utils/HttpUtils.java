package com.byun.modules.zh.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;


/**
 * @类名: HttpUtils
 * @date 2019/5/24 0024
 */
@Slf4j
public class HttpUtils {

    /**
     * HHTP发送JSON格式的post请求（UTF-8）
     *
     * @return java.lang.String
     * @Title: sendJsonPostUTF8
     * @date 2019/6/25 0025
     */
    public static String sendJsonPostUTF8(String urlPath, String Json, Map<String, String> header) throws Exception {
        // HttpClient 6.0被抛弃了
        String result = "";
        BufferedReader reader = null;
        URL url = new URL(urlPath);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        conn.setRequestProperty("Connection", "Keep-Alive");
        conn.setRequestProperty("Charset", "UTF-8");
        // 设置文件类型:
        conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        if (null != header) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                conn.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }
        // 设置接收类型否则返回415错误
        //conn.setRequestProperty("accept","*/*")此处为暴力方法设置接受所有类型，以此来防范返回415;
        conn.setRequestProperty("accept", "application/json");
        // 往服务器里面发送数据
        if (Json != null && !TextUtils.isEmpty(Json)) {
            byte[] writebytes = Json.getBytes();
            // 设置文件长度
            conn.setRequestProperty("Content-Length", String.valueOf(writebytes.length));
            OutputStream outwritestream = conn.getOutputStream();
            outwritestream.write(Json.getBytes("utf-8"));
            outwritestream.flush();
            outwritestream.close();
        }
        if (conn.getResponseCode() == 200) {
            reader = new BufferedReader(
                    new InputStreamReader(conn.getInputStream()));
            result = reader.readLine();
        }
        if (reader != null) {
            reader.close();
        }
        return result;
    }
}
