package com.byun.modules.zh.vo;

import lombok.Data;

@Data
public class CreateContractVO {
    //三方签约主体编码
    private String thirdPartySubjectNo;
    //签约主体
    private String thirdPartySubjectName;
    //三方员工唯一识别码
    private String thirdPartyWorkerNo;
    //人员姓名
    private String workerName;
    //人员手机号
    private String workerPhone;
    //身份证号
    private String workerIdentityCard;
    //人员类型:6-社会工
    private Integer workerType;
    //人员来源 2-自主招募(挂靠)
    private Integer workerSource;
    //人员在职状态 1-实习,2-转正,3-离职
    private Integer workerStatus;
    //人员签约类型 1-新签,2-续约,3-换签
    private Integer contractType;
    //工作类型:1-全职,2-短期兼职,3-长期兼职
    private Integer jobType;
    //合同开始时间
    private String contractStart;
    //合同截止时间
    private String contractEnd;
    //用工关系:1-经营合作
    private Integer empRelation;
}