package com.byun.modules.zh.vo;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Title: E签宝创建签署链接入参
 * @类名: ECloudCreateSignUrlVO
 * @date 2023/11/2
 */
@Data
public class ECloudCreateSignUrlVO {
    //姓名
    private String name;
    //身份证
    private String idNo;
    //手机号
    private String phone;
    //企业名
    private String entName;
    //社会统一信用代码
    private String creditCode;
    //自然人签署区
    private String userSign;
    //企业签署区
    private String entSign;
    //模版编号
    private String tmpNo;
    //模版名称
    private String tmpName;
    //1优选 2租车 3调岗通知书 4美团有五服务合作规范（2024）
    private Integer contractType;
    //签章是否带日期
    private boolean signHaveDate;
    //创建人
    private Integer createUser;
    //分类名
    private String typeName;
    //模版填充字段
    private Map<String, String> map;
}
