package com.byun.modules.zh.vo;

import lombok.Data;

@Data
public class PersonAuthVO {
    //姓名
    private String name;
    //身份证号
    private String certNo;
    //1银行卡 2支付宝
    private Integer accountType;
    //手机号
    private String mobile;
    //银行名（accountType为支付宝，可不填）
    private String bank;
    //银行卡号（accountType为支付宝，送支付宝账号）
    private String bankNo;
    //微信公众号appId
    private String wxAppId;
    //微信openId
    private String wxOpenId;
    //身份证正面(base64)
    private String certImgFront;
    //身份证反面(base64)
    private String certImgBack;
    //身份证正面(http链接)
    private String certImgFrontHttp;
    //身份证反面(http链接)
    private String certImgBackHttp;
}
