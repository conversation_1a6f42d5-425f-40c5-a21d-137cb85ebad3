package com.byun.modules.zh.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Title:
 * @类名: WithdrawListVO
 * @date 2022/2/28
 */
@Data
public class WithdrawListVO {
    //客户提供的唯一ID 默认长度限制为50
    private String bizId;
    //姓名 不超过20个字符，不允许出现字母，数字，支持·号
    private String name;
    //身份证号
    private String certNo;
    //手机号
    private String mobile;
    //银行名
    private String bank;
    //收款账号（accountType为支付宝，送支付宝账号）
    private String bankNo;
    //金额 整数部分最多五位，小数部分最多两位，且不超过100000
    private String amount;
    //收款方式 1银行卡 2支付宝
    private Integer accountType;
}
