package com.byun.modules.zh.vo;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 单笔提现请求VO
 */
@Data
public class WithdrawVO {
    private String taskNo;
    //客户提供的唯一ID 默认长度限制为50
    private String bizId;
    //姓名 不超过20个字符，不允许出现字母，数字，支持·号
    private String name;
    //身份证号
    private String certNo;
    //手机号
    private String mobile;
    //银行名
    private String bank;
    //收款账号
    private String bankNo;
    //金额 整数部分最多五位，小数部分最多两位，且不超过100000
    private String amount;

    private Integer accountType;


    public void setAmount(String amount) {
        try {
            new BigDecimal(amount);
        } catch (Exception e) {
            this.amount = null;
            return;
        }
        if (new BigDecimal(amount).compareTo(new BigDecimal("0")) <= 0) {
            this.amount = null;
            return;
        }
        String[] split = amount.split("\\.");
        if (split[0].length() > 5) {
            this.amount = null;
            return;
        }
        if (split.length == 2 && split[1].length() > 2) {
            this.amount = null;
            return;
        }
        this.amount = amount;
    }

    public void setCertNo(String certNo) {
        if (StringUtils.isNotBlank(certNo)) {
            this.certNo = certNo.replace("x", "X");
        }
    }
}
