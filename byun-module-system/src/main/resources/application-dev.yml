server:
  port: 80
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /api
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
#SrpingBoot应用监控
management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace
spring:
  servlet:
    multipart:
      ## 上传单个文件大小限制
      max-file-size: 10MB
      ## 上传多个文件大小总限制
      max-request-size: 20MB
  mail:
    host: smtp.163.com #发送邮件服务器
    username: <EMAIL>  #发送邮件的邮箱地址
    password: QLYJHTMVIULCUJZY #客户端授权码，不是邮箱密码,网易的是自己设置的
    properties.mail.smtp.port: 465 #465或者994
    from: <EMAIL>  # 发送邮件的地址，和上面username一致
    properties.mail.smtp.starttls.enable: true
    properties.mail.smtp.starttls.required: true
    properties.mail.smtp.ssl.enable: true
    default-encoding: utf-8
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  #对于spring.jpa.open-in-view这个配置大致存在两种观点，
  #一种认为需要这个配置，它有利于提升开发效率，另一个部分人认为这个配置会影响到性能（Controller方法执行完成之后才释放连接），
  #造成资源的浪费。但是如果执行完数据库操作就释放连接的话，就无法通过get方法获取ToMany关系对应的实体集合（或者获取手动获取，但显然不合适）。
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    # 数据库监控
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          #本地Mysql版本 5.7x
          #serverTimezone=Asia/Shanghai 改为 serverTimezone=GMT%2B8
          url: *********************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置m
          #multi-datasource1:
          #url: **************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 1
    #host: *************
    #host: *************
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: ''
    port: 6379
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:com/byun/modules/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
      logic-delete-field: delFlag #全局逻辑删除字段名
      logic-delete-value: 1    #逻辑已删除值
      logic-not-delete-value: 0 #逻辑未删除值
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#byun专用配置
byun :
  # 是否启用安全模式
  safeMode: false
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: alioss
  path :
    #文件上传根目录 设置
    upload: opt/upFiles
    #webapp文件路径
    webapp: opt/webapp
  shiro:
    excludeUrls: /category/**,/visual/**,/map/**
  #阿里云oss存储和短信秘钥配置
  oss:
    accessKey: LTAI5tPYgCJyxLFUEuZAc1PE
    secretKey: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: wxlmcl-test
  #微信配置
  wechat:
    applet:
#      staAppid: wxe5a512c7689eff29
      staAppid: wx010a461e54c12866
#      staSecret: 0f958d9a5daea668a7e165f2b7d3ef2c
      staSecret: b80919b74b70eef8fc0a42273b03a5c1
#      env: cloud1-7gj6no3287853d12 #云环境
    version:
      #二维码要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
      qrcode: develop
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: staffing-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: true
#Mybatis输出sql日志
logging:
  level:
    com.byun.modules.system.mapper : info
    com.byun.modules.staffing.mapper : info
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: false
    username: byun
    password: byun999
aliyuncor:
  host: https://cardnumber.market.alicloudapi.com
  path: /rest/160601/ocr/ocr_idcard.json
  appcode: b5d1b3981ec140a78ad94ed515f11324
#实名认证
aliyunsm:
  host: https://idcert.market.alicloudapi.com
  path: /idcard
  appcode: b5d1b3981ec140a78ad94ed515f11324
#liyunsm:
#  host: https://edis3p.market.alicloudapi.com
#  path: /edis_ctid_id_name_image
#  appcode: b5d1b3981ec140a78ad94ed515f11324
#银行卡四要素认证
aliyunBankCards:
  url: https://bankcard4c.shumaidata.com/bankcard4c
  appcode: b5d1b3981ec140a78ad94ed515f11324
#根据银行卡号查询银行信息
aliyunCatBankInfo:
  host: https://bankarea.market.alicloudapi.com
  path: /lundear/bankArea
  appcode: b5d1b3981ec140a78ad94ed515f11324