server:
  port: 2311
  tomcat:
    connection-timeout: 60s
    max-swallow-size: -1
  #    max-http-header-size: 8192 //待测试
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /staff
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*
#  ssl:
#    key-store: classpath:mcl-staffing.pfx
#    key-store-password: K0i9f2Jc
#    key-store-type: PKCS12
#    key-store: classpath:byun.pfx
#    key-store-password: r9SkqgJr
#    key-store-type: PKCS12
management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  servlet:
    multipart:
      ## 上传单个文件大小限制
      max-file-size: 50MB
      ## 上传多个文件大小总限制
      max-request-size: 100MB
  mail:
    host: smtp.163.com #发送邮件服务器
    username: <EMAIL>  #发送邮件的邮箱地址
    password: XJEZNPUBFBQNAUTP #客户端授权码，不是邮箱密码,网易的是自己设置的
    properties.mail.smtp.port: 465 #465或者994
    from: <EMAIL>  # 发送邮件的地址，和上面username一致
    properties.mail.smtp.starttls.enable: true
    properties.mail.smtp.starttls.required: true
    properties.mail.smtp.ssl.enable: true
    default-encoding: utf-8
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: hnmcl_123
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 1000
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
#          url: ************************************************************************************************************************************************************************************************************************
          url: ***************************************************************************************************************************************************************************
#          username: mysql_mcl
          username: root
#          password: hnmcl_666
          password: zzbs_666
          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: **************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 2
#    host: r-8vby1ausnobxbielstpd.redis.zhangbei.rds.aliyuncs.com
    host: localhost
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: 'zzbs_666'
    port: 6379
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:com/byun/modules/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#byun专用配置
byun :
  # 是否启用安全模式
  safeMode: false
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: alioss
  path :
    #文件上传根目录 设置
    upload: opt/staffing/upload
    #webapp文件路径
    webapp: opt/staffing/webapp
  shiro:
    excludeUrls: /category/**,/visual/**,/map/**
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: LTAI5t7ohwnbxwbFDkeKjdkX
    secretKey: ******************************
    endpoint: oss-cn-zhangjiakou.aliyuncs.com
    bucketName: mcl666
  #    staticDomain: https://static.byun.com
  #微信配置
  wechat:
    applet:
      staAppid: wx4a9efad99ae4ca92
      staSecret: e90e3de48a3cdfcbc2a8246e0537d3c1
      env: cloud1-0goyqhfe723eec61 #云环境
    version:
      #二维码要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"
      qrcode: release
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: staffing-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: true
#Mybatis输出sql日志
logging:
  level:
    com.byun.modules.system.mapper : info
    com.byun.modules.staffing.mapper : info
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: true
  basic:
    enable: true
    username: byun
    password: byun999
#cor身份证文字识别
aliyuncor:
  host: https://cardnumber.market.alicloudapi.com
  path: /rest/160601/ocr/ocr_idcard.json
  appcode: b5d1b3981ec140a78ad94ed515f11324
#实名认证
aliyunsm:
  host: https://idcert.market.alicloudapi.com
  path: /idcard
  appcode: b5d1b3981ec140a78ad94ed515f11324
#liyunsm:
#  host: https://edis3p.market.alicloudapi.com
#  path: /edis_ctid_id_name_image
#  appcode: b5d1b3981ec140a78ad94ed515f11324
#银行卡四要素认证
aliyunBankCards:
  url: https://bankcard4c.shumaidata.com/bankcard4c
  appcode: b5d1b3981ec140a78ad94ed515f11324
#根据银行卡号查询银行信息
aliyunCatBankInfo:
  host: https://bankarea.market.alicloudapi.com
  path: /lundear/bankArea
  appcode: b5d1b3981ec140a78ad94ed515f11324
#zh:
#  #私钥
#  privateKey: "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCiYm5r55ehhSUbpe8yZ/CpsXwx7oxcQCBAgTdVhDwqoTw5c4C8mCvB+Jki2gEW6PCPbI0T16cHIVMAfd0xXpyTKsAGOo+y8KngPnM//qbSamPnGSXNMTMmiHTQTNKgSwzRm+fgkvmli/OssuxW4VSmq+liLayigxJPXNOqkQyDaj3c5tfa8vOmr+Idu1bPg4q22psv4zLOwlZH5xNt0kaqDlpvmIMAJBs9HhDvUa3id4iBZ+x8zjzSgkyF5FSs1DyEWe5aLfdeYMSOK4R1JTe5BiUgjmeStRs17eddjrq+TAVsnqZ1ERrQHQUlMb9pjZQoCGSXtNEf1n0gf2AaB1e5AgMBAAECggEAPiq033agZGPW2wjh1EhaqHyccXlEHyUls8xEZOblKeTka5I+nPUc+3uzFNalSCIRxrbmI8yR03b8kdmdzwLLqsPZFAljbjwYrRL7iGpzb2bvrE/CWxYU4RGotv5SPK++zpiDY0ShcrFrrpgpRvGpOM3j4YRe7TCE2AFPv6McSUrNBSM8ZuH0GdfSbLZiWVKvX4LXORAOmBs1oA+ZssWwCqla3rJkH5SQJxWDyqN7ced2tPttra7JIaGCXAU9Ct+Als3r81pvTBOR8vTyYE8WgPy0eWlmqTlkaj16M2qXHoYO2iEc4Yf1cC5S/4+AFZy0SAKeKop9oGzV7maNbS9aAQKBgQDSvOcvxpQUp0CDmL3HtJPVjyr45F78RfHrzPhEmOKef+Sgnmj0ezlXpAAY1dFal3mCn/VlBF09Jf+f33B/IV+ovZy++OBArnSnq6qlnOfI2/iqdSdo1/0vsSERD04IRCrB8BYPvZV5D+pQhkOT+V9Ju6HzOR7vTnViMMK39Wu72QKBgQDFQuQnO0MGyujOR9gfYeZVfLfKAdC9tKY9BZposXnSPUpCNsGzMKbFruTshw0MSNHl1nzZIEfKJK2RrXS92gPWLRKUJ6vsiOqkr1XHWA00QVFrBDu2R6AB2OWGSFOmPgARjLyYPZtFwbXoVQh/a4+XFWClRZnmhb97WEEY1KBu4QKBgQDJSmStDsnM/ICz8kZ8JfD7kRfjywcrgof0ysDtjPcRnGm+PfFUbQs1ulHZZwrktED9U1rrVYLV8KC9jYh/9lnP7OV2yHQzdC+7JE8Ih9oh0nMwJl0xWPXfAxrpl6vaW3pX6sK34EeQ7nHK3qRYCF9LJ20mzI3O2StoUdj8K/Du6QKBgGVGXXqi21uua24Uxqn+ClKTkIx++Budlse3i7n6fE/rNaffEv2bmdPeYhvpjlJjRJha0YhsIU9wG0iypEAgoV2hkGtOHt92v/lDJ6gL1eOhMVrfNoT0Kvsnz1ds5L1yEXjxJ5aF8qUSugEJPy6kG3l2+UKBBG2s900tV2aXK7MhAoGAa/4ZloP53/uX5AwIYmHiZrPMJAJensqomTfQbbsUrVKZBCqjafdlMyra4msLXJ4Ge9J514c0ZL9ck11pVzcQQkosV8qh7jdzKDJZtjrhs1wFsBowDWBfO5Gy7fLPTF/2TXMIHyTGEwv0NbDwzoOJ9hxCwyuDzf1FcLoTEl9+OR8="
#  #客户端编码
#  clientId: "967a7eafd9c3d117d4fa139e443ffa99"
#  #客户端密码
#  secret: "acc6b6a60fc127a6aac6a078253159fe"
#  #Token
#  getTokenUrl: "https:///jlmapinew.zanhua.com.cn/api/open/authorize"
#  getTokenParam: "grant_type=client_credentials"
#  #请求路径
#  commonUrl: "https:///jlmapinew.zanhua.com.cn/api/v1"
#  taskNo: "QY20240603629900001"
