<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.byun</groupId>
    <artifactId>byun-parent</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1-SNAPSHOT</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.5.RELEASE</version>
        <relativePath/>
    </parent>
    <modules>
        <module>byun-base</module>
        <module>byun-module-system</module>
    </modules>

    <properties>
        <byun.version>0.0.1-SNAPSHOT</byun.version>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <fastjson.version>1.2.78</fastjson.version>
        <bouncycastle.version>1.46</bouncycastle.version>
        <knife4j-spring-boot-starter.version>2.0.9</knife4j-spring-boot-starter.version>
        <knife4j-spring-ui.version>2.0.9</knife4j-spring-ui.version>
        <!-- 数据库驱动 -->
        <mysql-connector-java.version>8.0.27</mysql-connector-java.version>
        <!-- 动态数据源-->
        <dynamic-datasource-spring-boot-starter.version>3.2.0</dynamic-datasource-spring-boot-starter.version>
        <hutool.version>5.3.8</hutool.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <guava.version>29.0-jre</guava.version>
        <mybatis-plus.version>3.4.3.1</mybatis-plus.version>
        <druid.version>1.1.22</druid.version>
        <commons.version>2.6</commons.version>
        <aliyun-java-sdk-dysmsapi.version>2.1.0</aliyun-java-sdk-dysmsapi.version>
        <aliyun.oss.version>3.11.2</aliyun.oss.version>
        <shiro.version>1.7.1</shiro.version>
        <java-jwt.version>3.11.0</java-jwt.version>
        <shiro-redis.version>3.1.0</shiro-redis.version>
        <minio.version>8.0.3</minio.version>
        <justauth-spring-boot-starter.version>1.3.4</justauth-spring-boot-starter.version>
        <dom4j.version>1.6.1</dom4j.version>
        <autopoi-web.version>1.3.5</autopoi-web.version>
        <poi-tl.version>1.9.1</poi-tl.version>
        <!--解决log4j 注入问题，版本升级到2.15.0-rc2以上,中央仓库目前没有需要下载到本地https://github.com/apache/logging-log4j2/releases/tag/log4j-2.15.0-rc2-->
        <log4j2.version>2.15.0</log4j2.version>
    </properties>


    <dependencies>
        <!-- 移除旧的spring-mock依赖，它会引入servlet-api 2.3，与Spring Boot 2.3.5不兼容 -->
        <!-- <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-mock</artifactId>
            <version>2.0.8</version>
        </dependency> -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--电子合同-开始-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.11</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.itextpdf/itext-asian -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!--电子合同-结束-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.20</version>
            <scope>provided</scope>
        </dependency>

<!--        赞华开始-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-ext-jdk15on</artifactId>
            <version>1.60</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
<!--        赞华结束-->
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- json -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <!--BouncyCastle库中的算法，小程序解密用-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk16</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <!-- system 模块-->
            <dependency>
                <groupId>com.byun</groupId>
                <artifactId>byun-module-system</artifactId>
                <version>${byun.version}</version>
            </dependency>

            <!-- byun tools -->
            <dependency>
                <groupId>com.byun</groupId>
                <artifactId>byun-base-tools</artifactId>
                <version>${byun.version}</version>
            </dependency>
            <!-- byun core -->
            <dependency>
                <groupId>com.byun</groupId>
                <artifactId>byun-base-core</artifactId>
                <version>${byun.version}</version>
            </dependency>
            <!-- system 单体 api -->
            <dependency>
                <groupId>com.byun</groupId>
                <artifactId>byun-base-api</artifactId>
                <version>${byun.version}</version>
            </dependency>

            <!-- dom4j -->
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <!-- guava工具类 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- hutool工具类-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- commons-beanutils -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- justauth第三方登录  -->
            <dependency>
                <groupId>com.xkcoding.justauth</groupId>
                <artifactId>justauth-spring-boot-starter</artifactId>
                <version>${justauth-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hutool-core</artifactId>
                        <groupId>cn.hutool</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.4.1</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>okio</artifactId>
                        <groupId>com.squareup.okio</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okhttp</artifactId>
                        <groupId>com.squareup.okhttp3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--<plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
             指定JDK编译版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 打包跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- 避免font文件的二进制文件格式压缩破坏 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.ftl</include>
                </includes>
            </resource>
        </resources>
    </build>
    <!-- 环境 -->
    <profiles>
        <!-- 开发 -->
        <profile>
            <id>dev</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>
                <!--配置文件前缀-->
                <prefix.name>byun</prefix.name>
            </properties>
        </profile>
        <!-- 测试 -->
        <profile>
            <id>test</id>
            <properties>
                <!--当前环境-->
                <profile.name>test</profile.name>
                <!--配置文件前缀-->
                <prefix.name>byun</prefix.name>
            </properties>
        </profile>
        <!-- 生产 -->
        <profile>
            <id>prod</id>
            <properties>
                <!--当前环境,生产环境为空-->
                <profile.name>prod</profile.name>
                <!--配置文件前缀-->
                <prefix.name>byun</prefix.name>
            </properties>
        </profile>
    </profiles>

</project>